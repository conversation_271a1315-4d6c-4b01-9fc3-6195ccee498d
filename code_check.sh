#!/bin/bash
MASTER_BRANCH="origin/RELEASE"

init() {
   SCRIPT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)/$(basename "${BASH_SOURCE[0]}")"
   GIT_HOOKS="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)/.git/hooks/pre-commit"
   cp ${SCRIPT_PATH} ${GIT_HOOKS}
   chmod u+x ${GIT_HOOKS}
   echo "done"
}

do_check() {
    while getopts 'ad' OPT; do
        case ${OPT} in
            a)
                CHECK_ALL="all";;
        esac
    done

    which mvn  &> /dev/null
    if [[ "$?" == 1 ]]; then
        echo "[ERROR] Please add the 'mvn' directory to your PATH"
        exit 1
    fi

    if [ "$CHECK_ALL" != "all" ]; then
        echo "[INFO] Change java file list:";
        changed_files=$(git diff --name-only --cached | grep .java ;git diff --name-only| grep .java ;git diff --name-only HEAD ${MASTER_BRANCH}| grep .java;);
        changed_files_str=$(echo "${changed_files[*]}" | awk '{print}' ORS=',')
        echo "${changed_files[*]}" | awk '{print "\t" $0}' ORS='\n'
    else
        changed_files_str="**/*.java"
        echo "[INFO] Change all java file";
    fi
    if [ "$changed_files_str" != "," ];then
        res=$(mvn pmd:check -N -Dincludes=${changed_files_str})
        res_code=$?
        code_check_info=$(echo "${res[*]}" | grep -F "PMD Failure")
        if [[ ${code_check_info} == '' && ${res_code} == 1 ]];then
            echo "${res[*]}"
        fi

        if [ ${res_code} -eq 0 ]
        then
            echo "[INFO] Code review completed"
            exit 0
        else
            echo "${res[*]}"| grep -F "PMD Failure"
            echo "[ERROR] There is not standard code, please correct after the submission. Or use 'git commit --no-verify' to ignore code checks"
            exit 1
        fi
    fi
}

case "$1" in
        init)
                init
                ;;
        do_check)
                do_check
                ;;
        *)
                do_check $@
esac