package api;

import base.BaseTest;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.constants.ProcessStatusConstant;
import com.mioffice.ums.admin.controller.api.MessageController;
import com.mioffice.ums.admin.entity.bo.*;
import com.mioffice.ums.admin.entity.dto.LarkTaskDTO;
import com.mioffice.ums.admin.entity.dto.MessagePrePushDTO;
import com.mioffice.ums.admin.entity.vo.AlarmTrendVO;
import com.mioffice.ums.admin.entity.vo.TaskMonitorListVO;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.manager.PostGuardValidate;
import com.mioffice.ums.admin.manager.SendMessage;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.BotApiService;
import com.mioffice.ums.admin.service.MessageService;
import com.mioffice.ums.admin.service.WarningService;
import com.mioffice.ums.admin.utils.JsonUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.14
 */
public class ApiTest extends BaseTest {

    @Autowired
    private MessageController messageController;

    @Autowired
    private MessageService messageService;

    @Autowired
    private BotApiService botApiService;

    @Autowired
    private PostGuardValidate postGuardValidate;

    @Test
    public void testAddMessageTask() {

        UserBO userBO = new UserBO("yangguanlin", "<EMAIL>");

        LarkTaskDTO larkTaskDTO = new LarkTaskDTO();
        larkTaskDTO.setSubmitType((byte) 1);
        larkTaskDTO.setBotId(1L);
        larkTaskDTO.setPublishDate("2020-08-20 10:30:00");
        larkTaskDTO.setLarkMessageType((byte) 4);
        larkTaskDTO.setPublishScope(PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode());
        larkTaskDTO.setScopeKey(Arrays.asList("7384723947923"));
        larkTaskDTO.setTitleCn("测试消息");
        larkTaskDTO.setContentCn("测试消息，请忽略");
        larkTaskDTO.setButton((byte) 1);


//        messageController.addMessageTask(userBO, larkTaskDTO);
    }

    @Test
    public void testUpdateMessageTask() {
        UserBO userBO = new UserBO("niuwenyu", "<EMAIL>");

        LarkTaskDTO larkTaskDTO = new LarkTaskDTO();
        larkTaskDTO.setTaskId(5L);
        larkTaskDTO.setSubmitType((byte) 1);
        larkTaskDTO.setBotId(1L);
        larkTaskDTO.setLarkMessageType((byte) 2);
        larkTaskDTO.setPublishScope((byte) 1);
        larkTaskDTO.setTitleCn("测试消息");
        larkTaskDTO.setContentCn("测试富文本消息，请忽略");
        larkTaskDTO.setButton((byte) 1);

        messageController.updateLarkMessageTask(userBO, larkTaskDTO);
    }

    @Test
    public void testPreSend() throws InterruptedException {
        UserBO userBO = new UserBO("yangguanlin", "<EMAIL>");

        MessagePrePushDTO messagePrePushDTO = new MessagePrePushDTO();
        messagePrePushDTO.setTaskId(74L);
        messagePrePushDTO.setUsernameList(Arrays.asList("yangguanlin", "yangguanlin"));

        messageService.prePushMsg(userBO, messagePrePushDTO);

        Thread.sleep(10000);
    }


    @Autowired
    private SendMessage sendMessage;

    @Test
    public void testSendBotApplyCard() {

        BotApplyCardContentBo botApplyCardContentBo = new BotApplyCardContentBo();
        botApplyCardContentBo.setApplyDesc("工作需要");
        botApplyCardContentBo.setBotApplyId(1L);
        botApplyCardContentBo.setName("牛文雨");
        botApplyCardContentBo.setCreateUsername("yangguanlin");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("landingUrl", "http://www.baidu.com");
        botApplyCardContentBo.setAction(paramMap);
        botApplyCardContentBo.setRobotName("小米行政");


        botApplyCardContentBo.setPushUserList(Arrays.asList(new UserBO("niuwenyu", "")));

        boolean isSend = sendMessage.sendBotApplyCardMessage(botApplyCardContentBo);
        Assert.assertTrue(isSend);
    }

    @Test
    public void testSendBotApplyResultCard() {

        BotApproveCardContentBo bo = new BotApproveCardContentBo();
        bo.setApproveName("杨冠林");
        bo.setApproveUsername("yangguanlin");
        bo.setName("牛文雨");
        bo.setCreateUsername("niuwenyu");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("landingUrl", "http://www.baidu.com");
        bo.setAction(paramMap);
        bo.setProcessStatus(ProcessStatusConstant.ACTION_REJECT);

        bo.setRobotName("小米行政");

        bo.setRefuseReason("通过");

        boolean isSend = sendMessage.sendBotApplyResultCardMessage(bo);
        Assert.assertTrue(isSend);
    }

    @Autowired
    private WarningService warningService;

    private UserBO userBO = new UserBO("yangguanlin", null);

    @Test
    public void testWarn() {

        BaseResult<IPage<AlarmBo>> alarmPage = warningService.getAlarmPage(userBO, 1L, 10L);
        System.out.println(JsonUtils.toJson(alarmPage));
    }

    @Test
    public void testWarn01() {
        DateTime beginTime = DateUtil.beginOfDay(DateUtil.parseDate("2020-09-10"));
        DateTime endTime = DateUtil.endOfDay(DateUtil.parseDate("2020-09-15"));
        BaseResult<AlarmTrendVO> alarmTrend = warningService.getAlarmTrend(userBO, beginTime, endTime);
        System.out.println(JsonUtils.toJson(alarmTrend));
    }

    @Test
    public void testWarn02() {
        BaseResult<TaskMonitorListVO> nowMonitorList = warningService.getNowMonitorList(userBO);
        System.out.println(JsonUtils.toJson(nowMonitorList));
    }

    @Test
    public void testWarn03() {
        System.out.println(postGuardValidate.getPostGuardBoWithToken("<EMAIL>"));
    }
}
