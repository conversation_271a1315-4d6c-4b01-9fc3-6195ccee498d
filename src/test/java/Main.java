import com.mioffice.ums.admin.utils.AesEcbUtils;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.18
 */
public class Main {

    public static void main(String[] args) throws Exception {
//        NotifyDataDecrypter notifyDataDecrypter = new NotifyDataDecrypter("xQiQhOXvSlAciFODeUuHfb2JQLw7jiGO");
//        String decrypt = notifyDataDecrypter.decrypt("5JNTGRq1jKVWTabPuIXKJFQ2G8BaJIxTDbbSyt26ihQMnfgI9tqTyOXfOcishh63QoZoGqEl/LJquQ5jriup9Qo1gNtquxv18v+7drsX6FjmZkXDmlQv8xA7bVWTXSyVuduNfUS5px/ZQFubdO+LzUY32S/6qJVP+2lcjTzjOp9EkHSa6eLMm38OCayjJ7M6");
//        System.out.println(decrypt);
//        System.out.println(notifyDataDecrypter);

//        String process = "{\"processId\":\"messageRelease\",\"processName\":\"消息发布审核\",\"startTask\":{\"taskId\":\"1\",\"taskName\":\"机器人管理员审批\",\"assignees\":\"${managers}\",\"nextTask\":{\"taskId\":\"2\",\"taskName\":\"运营负责人审批\",\"assignees\":\"${operaters}\",\"nextTask\":{\"taskId\":\"3\",\"taskName\":\"部门VP审批\",\"assignees\":\"${vp}\"}}}}";
//
//        System.out.println(process);
//
//
//        Map<String, Object> map = new HashMap<>(2);
//        map.put("name", "消息服务助手");
//        map.put("sender", "<EMAIL>");
//
//        List<Map<String, Object>> list = Arrays.asList(map);
//        Map<String, Object> data = new HashMap<>(1);
//        data.put("list", list);
//
//        BaseResult<Map<String, Object>> result = BaseResult.of(data);
//        System.out.println(JsonUtil.toJson(result));

        System.out.println(AesEcbUtils.decrypt("W06OvZBdsscqxQCyfl3pHg=="));
        System.out.println(AesEcbUtils.decrypt("W06OvZBdsscqxQCyfl3pHg=="));
        System.out.println("--------");

        System.out.println("https://applink.feishu.cn/client/mini_program/open?appId\u003dcli_9f6b19b74f779062\u0026path\u003dpages%2Fgaode%2Findex%3Fapi%3Dgoing%26amapOrderId%3D152064388330003364076210");

    }
}
