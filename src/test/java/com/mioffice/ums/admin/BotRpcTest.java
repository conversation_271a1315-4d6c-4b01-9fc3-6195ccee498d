package com.mioffice.ums.admin;

import base.BaseTest;
import com.mioffice.ums.admin.entity.dto.RobotRpcDTO;
import com.mioffice.ums.admin.remote.grpc.MessageBotGrpcClient;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.11
 */
public class BotRpcTest extends BaseTest {

    @Autowired
    private MessageBotGrpcClient messageBotGrpcClient;

    @Test
    public void test01() {

        RobotRpcDTO robotRpcDTO = new RobotRpcDTO();
        robotRpcDTO.setAppId("cli_9e6998059bb59062");
        robotRpcDTO.setAppSecret("KkKNIGGgjv4IYDMT481Rwg8cW5yuiDqX");
        robotRpcDTO.setAppShortName("administration");

        messageBotGrpcClient.addRobot(robotRpcDTO);
    }
}
