package com.mioffice.ums.admin;

import base.BaseTest;
import com.mioffice.ums.admin.service.SendWeeklyMsgService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2021/3/25 12:25 上午
 */
@Slf4j
public class RefreshThresholdTest extends BaseTest {

    @Autowired
    private SendWeeklyMsgService sendWeeklyMsgService;

    @Test
    public void test1() {
        sendWeeklyMsgService.refreshToken();
    }

    @Test
    public void test2() {
        sendWeeklyMsgService.processAccessToken("zzbYLvpBwxCmqxqa58Hiha", "1");
    }
}
