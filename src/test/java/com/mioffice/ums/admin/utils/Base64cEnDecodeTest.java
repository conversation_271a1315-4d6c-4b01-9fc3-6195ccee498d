package com.mioffice.ums.admin.utils;

import base.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.net.URLEncoder;
import java.util.Base64;

/**
 * @ClassName AesUtilsTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/7/17 9:57
 **/
@Slf4j
public class Base64cEnDecodeTest extends BaseTest {
    @Test
    public void testEncDec(){
        try {
            String urlEnc = URLEncoder.encode("https://www.qinghedaxue.com/clientcn/exam/20230419165945104da792547dc107a0fd1a?flag=test","UTF-8");
            log.info(urlEnc);
            String enc =
                    Base64.getEncoder().encodeToString(("junfudong|920e946aff414a0dbf6b3e1dd845ac8d|"+urlEnc).getBytes());
            log.info(enc);
            String dec= new String(Base64.getDecoder().decode(enc));
            log.info(dec);
        } catch (Exception e) {
            log.error("",e);
        }
    }
}
