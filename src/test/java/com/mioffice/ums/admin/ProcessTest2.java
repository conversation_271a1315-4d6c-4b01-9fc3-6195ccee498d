package com.mioffice.ums.admin;

import base.BaseTest;
import com.mioffice.ums.admin.entity.dto.TaskApprovalNodeQueryDTO;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.enums.TaskChannelEnum;
import com.mioffice.ums.admin.process.MessageProcess;
import com.mioffice.ums.admin.service.ApprovalConfigService;
import com.mioffice.ums.admin.service.UserService;
import com.mioffice.ums.admin.utils.JsonUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/3 5:59 下午
 */
public class ProcessTest2 extends BaseTest {

    @Autowired
    private MessageProcess messageProcess;

    @Autowired
    private ApprovalConfigService approvalConfigService;

    @Autowired
    private UserService userService;

    @Test
    public void test1(){
//        messageProcess.createProcess();
        System.out.println(userService.getPreSendList(-1l, "shitietou"));
    }

    @Test
    public void test2(){
        System.out.println(JsonUtils.toJson(approvalConfigService.getApprovalTimeLine(371L, null)));
        TaskApprovalNodeQueryDTO taskApprovalNodeQueryDTO = new TaskApprovalNodeQueryDTO();
        taskApprovalNodeQueryDTO.setBotId(7L);
        taskApprovalNodeQueryDTO.setPublishScope(PublishScopeEnum.ALL_PUSH.getCode());
        taskApprovalNodeQueryDTO.setChannel(TaskChannelEnum.CHANNEL_LARK.getCode());
        System.out.println(JsonUtils.toJson(approvalConfigService.getApproval(taskApprovalNodeQueryDTO, "shitietou")));

    }
}
