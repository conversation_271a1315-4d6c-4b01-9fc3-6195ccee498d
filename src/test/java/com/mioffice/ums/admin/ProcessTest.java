package com.mioffice.ums.admin;

import base.BaseTest;
import com.google.gson.Gson;
import com.mioffice.ums.admin.entity.bo.ProcessCreation;
import com.mioffice.ums.admin.entity.bo.ProcessDefinition;
import com.mioffice.ums.admin.manager.ProcessDefinitionResource;
import com.mioffice.ums.admin.manager.ProcessManager;
import org.junit.Assert;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/14 18:18
 */
public class  ProcessTest extends BaseTest {

    private Logger log = LoggerFactory.getLogger(ProcessTest.class);

    @Autowired
    private ProcessManager processManager;
    @Autowired
    private ProcessDefinitionResource processDefinitionResource;

    @Test
    public void processGenerator() {
        ProcessDefinition messageProcess = new ProcessDefinition();
        messageProcess.setProcessId("messageRelease");
        messageProcess.setProcessName("消息发布审核");

        ProcessDefinition.Task startTask = new ProcessDefinition.Task();
        startTask.setTaskId("1");
        startTask.setTaskName("机器人管理员审批");
        startTask.setAssignees("${managers}");

        ProcessDefinition.Task task2 = new ProcessDefinition.Task();
        task2.setTaskId("2");
        task2.setTaskName("运营负责人审批");
        task2.setAssignees("${operaters}");

        ProcessDefinition.Task task3 = new ProcessDefinition.Task();
        task3.setTaskId("3");
        task3.setTaskName("部门VP审批");
        task3.setAssignees("${vp}");

        startTask.setNextTask(task2);
        task2.setNextTask(task3);

        messageProcess.setStartTask(startTask);
        Gson gson = new Gson();
        String processJson = gson.toJson(messageProcess);
        log.info(processJson);
        // ************************************************
    }

    @Test
    public void deployTest() {
        System.out.println(new Gson().toJson(processDefinitionResource.get((byte)1)));
    }

    @Test
    public void createProcessInstance() {
        Map<String, Object> variables = new HashMap<>(3);
        variables.put("managers", "shitietou");
        variables.put("customize", "niuwenyu");

        ProcessCreation processCreation = new ProcessCreation(ProcessDefinitionResource.PROCESS_MESSAGE_RELEASE,
                76L, "",variables, "shitietou", (byte) 1);

        processManager.createProcessInstance(processCreation);
    }

    @Test
    public void approval() throws InterruptedException {
        processManager.approval(76L, 328L, "shitietou", (byte) 1);
        // 1597851837197
        // 1597851837229

        /*new Thread(() -> processManager.approval(52L, 236L, "wangchen21")).start();
        new Thread(() -> processManager.approval(52L, 237L, "yangguanlin")).start();


        Thread.sleep(5000);*/

    }

    @Test
    public void reject() {
        processManager.reject(52L, 154L, "不同意，请重新编辑", "yangguanlin",(byte)1);
    }

    @Test
    public void terminate() {
        processManager.terminate(52L, "wangchen21");
    }
}
