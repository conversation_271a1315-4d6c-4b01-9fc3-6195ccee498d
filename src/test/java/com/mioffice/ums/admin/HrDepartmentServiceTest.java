package com.mioffice.ums.admin;

import base.BaseTest;
import com.mioffice.ums.admin.service.HrDepartmentService;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * @ClassName HrDepartmentServiceTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/5/8 10:04
 **/
public class HrDepartmentServiceTest extends BaseTest {
    @Resource
    private HrDepartmentService hrDepartmentService;

    @Test
    public void testFullSync(){
        hrDepartmentService.syncDepartmentWithFullLoad();
    }

    @Test
    public void testCalculate(){
        hrDepartmentService.asyncCalculateCompleteDeptName();
    }
}
