package com.mioffice.ums.admin;

import base.BaseTest;
import com.mioffice.ums.admin.exception.RobotVerifyException;
import com.mioffice.ums.admin.manager.RobotVerifyManager;
import com.mioffice.ums.admin.service.BotApiService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020/12/18 4:31 下午
 */
public class BotVerifyTest extends BaseTest {

    @Autowired
    private BotApiService botApiService;

    @Autowired
    private RobotVerifyManager robotVerifyManager;

    @Test
    public void test1(){
        try {
            System.out.println(robotVerifyManager.getTenantAccessToken("cli_9f3e029ca9731062", "v7dAHBmPLiAtebSDvcwJLffhDI3RDHOl"));
        } catch (RobotVerifyException e) {
            e.printStackTrace();
        }
    }

}
