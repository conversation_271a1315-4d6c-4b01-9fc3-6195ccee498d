package com.mioffice.ums.admin;

import base.BaseTest;
import com.mioffice.ums.admin.manager.UserRoleManager;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/7 5:18 下午
 */
public class RoleTest extends BaseTest {

    @Autowired
    private UserRoleManager userRoleManager;

    @Test
    public void test(){
        System.out.println(userRoleManager.getUserRole("zhangqingbo3", 11L));
//        System.out.println(userRoleManager.isSuperAdmin("shitietou"));
//        System.out.println(userRoleManager.isSystemAdmin("shitietou"));
//        System.out.println(userRoleManager.isBotManager("shitietou", 2L));
//        System.out.println(userRoleManager.isOperator("shitietou"));

    }

}
