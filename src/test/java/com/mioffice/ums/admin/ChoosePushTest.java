package com.mioffice.ums.admin;

import base.BaseTest;
import com.mioffice.ums.admin.controller.api.ChooseController;
import com.mioffice.ums.admin.entity.vo.ChooseListVO;
import com.mioffice.ums.admin.result.BaseResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.11.11
 */
public class ChoosePushTest extends BaseTest {

    @Autowired
    private ChooseController chooseController;

    @Test
    public void testApi() {
        BaseResult<ChooseListVO> chooseListVOBaseResult = chooseController.chooseList();
        System.out.println(chooseListVOBaseResult);
    }
}



