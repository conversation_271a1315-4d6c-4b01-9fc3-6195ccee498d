package com.mioffice.ums.admin;

import base.BaseTest;
import com.google.gson.Gson;
import com.mioffice.ums.admin.controller.api.SysDashBoardController;
import com.mioffice.ums.admin.entity.vo.AppMessageChannelVO;
import com.mioffice.ums.admin.entity.vo.AppMessageCountVO;
import com.mioffice.ums.admin.entity.vo.AppSummaryVO;
import com.mioffice.ums.admin.entity.vo.ListVO;
import com.mioffice.ums.admin.result.BaseResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.26
 */
public class SysDashBoardTest extends BaseTest {

    @Autowired
    private SysDashBoardController sysDashBoardController;

    @Autowired
    private Gson gson;

    @Test
    public void test01() {

        BaseResult<ListVO<AppMessageCountVO>> sysMessageTop = sysDashBoardController.getSysMessageTop(null, "2020-09-09", "2020-10-25");
        System.out.println(gson.toJson(sysMessageTop));
    }

    @Test
    public void test02() {
        BaseResult<ListVO<AppMessageChannelVO>> sysMessageChannelCount = sysDashBoardController.getSysMessageChannelCount(null, "1,12", "2019-09-09", "2022-10-25");
        System.out.println(gson.toJson(sysMessageChannelCount));
    }

    @Test
    public void test03() {
        BaseResult<ListVO<AppSummaryVO>> appSummaryList = sysDashBoardController.getAppSummaryList();
        System.out.println(gson.toJson(appSummaryList));
    }

    @Test
    public void test100() {
        test01();
        test02();
    }
}
