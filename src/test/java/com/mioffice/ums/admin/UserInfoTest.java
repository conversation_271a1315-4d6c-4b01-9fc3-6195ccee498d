package com.mioffice.ums.admin;

import base.BaseTest;
import com.mioffice.ums.admin.manager.UserInfoManager;
import com.mioffice.ums.admin.utils.AesEcbUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/8 1:12 下午
 */
@Slf4j
public class UserInfoTest extends BaseTest {

    @Autowired
    private UserInfoManager userInfoManager;

    @Test
    public void Test() {
//        Gson gson = new Gson();
//        System.out.println(gson.toJson(userInfoManager.getBotManagerCount()));
//        System.out.println(gson.toJson(userInfoManager.getBotManagerByDeptId("IT")));
//
//        System.out.println(gson.toJson(userInfoManager.getTaskReleaseByDeptId("IT", "")));

        Map<String, String> map = new HashMap<String, String>() {{
            put("fengmingdong", "gzH2K03c4dAOxCQoq2Ki5g==");
            put("liuben", "HJ0Cy5s9fRle09PLpdAxWA==");
            put("liujialin6", "l99kVx47LbR/7Hf48V30Zw==");
            put("luoyifan1", "BRR54B6z1Eks9P+zVXLqVw==");
            put("ouyangshuang", "7/8mZpCMz5mvWYuimKt6cA==");
            put("raojian", "YRlb8vYbONfSm7jW8D1Tww==");
            put("shizhen", "4e12QjAZDWeifnCOlk5RMg==");
            put("songdenghui1", "HrBdjbUH3t61HS87eLeoIg==");
            put("sunsheng", "BgfjrMuHUPIAyzMu++HUqA==");
            put("v-luyunfeng", "P3GV98cPuMmYhvyb2amCuA==");
            put("wangchen21", "TJZtKzMT7+IqRpNvlCMMFg==");
            put("wangjun30", "brjte/nIGl48EqrzfLtzhw==");
            put("wangwei77", "WSFeaWpCng5SJkGN+UL6Jw==");
            put("wubo6", "HXUz+3KDIvMPipygSePYCw==");
            put("wuhao10", "KjeI7JUkl4r+FLcXog2lHA==");
            put("zhuzhichuan", "4RJGLrmHEyaohnbPJrGM8A==");
            put("zuolei1", "gaz+VcWQl0Do9g2bmz57RA==");
            put("chijiaojiao","351y4hbPX0gC3fVyvjJsOg==");
            put("luweibing1","i6m6llXNJoHvYJZsnApxnw==");
        }};

        map.forEach((key, value) -> {
            try {
                String phone = AesEcbUtils.decrypt(value);
                log.info("{},{}", key, phone);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

    }

}
