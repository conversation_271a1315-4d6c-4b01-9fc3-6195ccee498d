package com.mioffice.ums.admin;

import base.BaseTest;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.entity.bo.CostTimeFromSqlBO;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.BotApplyApprovalPageDTO;
import com.mioffice.ums.admin.entity.dto.BotApplyDTO;
import com.mioffice.ums.admin.entity.dto.BotApplyDetailDTO;
import com.mioffice.ums.admin.entity.dto.BotApplyListDTO;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import com.mioffice.ums.admin.service.BotApprovalService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/1 8:07 下午
 * version: 1.0.0
 */
public class BotApplyTest extends BaseTest {

    @Autowired
    private BotApprovalService botApprovalService;

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Test
    public void test() {
        UserBO userBO = new UserBO("duxiangming", "<EMAIL>");
        IPage<BotApplyApprovalPageDTO> botApplyApprovalPageDTOIPage = botApprovalService.botApprovalPage(userBO, 1L, 10L);
        System.out.println(botApplyApprovalPageDTOIPage);
    }

    @Test
    public void testApply(){
        UserBO userBO = new UserBO("niuwenyu", "<EMAIL>");
        BotApplyDTO botApplyDTO = new BotApplyDTO();
        botApplyDTO.setApplyDesc("测试");
        botApplyDTO.setBotId(6L);
        botApplyDTO.setUseUsernameList(Collections.singletonList("duxiangming"));
        botApprovalService.botApply(botApplyDTO, userBO);
    }

    @Test
    public void testApplyList() {
        UserBO userBO = new UserBO("duxiangming", "<EMAIL>");
        IPage<BotApplyListDTO> botApplyListDTOIPage = botApprovalService.botApplyListMy(userBO, 1L, 10L);
        System.out.println(botApplyListDTOIPage);
    }

    @Test
    public void test1() {
        BotApplyDetailDTO botApplyDetailDTO = botApprovalService.botApplyDetail(1L);
        System.out.println(botApplyDetailDTO);
    }
    
}
