package com.mioffice.ums.admin;

import base.BaseTest;
import com.mioffice.ums.admin.enums.BotTenantEnum;
import com.mioffice.ums.admin.manager.DeptManager;
import com.mioffice.ums.admin.service.DeptService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020/8/17 10:41
 */
public class DeptTest extends BaseTest {

    @Autowired
    DeptService deptService;

    @Autowired
    DeptManager deptManager;

    @Test
    public void test() {
        System.out.println(deptService.getDeptCount("IT,IT37", BotTenantEnum.BOT_TENANT_MI));
    }

    @Test
    public void test2() {
        System.out.println(deptManager.filterDeptIds(new ArrayList<String>(Arrays.asList("OT15", "OT08", "OT19", "OT18", "OT17", "OT03", "OT16", "OT09", "OT12", "OT11", "OT",
               "IT40", "IT34", "IT41", "IT39", "IT38", "IT36", "IT3601","IT37", "IT35", "IT10",
                "MW66", "MW63", "MW65", "MW64"))));
    }
    @Test
    public void test3() {
        System.out.println(deptManager.getFirstLevelDeptString(new ArrayList<String>(Arrays.asList("OT15", "OT08", "OT19", "OT18", "OT17", "OT03", "OT16", "OT09", "OT12", "OT11","OT",
                "IT3601","IT41",
                "MW66", "MW63", "MW65", "MW64"))));
    }

    @Test
    public void test4() {
        System.out.println(deptService.getDeptBySearchWord("信息",BotTenantEnum.BOT_TENANT_MI));
    }
}
