package com.mioffice.ums.admin;

import base.BaseTest;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Preconditions;
import com.mioffice.ums.admin.constants.ProcessStatusConstant;
import com.mioffice.ums.admin.entity.bo.MsgApplyCardContentBo;
import com.mioffice.ums.admin.entity.bo.MsgApproveCardContentBo;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.bo.WarningCardContentBo;
import com.mioffice.ums.admin.entity.dto.LarkTaskDTO;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.LarkButtonEnum;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.manager.impl.SendMessageManager;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import com.mioffice.ums.admin.service.MessageService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * date 2020.08.18
 */
public class SendMsgTest extends BaseTest {

    @Autowired
    private SendMessageManager sendMessageManager;

    @Autowired
    private EmployeeInfoMapper employeeInfoMapper;

    @Autowired
    private MessageService messageService;

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Test
    public void testSendApplyCard() {
        MsgApplyCardContentBo msgApplyCardContentBo = new MsgApplyCardContentBo("测试消息", 1L, 1L, "https://www.baidu.com", "杨冠林", "yangguanlin", "2020-10-25", 5000L, "小米行政", PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE,
                Arrays.asList(new UserBO("yangguanlin", "<EMAIL>"), new UserBO("shitietou", "<EMAIL>"), new UserBO("niuwenyu", "<EMAIL>"))
        );


        boolean isSend = sendMessageManager.sendApplyCardMessage("cli_9e6998059bb59062", msgApplyCardContentBo);
        Preconditions.checkArgument(isSend, "发送失败");
    }

    @Test
    public void testSendApplyResultCard() {
        MsgApproveCardContentBo msgApproveCardContentBo = new MsgApproveCardContentBo();
        msgApproveCardContentBo.setTitle("测试消息");
        msgApproveCardContentBo.setApproveName("时铁头");
        msgApproveCardContentBo.setApproveUsername("shitietou");
        msgApproveCardContentBo.setCreateEmail("<EMAIL>");
        msgApproveCardContentBo.setCreateName("杨冠林");
        msgApproveCardContentBo.setCreateUsername("yangguanlin");
        msgApproveCardContentBo.setLandingUrl("https://www.baidu.com");
        msgApproveCardContentBo.setProcessStatus(ProcessStatusConstant.ACTION_AGREE);

        boolean isSend = sendMessageManager.sendApplyResultCardMessage("cli_9e6998059bb59062", msgApproveCardContentBo);
        Preconditions.checkArgument(isSend, "发送失败");
    }

    @Test
    public void testSendMsg() throws InterruptedException {

//        TaskInfo taskInfo = taskInfoMapper.selectById(153);
        sendMessageManager.syncSendMessageTask(153L, null);

//        Thread.sleep(1000 * 100);
    }

    @Test
    public void testCount() {

        List<String> scopeKeyList = Arrays.asList("IT40","IT34","IT41","IT39","IT38","IT36","IT37","IT35","IT10","IT");
        int allCount = employeeInfoMapper.selectCount(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                        .ne(EmployeeInfo::getUsername, "")
                        .and(wrapper -> wrapper.in(EmployeeInfo::getMiDeptLevel2, scopeKeyList).or().in(EmployeeInfo::getMiDeptLevel3, scopeKeyList))

        );

        System.out.println("allCount = " + allCount);
    }


    @Test
    public void testSend() throws InterruptedException {
        messageService.triggerSendTask();

        Thread.sleep(60000);
    }

    @Test
    public void testAddMessageTask() throws InterruptedException {

        UserBO userBO = new UserBO("yangguanlin", "");

        LarkTaskDTO larkTaskDTO = new LarkTaskDTO();
        larkTaskDTO.setBotId(1L);

        larkTaskDTO.setTitleCn("测试大陆人员");
        larkTaskDTO.setContentCn("测试大陆人员消息，请忽略<at id=\"all\"></at>");
        larkTaskDTO.setLarkMessageType((byte) 1);

        larkTaskDTO.setPublishDate("2020-08-19 19:23:54");

        larkTaskDTO.setButton(LarkButtonEnum.NO.getType());
//        larkTaskDTO.setScheduleStartDate("2020-08-19 19:23:54");
//        larkTaskDTO.setScheduleEndDate("2020-08-19 20:23:54");
//        larkTaskDTO.setScheduleDescription("测试");

        larkTaskDTO.setPublishScope(PublishScopeEnum.GROUP_PUSH.getCode());
        larkTaskDTO.setScopeKey(Arrays.asList("5f635b30-3490-11eb-adc1-0242ac120002"));
        larkTaskDTO.setSubmitType((byte) 2);

        larkTaskDTO.setBotId(6L);



        messageService.createLarkTask(userBO, larkTaskDTO);
        Thread.sleep(10000);
    }

    @Test
    public void testSendTimeOutWarningCard() {
        WarningCardContentBo warningCardContentBo = new WarningCardContentBo();
        warningCardContentBo.setName("牛文雨");
        warningCardContentBo.setTitle("行政通知");
        warningCardContentBo.setScope("全员消息");
        warningCardContentBo.setTime("1小时");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("landingUrl", "https://www.baidu.com");
        warningCardContentBo.setAction(paramMap);
        warningCardContentBo.setPushUserList(Arrays.asList(new UserBO("niuwenyu", "<EMAIL>"), new UserBO("yangguanlin", "<EMAIL>")));
        boolean isSend = sendMessageManager.sendTimeOutWarningCardMessage(warningCardContentBo);
        Preconditions.checkArgument(isSend, "发送失败");
    }

    @Test
    public void testSendVisibleWarningCard() {
        WarningCardContentBo warningCardContentBo = new WarningCardContentBo();
        warningCardContentBo.setName("牛文雨");
        warningCardContentBo.setTitle("行政通知");
        warningCardContentBo.setScope("全员消息");
        warningCardContentBo.setNumber("5000");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("landingUrl", "https://www.baidu.com");
        warningCardContentBo.setAction(paramMap);
        warningCardContentBo.setPushUserList(Arrays.asList(new UserBO("niuwenyu", "<EMAIL>"), new UserBO("yangguanlin", "<EMAIL>")));
        boolean isSend = sendMessageManager.sendVisibleWarningCardMessage(warningCardContentBo);
        Preconditions.checkArgument(isSend, "发送失败");
    }
}
