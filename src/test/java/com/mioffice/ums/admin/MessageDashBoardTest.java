package com.mioffice.ums.admin;

import base.BaseTest;
import com.mioffice.ums.admin.entity.bo.ChannelCostTimeFromSqlBO;
import com.mioffice.ums.admin.entity.bo.DateAndExtraIdListBO;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.bo.UserInfoBaseBO;
import com.mioffice.ums.admin.entity.vo.MessageDashBoardVO;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.MessageDashBoardService;
import com.mioffice.ums.admin.service.impl.MessageDashBoardServiceImpl;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/9 9:41 上午
 * version: 1.0.0
 */
public class MessageDashBoardTest extends BaseTest {

    @Autowired
    private MessageDashBoardService messageDashBoardService;
    
    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Test
    public void test1() {
        UserBO userBO = new UserBO("yangguanlin", "<EMAIL>");
        BaseResult<MessageDashBoardVO> it = messageDashBoardService.getDataAnalysisMessage(userBO, "", "", "2019-09-04", "2020-09-14");
        System.out.println(it);
    }

    @Test
    public void test2() {
        UserBO userBO = new UserBO("shitietou", "<EMAIL>");
        List<ChannelCostTimeFromSqlBO> it = taskInfoMapper.selectMessageDashBoardChannelCostTime(Collections.singletonList("IT"), null, 0L, 1200000000000000000L);
        System.out.println(it);
    }

    @Test
    public void test3() {
        UserBO userBO = new UserBO("shitietou", "<EMAIL>");
        BaseResult<List<UserInfoBaseBO>> li = messageDashBoardService.getPublisherSystemAdmin(userBO, "yang");
        System.out.println(li);
    }

    @Test
    public void test4() {
        UserBO userBO = new UserBO("shitietou", "<EMAIL>");
        List<DateAndExtraIdListBO> dateAndExtraIdListBOS = taskInfoMapper.selectExtraIdListByDay(null, null, 1599494477000L, 1599580877000L, Arrays.asList("2020-09-08"));
        System.out.println(dateAndExtraIdListBOS);
    }
}
