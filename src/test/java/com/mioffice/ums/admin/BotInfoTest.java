package com.mioffice.ums.admin;

import base.BaseTest;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.admin.async.DefaultBotDashboardAsyncCalculator;
import com.mioffice.ums.admin.entity.info.BotInfo;
import com.mioffice.ums.admin.mapper.BotInfoMapper;
import com.mioffice.ums.admin.service.BotApiService;
import com.mioffice.ums.admin.service.BotDashboardService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * </p>
 *
 * @author: shi tie tou
 * @date: 2020/8/17 0:48
 */
public class BotInfoTest extends BaseTest{

    @Autowired
    private BotApiService botApiService;

    @Autowired
    private BotInfoMapper botInfoMapper;

    @Autowired
    private DefaultBotDashboardAsyncCalculator botDashboardAsyncCalculator;

    @Autowired
    private BotDashboardService botDashboardService;

    @Test
    public void test1(){
        botApiService.getMyLarkBotList("niuwenyu");
    }

    @Test
    public void testStopBot() {
        botApiService.stopTaskByBot(6L, "yangguanlin");
    }

    @Test
    public void testStartBot() {
        botApiService.startTaskByBot(6L, "yangguanlin");
    }

    @Test
    public void testBigDecimal(){
        BigDecimal totalDecimal = new BigDecimal(200);
        BigDecimal allPushDecimal = new BigDecimal(66);
        BigDecimal deptPushDecimal = new BigDecimal(66);
        BigDecimal customPushDecimal = new BigDecimal(66);

        NumberFormat percent = NumberFormat.getPercentInstance();
        percent.setMaximumFractionDigits(0);

        String allPushPercent = percent.format(allPushDecimal.divide(totalDecimal,3, RoundingMode.HALF_EVEN));
        String deptPushPercent = percent.format(deptPushDecimal.divide(totalDecimal));
        String customPushPercent = percent.format(customPushDecimal.divide(totalDecimal));

        System.out.println(allPushPercent);
    }

    @Test
    public void test2() {
//        System.out.println(botDashboardAsyncCalculator.getChartAxis("2020-01-01","2020-12-31"));
//        System.out.println(botDashboardAsyncCalculator.getChartAxis("2020-09-01","2020-9-30"));
//
//        System.out.println(botDashboardAsyncCalculator.getChartAxis("2020-09-01","2020-9-8"));
//        System.out.println(botDashboardService.getBotDashboardManagerList("IT","shitietou", "tietou"));
        List<String> axisList = new ArrayList<>();

        LocalDate endDay = LocalDate.parse("2020-09-09");

        for (int i = 11; i > 0; i--) {
            LocalDate localDate = endDay.minusMonths(i);
            axisList.add(localDate.toString().substring(0,7));
        }
        axisList.add(endDay.toString().substring(0,7));

        System.out.println(axisList);
    }

    @Test
    public void test3() {
        botDashboardService.getBotDashboardVO(null, null, "2019-09-15", "2020-09-15", "wangjieqin");
    }

    @Test
    public void test4(){
        System.out.println(getBizId(null));
//        List<BotInfo> botInfos = botInfoMapper.selectList(
//                Wrappers.<BotInfo>lambdaQuery()
//
//        );
//        botInfos.forEach(
//                botInfo -> {
//                    BotInfo botInfo1 = new BotInfo();
//                    botInfo1.setId(botInfo.getId());
//                    botInfo1.setBotBizId(getBizId(botInfo.getId()));
//                    botInfoMapper.updateById(botInfo1);
//                }
//        );
    }

    private String getBizId(Long id){
        BotInfo botInfo = botInfoMapper.selectOne(
                Wrappers.<BotInfo>lambdaQuery()
                        .orderByDesc(BotInfo::getId)
                        .last("limit 1")
        );
      return String.format("B%04d", 100+botInfo.getId()+1);
    }
}
