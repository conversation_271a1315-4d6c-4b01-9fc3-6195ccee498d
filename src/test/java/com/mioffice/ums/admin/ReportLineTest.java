package com.mioffice.ums.admin;

import base.BaseTest;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.mioffice.ums.admin.entity.dto.ReportLineDTO;
import com.mioffice.ums.admin.entity.vo.hrod.HrodUser;
import com.mioffice.ums.admin.manager.ReportLineManager;
import com.mioffice.ums.admin.service.UserService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020/8/17 15:07
 */
public class ReportLineTest extends BaseTest {

    @Autowired
    private ReportLineManager reportLineManager;
    @Autowired
    private UserService userService;

    @Test
    public void test1() {
        System.out.println(reportLineManager.getDeptVp("wangjieqin", null).toString());
//        System.out.println(reportLineManager.get2thDeptLeader("chengying5",null).toString());
//        System.out.println(reportLineManager.getDirectLeader("duxiangming",null).toString());
    }

    @Test
    public void test2() {
        System.out.println("1" + userService.searchUser("daiyiyun", true));
        System.out.println("1" + userService.searchUser("v-dai", true));
    }

    @Test
    public void testLevelNLeader() {
        List<HrodUser> level1ReportLine = reportLineManager.getUntilLevelNLeader("zhushuai3", 1);
        List<HrodUser> level2ReportLine = reportLineManager.getUntilLevelNLeader("zhushuai3", 2);
        ReportLineDTO leader = reportLineManager.getDirectLeader("zhushuai3", "");
        System.out.println(JacksonUtils.bean2Json(leader));
        System.out.println(level1ReportLine.stream().map(HrodUser::getName).collect(Collectors.joining("->")));
        System.out.println(level2ReportLine.stream().map(HrodUser::getName).collect(Collectors.joining("->")));
    }

}
