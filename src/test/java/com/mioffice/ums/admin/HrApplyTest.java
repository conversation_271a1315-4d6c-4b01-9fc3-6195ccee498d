package com.mioffice.ums.admin;

import base.BaseTest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.admin.entity.dto.HrApplyPageDTO;
import com.mioffice.ums.admin.entity.dto.RobotVerifyDTO;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.ReceiveMemberInfo;
import com.mioffice.ums.admin.exception.RobotVerifyException;
import com.mioffice.ums.admin.manager.RobotVerifyManager;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.ReceiveMemberInfoMapper;
import com.mioffice.ums.admin.message.member.ChooseIdEnum;
import com.mioffice.ums.admin.service.HrEmployeeService;
import com.mioffice.ums.admin.service.impl.HrDepartmentServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/10 16:20
 */
@Slf4j
public class HrApplyTest extends BaseTest {
    @Autowired
    private HrDepartmentServiceImpl hrDepartmentService;

    @Autowired
    private HrEmployeeService hrEmployeeService;

    @Autowired
    private RobotVerifyManager robotVerifyManager;

    @Autowired
    private EmployeeInfoMapper employeeInfoMapper;

    @Autowired
    private ReceiveMemberInfoMapper receiveMemberInfoMapper;

    @Test
    public void test1() {
        HrApplyPageDTO hrApplyPageDTO = new HrApplyPageDTO();
        hrApplyPageDTO.setStartTime("2020-08-06");
        hrApplyPageDTO.setEndTime("2020-08-10");
        hrEmployeeService.syncEmployeeWithIncrement("2020-08-06", "2020-08-10");
    }

    @Test
    public void test2() {
        String appId = "cli_9fa3bc48eefa5062";
        String appKey = "kZZfeJ5BgtoFtWqCW2t98ghkc67j4JQL";
        try {
            String token = robotVerifyManager.getTenantAccessToken(appId, appKey);
            RobotVerifyDTO robotVerifyDTO = robotVerifyManager.verifyRobot(token);
            System.out.println(1);
        } catch (RobotVerifyException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void test3() {
        hrDepartmentService.asyncCalculateCompleteDeptName();
    }

    @Test
    public void test04() {
        LambdaQueryWrapper<EmployeeInfo> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.and(ChooseIdEnum.CHN.getConsumer());
        lambdaQueryWrapper.and(ChooseIdEnum.NO_CHN.getConsumer());
        lambdaQueryWrapper.last("limit 100");

        List<EmployeeInfo> employeeInfos = employeeInfoMapper.selectList(lambdaQueryWrapper);
        System.out.println(employeeInfos);
    }

    @Test
    public void test05() {

        Long taskId = 100L;
        List<String> scopeKeyList = Arrays.asList("1", "3");

        Set<Integer> chooseIdList = scopeKeyList.stream().map(Integer::valueOf).collect(Collectors.toSet());

        List<Consumer<LambdaQueryWrapper<EmployeeInfo>>> consumerList = chooseIdList.stream().map(ChooseIdEnum::getById).map(ChooseIdEnum::getConsumer).collect(Collectors.toList());
        long flagId = 0L;
        while (true) {
            LambdaQueryWrapper<EmployeeInfo> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.and(p -> consumerList.forEach(p::or));

            lambdaQueryWrapper
                    .gt(EmployeeInfo::getId, flagId)
                    .orderByAsc(EmployeeInfo::getId)
                    .last("limit 100");

            List<EmployeeInfo> employeeInfoList = employeeInfoMapper.selectList(lambdaQueryWrapper);
            if (employeeInfoList.isEmpty()) {
                break;
            } else {
                flagId = employeeInfoList.get(employeeInfoList.size() - 1).getId();
            }

            List<ReceiveMemberInfo> infoList = employeeInfoList.stream()
                    .map(p -> newReceiveMemberInfo(taskId, p))
                    .collect(Collectors.toList());

            receiveMemberInfoMapper.batchInsert(infoList);
        }
    }

    public ReceiveMemberInfo newReceiveMemberInfo(long taskId, EmployeeInfo employeeInfo) {
        ReceiveMemberInfo receiveMemberInfo = new ReceiveMemberInfo();
        BeanUtils.copyProperties(employeeInfo, receiveMemberInfo);
        receiveMemberInfo.setTaskId(taskId);
        receiveMemberInfo.setCreateTime(System.currentTimeMillis());
        receiveMemberInfo.setUpdateTime(System.currentTimeMillis());
        receiveMemberInfo.setUserId("");
        receiveMemberInfo.setEmail(Optional.ofNullable(employeeInfo.getEmail()).orElse(""));
        receiveMemberInfo.setPhone(Optional.ofNullable(employeeInfo.getPhone()).orElse(""));
        return receiveMemberInfo;
    }
}
