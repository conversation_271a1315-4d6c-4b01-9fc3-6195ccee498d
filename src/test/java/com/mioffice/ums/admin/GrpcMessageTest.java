package com.mioffice.ums.admin;

import base.BaseTest;
import com.mioffice.ums.admin.manager.impl.SendMessageManager;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageTemplateId;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageTemplateRequest;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageTemplateResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUser;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/11 8:32 下午
 * version: 1.0.0
 */
public class GrpcMessageTest extends BaseTest {

    @Autowired
    private MessageGrpcClient messageGrpcClient;

    @Autowired
    private SendMessageManager sendMessageManager;

    @Test
    public void test() {

        MessageTemplateRequest.Builder messageTemplateRequestBuilder = MessageTemplateRequest.newBuilder();
        messageTemplateRequestBuilder.setTitleCn("hello").setContentCn("测试消息");

        MessageTemplateResponse templateResponse = messageGrpcClient.createMessageTemplate(messageTemplateRequestBuilder.build());
        System.out.println(templateResponse);
    }

    @Test
    public void test1() {

        MessageUser.Builder messageUserBuilder = MessageUser.newBuilder();
        messageUserBuilder.setAppId("cli_9f9243cbbbae9062")
                .setEmail("<EMAIL>")
                .setUsername("niuwenyu")
                .setExtraId("12617863781637");


        MessageTemplateId.Builder messageTemplateIdBuilder = MessageTemplateId.newBuilder();
        messageTemplateIdBuilder.setMessageTemplateId(1);

        messageGrpcClient.sendMessage(messageUserBuilder.build(), messageTemplateIdBuilder.build());
    }

    @Test
    public void testSendMessage() {
//        sendMessageManager.syncSendMessageTask();
    }
}
