package com.mioffice.ums.admin;

import base.BaseTest;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.AppLogService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/27 8:23 下午
 * version: 1.0.0
 */
public class AppLogServiceTest extends BaseTest {

    @Autowired
    AppLogService appLogService;

    @Test
    public void test3() {
        BaseResult<Map<String, Object>> download = appLogService.download("977e905043af478e9375ff6a6ac49d5d");
        System.out.println(download);
    }
}
