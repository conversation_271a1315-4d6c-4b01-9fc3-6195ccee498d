package com.mioffice.ums.admin;

import base.BaseTest;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.vo.AppApprovalListVO;
import com.mioffice.ums.admin.entity.vo.AppSystemListVO;
import com.mioffice.ums.admin.remote.grpc.AppListGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.AppApprovalListService;
import com.mioffice.ums.admin.service.AppSystemListService;
import com.mioffice.ums.admin.utils.JsonUtils;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppTopicInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.QueryRobotAppTopicResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/23 4:39 下午
 * version: 1.0.0
 */
@Slf4j
public class AppListTest extends BaseTest {
    @Autowired
    AppSystemListService appSystemListService;
    @Autowired
    AppApprovalListService appApprovalListService;
    @Autowired
    private AppListGrpcClient appListGrpcClient;

    @Test
    public void test() {
        BaseResult<AppSystemListVO> appPage = appSystemListService.getAppPage(new UserBO("niuwenyu", "<EMAIL>"), 1L, 10L, "", null, null, null, "", "", "");
        System.out.println(appPage);
    }

    @Test
    public void test1() {
        BaseResult<AppApprovalListVO> listVOBaseResult = appApprovalListService.appApprovalPage(new UserBO("niuwenyu", "<EMAIL>"), 1L, 10L, null, null, null, "", "");
        System.out.println(listVOBaseResult);
    }

    @Test
    public void getRobotTopicTest(){
        QueryRobotAppTopicResponse queryRobotAppTopicResponse = appListGrpcClient.getRobotTopic("cli_9fba144ce633d063");
        List<AppTopicInfo> appTopicInfoList = queryRobotAppTopicResponse.getRobotAppTopicInfo().getAppTopicInfoList();
        log.info(JsonUtils.toJson(appTopicInfoList));
    }
}
