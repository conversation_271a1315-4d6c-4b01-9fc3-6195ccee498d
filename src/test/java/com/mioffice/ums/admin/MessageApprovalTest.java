package com.mioffice.ums.admin;

import base.BaseTest;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.MessageApprovalQueryDTO;
import com.mioffice.ums.admin.service.MessageApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020/12/11 10:27 上午
 */
@Slf4j
public class MessageApprovalTest extends BaseTest {

    @Autowired
    MessageApprovalService messageApprovalService;

    @Test
    public void Test(){
        MessageApprovalQueryDTO messageApprovalQueryDTO = new MessageApprovalQueryDTO();
        messageApprovalQueryDTO.setTaskId(790L);
        UserBO userBO = new UserBO();
        userBO.setUsername("yangguanlin");
        messageApprovalService.approved(messageApprovalQueryDTO, userBO);

    }


}
