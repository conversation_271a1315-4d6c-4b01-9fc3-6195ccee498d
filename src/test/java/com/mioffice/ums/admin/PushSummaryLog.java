package com.mioffice.ums.admin;

import base.BaseTest;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.entity.vo.OpenPushSummaryVo;
import com.mioffice.ums.admin.entity.vo.OpenTaskLogVo;
import com.mioffice.ums.admin.remote.grpc.PushSummaryGrpcClient;
import com.mioffice.ums.admin.service.OpenApiPushSummaryService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/24 10:48 上午
 *
 */
@Slf4j
public class PushSummaryLog extends BaseTest {

    @Autowired
    private OpenApiPushSummaryService pushSummaryService;

    @Test
    public void Test(){
//        OpenPushSummaryVo openPushSummaryVo = pushSummaryService.getUseSummary(2L);
        IPage<OpenTaskLogVo> page =  pushSummaryService.getAppTaskLog("",2L,"",1,10);
//        log.warn(openPushSummaryVo.toString());
    }

}
