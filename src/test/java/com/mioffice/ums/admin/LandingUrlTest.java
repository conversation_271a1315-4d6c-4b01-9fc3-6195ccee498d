package com.mioffice.ums.admin;

import base.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Value;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020/8/20 17:27
 */
@Slf4j
public class LandingUrlTest extends BaseTest {

    @Value("${frontendUrl}")
    private String frontendUrl;

    private static final String LANDING_URL = "/message/detail/";

    @Test
    public void test(){
        log.info(frontendUrl.concat(LANDING_URL).concat(String.valueOf("31")));
    }

}
