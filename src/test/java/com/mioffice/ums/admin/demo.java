package com.mioffice.ums.admin;

import com.google.gson.Gson;
import com.mioffice.ums.admin.entity.bo.LarkSheetContentBO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2021/3/23 3:09 下午
 */
public class demo {

    private static  char[] abc = {'A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z'};

    @Data
    public static class Link {
        private String link;
        private String text;
        private String type;
    }

    public static void main(String[] args) {
//        int rowCount = 200;
//        int columnCount = 20;
//        String sheetId = "2b17e4";
//
//        List<String> ranges2 = new ArrayList<>();
//
//        for (int i = 2; i <= rowCount; i++) {
//            ranges2.add(sheetId.concat("!").concat("A").concat(String.valueOf(i)).concat(":").concat(String.valueOf(abc[columnCount-1])).concat(String.valueOf(i)));
//            System.out.println(ranges2.get(i-2));
//        }
//

        Object s = "{\\\"text\\\":\\\"132\\\"}";

        System.out.println(String.valueOf(s));



    }

    private static boolean judgeIsLink(String object){

        Gson gson = new Gson();

            Link link = gson.fromJson(object, Link.class);
            return link != null && link.getText() != null;

    }

}
