package com.mioffice.ums.admin;

import base.BaseTest;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.enums.TaskStatusEnum;
import com.mioffice.ums.admin.manager.IdManager;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import com.mioffice.ums.admin.utils.JsonUtils;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageTemplateRequest;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.14
 */
public class ManagerTest  extends BaseTest {

    @Autowired
    private IdManager idManager;

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Test
    public void testCreateId() {
        System.out.println(idManager.createBizId());
    }

    @Test
    public void test01() {
        Long timeout = System.currentTimeMillis() - 30 * 60 * 1000;

        List<TaskInfo> taskInfoSendingList = taskInfoMapper.selectList(
                Wrappers.<TaskInfo>lambdaQuery()
                        .eq(TaskInfo::getTaskStatus, TaskStatusEnum.PUBLISHING.getCode())
                        .lt(TaskInfo::getUpdateTime, timeout)
        );

        System.out.println(JsonUtils.toJson(taskInfoSendingList));
    }
}
