package com.mioffice.ums.admin;

import base.BaseTest;
import com.google.gson.reflect.TypeToken;
import com.mioffice.ums.admin.entity.bo.CalendarListBO;
import com.mioffice.ums.admin.exception.AppNotException;
import com.mioffice.ums.admin.manager.CalendarManager;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/19 6:24 下午
 * version: 1.0.0
 */
@Slf4j
public class CalendarTest extends BaseTest {

    @Autowired
    private CalendarManager calendarManager;

    @Test
    public void testGetAccessToken() throws AppNotException {
        calendarManager.getLarkAppAccessToken();
        BaseResult baseResult = calendarManager.getCalendarList();
        List<CalendarListBO> calendarListBOList = JsonUtils.parse(JsonUtils.toJson(baseResult.getData()), new TypeToken<List<CalendarListBO>>() {
        }.getType());
        for (CalendarListBO c : calendarListBOList) {
            System.out.println(c.getId());
        }
        log.info("");
    }
}
