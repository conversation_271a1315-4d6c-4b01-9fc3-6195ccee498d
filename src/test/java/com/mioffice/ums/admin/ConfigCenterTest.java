package com.mioffice.ums.admin;

import base.BaseTest;
import com.mi.oa.infra.oaucf.bpm.service.ProcessInstanceService;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.UserRoleQueryDTO;
import com.mioffice.ums.admin.enums.TaskStatusEnum;
import com.mioffice.ums.admin.manager.ProcessDefinitionResource;
import com.mioffice.ums.admin.manager.impl.ProcessManagerImpl;
import com.mioffice.ums.admin.service.ApprovalConfigService;
import com.mioffice.ums.admin.service.impl.AuthConfigServiceImpl;
import com.mioffice.ums.admin.utils.JsonUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/1 10:26 上午
 */
public class ConfigCenterTest extends BaseTest {

    @Autowired
    private AuthConfigServiceImpl authConfigService;

    @Autowired
    private ProcessDefinitionResource processDefinitionResource;

    @Autowired
    private ApprovalConfigService approvalConfigService;

    @Autowired
    ProcessManagerImpl processManager;

    @Resource
    private ProcessInstanceService processInstanceService;

    @Test
    public void test1() {
        UserRoleQueryDTO userRoleQueryDTO = new UserRoleQueryDTO();

        System.out.println(authConfigService.queryUserRolePage(userRoleQueryDTO));
    }

    @Test
    public void test2() {
        System.out.println(JsonUtils.toJson(approvalConfigService.getApprovalTimeLine(415L, null)));
    }

    @Test
    public void test3() {
        UserBO userBO = new UserBO();
        userBO.setUsername("haofeng");
        System.out.println(JacksonUtils.bean2Json(approvalConfigService.getBpmApprovalTimeLine(846L, userBO)));
    }

    @Test
    public void test4() {
//        String businessKey = "";
//        BaseResp<QueryProcInstResp> resp = processInstanceService.get(businessKey);
//        System.out.println(JacksonUtils.bean2Json(resp));
        System.out.println(TaskStatusEnum.APPROVE_BACK.getCode()>TaskStatusEnum.APPROVING.getCode());
    }
}
