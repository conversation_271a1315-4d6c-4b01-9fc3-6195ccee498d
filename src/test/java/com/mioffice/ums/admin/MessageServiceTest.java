package com.mioffice.ums.admin;

import base.BaseTest;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.admin.entity.bo.DeptCountBO;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.TaskResultDTO;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.ParseTmpInfo;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.TaskStatusEnum;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.ParseTmpInfoMapper;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.MessageJobExecuteRecordService;
import com.mioffice.ums.admin.service.MessageService;
import com.mioffice.ums.admin.utils.JsonUtils;
import com.mioffice.ums.admin.utils.MessageUtils;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.FinalContentResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageNumberAndTime;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageNumberAndTimeResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.18
 */
@Slf4j
public class MessageServiceTest extends BaseTest {

    @Autowired
    private MessageService messageService;

    @Autowired
    private MessageGrpcClient messageGrpcClient;

    @Autowired
    private EmployeeInfoMapper employeeInfoMapper;

    @Autowired
    private ParseTmpInfoMapper parseTmpInfoMapper;

    @Autowired
    private MessageJobExecuteRecordService messageJobExecuteRecordService;


    @Test
    public void testUnClickUsers() {
        String parentExtraId = "cf28ad0882b544fabbf464f8a1fe7271";
        List<String> subExtraIdList = new ArrayList<String>() {{
            add("c84c0295d0484930a84f7948427f8b53");
            add("bcc1bf42358a48c2824f20e9fc15720a");
            add("e17325cb41464b058d60912e38a5e845");
            add("195f3e49176f40edb326357289550c27");
            add("108347daddca46a59a7e9c62cd163558");
            add("84c4340871804dde8468ef385966980d");
            add("eb6582ba43894579b95ac940c7b4ba83");
            add("a262ad4904de462ab20b0fd56221a499");
            add("ba63cadd6feb4eeba7f81a9697de75dc");
            add("5d3b84b194b34e8b8958dc9f2d57062f");
            add("29e4134875a84848980d89a419fd339e");
            add("c78f92446a564e48b45f60485dc559ab");
            add("440cf65b935b44bfae43b9d032ff95de");
            add("e3568a0cf31840e2b972a25792379aa7");
            add("213c03e741a141069ef981a2505b7d23");
            add("8969cb8cbe4f45abbcae8bfae97ea22f");
            add("81b8d8234bf6465a9595f79982b8137a");
            add("8d61981252134d099567a7f1d404c634");
            add("a628c6d5eeb947da8d936bff37abeb87");
            add("f46741c597df48298e95c219e59e1197");
            add("0ecefb3e912940278aba90d4dcd0336e");
            add("46ee093b0eed482e8cefe7edaf5b951b");
            add("70e92c6e2caa4d3d8d65348ea4994064");
        }};
        Set<String> usernames = messageJobExecuteRecordService.getUnClickUserByExtraIdList(parentExtraId,
                subExtraIdList);
        log.info("{}", usernames);
    }

    @Test
    public void testGetSummary() {
        BaseResult<Object> baseResult = messageService.getSummaryList(new UserBO("yangguanlin", ""));
        System.out.println(baseResult);

    }

    @Test
    public void testStop() {
        UserBO userBO = new UserBO("yangguanlin", "");

        TaskResultDTO taskResultDTO = new TaskResultDTO();
        taskResultDTO.setTaskId(159L);
        taskResultDTO.setTaskStatus(TaskStatusEnum.CANCEL.getCode());

        BaseResult<Object> messageTask = messageService.stopMessageTask(userBO, taskResultDTO);
        System.out.println(messageTask);
    }

    @Test
    public void testGetFinalMessage() {
        FinalContentResponse finalMessageContent =
                messageGrpcClient.getFinalMessageContent("d9d476fcb0f443309f4901b3bef46738", "yangguanlin");
        String finalContent = finalMessageContent.getFinalContent(0).getFinalContent();
        System.out.println(finalContent);

        String s = finalContent.replaceAll("\"lines\":0,", "");
        System.out.println(s);

        Map<String, Object> map = JsonUtils.toMap(finalContent);
        Map<String, Object> resultMap = MessageUtils.resetBtnContent(map, "测试通过");
        System.out.println(resultMap);
    }

    @Test
    public void testGetMessageNumberByExtraIdBatch(){
        MessageNumberAndTimeResponse messageNumberByExtraIdBatch =
                messageGrpcClient.getMessageNumberByExtraIdBatch(Collections.singletonList("e6e299ec223b426886ea6b9a9704cee3"));
        List<MessageNumberAndTime> messageNumberAndTimeListList = messageNumberByExtraIdBatch.getMessageNumberAndTimeListList();
        Map<String, Long> extraIdAndTodoCountMap = messageNumberAndTimeListList.stream().collect(Collectors.toMap(
                MessageNumberAndTime::getExtraId, MessageNumberAndTime::getTodoCount, (x, y) -> y));
        Map<String, Long> extraIdAndAllCountMap = messageNumberAndTimeListList.stream().collect(Collectors.toMap(MessageNumberAndTime::getExtraId, MessageNumberAndTime::getAllCount, (x, y) -> y));
    }

    @Test
    public void test03() {

        List<String> deptIdList = Arrays.asList("IT37");

        long flagId = 0L;

        while (true) {
            List<EmployeeInfo> employeeInfoList = employeeInfoMapper.selectList(
                    Wrappers.<EmployeeInfo>lambdaQuery()
                            .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                            .and(wrapper -> wrapper.in(EmployeeInfo::getMiDeptLevel2, deptIdList).or()
                                    .in(EmployeeInfo::getMiDeptLevel3, deptIdList))
                            .gt(EmployeeInfo::getId, flagId)
                            .ne(EmployeeInfo::getUsername, "")
                            .orderByAsc(EmployeeInfo::getId)
                            .last("limit 100")
            );

            log.info("moveDeptMember 根据部门发送，移动发送人员 size = [{}], flagId = [{}]", employeeInfoList.size(),
                    flagId);

            if (employeeInfoList.isEmpty()) {
                break;
            } else {
                flagId = employeeInfoList.get(employeeInfoList.size() - 1).getId();
            }

        }

    }

    @Test
    public void testInsertAuto() {

        String excelId = "123123213123213";

        ParseTmpInfo parseTmpInfo1 = new ParseTmpInfo();
        parseTmpInfo1.setExcelId(excelId);
        parseTmpInfo1.setEmail("yangguanlin1");
        parseTmpInfo1.setEmpNo("32510");

        ParseTmpInfo parseTmpInfo2 = new ParseTmpInfo();
        parseTmpInfo2.setExcelId(excelId);
        parseTmpInfo2.setEmail("yangguanlin1");
        parseTmpInfo2.setEmpNo("32510");

        int line = parseTmpInfoMapper.insertAutoDuplicate(Arrays.asList(parseTmpInfo1, parseTmpInfo2),
                System.currentTimeMillis());
        System.out.println(line);
        System.out.println(JsonUtils.toJson(parseTmpInfo1));
        System.out.println(JsonUtils.toJson(parseTmpInfo2));
    }

    @Test
    public void testCount() {
        List<DeptCountBO> deptCountBOList = employeeInfoMapper.selectGroupCountByDeptId(
                Arrays.asList("IT40", "IT34", "IT41", "IT39", "IT38", "IT36", "IT37", "IT35", "IT10", "IT"));
        Map<String, DeptCountBO> deptIdMap = deptCountBOList.stream()
                .collect(Collectors.toMap(DeptCountBO::getDeptId, Function.identity(), (v1, v2) -> v2));

        System.out.println(JsonUtils.toJson(deptIdMap));
    }

    @Test
    public void testTaskDetail() {
        Long taskId = 104L;
        UserBO userBO = new UserBO("niuwenyu", "<EMAIL>");
        BaseResult baseResult = messageService.taskDetail(taskId, userBO);
        System.out.println(baseResult);
    }

    @Test
    public void testTaskMessagePage() {
        BaseResult baseResult =
                messageService.queryTaskMessageByPage(1L, 10, 564L, new UserBO("niuwenyu", "<EMAIL>"));
        System.out.println(baseResult);
    }

    @Test
    public void testTaskHistoryPage() {
        BaseResult baseResult =
                messageService.queryTaskHistoryByPage(1L, 10L, "", "", Arrays.asList((byte) 8), "", "", "",
                        Arrays.asList(""), new UserBO("shitietou", "<EMAIL>"));
        System.out.println(baseResult);
    }

    @Test
    public void testMoveMember() {

        long flagId = 0L;
        while (true) {
            List<ParseTmpInfo> parseTmpInfoList = parseTmpInfoMapper.selectList(
                    Wrappers.<ParseTmpInfo>lambdaQuery()
//                            .eq(ParseTmpInfo::getExcelId, excelId)
                            .gt(ParseTmpInfo::getId, flagId)
//                            .eq(ParseTmpInfo::getDataStatus, ParseDataStatusEnum.WAIT_PROCESS.getCode())
                            .last("limit 100")
            );

            log.info("开始移动excel员工数据 flagId = [{}], count = [{}]", flagId, parseTmpInfoList.size());

            if (parseTmpInfoList.isEmpty()) {
                break;
            } else {
                flagId = parseTmpInfoList.get(parseTmpInfoList.size() - 1).getId();
            }

//            List<ReceiveMemberInfo> infoList = parseTmpInfoList.stream()
//                    .map(p -> newReceiveMemberInfo(taskId, p))
//                    .collect(Collectors.toList());
//            receiveMemberInfoMapper.batchInsert(infoList);

//            ParseTmpInfo updateParseTmpInfo = new ParseTmpInfo();
//            updateParseTmpInfo.setDataStatus(ParseDataStatusEnum.PROCESSED.getCode());
//            updateParseTmpInfo.setUpdateTime(System.currentTimeMillis());
//            List<Long> idList = parseTmpInfoList.stream().map(ParseTmpInfo::getId).collect(Collectors.toList());
//            parseTmpInfoMapper.update(updateParseTmpInfo, Wrappers.<ParseTmpInfo>lambdaUpdate().in(ParseTmpInfo::getId, idList));
        }
    }
}
