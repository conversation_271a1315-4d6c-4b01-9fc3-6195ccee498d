package com.mioffice.ums.admin;

import base.BaseTest;
import static com.mioffice.ums.admin.constants.RuleConventionConstants.OPR_ID_KEY;
import static com.mioffice.ums.admin.constants.RuleConventionConstants.PARAM_KEY;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.SendUsersDTO;
import com.mioffice.ums.admin.entity.vo.AppApprovalListVO;
import com.mioffice.ums.admin.entity.vo.AppSystemListVO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.AppApprovalListService;
import com.mioffice.ums.admin.service.AppSystemListService;
import com.mioffice.ums.admin.service.EmployeeService;
import com.mioffice.ums.admin.utils.FreeMarkerUtils;
import com.mioffice.ums.admin.utils.JsonUtils;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/23 4:39 下午
 * version: 1.0.0
 */
@Slf4j
public class FreeMarkUtilTest extends BaseTest {
    @Autowired
    AppSystemListService appSystemListService;
    @Autowired
    AppApprovalListService appApprovalListService;

    @Autowired
    EmployeeService employeeService;


    @Test
    public void test() {
        BaseResult<AppSystemListVO> appPage = appSystemListService.getAppPage(new UserBO("niuwenyu", "<EMAIL>"), 1L, 10L, "", null, null, null, "", "", "");
        System.out.println(appPage);
    }

    @Test
    public void test1() {
        BaseResult<AppApprovalListVO> listVOBaseResult = appApprovalListService.appApprovalPage(new UserBO("niuwenyu", "<EMAIL>"), 1L, 10L, null, null, null, "", "");
        System.out.println(listVOBaseResult);
    }

    @Test
    public void testRender() throws TemplateException, IOException {
        String tpl="{\n" +
                "\t\"msg_type\": \"interactive\",\n" +
                "\t\"update_multi\": false,\n" +
                "\t\"card\": {\n" +
                "\t\t\"config\": {\n" +
                "\t\t\t\"wide_screen_mode\": true\n" +
                "\t\t},\n" +
                "\t\t\"header\": {\n" +
                "\t\t\t\"template\":\"blue\",\n" +
                "\t\t\t\"title\": {\n" +
                "\t\t\t\t\"tag\": \"plain_text\",\n" +
                "\t\t\t\t\"i18n\": {\n" +
                "\t\t\t\t\t\"zh_cn\": \"${title!}\",\n" +
                "\t\t\t\t\t\"en_us\": \"${titleEn!}\"\n" +
                "\t\t\t\t}\n" +
                "\t\t\t}\n" +
                "\t\t},\n" +
                "\t\t\"i18n_elements\": {\n" +
                "\t\t\t\"zh_cn\": [\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"tag\": \"markdown\",\n" +
                "\t\t\t\t\t\"content\":\"${content!}<#if subList?? && (subList?size >0) >\\n未完成人数:${subList?size!0}\\n<#list subList as item>${item.name!}<#if item_has_next>,</#if></#list></#if>\"\n" +
                "\t\t\t\t}\n" +
                "\t\t\t\t<#if btnText?? &&  (btnText?length gt 1)>,\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"tag\":\"action\",\n" +
                "\t\t\t\t\t\"actions\":[\n" +
                "\t\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\t\"tag\":\"button\",\n" +
                "\t\t\t\t\t\t\t\"text\":{\n" +
                "\t\t\t\t\t\t\t\t\"tag\":\"plain_text\",\n" +
                "\t\t\t\t\t\t\t\t\"content\":\"${btnText!}\"\n" +
                "\t\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\t\"type\":\"primary\",\n" +
                "\t\t\t\t\t\t\t\"value\":{\n" +
                "\t\t\t\t\t\t\t\t\"action\":\"URGE_MANAGER\",\n" +
                "\t\t\t\t\t\t\t\t\"urgeData\":\"${subListJson!}\"\n" +
                "\t\t\t\t\t\t\t\t<#if msgJobId??>,\n" +
                "\t\t\t\t\t\t\t\t\"msgJobId\":\"${msgJobId!}\"\n" +
                "\t\t\t\t\t\t\t\t</#if>\n" +
                "\t\t\t\t\t\t\t\t<#if taskId??>,\n" +
                "\t\t\t\t\t\t\t\t\"taskId\":\"${taskId!}\"\n" +
                "\t\t\t\t\t\t\t\t</#if>,\n" +
                "\t\t\t\t\t\t\t\t\"extraId\":\"${UMS_GROUP_ID!}\"\n" +
                "\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t]\n" +
                "\t\t\t\t}\n" +
                "\t\t\t\t</#if>\n" +
                "\t\t\t],\n" +
                "\t\t\t\"en_us\": [\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"tag\": \"markdown\",\n" +
                "\t\t\t\t\t\"content\":\"${contentEn!}<#if subList?? && (subList?size >0) >\\n未完成人数:${subList?size!0}\\n<#list subList as item>${item.name!}<#if item_has_next>,</#if></#list></#if>\"\n" +
                "\t\t\t\t}\n" +
                "\t\t\t\t<#if btnTextEn?? &&  (btnTextEn?length gt 1)>,\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"tag\":\"action\",\n" +
                "\t\t\t\t\t\"actions\":[\n" +
                "\t\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\t\"tag\":\"button\",\n" +
                "\t\t\t\t\t\t\t\"text\":{\n" +
                "\t\t\t\t\t\t\t\t\"tag\":\"plain_text\",\n" +
                "\t\t\t\t\t\t\t\t\"content\":\"${btnTextEn!}\"\n" +
                "\t\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\t\"type\":\"primary\",\n" +
                "\t\t\t\t\t\t\t\"value\":{\n" +
                "\t\t\t\t\t\t\t\t\"action\":\"URGE_MANAGER\",\n" +
                "\t\t\t\t\t\t\t\t\"urgeData\":\"${subListJson!}\"\n" +
                "\t\t\t\t\t\t\t\t<#if msgJobId??>,\n" +
                "\t\t\t\t\t\t\t\t\"msgJobId\":\"${msgJobId!}\"\n" +
                "\t\t\t\t\t\t\t\t</#if>\n" +
                "\t\t\t\t\t\t\t\t<#if taskId??>,\n" +
                "\t\t\t\t\t\t\t\t\"taskId\":\"${taskId!}\"\n" +
                "\t\t\t\t\t\t\t\t</#if>,\n" +
                "\t\t\t\t\t\t\t\t\"extraId\":\"${UMS_GROUP_ID!}\"\n" +
                "\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t]\n" +
                "\t\t\t\t}\n" +
                "\t\t\t\t</#if>\n" +
                "\t\t\t]\n" +
                "\t\t}\n" +
                "\t}\n" +
                "}";

        List<SendUsersDTO> list = new ArrayList<>();

        HashMap<String,Object> h0 = new HashMap<>();
        h0.put(OPR_ID_KEY,"junfudong");
        HashMap<String,Object> h0_param = new HashMap<>();
        h0_param.put("no",0);
        h0.put(PARAM_KEY,h0_param);
        SendUsersDTO sendUsers0 = JsonUtils.parse(h0, SendUsersDTO.class);
        list.add(sendUsers0);

        HashMap<String,Object> h1 = new HashMap<>();
        h1.put(OPR_ID_KEY,"raojian");
        HashMap<String,Object> h1_param = new HashMap<>();
        h1_param.put("no",1);
        h1.put(PARAM_KEY,h1_param);
        SendUsersDTO sendUsers1 = JsonUtils.parse(h1, SendUsersDTO.class);
        list.add(sendUsers1);

        HashMap<String,Object> h2 = new HashMap<>();
        h2.put(OPR_ID_KEY,"chenxingyu");
        HashMap<String,Object> h2_param = new HashMap<>();
        h2_param.put("no",2);
        h2.put(PARAM_KEY,h2_param);
        SendUsersDTO sendUsers2 = JsonUtils.parse(h2, SendUsersDTO.class);
        list.add(sendUsers2);

        HashMap<String,Object> h3 = new HashMap<>();
        h3.put(OPR_ID_KEY,"zhoumaohua");
        HashMap<String,Object> h3_param = new HashMap<>();
        h3_param.put("no",3);
        h3.put(PARAM_KEY,h3_param);
        SendUsersDTO sendUsers3 = JsonUtils.parse(h3, SendUsersDTO.class);
        list.add(sendUsers3);

        List<SendUsersDTO> sendUsersDTOList = employeeService.convert2LeaderList(list);

        sendUsersDTOList.forEach(sendUsersDTO -> {
            sendUsersDTO.getParam().put("title","title【cn】");
            sendUsersDTO.getParam().put("titleEn","title【en】");
            sendUsersDTO.getParam().put("body","body【cn】");
            sendUsersDTO.getParam().put("bodyEn","body【en】");
            sendUsersDTO.getParam().put("btnText","btnText【cn】");
            sendUsersDTO.getParam().put("btnTextEn","btnText【en】");

            try {
                String tplRender =  FreeMarkerUtils.render(tpl, sendUsersDTO.getParam()).orElse(StringUtils.EMPTY);
                log.info("{},{}",sendUsersDTO.getName(),tplRender);
            } catch (IOException e) {
                throw new RuntimeException(e);
            } catch (TemplateException e) {
                throw new RuntimeException(e);
            }
        });


    }
}
