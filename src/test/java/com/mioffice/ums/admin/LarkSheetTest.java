package com.mioffice.ums.admin;

import base.BaseTest;
import com.mioffice.ums.admin.entity.bo.LarkCallBackBO;
import com.mioffice.ums.admin.entity.bo.LarkSheetContentBO;
import com.mioffice.ums.admin.entity.bo.LarkSheetMetaInfoBO;
import com.mioffice.ums.admin.manager.file.LarkCloudFileManager;
import com.mioffice.ums.admin.service.SendWeeklyMsgService;
import com.mioffice.ums.admin.utils.JsonUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2021/3/19 12:07 下午
 */
public class LarkSheetTest  extends BaseTest {

    private static  char[] abc = {'A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z'};

    @Autowired
    private LarkCloudFileManager larkCloudFileManager;
    @Autowired
    private SendWeeklyMsgService sendWeeklyMsgService;

    @Test
    public void  test1 (){
        LarkSheetMetaInfoBO larkSheetMetaInfoBO = larkCloudFileManager.getLarkSheetMetaInfo("shtk4gBatpwNYTOF4RhZLzzNFLb","u-3uHIpC1Ao0doKem31gjOmb");
        System.out.println(JsonUtils.toJson(larkSheetMetaInfoBO));
        List<LarkSheetMetaInfoBO.SheetInfo> sheetInfoList  = larkSheetMetaInfoBO.getSheets();

        LarkSheetMetaInfoBO.SheetInfo sheetInfo = sheetInfoList.get(0);
        String sheetId = sheetInfo.getSheetId();
        Integer rowCount = sheetInfo.getRowCount();
        Integer columnCount = sheetInfo.getColumnCount();

        List<String> ranges = new ArrayList<>();
        ranges.add(sheetId.concat("!").concat("A1").concat(":").concat(String.valueOf(abc[columnCount-1])).concat("1"));

//        larkCloudFileManager.getLarkSheetHeader("shtk4gBatpwNYTOF4RhZLzzNFLb","u-3uHIpC1Ao0doKem31gjOmb", ranges.get(0));

        List<String> ranges2 = new ArrayList<>();

        for (int i = 2; i <= rowCount; i++) {
            ranges2.add(sheetId.concat("!").concat("A").concat(String.valueOf(i)).concat(":").concat(String.valueOf(abc[columnCount-1])).concat(String.valueOf(i)));
            System.out.println(ranges2.get(i-2));
        }

        LarkSheetContentBO larkSheetContentBO = larkCloudFileManager.getLarkSheetData("shtk4gBatpwNYTOF4RhZLzzNFLb", "u-3uHIpC1Ao0doKem31gjOmb", ranges2);
                System.out.println(JsonUtils.toJson(larkSheetContentBO));

    }


    @Test
    public void  test2 (){
        for (int i = 0; i <3 ; i++) {
            LarkCallBackBO larkCallBackBO = new LarkCallBackBO();
            sendWeeklyMsgService.sendWeeklyMsg(larkCallBackBO);
        }

    }

    @Test
    public void  test3 (){
        sendWeeklyMsgService.autoSendWeeklyMsg();
    }
}
