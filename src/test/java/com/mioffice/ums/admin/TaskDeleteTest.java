package com.mioffice.ums.admin;

import base.BaseTest;
import com.mioffice.ums.admin.service.TaskService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/19 6:36 下午
 * version: 1.0.0
 */
public class TaskDeleteTest extends BaseTest {

    @Autowired
    private TaskService taskService;

//    @Test
//    public void test() {
//        BaseResult<Object> result = taskDeleteService.taskDelete(Arrays.asList(17L, 111L, 135L));
//        System.out.println(result);
//    }

}
