package com.mioffice.ums.admin.message.member;

import com.mioffice.ums.admin.entity.info.DepartmentInfo;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.LarkGroupPushInfo;
import com.mioffice.ums.admin.entity.info.ParseTmpInfo;
import com.mioffice.ums.admin.entity.info.ReceiveMemberInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 移动数据到待发送列表
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.24
 */
public interface MoveToMember {

    /**
     * 移动数据到待发送列表
     *
     * @param taskInfo
     */
    void moveToReceive(TaskInfo taskInfo);

    void moveToReceive(TaskInfo taskInfo, List<String> usernameList);

    ReceiveMemberInfo newReceiveMemberInfo(long taskId, ParseTmpInfo parseTmpInfo, Map<String, DepartmentInfo> deptIdMap);

    ReceiveMemberInfo newReceiveMemberInfo(long taskId, EmployeeInfo employeeInfo, Map<String, DepartmentInfo> deptIdMap);

    ReceiveMemberInfo newReceiveMemberInfo(long taskId, LarkGroupPushInfo larkGroupPushInfo);
}
