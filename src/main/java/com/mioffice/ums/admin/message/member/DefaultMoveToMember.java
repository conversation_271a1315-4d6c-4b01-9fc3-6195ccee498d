package com.mioffice.ums.admin.message.member;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.google.gson.reflect.TypeToken;
import com.mioffice.ums.admin.entity.dto.SendUsersDTO;
import com.mioffice.ums.admin.entity.info.BotInfo;
import com.mioffice.ums.admin.entity.info.DepartmentInfo;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.LarkGroupPushInfo;
import com.mioffice.ums.admin.entity.info.LarkTaskInfo;
import com.mioffice.ums.admin.entity.info.ParseTmpInfo;
import com.mioffice.ums.admin.entity.info.PublishScopeInfo;
import com.mioffice.ums.admin.entity.info.ReceiveMemberInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.enums.BotTenantEnum;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.HrEmployeeTypeEnum;
import com.mioffice.ums.admin.enums.ParseDataStatusEnum;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.enums.TaskChannelEnum;
import com.mioffice.ums.admin.mapper.BotInfoMapper;
import com.mioffice.ums.admin.mapper.DepartmentInfoMapper;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.LarkGroupPushInfoMapper;
import com.mioffice.ums.admin.mapper.LarkTaskInfoMapper;
import com.mioffice.ums.admin.mapper.ParseTmpInfoMapper;
import com.mioffice.ums.admin.mapper.PublishScopeInfoMapper;
import com.mioffice.ums.admin.mapper.ReceiveMemberInfoMapper;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.mioffice.ums.admin.service.EmployeeService;
import com.mioffice.ums.admin.service.TaskService;
import com.mioffice.ums.admin.utils.JsonUtils;
import com.mioffice.ums.admin.utils.MapperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.24
 */
@Slf4j
@Component
public class DefaultMoveToMember implements MoveToMember {

    private final PublishScopeInfoMapper publishScopeInfoMapper;
    private final ParseTmpInfoMapper parseTmpInfoMapper;
    private final EmployeeInfoMapper employeeInfoMapper;
    private final ReceiveMemberInfoMapper receiveMemberInfoMapper;
    private final LarkGroupPushInfoMapper larkGroupPushInfoMapper;
    private final DepartmentInfoMapper departmentInfoMapper;
    private final BotInfoMapper botInfoMapper;
    private final LarkTaskInfoMapper larkTaskInfoMapper;

    private final EmployeeService employeeService;

    private final TaskService taskService;

    private final MessageGrpcClient messageGrpcClient;

    @NacosValue(value = "${ums.admin.executives:}", autoRefreshed = true)
    private String executives;

    public DefaultMoveToMember(
            PublishScopeInfoMapper publishScopeInfoMapper,
            ParseTmpInfoMapper parseTmpInfoMapper,
            EmployeeInfoMapper employeeInfoMapper,
            ReceiveMemberInfoMapper receiveMemberInfoMapper,
            LarkGroupPushInfoMapper larkGroupPushInfoMapper,
            DepartmentInfoMapper departmentInfoMapper,
            BotInfoMapper botInfoMapper,
            LarkTaskInfoMapper larkTaskInfoMapper,
            TaskService taskService,
            EmployeeService employeeService,
            MessageGrpcClient messageGrpcClient) {
        this.publishScopeInfoMapper = publishScopeInfoMapper;
        this.parseTmpInfoMapper = parseTmpInfoMapper;
        this.employeeInfoMapper = employeeInfoMapper;
        this.receiveMemberInfoMapper = receiveMemberInfoMapper;
        this.larkGroupPushInfoMapper = larkGroupPushInfoMapper;
        this.departmentInfoMapper = departmentInfoMapper;
        this.botInfoMapper = botInfoMapper;
        this.larkTaskInfoMapper = larkTaskInfoMapper;
        this.taskService = taskService;
        this.employeeService = employeeService;
        this.messageGrpcClient = messageGrpcClient;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void moveToReceive(TaskInfo taskInfo) {

        Long taskId = taskInfo.getId();

        // 是否要排除掉合作伙伴账号
        boolean excludePartner = true;

        List<PublishScopeInfo> scopeInfoList = publishScopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery()
                        .eq(PublishScopeInfo::getTaskId, taskId)
        );

        List<String> scopeKeyList =
                scopeInfoList.stream().map(PublishScopeInfo::getScopeKey).collect(Collectors.toList());

        PublishScopeEnum publishScopeEnum = PublishScopeEnum.getByCode(taskInfo.getPublishScope());

        TaskChannelEnum channelEnum = TaskChannelEnum.getByCode(taskInfo.getChannel());

        if (TaskChannelEnum.CHANNEL_LARK.equals(channelEnum)) {
            LarkTaskInfo larkTaskInfo = larkTaskInfoMapper.selectOne(
                    Wrappers.<LarkTaskInfo>lambdaQuery().eq(LarkTaskInfo::getTaskId, taskId));
            if (!Objects.isNull(larkTaskInfo)) {
                BotInfo botInfo = botInfoMapper.selectById(larkTaskInfo.getBotId());
                BotTenantEnum botTenantEnum = BotTenantEnum.getByCode(botInfo.getBotTenant());
                if (BotTenantEnum.BOT_TENANT_PARTNER.equals(botTenantEnum)) {
                    excludePartner = false;
                }
            }
        }

        switch (publishScopeEnum) {
            case ALL_PUSH:
                moveAllMember(taskId, channelEnum, excludePartner);
                break;
            case DEPT_PUSH:
                moveDeptMember(taskId, scopeKeyList, channelEnum, excludePartner);
                break;
            case CUSTOM_PUSH_EMPLOYEE:
                moveCustomImportMember(taskId, scopeKeyList.get(0), channelEnum);
                break;
            case CHOOSE_PUSH:
                moveChooseMember(taskId, scopeKeyList, channelEnum);
                break;
            case GROUP_PUSH:
                moveGroups(taskId, scopeKeyList);
                break;
            case CUSTOM_PUSH_MANAGER:
                moveManagerImportMember(taskId, scopeKeyList.get(0));
                break;
            case UNREAD_PUSH:
                moveUnRead(taskInfo, scopeKeyList.get(0));
                break;
            default:
                log.warn("推送范围类型不存在 publishScope = [{}]", taskInfo.getPublishScope());
        }
    }

    @Override
    public void moveToReceive(TaskInfo taskInfo, List<String> usernameList) {
        List<EmployeeInfo> employeeInfoList = employeeInfoMapper.selectList(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .in(EmployeeInfo::getUsername, usernameList)
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        );

        // 设置异一次性的extraId
        taskInfo.setExtraId(UUID.randomUUID().toString().replace("-", StringUtils.EMPTY));

        List<String> deptIdList = employeeInfoList.stream().map(EmployeeInfo::getDeptId).collect(Collectors.toList());
        List<ReceiveMemberInfo> infoList = employeeInfoList.stream()
                .map(p -> newReceiveMemberInfo(taskInfo.getId(), p, deptIdMap(deptIdList)))
                .collect(Collectors.toList());

        receiveMemberInfoMapper.batchInsert(infoList);
    }

    private void moveAllMember(long taskId, TaskChannelEnum channelEnum, boolean excludePartner) {

        long flagId = 0L;
        while (true) {
            LambdaQueryWrapper<EmployeeInfo> lambdaQueryWrapper = Wrappers.<EmployeeInfo>lambdaQuery()
                    .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                    .gt(EmployeeInfo::getId, flagId);

            if (channelEnum.equals(TaskChannelEnum.CHANNEL_LARK)) {
                lambdaQueryWrapper.ne(EmployeeInfo::getUsername, StringUtils.EMPTY);
            } else if (channelEnum.equals(TaskChannelEnum.CHANNEL_EMAIL)) {
                lambdaQueryWrapper.ne(EmployeeInfo::getEmail, StringUtils.EMPTY);
            } else if (channelEnum.equals(TaskChannelEnum.CHANNEL_SMS)) {
                lambdaQueryWrapper.ne(EmployeeInfo::getPhone, StringUtils.EMPTY);
            } else {
                lambdaQueryWrapper.ne(EmployeeInfo::getUsername, StringUtils.EMPTY);
            }
            if (excludePartner) {
                lambdaQueryWrapper.ne(EmployeeInfo::getEmpType, HrEmployeeTypeEnum.PARTNER.getCode());
            } else {
                lambdaQueryWrapper.eq(EmployeeInfo::getEmpType, HrEmployeeTypeEnum.PARTNER.getCode());
            }

            lambdaQueryWrapper
                    .orderByAsc(EmployeeInfo::getId)
                    .last("limit 100");

            List<EmployeeInfo> employeeInfoList = employeeInfoMapper.selectList(lambdaQueryWrapper);

            if (employeeInfoList.isEmpty()) {
                break;
            } else {
                flagId = employeeInfoList.get(employeeInfoList.size() - 1).getId();
            }

            List<String> deptIdList =
                    employeeInfoList.stream().map(EmployeeInfo::getDeptId).collect(Collectors.toList());
            List<ReceiveMemberInfo> infoList = employeeInfoList.stream()
                    .map(p -> newReceiveMemberInfo(taskId, p, deptIdMap(deptIdList)))
                    .collect(Collectors.toList());

            receiveMemberInfoMapper.batchInsert(infoList);
        }
    }

    private void moveDeptMember(long taskId, List<String> deptIdList, TaskChannelEnum channelEnum,
                                boolean excludePartner) {

        long flagId = 0L;
        while (true) {

            LambdaQueryWrapper<EmployeeInfo> lambdaQueryWrapper = Wrappers.<EmployeeInfo>lambdaQuery()
                    .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                    .and(wrapper ->
                            wrapper.in(EmployeeInfo::getMiDeptLevel2, deptIdList).or()
                                    .in(EmployeeInfo::getMiDeptLevel3, deptIdList).or()
                                    .in(EmployeeInfo::getMiDeptLevel4, deptIdList)
                    )
                    .gt(EmployeeInfo::getId, flagId);

            if (channelEnum.equals(TaskChannelEnum.CHANNEL_LARK)) {
                lambdaQueryWrapper.ne(EmployeeInfo::getUsername, StringUtils.EMPTY);
            } else if (channelEnum.equals(TaskChannelEnum.CHANNEL_EMAIL)) {
                lambdaQueryWrapper.ne(EmployeeInfo::getEmail, StringUtils.EMPTY);
            } else if (channelEnum.equals(TaskChannelEnum.CHANNEL_SMS)) {
                lambdaQueryWrapper.ne(EmployeeInfo::getPhone, StringUtils.EMPTY);
            } else {
                lambdaQueryWrapper.ne(EmployeeInfo::getUsername, StringUtils.EMPTY);
            }
            if (excludePartner) {
                lambdaQueryWrapper.ne(EmployeeInfo::getEmpType, HrEmployeeTypeEnum.PARTNER.getCode());
            } else {
                lambdaQueryWrapper.eq(EmployeeInfo::getEmpType, HrEmployeeTypeEnum.PARTNER.getCode());
            }

            lambdaQueryWrapper
                    .orderByAsc(EmployeeInfo::getId)
                    .last("limit 100");

            List<EmployeeInfo> employeeInfoList = employeeInfoMapper.selectList(lambdaQueryWrapper);

            log.info("moveDeptMember 根据部门发送，移动发送人员 size = [{}]", employeeInfoList.size());

            if (employeeInfoList.isEmpty()) {
                break;
            } else {
                flagId = employeeInfoList.get(employeeInfoList.size() - 1).getId();
            }

            List<String> empDeptIdList =
                    employeeInfoList.stream().map(EmployeeInfo::getDeptId).collect(Collectors.toList());
            List<ReceiveMemberInfo> infoList = employeeInfoList.stream()
                    .map(p -> newReceiveMemberInfo(taskId, p, deptIdMap(empDeptIdList)))
                    .collect(Collectors.toList());

            receiveMemberInfoMapper.batchInsert(infoList);
        }
    }

    private void moveManagerImportMember(long taskId, String excelId) {
        //高管名单
        List<String> executiveUsernames = Splitter.on(",").splitToList(executives);

        List<SendUsersDTO> list = new ArrayList<>();
        long flagId = 0L;
        while (true) {
            LambdaQueryWrapper<ParseTmpInfo> lambdaQueryWrapper = Wrappers.<ParseTmpInfo>lambdaQuery()
                    .eq(ParseTmpInfo::getExcelId, excelId)
                    .eq(ParseTmpInfo::getPublishScope, PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode())
                    .ne(ParseTmpInfo::getUsername, StringUtils.EMPTY)
                    .gt(ParseTmpInfo::getId, flagId)
                    .in(ParseTmpInfo::getDataStatus,
                            ParseDataStatusEnum.WAIT_PROCESS.getCode(),
                            ParseDataStatusEnum.PROCESSED.getCode());

            lambdaQueryWrapper
                    .orderByAsc(ParseTmpInfo::getId)
                    .last("limit 400");

            List<ParseTmpInfo> parseTmpInfoList = parseTmpInfoMapper.selectList(lambdaQueryWrapper);

            log.info("开始移动excel员工数据 taskId = [{}], flagId = [{}],count = [{}]",
                    taskId,
                    flagId,
                    parseTmpInfoList.size());

            if (parseTmpInfoList.isEmpty()) {
                break;
            } else {
                flagId = parseTmpInfoList.get(parseTmpInfoList.size() - 1).getId();
            }

            List<SendUsersDTO> subList = parseTmpInfoList
                    .stream()
                    .map(MapperUtil.INSTANCE::parseTmpInfo2SendUsersDTO)
                    .collect(Collectors.toList());
            list.addAll(subList);
        }
        list = list.stream().filter(sendUsersDTO -> !executiveUsernames.contains(sendUsersDTO.getOprid())).collect(
                Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            List<SendUsersDTO> leaders = employeeService.convert2LeaderList(list);
            List<ReceiveMemberInfo> infoList = leaders
                    .stream()
                    .map(leader -> {
                        EmployeeInfo leaderInfo =
                                employeeInfoMapper.selectOne(
                                        Wrappers.<EmployeeInfo>lambdaQuery().eq(EmployeeInfo::getUsername,
                                                        leader.getOprid())
                                                .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode()));
                        if (Objects.isNull(leaderInfo)) {
                            return null;
                        }
                        ReceiveMemberInfo receiveMemberInfo =
                                MapperUtil.INSTANCE.employeeInfo2ReceiveMemberInfo(leaderInfo);
                        receiveMemberInfo.setTaskId(taskId);
                        receiveMemberInfo.setCreateTime(System.currentTimeMillis());
                        receiveMemberInfo.setUpdateTime(System.currentTimeMillis());
                        receiveMemberInfo.setUserId(StringUtils.EMPTY);
                        receiveMemberInfo.setChatId(StringUtils.EMPTY);
                        if (!leader.getParam().isEmpty()) {
                            receiveMemberInfo.setExtraContent(JsonUtils.toJson(leader.getParam()));
                        }
                        return receiveMemberInfo;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(infoList)) {
                receiveMemberInfoMapper.batchInsert(infoList);
            }
        }
    }

    private void moveCustomImportMember(long taskId, String excelId, TaskChannelEnum channelEnum) {
        log.info("[4-1]发送消息, 开始移动excel员工数据 taskId = [{}]", taskId);
        long flagId = 0L;
        while (true) {

            LambdaQueryWrapper<ParseTmpInfo> lambdaQueryWrapper = Wrappers.<ParseTmpInfo>lambdaQuery()
                    .eq(ParseTmpInfo::getExcelId, excelId)
                    .eq(ParseTmpInfo::getPublishScope, PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode())
                    .gt(ParseTmpInfo::getId, flagId)
                    .in(ParseTmpInfo::getDataStatus, ParseDataStatusEnum.WAIT_PROCESS.getCode(),
                            ParseDataStatusEnum.PROCESSED.getCode());

            if (channelEnum.equals(TaskChannelEnum.CHANNEL_LARK)) {
                lambdaQueryWrapper.ne(ParseTmpInfo::getUsername, StringUtils.EMPTY);
            } else if (channelEnum.equals(TaskChannelEnum.CHANNEL_EMAIL)) {
                lambdaQueryWrapper.ne(ParseTmpInfo::getEmail, StringUtils.EMPTY);
            } else if (channelEnum.equals(TaskChannelEnum.CHANNEL_SMS)) {
                lambdaQueryWrapper.ne(ParseTmpInfo::getPhone, StringUtils.EMPTY);
            } else {
                lambdaQueryWrapper.ne(ParseTmpInfo::getUsername, StringUtils.EMPTY);
            }

            lambdaQueryWrapper
                    .orderByAsc(ParseTmpInfo::getId)
                    .last("limit 400");

            List<ParseTmpInfo> parseTmpInfoList = parseTmpInfoMapper.selectList(lambdaQueryWrapper);

            log.info("开始移动excel员工数据 taskId = [{}], flagId = [{}],count = [{}]", taskId, flagId,
                    parseTmpInfoList.size());

            if (parseTmpInfoList.isEmpty()) {
                break;
            } else {
                flagId = parseTmpInfoList.get(parseTmpInfoList.size() - 1).getId();
            }

            List<String> deptIdList =
                    parseTmpInfoList.stream().map(ParseTmpInfo::getDeptId).collect(Collectors.toList());
            List<ReceiveMemberInfo> infoList = parseTmpInfoList
                    .stream()
                    .map(p -> newReceiveMemberInfo(taskId, p, deptIdMap(deptIdList)))
                    .collect(Collectors.toList());
            receiveMemberInfoMapper.batchInsert(infoList);

        }

        log.info("移动excel员工数据结束 taskId = [{}]", taskId);
    }

    private void moveChooseMember(Long taskId, List<String> scopeKeyList, TaskChannelEnum channelEnum) {

        Set<Integer> chooseIdList = scopeKeyList.stream().map(Integer::valueOf).collect(Collectors.toSet());
        long flagId = 0L;
        List<Consumer<LambdaQueryWrapper<EmployeeInfo>>> consumerList = chooseIdList.stream()
                .map(ChooseIdEnum::getById)
                .map(ChooseIdEnum::getConsumer)
                .collect(Collectors.toList());

        while (true) {
            LambdaQueryWrapper<EmployeeInfo> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.and(p -> consumerList.forEach(p::or));

            if (channelEnum.equals(TaskChannelEnum.CHANNEL_LARK)) {
                lambdaQueryWrapper.ne(EmployeeInfo::getUsername, StringUtils.EMPTY);
            } else if (channelEnum.equals(TaskChannelEnum.CHANNEL_EMAIL)) {
                lambdaQueryWrapper.ne(EmployeeInfo::getEmail, StringUtils.EMPTY);
            } else if (channelEnum.equals(TaskChannelEnum.CHANNEL_SMS)) {
                lambdaQueryWrapper.ne(EmployeeInfo::getPhone, StringUtils.EMPTY);
            } else {
                lambdaQueryWrapper.ne(EmployeeInfo::getUsername, StringUtils.EMPTY);
            }

            lambdaQueryWrapper
                    .gt(EmployeeInfo::getId, flagId)
                    .orderByAsc(EmployeeInfo::getId)
                    .last("limit 100");

            List<EmployeeInfo> employeeInfoList = employeeInfoMapper.selectList(lambdaQueryWrapper);
            if (employeeInfoList.isEmpty()) {
                break;
            } else {
                flagId = employeeInfoList.get(employeeInfoList.size() - 1).getId();
            }

            List<String> empDeptIdList =
                    employeeInfoList.stream().map(EmployeeInfo::getDeptId).collect(Collectors.toList());
            List<ReceiveMemberInfo> infoList = employeeInfoList.stream()
                    .map(p -> newReceiveMemberInfo(taskId, p, deptIdMap(empDeptIdList)))
                    .collect(Collectors.toList());

            receiveMemberInfoMapper.batchInsert(infoList);
        }
    }

    public void moveGroups(Long taskId, List<String> scopeKeyList) {
        List<LarkGroupPushInfo> larkGroupPushInfos = larkGroupPushInfoMapper.selectList(
                Wrappers.<LarkGroupPushInfo>lambdaQuery().eq(LarkGroupPushInfo::getTagId, scopeKeyList.get(0)));
        List<ReceiveMemberInfo> infoList =
                larkGroupPushInfos.stream().map(p -> newReceiveMemberInfo(taskId, p)).collect(Collectors.toList());
        receiveMemberInfoMapper.batchInsert(infoList);
    }

    public void moveUnRead(TaskInfo taskInfo, String excelId) {

        if (Objects.isNull(taskInfo.getParentTaskId())) {
            log.error("parent taskId null");
            return;
        }
        TaskInfo parentTaskInfo = taskService.getById(taskInfo.getParentTaskId());
        if (Objects.isNull(parentTaskInfo)) {
            log.error("parent taskInfo null");
            return;
        }

        Set<String> excludeUserNameSet = new HashSet<>();
        long flagId = 0L;
        while (true) {
            LambdaQueryWrapper<ParseTmpInfo> lambdaQueryWrapper = Wrappers.<ParseTmpInfo>lambdaQuery()
                    .eq(ParseTmpInfo::getExcelId, excelId)
                    .eq(ParseTmpInfo::getPublishScope, PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode())
                    .ne(ParseTmpInfo::getUsername, StringUtils.EMPTY)
                    .gt(ParseTmpInfo::getId, flagId)
                    .in(ParseTmpInfo::getDataStatus,
                            ParseDataStatusEnum.WAIT_PROCESS.getCode(),
                            ParseDataStatusEnum.PROCESSED.getCode());

            lambdaQueryWrapper
                    .orderByAsc(ParseTmpInfo::getId)
                    .last("limit 400");

            List<ParseTmpInfo> parseTmpInfoList = parseTmpInfoMapper.selectList(lambdaQueryWrapper);

            if (parseTmpInfoList.isEmpty()) {
                break;
            } else {
                flagId = parseTmpInfoList.get(parseTmpInfoList.size() - 1).getId();
            }

            excludeUserNameSet.addAll(
                    parseTmpInfoList.stream().map(ParseTmpInfo::getUsername).collect(Collectors.toSet()));
        }

        List<String> subExtraIdList =
                taskService.list(Wrappers.<TaskInfo>lambdaQuery().eq(TaskInfo::getParentTaskId,
                                parentTaskInfo.getId()))
                        .stream()
                        .map(TaskInfo::getExtraId)
                        .collect(Collectors.toList());

        List<ReceiveMemberInfo> infoList =
                messageGrpcClient.getAllUnRead(parentTaskInfo.getExtraId(), subExtraIdList)
                        .stream()
                        .filter(userName -> !excludeUserNameSet.contains(userName))
                        .map(userName -> {
                            EmployeeInfo employeeInfo =
                                    employeeInfoMapper.selectOne(
                                            Wrappers.<EmployeeInfo>lambdaQuery().eq(EmployeeInfo::getUsername,
                                                            userName)
                                                    .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode()));
                            if (Objects.isNull(employeeInfo)) {
                                return null;
                            }
                            ReceiveMemberInfo receiveMemberInfo =
                                    MapperUtil.INSTANCE.employeeInfo2ReceiveMemberInfo(employeeInfo);
                            receiveMemberInfo.setTaskId(taskInfo.getId());
                            receiveMemberInfo.setCreateTime(System.currentTimeMillis());
                            receiveMemberInfo.setUpdateTime(System.currentTimeMillis());
                            receiveMemberInfo.setUserId(StringUtils.EMPTY);
                            receiveMemberInfo.setChatId(StringUtils.EMPTY);
                            receiveMemberInfo.setExtraContent(
                                    JsonUtils.toJson(
                                            packExtraContent(employeeInfo.getDeptDesc(), employeeInfo.getDeptId(),
                                                    processDeptDesc(employeeInfo))));
                            return receiveMemberInfo;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(infoList)) {
            receiveMemberInfoMapper.batchInsert(infoList);
        }
    }

    private String processDeptDesc(EmployeeInfo employeeInfo) {
        StringBuilder sb = new StringBuilder();
        if (!StringUtils.isBlank(employeeInfo.getMiDeptLevel2Desc())) {
            sb.append(employeeInfo.getMiDeptLevel2Desc());
        }
        if (!StringUtils.isBlank(employeeInfo.getMiDeptLevel3Desc())) {
            sb.append("·");
            sb.append(employeeInfo.getMiDeptLevel3Desc());
        }
        if (!StringUtils.isBlank(employeeInfo.getMiDeptLevel4Desc())) {
            sb.append("·");
            sb.append(employeeInfo.getMiDeptLevel4Desc());
        }
        return sb.toString();
    }

    @Override
    public ReceiveMemberInfo newReceiveMemberInfo(long taskId, ParseTmpInfo parseTmpInfo,
                                                  Map<String, DepartmentInfo> deptIdMap) {
        ReceiveMemberInfo receiveMemberInfo = new ReceiveMemberInfo();
        BeanUtils.copyProperties(parseTmpInfo, receiveMemberInfo);
        receiveMemberInfo.setTaskId(taskId);
        receiveMemberInfo.setCreateTime(System.currentTimeMillis());
        receiveMemberInfo.setUpdateTime(System.currentTimeMillis());
        receiveMemberInfo.setUserId(StringUtils.EMPTY);
        receiveMemberInfo.setChatId(StringUtils.EMPTY);
        receiveMemberInfo.setExtraContent(
                JsonUtils.toJson(packExtraContent(parseTmpInfo.getName(), parseTmpInfo.getDeptId(), deptIdMap)));
        return receiveMemberInfo;
    }

    @Override
    public ReceiveMemberInfo newReceiveMemberInfo(long taskId, EmployeeInfo employeeInfo,
                                                  Map<String, DepartmentInfo> deptIdMap) {
        ReceiveMemberInfo receiveMemberInfo = new ReceiveMemberInfo();
        BeanUtils.copyProperties(employeeInfo, receiveMemberInfo);
        receiveMemberInfo.setTaskId(taskId);
        receiveMemberInfo.setCreateTime(System.currentTimeMillis());
        receiveMemberInfo.setUpdateTime(System.currentTimeMillis());
        receiveMemberInfo.setUserId(StringUtils.EMPTY);
        receiveMemberInfo.setChatId(StringUtils.EMPTY);
        receiveMemberInfo.setEmail(Optional.ofNullable(employeeInfo.getEmail()).orElse(StringUtils.EMPTY));
        receiveMemberInfo.setPhone(Optional.ofNullable(employeeInfo.getPhone()).orElse(StringUtils.EMPTY));
        receiveMemberInfo.setExtraContent(
                JsonUtils.toJson(packExtraContent(employeeInfo.getName(), employeeInfo.getDeptId(), deptIdMap)));
        return receiveMemberInfo;
    }

    private Map<String, Object> packExtraContent(String name, String deptId, String deptDesc) {

        Map<String, Object> map = new HashMap<>(3);

        map.put("name", name);
        map.put("deptId", deptId);
        if (StringUtils.isNotBlank(deptDesc)) {
            map.put("deptName", deptDesc);
        }
        return map;
    }

    private Map<String, Object> packExtraContent(String name, String deptId, Map<String, DepartmentInfo> deptIdMap) {

        Map<String, Object> map = new HashMap<>(3);

        map.put("name", name);
        map.put("deptId", deptId);

        DepartmentInfo departmentInfo = deptIdMap.get(deptId);

        if (Objects.isNull(departmentInfo)) {
            return map;
        }
        List<String> fullDeptList =
                JsonUtils.parse(departmentInfo.getCompleteDeptName(), new TypeToken<List<String>>() {
                }.getType());

        if (Objects.nonNull(fullDeptList) && !fullDeptList.isEmpty()) {
            map.put("deptName", String.join("·", fullDeptList));
        }

        return map;
    }

    private Map<String, DepartmentInfo> deptIdMap(List<String> deptIdList) {
        return departmentInfoMapper.selectList(
                        Wrappers.<DepartmentInfo>lambdaQuery()
                                .in(DepartmentInfo::getDeptId, deptIdList)
                ).stream()
                .collect(Collectors.toMap(DepartmentInfo::getDeptId, Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public ReceiveMemberInfo newReceiveMemberInfo(long taskId, LarkGroupPushInfo larkGroupPushInfo) {
        ReceiveMemberInfo receiveMemberInfo = new ReceiveMemberInfo();
        receiveMemberInfo.setTaskId(taskId);
        receiveMemberInfo.setCreateTime(System.currentTimeMillis());
        receiveMemberInfo.setUpdateTime(System.currentTimeMillis());
        receiveMemberInfo.setChatId(larkGroupPushInfo.getChatId());
        receiveMemberInfo.setName(larkGroupPushInfo.getChatName());

        Map<String, Object> extraMap = new HashMap<>(6);
        extraMap.put("chatId", larkGroupPushInfo.getChatId());
        extraMap.put("chatName", larkGroupPushInfo.getChatName());
        extraMap.put("ownerUserType", larkGroupPushInfo.getOwnerUserType());
        extraMap.put("ownerUserId", larkGroupPushInfo.getOwnerUserId());
        extraMap.put("ownerName", larkGroupPushInfo.getOwnerName());
        receiveMemberInfo.setExtraContent(JsonUtils.toJson(extraMap));

        receiveMemberInfo.setPhone(StringUtils.EMPTY);
        receiveMemberInfo.setUserId(StringUtils.EMPTY);
        receiveMemberInfo.setEmail(StringUtils.EMPTY);
        receiveMemberInfo.setUsername(StringUtils.EMPTY);
        receiveMemberInfo.setDeptDesc(StringUtils.EMPTY);
        receiveMemberInfo.setDeptId(StringUtils.EMPTY);
        receiveMemberInfo.setMiDeptLevel2(StringUtils.EMPTY);
        receiveMemberInfo.setMiDeptLevel2Desc(StringUtils.EMPTY);
        receiveMemberInfo.setMiDeptLevel3(StringUtils.EMPTY);
        receiveMemberInfo.setMiDeptLevel3Desc(StringUtils.EMPTY);
        receiveMemberInfo.setMiDeptLevel4(StringUtils.EMPTY);
        receiveMemberInfo.setMiDeptLevel4Desc(StringUtils.EMPTY);
        return receiveMemberInfo;
    }
}
