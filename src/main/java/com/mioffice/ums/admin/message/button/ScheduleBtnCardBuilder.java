package com.mioffice.ums.admin.message.button;

import com.mioffice.ums.admin.enums.BtnActionEnum;
import com.mioffice.ums.admin.enums.LarkButtonEnum;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.CardAction;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.24
 */
public class ScheduleBtnCardBuilder {

    public static CardAction.Builder landingBtnCn(Long taskId, String extraId) {
        CardAction.Builder cardActionBuilder = CardAction.newBuilder();
        cardActionBuilder.setName(LarkButtonEnum.SCHEDULE.getDesc());
        cardActionBuilder.putVal("taskId", String.valueOf(taskId));
        cardActionBuilder.putVal("action", BtnActionEnum.ADD_SCHEDULE.getAction());
        cardActionBuilder.putVal("extraId", extraId);
        return cardActionBuilder;
    }

    public static CardAction.Builder landingBtnEn(Long taskId, String extraId) {
        CardAction.Builder cardActionBuilder = CardAction.newBuilder();
        cardActionBuilder.setName(LarkButtonEnum.SCHEDULE.getDescEn());
        cardActionBuilder.putVal("taskId", String.valueOf(taskId));
        cardActionBuilder.putVal("action", BtnActionEnum.ADD_SCHEDULE.getAction());
        cardActionBuilder.putVal("extraId", extraId);
        return cardActionBuilder;
    }
}
