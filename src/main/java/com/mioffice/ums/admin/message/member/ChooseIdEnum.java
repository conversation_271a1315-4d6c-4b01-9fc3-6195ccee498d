package com.mioffice.ums.admin.message.member;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Consumer;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.11.10
 */
@Getter
@AllArgsConstructor
public enum ChooseIdEnum {

    /**
     * 筛选id
     */
    NO(0, "非法条件",
            p -> p.eq(EmployeeInfo::getId, -1)),

    CHN(1, "大陆员工",
            p -> p.eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                    .eq(EmployeeInfo::getMiCompanyCountry, "CHN")
    ),

    NO_CHN(2, "非大陆员工",
            p -> p.eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                    .ne(EmployeeInfo::getMiCompanyCountry, "CHN")
                    .ne(EmployeeInfo::getMiCompanyCountry, "")
    ),

    BEIJING_CHN(
            3, "北京",
            p -> p.eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                    .eq(EmployeeInfo::getWorkCity, "北京")
    ),

    SHANGHAI_CHN(
            4, "上海",
            p -> p.eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                    .eq(EmployeeInfo::getWorkCity, "上海")
    ),

    WUHAN_CHN(5, "武汉",
            p -> p.eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                    .eq(EmployeeInfo::getWorkCity, "武汉")
    ),

    NANJING_CHN(6, "南京",
            p -> p.eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                    .eq(EmployeeInfo::getWorkCity, "南京")
    ),

    SHENZHEN_CHN(7, "深圳",
            p -> p.eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                    .eq(EmployeeInfo::getWorkCity, "深圳")
    ),
    GUANGZHOU_CHN(8, "广州",
            p -> p.eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                    .eq(EmployeeInfo::getWorkCity, "广州")
    ),
    INDIA(9, "印度",
            p -> p.eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                    .likeRight(EmployeeInfo::getWorkCity, "印度-")
    ),
    BGD(10, "孟加拉",
            p -> p.eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                    .likeRight(EmployeeInfo::getWorkCity, "孟加拉-")
    ),
    LKA(11, "斯里兰卡",
            p -> p.eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                    .likeRight(EmployeeInfo::getWorkCity, "斯里兰卡-")
    ),
    NPL(12, "尼泊尔",
            p -> p.eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                    .likeRight(EmployeeInfo::getWorkCity, "尼泊尔-")
    );

    private final Integer id;
    private final String desc;
    private final Consumer<LambdaQueryWrapper<EmployeeInfo>> consumer;

    public static ChooseIdEnum getById(Integer id) {
        for (ChooseIdEnum value : ChooseIdEnum.values()) {
            if (value.getId().equals(id)) {
                return value;
            }
        }
        return NO;
    }
}
