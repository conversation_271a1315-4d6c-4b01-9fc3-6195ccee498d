package com.mioffice.ums.admin.message.button;

import com.mioffice.ums.admin.entity.info.LarkTaskInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.enums.LarkButtonEnum;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.CardAction;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageTemplateRequest;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.24
 */
public class AttachBtn {

    private byte button;

    public AttachBtn(byte button) {
        this.button = button;
    }

    public void addAttachBtn(MessageTemplateRequest.Builder builder, TaskInfo taskInfo, LarkTaskInfo larkTaskInfo) {
        if (button == LarkButtonEnum.LAND_PAGE.getType()) {
            // 落地页
            CardAction.Builder btnCn = LandingBtnCardBuilder.landingBtnCn(larkTaskInfo);
            if (btnCn != null) {
                builder.addCardActionsCn(btnCn);
            }
            CardAction.Builder btnEn = LandingBtnCardBuilder.landingBtnEn(larkTaskInfo);
            if (btnEn != null) {
                builder.addCardActionsEn(btnEn);
            } else if (btnCn != null) {
                builder.addCardActionsEn(btnCn);
            }

            // 使用默认的 "查看更多"
            if (builder.getCardActionsEnList().isEmpty() && builder.getCardActionsCnList().isEmpty()) {
                builder.addCardActionsCn(LandingBtnCardBuilder.landingDefaultBtn(larkTaskInfo));
            }
        } else if (button == LarkButtonEnum.SCHEDULE.getType()) {
            // 日程
            builder.addCardActionsCn(ScheduleBtnCardBuilder.landingBtnCn(larkTaskInfo.getTaskId(), taskInfo.getExtraId()));
            builder.addCardActionsEn(ScheduleBtnCardBuilder.landingBtnEn(larkTaskInfo.getTaskId(), taskInfo.getExtraId()));
        }
    }
}
