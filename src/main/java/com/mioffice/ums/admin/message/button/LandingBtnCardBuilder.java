package com.mioffice.ums.admin.message.button;

import com.mioffice.ums.admin.entity.info.LarkTaskInfo;
import com.mioffice.ums.admin.enums.LarkButtonEnum;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.CardAction;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 详情页按钮
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.24
 */
public class LandingBtnCardBuilder {

    public static CardAction.Builder landingBtnCn(LarkTaskInfo larkTaskInfo) {
        if (StringUtils.isBlank(larkTaskInfo.getButtonName())) {
            return null;
        }
        CardAction.Builder cardActionBuilder = CardAction.newBuilder();
        cardActionBuilder.setName(larkTaskInfo.getButtonName());
        cardActionBuilder.setLandingUrl(larkTaskInfo.getLandingPageUrl());
        return cardActionBuilder;
    }

    public static CardAction.Builder landingBtnEn(LarkTaskInfo larkTaskInfo) {
        if (StringUtils.isBlank(larkTaskInfo.getButtonNameEn())) {
            return null;
        }
        CardAction.Builder cardActionBuilder = CardAction.newBuilder();
        cardActionBuilder.setName(larkTaskInfo.getButtonNameEn());
        cardActionBuilder.setLandingUrl(larkTaskInfo.getLandingPageUrl());
        return cardActionBuilder;
    }

    public static CardAction.Builder landingDefaultBtn(LarkTaskInfo larkTaskInfo) {
        CardAction.Builder cardActionBuilder = CardAction.newBuilder();
        cardActionBuilder.setName(LarkButtonEnum.LAND_PAGE.getDesc());
        cardActionBuilder.setLandingUrl(larkTaskInfo.getLandingPageUrl());
        return cardActionBuilder;
    }
}
