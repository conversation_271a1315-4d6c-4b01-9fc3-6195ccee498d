package com.mioffice.ums.admin.x5client;

import java.io.Serializable;

/**
 * X5协议的返回头
 *
 * <AUTHOR>
 */
public class X5ResponseHeader implements Serializable {
    private static final long serialVersionUID = 2684504826432486597L;
    private String code;
    private String desc;
    private String message;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public X5ResponseHeader() {
    }

    public X5ResponseHeader(String code, String message) {
        this.code = code;
        this.message = message;
    }
}
