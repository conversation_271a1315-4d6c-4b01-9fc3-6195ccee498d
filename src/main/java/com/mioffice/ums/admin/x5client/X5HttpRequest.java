package com.mioffice.ums.admin.x5client;

import com.github.kevinsawicki.http.HttpRequest;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Base64Utils;
import org.springframework.util.DigestUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * X5 请求组件
 * </p>
 *
 * <AUTHOR>
 * @date 2020.07.03
 */
@Slf4j
public class X5HttpRequest {

    private static final String APP_ID_KEY = "appid";
    private static final String SIGN_KEY = "sign";
    private static final String HEADER_KEY = "header";
    private static final String BODY_KEY = "body";
    private static final String DATA_KEY = "data";

    public static String post(String appId, String appKey, String url, Object param, Boolean signUpperCase) {
        String sign = x5Sign(appId, appKey, param, signUpperCase);

        Gson gson = new Gson();

        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put(APP_ID_KEY, appId);
        headerMap.put(SIGN_KEY, sign);

        Map<String, Object> jsonMap = new HashMap<>();
        jsonMap.put(HEADER_KEY, headerMap);
        jsonMap.put(BODY_KEY, gson.toJson(param));

        String requestParam = gson.toJson(jsonMap);
        log.info("X5 request = [{}]", requestParam);
        String data = Base64Utils.encodeToString(requestParam.getBytes());
        HttpRequest httpRequest = HttpRequest.post(url).form(DATA_KEY, data);
        if (httpRequest.ok()) {
            return httpRequest.body();
        } else {
            log.warn("x5 响应错误, url = [{}], code = [{}]", url, httpRequest.code());
        }
        return null;
    }

    private static String x5Sign(String appId, String appKey, Object param, Boolean signUpperCase) {

        if (Objects.isNull(signUpperCase)) {
            signUpperCase = true;
        }
        Gson gson = new Gson();
        String body = gson.toJson(param);
        String sign = DigestUtils.md5DigestAsHex((appId + body + appKey).getBytes()).toUpperCase();
        if (signUpperCase) {
            sign = sign.toUpperCase();
        }
        return sign;
    }

}
