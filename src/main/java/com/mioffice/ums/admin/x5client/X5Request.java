package com.mioffice.ums.admin.x5client;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class X5Request {

    private Header header;
    private String body;

    @Getter
    @Setter
    @ToString
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Header {

        @JsonProperty("appid")
        private String appId;
        private String method;
        private String sign;
        /**
         * extended attributes
         */
        private Map<String, Object> metadata;
    }

}
