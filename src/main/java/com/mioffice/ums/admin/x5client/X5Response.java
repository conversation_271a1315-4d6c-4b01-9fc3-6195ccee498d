package com.mioffice.ums.admin.x5client;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class X5Response<T> implements Serializable {

    private X5ResponseHeader header;
    private T body;

    public X5ResponseHeader getHeader() {
        return header;
    }

    public void setHeader(X5ResponseHeader header) {
        this.header = header;
    }

    public T getBody() {
        return body;
    }

    public void setBody(T body) {
        this.body = body;
    }

    public X5Response() {
    }

    public X5Response(X5ResponseHeader header) {
        this.header = header;
    }
}
