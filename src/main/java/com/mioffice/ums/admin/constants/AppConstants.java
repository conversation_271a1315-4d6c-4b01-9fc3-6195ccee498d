package com.mioffice.ums.admin.constants;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2020/10/13
 */
public class AppConstants {
    // 1 内部，2 外部, 3 内部和外部

    public static final int INNER = 1;
    public static final int OUTER = 2;
    public static final int BOTH = 3;

    public static final Pattern TOPIC_NAME_PATTERN = Pattern.compile("^[0-9a-zA-Z_]{1,}$");

    private AppConstants() {
    }
}
