package com.mioffice.ums.admin.constants;

import com.google.common.collect.Maps;
import com.mioffice.ums.admin.entity.dto.SendUsersDTO;
import com.mioffice.ums.admin.enums.CommonJudgeEnum;
import com.mioffice.ums.admin.utils.JsonUtils;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.MessageUser;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * @ClassName RuleConventionConstants
 * @Description 规则相关的常量，比如回调接口返回的参数的名字等
 * <AUTHOR>
 * @Date 2023/6/8 18:48
 **/
public class RuleConventionConstants {
    public static final String OPR_ID_KEY = "oprid";
    public static final String CHAT_ID_KEY = "chatid";
    public static final String MAIL_YEK = "mail";
    public static final String PHONE_KEY = "mobile";
    public static final String NAME_KEY = "name";
    public static final String PARAM_KEY = "param";
    public static final String JOB_PARAM_KEY = "param";
    public static final String JOB_ID_KEY = "msgJobId";
    public static final String TASK_ID_KEY = "taskId";

    public static final String EXTRA_ID_KEY = "extraId";
    public static final String URGE_DATA_KEY = "urgeData";
    public static final String SUB_LIST = "subList";
    public static final String SUB_LIST_JSON = "subListJson";
    public static final String URGE_MANGER_BTN_DEFAULT_TXT_CN = "一键提醒";
    public static final String URGE_MANGER_BTN_DEFAULT_TXT_EN = "Remind";

    public static final String UNREAD_PUSH_TASK_TITLE_PREFIX_CN = "⚡未读加推⚡";

    public static final String UNREAD_PUSH_TASK_TITLE_PREFIX_EN = "⚡Unread Push⚡";

    public static final String TIMESTAMP_KEY = "timestamp";

    public static final String CLICK_KEY_PREFIX = "CLICK:";

    public static final String SEND_KEY_PREFIX = "SEND:";

    public static final BiFunction<List<SendUsersDTO>, String, List<MessageUser>> larkUserFunction =
            (sender, placeholderStr) -> sender
                    .stream()
                    .filter(i -> StringUtils.isNotEmpty(i.getOprid()) || StringUtils.isNotBlank(i.getChatid()))
                    .map(e -> {
                        Map<String, Object> param = Objects.nonNull(e.getParam()) ? e.getParam() : Maps.newHashMap();
                        extendParamByPlaceholder(param, placeholderStr);
                        return MessageUser.newBuilder()
                                .setUsername(Optional.ofNullable(e.getOprid()).orElse(StringUtils.EMPTY))
                                .setChatId(Optional.ofNullable(e.getChatid()).orElse(StringUtils.EMPTY))
                                .setContentFlag(CommonJudgeEnum.YES.getCode())
                                .setPlaceholderContent(JsonUtils.toJson(param))
                                .build();
                    }).collect(Collectors.toList());

    private static void extendParamByPlaceholder(Map<String, Object> param, String placeholderStr) {
        if (StringUtils.isNotBlank(placeholderStr)) {
            Map<String, String> placeholderMap = JsonUtils.toMap(placeholderStr);
            placeholderMap.forEach((key, val) -> {
                //占位符
                if (StringUtils.isNotBlank(val)) {
                    param.put(key, val);
                }
            });
        }
    }

}
