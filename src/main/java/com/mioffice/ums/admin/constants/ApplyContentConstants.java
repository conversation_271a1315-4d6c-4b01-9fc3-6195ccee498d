package com.mioffice.ums.admin.constants;

/**
 * <p>
 * 审核相关的内容提模板
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.18
 */
public class ApplyContentConstants {

    private ApplyContentConstants() {
    }

    public static final String TITLE = "消息发送审核";

    public static final String APPROVE_TEMPLATE_CONTENT = "**${name}**申请使用${pushMode}发送消息" +
            "\n消息主题：${title}" +
            "\n推送时间：${publishDate}" +
            "\n推送范围：${publishScopeName} **（${allCount}${unit}）**";

    public static final String AGREE_TEMPLATE_CONTENT = "Dear ${name}\n**${approveName}**通过了您申请的【${title}】消息发布申请";
    public static final String REFUSE_TEMPLATE_CONTENT = "Dear ${name}\n**${approveName}**驳回了您申请的【${title}】消息发布申请\n驳回原因：${refuseReason}";

}
