package com.mioffice.ums.admin.constants;

/**
 * 流程状态常量类
 * 审批后，流程状态（1：审批中，2：驳回，3：已完成，4：终止)
 * 审批动作 (0:待办，1:同意，2:驳回,3:终止)
 *
 * <AUTHOR>
 * @date 2020/8/14 19:52
 */
public class ProcessStatusConstant {

    private ProcessStatusConstant() {
    }

    public static final String PROCESS_STATUS_APPROVAL = "1";
    public static final String PROCESS_STATUS_REJECT = "2";
    public static final String PROCESS_STATUS_FINISH = "3";
    public static final String PROCESS_STATUS_TERMINATION = "4";

    public static final int NO_ACTION = 0;
    public static final int ACTION_AGREE = 1;
    public static final int ACTION_REJECT = 2;
    public static final int ACTION_TERMINATE = 3;
}
