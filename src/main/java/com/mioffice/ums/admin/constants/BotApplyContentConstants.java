package com.mioffice.ums.admin.constants;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/2 2:32 下午
 * version: 1.0.0
 */
public class BotApplyContentConstants {

    private BotApplyContentConstants() {
    }

    public static final String BOT_APPLY_TITLE = "机器人使用申请审核";
    public static final String BOT_APPLY_TEMPLATE_CONTENT = "机器人名称：${robotName}\n" + "申请人：${name}\n" + "申请理由：${applyDesc}";

    public static final String BOT_APPLY_RESULT_TITLE = "机器人使用申请结果";
    public static final String AGREE_TEMPLATE_CONTENT = "Dear ${name}\n**${approveName}**通过了您的${robotName}机器人使用申请，机器人已存在于您的机器人列表中";
    public static final String REFUSE_TEMPLATE_CONTENT = "Dear ${name}\n**${approveName}**驳回了您的${robotName}机器人使用申请\n驳回理由：${refuseReason}";
}
