package com.mioffice.ums.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mioffice.ums.admin.entity.bo.ChannelAndExtraIdListBO;
import com.mioffice.ums.admin.entity.bo.ChannelCostTimeFromSqlBO;
import com.mioffice.ums.admin.entity.bo.CostTimeFromSqlBO;
import com.mioffice.ums.admin.entity.bo.DateAndExtraIdListBO;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.entity.vo.BaseTaskVO;
import com.mioffice.ums.admin.entity.vo.BotDashboardVO;
import com.mioffice.ums.admin.entity.vo.MessageDashBoardVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * @since 2021/9/14 下午3:54
 * version: 1.0.0
 */

public interface TaskInfoMapper extends BaseMapper<TaskInfo> {
    int updateBatch(List<TaskInfo> list);

    int updateBatchSelective(List<TaskInfo> list);

    List<TaskInfo> selectByBotIdAndStatus(@Param("botId") Long botId, @Param("statusList") List<Byte> taskStatusList);

    BaseTaskVO selectTaskDetailCommon(@Param("taskId") Long taskId);

    List<TaskInfo> selectListByMap(@Param("status") Byte status, @Param("taskIdList") List<Long> taskIdList, @Param("botManagerUsername") String username, @Param("userDeptId") String deptId);

    List<Long> selectTaskIdSystemAdmin(@Param("deptId") String deptId);

    BotDashboardVO.PushScopeSummary getPushScope(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("taskIdList") List<Long> taskIdList, @Param("taskStatusList") List<Byte> taskStatusList);

    MessageDashBoardVO selectMessageDashBoardCount(@Param("deptIds") List<String> deptIds, @Param("publishUsernames") List<String> publishUsernames, @Param("start") Long start, @Param("end") Long end);

    List<CostTimeFromSqlBO> selectMessageDashBoardGroupByDay(@Param("deptIds") List<String> deptIds, @Param("publishUsernames") List<String> publishUsernames, @Param("start") Long start, @Param("end") Long end);

    List<CostTimeFromSqlBO> selectMessageDashBoardGroupByMonth(@Param("deptIds") List<String> deptIds, @Param("publishUsernames") List<String> publishUsernames, @Param("start") Long start, @Param("end") Long end);

    MessageDashBoardVO selectMessageStatusStatistics(@Param("deptIds") List<String> deptIds, @Param("publishUsernames") List<String> publishUsernames, @Param("start") Long start, @Param("end") Long end);

    List<ChannelCostTimeFromSqlBO> selectMessageDashBoardChannelCostTime(@Param("deptIds") List<String> deptIds, @Param("publishUsernames") List<String> publishUsernames, @Param("start") Long start, @Param("end") Long end);

    List<DateAndExtraIdListBO> selectExtraIdListByDay(@Param("deptIds") List<String> deptIds, @Param("publishUsernames") List<String> publishUsernames, @Param("start") Long start, @Param("end") Long end, @Param("dateGroupList") List<String> dateGroupList);

    List<DateAndExtraIdListBO> selectExtraIdListByMonth(@Param("deptIds") List<String> deptIds, @Param("publishUsernames") List<String> publishUsernames, @Param("start") Long start, @Param("end") Long end, @Param("dateGroupList") List<String> dateGroupList);

    List<ChannelAndExtraIdListBO> selectExtraIdListChannelCostTime(@Param("deptIds") List<String> deptIds, @Param("publishUsernames") List<String> publishUsernames, @Param("start") Long start, @Param("end") Long end, @Param("channelList") List<Byte> channelList);

    /**
     * 一次性查询所有子表的推送总数
     * @param taskIdList 任务ID列表
     * @param botIdList 机器人ID列表
     * @return 推送总数
     */
    Integer selectAllSubTablePushCount(@Param("taskIdList") List<Long> taskIdList, @Param("botIdList") List<Long> botIdList);
}
