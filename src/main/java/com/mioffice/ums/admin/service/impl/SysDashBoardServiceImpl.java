package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.gson.reflect.TypeToken;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.info.DepartmentInfo;
import com.mioffice.ums.admin.entity.vo.AppMessageChannelVO;
import com.mioffice.ums.admin.entity.vo.AppMessageCountVO;
import com.mioffice.ums.admin.entity.vo.AppSummaryVO;
import com.mioffice.ums.admin.entity.vo.DeptIdNameVO;
import com.mioffice.ums.admin.entity.vo.ListVO;
import com.mioffice.ums.admin.mapper.DepartmentInfoMapper;
import com.mioffice.ums.admin.remote.grpc.AppListGrpcClient;
import com.mioffice.ums.admin.remote.grpc.PushSummaryGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.SysDashBoardService;
import com.mioffice.ums.admin.utils.JsonUtils;
import com.mioffice.ums.admin.utils.MapperUtil;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppChannelCountResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppMessageCountResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppSummary;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.26
 */
@Slf4j
@Service
public class SysDashBoardServiceImpl implements SysDashBoardService {

    private final PushSummaryGrpcClient pushSummaryGrpcClient;
    private final AppListGrpcClient appListGrpcClient;

    private final DepartmentInfoMapper departmentInfoMapper;

    public SysDashBoardServiceImpl(PushSummaryGrpcClient pushSummaryGrpcClient, AppListGrpcClient appListGrpcClient, DepartmentInfoMapper departmentInfoMapper) {
        this.pushSummaryGrpcClient = pushSummaryGrpcClient;
        this.appListGrpcClient = appListGrpcClient;
        this.departmentInfoMapper = departmentInfoMapper;
    }

    @Override
    public BaseResult<ListVO<AppMessageCountVO>> getSysMessageTop(UserBO userBO, long beginTime, long endTime) {

        AppMessageCountResponse appMessageCountResponse = pushSummaryGrpcClient.getOpenTopAppMsgList(10, beginTime, endTime);
        List<AppMessageCountVO> appMessageCountVOList = MapperUtil.INSTANCE.mapToAppMessageCountVOList(appMessageCountResponse.getAppMsgCountInfoList());

        // 部门名称完善
        List<DeptIdNameVO> deptIdNameVOList = appMessageCountVOList.stream()
                .map(AppMessageCountVO::getDeptList)
                .flatMap(Collection::stream).collect(Collectors.toList());

        List<String> deptIdList = deptIdNameVOList.stream().map(DeptIdNameVO::getDeptId)
                .collect(Collectors.toList());

        if (!deptIdList.isEmpty()) {
            Map<String, DepartmentInfo> deptIdMap = departmentInfoMapper.selectList(Wrappers.<DepartmentInfo>lambdaQuery().in(DepartmentInfo::getDeptId, deptIdList))
                    .stream().collect(Collectors.toMap(DepartmentInfo::getDeptId, Function.identity(), (v1, v2) -> v2));

            deptIdNameVOList.forEach(deptIdNameVO -> {
                if (deptIdMap.containsKey(deptIdNameVO.getDeptId())) {
                    DepartmentInfo departmentInfo = deptIdMap.get(deptIdNameVO.getDeptId());
                    ArrayList<String> deptNameList = JsonUtils.parse(departmentInfo.getCompleteDeptName(), new TypeToken<ArrayList<String>>() {
                    }.getType());
                    deptIdNameVO.setDeptName(deptNameList.get(0));
                }
            });
        }

        return BaseResult.of(new ListVO<>(appMessageCountVOList));
    }

    @Override
    public BaseResult<ListVO<AppMessageChannelVO>> getSysMessageChannelCount(UserBO userBO, List<Long> sysIdList, long beginTime, long endTime) {
        AppChannelCountResponse appChannelCountResponse = pushSummaryGrpcClient.getOpenAppChannelCount(sysIdList, beginTime, endTime);
        List<AppMessageChannelVO> appMessageChannelVOList = MapperUtil.INSTANCE.mapToAppMessageChannelVOList(appChannelCountResponse.getAppChannelCountList());
        return BaseResult.of(new ListVO<>(appMessageChannelVOList));
    }

    @Override
    public BaseResult<ListVO<AppSummaryVO>> getSysSummaryList() {
        List<AppSummary> appSummary = appListGrpcClient.getAppSummary();
        List<AppSummaryVO> list = MapperUtil.INSTANCE.mapToAppSummaryVoList(appSummary);
        return BaseResult.of(new ListVO<>(list));
    }
}
