package com.mioffice.ums.admin.service;

import com.mioffice.ums.admin.entity.dto.PreSendDTO;
import com.mioffice.ums.admin.entity.dto.UserInfoDTO;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/13 16:18
 */
public interface UserService {

    /**
     * 查询用户
     *
     * @param searchWord 搜索词
     * @return List<UserInfoDTO>
     */
    List<UserInfoDTO> searchUser(String searchWord, boolean excludePartner);

    /**
     * 获取登录人姓名
     *
     * @param username cas账户
     * @return UserInfoDTO
     */
    UserInfoDTO getLoginUser(String username);

    /**
     * 查询已推送名单
     *
     * @param belongTo 预推送所属的机器人，邮件，或者SMS的ID
     * @param loginUser 当前登录人的username
     * @return List<UserInfoDTO>
     */
    List<PreSendDTO> getPreSendList(Long belongTo, String loginUser);

}
