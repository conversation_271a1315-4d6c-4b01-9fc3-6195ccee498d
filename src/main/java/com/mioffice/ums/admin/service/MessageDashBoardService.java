package com.mioffice.ums.admin.service;

import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.bo.UserInfoBaseBO;
import com.mioffice.ums.admin.entity.vo.MessageDashBoardVO;
import com.mioffice.ums.admin.result.BaseResult;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/7 7:40 下午
 * version: 1.0.0
 */
public interface MessageDashBoardService {

    /**
     * 获取消息看板
     *
     * @param userBO 登陆基本信息
     * @param deptIdList 部门List
     * @param publishUsernameList 发布人List
     * @param start 开始时间
     * @param end 结束时间
     * @return 返回
     */
    BaseResult<MessageDashBoardVO> getDataAnalysisMessage(UserBO userBO, String deptIdList, String publishUsernameList, String start, String end);

    BaseResult<List<UserInfoBaseBO>> getPublisherSystemAdmin(UserBO userBO, String searchWord);

    BaseResult<List<UserInfoBaseBO>> getPublisherSuperAdmin(UserBO userBO, String deptIds, String searchWord);
}
