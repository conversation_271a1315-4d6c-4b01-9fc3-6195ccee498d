package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mioffice.ums.admin.entity.dto.RoleDTO;
import com.mioffice.ums.admin.entity.dto.UserRoleDTO;
import com.mioffice.ums.admin.entity.dto.UserRoleQueryDTO;
import com.mioffice.ums.admin.entity.info.UserRoleInfo;
import com.mioffice.ums.admin.enums.RoleTypeEnum;
import com.mioffice.ums.admin.manager.UserInfoManager;
import com.mioffice.ums.admin.mapper.UserRoleInfoMapper;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.AuthConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 权限角色配置
 * </p>
 *
 * <AUTHOR>
 * @date 2020/8/31 3:34 下午
 */
@Slf4j
@Service
public class AuthConfigServiceImpl extends ServiceImpl<UserRoleInfoMapper, UserRoleInfo> implements AuthConfigService {

    private final UserRoleInfoMapper userRoleInfoMapper;

    private final UserInfoManager userInfoManager;

    public AuthConfigServiceImpl(UserRoleInfoMapper userRoleInfoMapper, UserInfoManager userInfoManager) {
        this.userRoleInfoMapper = userRoleInfoMapper;
        this.userInfoManager = userInfoManager;
    }

    @Override
    public BaseResult<List<RoleDTO>> queryRoleList() {

        Map<Byte, Long> userRoleCountList = userRoleInfoMapper
                .selectUserRoleCount()
                .stream()
                .collect(Collectors.toMap(RoleDTO::getRoleType, RoleDTO::getCount, (v1, v2) -> v2));

        List<RoleDTO> result = new ArrayList<>();
        for (RoleTypeEnum roleTypeEnum : RoleTypeEnum.values()) {
            RoleDTO roleDTO = new RoleDTO();
            roleDTO.setRoleType(roleTypeEnum.getCode());
            roleDTO.setRoleName(roleTypeEnum.getName());
            roleDTO.setRoleDesc(roleTypeEnum.getDesc());
            if (roleTypeEnum.getCode() == RoleTypeEnum.ROLE_BOT_ADMIN.getCode()) {
                roleDTO.setCount(userInfoManager.getBotManagerCount());
            } else {
                roleDTO.setCount(userRoleCountList.get(roleTypeEnum.getCode()));
            }
            result.add(roleDTO);
        }
        return BaseResult.of(result);
    }

    @Override
    public BaseResult<IPage<UserRoleDTO>> queryUserRolePage(UserRoleQueryDTO userRoleQueryDTO) {
        // page
        Page<UserRoleDTO> queryPage = new Page<>(userRoleQueryDTO.getPage(), userRoleQueryDTO.getSize());

        List<UserRoleDTO> userRoleDTOList = userRoleInfoMapper.selectUserRoleList(queryPage, this.getRoleTypeListByStr(userRoleQueryDTO.getRoleTypeArrStr()), userRoleQueryDTO.getKeyWord());

        userRoleDTOList.forEach(
                userRoleDTO -> {
                    String roleTypeStr = userRoleDTO.getRoleTypeStr();
                    if (StringUtils.isNotBlank(roleTypeStr)) {
                        // 1.返回给前端一个role type list
                        String[] roleTypeArr = roleTypeStr.split(",");
                        ArrayList<String> roleTypeList = new ArrayList<>(Arrays.asList(roleTypeArr));
                        userRoleDTO.setRoleTypeList(roleTypeList);
                        // 2.将权限角色名称以 ； 拼接
                        ArrayList<String> roleDescList = new ArrayList<>();
                        roleTypeList.forEach(
                                roleType -> {
                                    roleDescList.add(RoleTypeEnum.getMsgByCode(Byte.parseByte(roleType)));
                                }
                        );
                        userRoleDTO.setRoleDesc(String.join(";", roleDescList));
                    }
                }
        );
        queryPage.setRecords(userRoleDTOList);

        return BaseResult.of(queryPage);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResult<Object> addUserRole(UserRoleDTO userRoleDTO, String username) {

        // 1.先判断是否存在
        List<UserRoleInfo> userRoleInfoList = userRoleInfoMapper.selectList(
                Wrappers.<UserRoleInfo>lambdaQuery()
                        .eq(UserRoleInfo::getUsername, userRoleDTO.getUsername())
        );

        if (userRoleInfoList.size() > 1) {
            return new BaseResult<>(ResultCode.USER_EXIST_ERROR);
        }

        if (userRoleInfoList.size() == 1 && userRoleInfoList.get(0).getRoleType() != RoleTypeEnum.ROLE_INIT.getCode()) {
            return new BaseResult<>(ResultCode.USER_EXIST_ERROR);
        }

        if (userRoleInfoList.size() == 1 && userRoleInfoList.get(0).getRoleType() == RoleTypeEnum.ROLE_INIT.getCode()) {
            // 1.首先先删除他的初始权限
            userRoleInfoMapper.delete(
                    Wrappers.<UserRoleInfo>lambdaUpdate()
                            .eq(UserRoleInfo::getUsername, userRoleDTO.getUsername())
                            .eq(UserRoleInfo::getRoleType, RoleTypeEnum.ROLE_INIT.getCode())
            );
        }

        // 2.新增
        List<Byte> roleTypeList = this.getRoleTypeListByStr(userRoleDTO.getRoleTypeArrStr());
        List<UserRoleInfo> toAddList = new ArrayList<>();
        long curTime = System.currentTimeMillis();
        roleTypeList.forEach(
                roleType -> {
                    UserRoleInfo userRoleInfo = this.newUpdateCreateInstance(username, curTime);
                    userRoleInfo.setName(userRoleDTO.getName());
                    userRoleInfo.setRoleType(roleType);
                    userRoleInfo.setUsername(userRoleDTO.getUsername());
                    toAddList.add(userRoleInfo);
                }
        );
        try {
            this.saveBatch(toAddList);
        } catch (Exception e) {
            log.warn("添加用户角色失败, 原因:[{}]", e.getMessage());
            throw new RuntimeException("添加用户角色失败");
        }
        return BaseResult.of();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResult<Object> updateUserRole(UserRoleDTO userRoleDTO, String username) {
        // 1.首先先删除他的所有权限
        userRoleInfoMapper.delete(
                Wrappers.<UserRoleInfo>lambdaUpdate()
                        .eq(UserRoleInfo::getUsername, userRoleDTO.getUsername())
        );

        // 2.新增
        List<Byte> roleTypeList = this.getRoleTypeListByStr(userRoleDTO.getRoleTypeArrStr());
        List<UserRoleInfo> toAddList = new ArrayList<>();
        long curTime = System.currentTimeMillis();
        roleTypeList.forEach(
                roleType -> {
                    UserRoleInfo userRoleInfo = this.newUpdateCreateInstance(username, curTime);
                    userRoleInfo.setName(userRoleDTO.getName());
                    userRoleInfo.setRoleType(roleType);
                    userRoleInfo.setUsername(userRoleDTO.getUsername());
                    toAddList.add(userRoleInfo);
                }
        );
        try {
            this.saveBatch(toAddList);
        } catch (Exception e) {
            log.warn("添加用户角色失败, 原因:[{}]", e.getMessage());
            throw new RuntimeException("添加用户角色失败");
        }
        return BaseResult.of();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResult<Object> deleteUserRole(UserRoleDTO userRoleDTO) {
        userRoleInfoMapper.delete(
                Wrappers.<UserRoleInfo>lambdaUpdate()
                        .eq(UserRoleInfo::getUsername, userRoleDTO.getUsername())
        );
        return BaseResult.of();
    }

    private UserRoleInfo newUpdateCreateInstance(String username, long curTime) {
        UserRoleInfo userRoleInfo = new UserRoleInfo();
        userRoleInfo.setCreateTime(curTime);
        userRoleInfo.setCreateUsername(username);
        userRoleInfo.setUpdateUsername(username);
        userRoleInfo.setUpdateTime(curTime);
        return userRoleInfo;
    }

    private List<Byte> getRoleTypeListByStr(String str) {
        if (StringUtils.isNotBlank(str)) {
            String[] roleTypeArr = str.split(",");
            List<Byte> result = new ArrayList<>();
            for (String s : roleTypeArr) {
                result.add(Byte.valueOf(StringUtils.trim(s)));
            }
            return result;
        } else {
            return new ArrayList<>();
        }
    }
}
