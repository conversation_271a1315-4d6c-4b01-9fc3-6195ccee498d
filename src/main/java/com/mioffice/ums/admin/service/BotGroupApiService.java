package com.mioffice.ums.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.BotGroupPushListAddDTO;
import com.mioffice.ums.admin.entity.info.LarkGroupPushInfo;
import com.mioffice.ums.admin.entity.vo.BotGroupListResultVO;
import com.mioffice.ums.admin.result.BaseResult;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * @since 2020/11/26 下午3:52
 * version: 1.0.0
 */
public interface BotGroupApiService {

    BaseResult<BotGroupListResultVO> fetchBotGroupList(Long botId, UserBO userBO);

    BaseResult<IPage<LarkGroupPushInfo>> page(Long page, Long size, String tagId, String key);

    BaseResult<Object> delete(String tagId, List<Integer> idList);

    BaseResult<Object> add(BotGroupPushListAddDTO botGroupPushListAddDTO);
}
