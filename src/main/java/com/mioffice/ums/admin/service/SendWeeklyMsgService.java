package com.mioffice.ums.admin.service;

import com.mioffice.ums.admin.entity.bo.LarkCallBackBO;

import java.util.Map;

/**
 * <p>
 * 发送周报消息
 * </p>
 *
 * <AUTHOR>
 * @date 2021/3/23 3:32 下午
 */
public interface SendWeeklyMsgService {

    /**
     * 发送周报消息
     *
     * @param larkCallBackBO
     */
    void sendWeeklyMsg(LarkCallBackBO larkCallBackBO);

    /**
     * 自动发送周报消息
     */
    void autoSendWeeklyMsg();

    /**
     * 处理AccessToken
     *
     * @param code
     * @param status
     */
    void processAccessToken(String code, String status);

    /**
     * 处理飞书回调
     *
     * @param appId
     * @param params
     * @return
     */
    Map<String, Object> processLarkCallBack(String appId, Map<String, Object> params);

    /**
     * 重新刷新token
     */
    void refreshToken();

}
