package com.mioffice.ums.admin.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonParser;
import com.mioffice.ums.admin.entity.dto.HrApplyDepartmentDTO;
import com.mioffice.ums.admin.entity.dto.HrApplyPageDTO;
import com.mioffice.ums.admin.entity.info.DepartmentInfo;
import com.mioffice.ums.admin.entity.info.SyncRecordLatestTime;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.SyncRecordBelongToEnum;
import com.mioffice.ums.admin.enums.SyncRecordTypeEnum;
import com.mioffice.ums.admin.manager.HrApplyManager;
import com.mioffice.ums.admin.mapper.DepartmentInfoMapper;
import com.mioffice.ums.admin.mapper.SyncRecordLatestTimeMapper;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.HrDepartmentService;
import com.mioffice.ums.admin.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

/**
 * <p>
 * HR同步部门接口
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/10 20:52
 */
@Slf4j
@Service
public class HrDepartmentServiceImpl extends ServiceImpl<DepartmentInfoMapper, DepartmentInfo>
        implements HrDepartmentService {

    private final DepartmentInfoMapper departmentInfoMapper;

    private final HrApplyManager hrApplyManager;

    private final SyncRecordLatestTimeMapper syncRecordLatestTimeMapper;

    private final Gson gson = new GsonBuilder().disableHtmlEscaping().create();

    public HrDepartmentServiceImpl(DepartmentInfoMapper departmentInfoMapper, HrApplyManager hrApplyManager,
                                   SyncRecordLatestTimeMapper syncRecordLatestTimeMapper) {
        this.departmentInfoMapper = departmentInfoMapper;
        this.hrApplyManager = hrApplyManager;
        this.syncRecordLatestTimeMapper = syncRecordLatestTimeMapper;
    }

    @Override
    public BaseResult<String> syncDepartmentWithFullLoad() {
        HrApplyPageDTO hrApplyPageDTO = HrApplyPageDTO.getFullLoadInstant();
        return startSync(hrApplyPageDTO);
    }

    @Override
    public BaseResult<String> syncDepartmentWithIncrement(String startTime, String endTime) {
        //判断库里有无数据, 没有数据来一下全量
        long databaseCount = departmentInfoMapper.selectCount(Wrappers.lambdaQuery());
        LocalDateTime now = LocalDateTime.now();
        long currentTimeMillis = now.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        if (databaseCount == 0) {
            return this.syncDepartmentWithFullLoad();
        } else {
            HrApplyPageDTO hrApplyPageDTO = new HrApplyPageDTO();
            if (!StringUtils.isEmpty(startTime) && !StringUtils.isEmpty(endTime)) {
                hrApplyPageDTO.setStartTime(startTime);
                hrApplyPageDTO.setEndTime(endTime);
            } else {
                // 从库里面查出最后一次同步时间
                LambdaQueryWrapper<SyncRecordLatestTime> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(SyncRecordLatestTime::getBelongTo, SyncRecordBelongToEnum.HR_APPLY.getName());
                wrapper.eq(SyncRecordLatestTime::getSyncType, SyncRecordTypeEnum.SYNC_DEPARTMENT.getName());
                SyncRecordLatestTime asyncRecordLatestTime = syncRecordLatestTimeMapper.selectOne(wrapper);
                // 如果库没有记录, 就同步昨天0:00:00 至 昨天23:59:59
                if (Objects.isNull(asyncRecordLatestTime)) {
                    // 昨天开始
                    String yesterdayBegin = DateTimeUtil.getYesterdayBegin();
                    // 当前时间
                    String theEnd = LocalDateTimeUtil.format(now, DatePattern.NORM_DATETIME_PATTERN);
                    hrApplyPageDTO.setStartTime(yesterdayBegin);
                    hrApplyPageDTO.setEndTime(theEnd);
                    // 并将这次同步时间记录到库
                    SyncRecordLatestTime recordLatestTime = new SyncRecordLatestTime();
                    recordLatestTime.setBelongTo(SyncRecordBelongToEnum.HR_APPLY.getName());
                    recordLatestTime.setSyncType(SyncRecordTypeEnum.SYNC_DEPARTMENT.getName());
                    recordLatestTime.setLatestTime(currentTimeMillis);
                    recordLatestTime.setCreateTime(System.currentTimeMillis());
                    recordLatestTime.setUpdateTime(System.currentTimeMillis());
                    syncRecordLatestTimeMapper.insert(recordLatestTime);
                } else {
                    // 如果有记录
                    String startTime2 = DateTimeUtil.getStringTime(asyncRecordLatestTime.getLatestTime(),
                            DateTimeUtil.DATE_TIME_PATTERN);
                    String endTime2 = LocalDateTimeUtil.format(now, DatePattern.NORM_DATETIME_PATTERN);

                    hrApplyPageDTO.setStartTime(startTime2);
                    hrApplyPageDTO.setEndTime(endTime2);

                    // 更新一下这次同步的时间
                    LambdaQueryWrapper<SyncRecordLatestTime> updateWrapper = new LambdaQueryWrapper<>();
                    updateWrapper.eq(SyncRecordLatestTime::getId, asyncRecordLatestTime.getId());
                    asyncRecordLatestTime.setLatestTime(currentTimeMillis);
                    asyncRecordLatestTime.setUpdateTime(System.currentTimeMillis());
                    syncRecordLatestTimeMapper.update(asyncRecordLatestTime, updateWrapper);
                }
            }
            return startSync(hrApplyPageDTO);
        }
    }

    private BaseResult<String> startSync(HrApplyPageDTO hrApplyPageDTO) {
        log.info("===============拉取HR部门开始===============");
        int updateCount = 0;
        int insertCount = 0;
        Integer curPage = hrApplyPageDTO.getPageNum();

        while (true) {
            try {
                log.info("当前查询第[{}]页", curPage);
                List<HrApplyDepartmentDTO> curPageQueryList = hrApplyManager.getDepartmentList(hrApplyPageDTO);
                if (curPageQueryList.isEmpty()) {
                    break;
                }
                List<DepartmentInfo> curPageDepartmentList = this.getDepartmentList(curPageQueryList);
                List<DepartmentInfo> updateList = filterDepartmentList(curPageDepartmentList, this::getExistList);
                // 拆出来需要update的记录，剩下就是需要insert的记录
                curPageDepartmentList.removeAll(updateList);
                if (!updateList.isEmpty()) {
                    this.updateBatchById(updateList);
                }
                if (!curPageDepartmentList.isEmpty()) {
                    this.saveBatch(curPageDepartmentList);
                }
                insertCount += curPageDepartmentList.size();
                updateCount += updateList.size();
            } catch (RuntimeException e) {
                log.error("===============拉取HR部门出错===============,当前页为[{}]", curPage);
            } finally {
                curPage = curPage + 1;
                hrApplyPageDTO.setPageNum(curPage);
            }
        }
        log.info("===============拉取HR部门结束===============,当前插入部门:[" + insertCount + "]条，当前更新部门:[" +
                updateCount + "]条");
        // 异步计算部门完整的树
        ThreadUtil.execute(this::asyncCalculateCompleteDeptName);

        return BaseResult.of("当前插入部门:[" + insertCount + "]条，当前更新部门:[" + updateCount + "]条");
    }

    /**
     * 异步计算部门完整的部门名称
     */
    @Override
    public void asyncCalculateCompleteDeptName() {
        try {
            calculate(1);
        } catch (Exception e) {
            log.error("异步计算部门完整的部门名称失败，原因:[{}]", e.getMessage());
        }

    }

    private void calculate(int level) {

        if (level == 1) {
            // 取出所有的部门信息
            List<DepartmentInfo> departmentInfos = departmentInfoMapper.selectList(
                    Wrappers.<DepartmentInfo>lambdaQuery()
                            .eq(DepartmentInfo::getStatus, HrApplyStatusEnum.VALID.getCode())
                            .eq(DepartmentInfo::getTreeLevelNum, 1)
            );

            if (departmentInfos.isEmpty()) {
                return;
            }

            List<DepartmentInfo> toUpdateList = new ArrayList<>();

            departmentInfos.forEach(
                    item -> {
                        ArrayList<String> nameList = new ArrayList<String>();
                        nameList.add(item.getCnName());

                        DepartmentInfo departmentInfo = new DepartmentInfo();
                        departmentInfo.setId(item.getId());
                        departmentInfo.setCompleteDeptName(gson.toJson(nameList));

                        toUpdateList.add(departmentInfo);
                    }
            );
            if (!toUpdateList.isEmpty()) {
                this.updateBatchById(toUpdateList);
            }

            // 手动释放掉内存
            toUpdateList.clear();
            departmentInfos.clear();

            calculate(level + 1);
        } else {
            // 取出所有的部门信息
            List<DepartmentInfo> departmentInfos = departmentInfoMapper.selectList(
                    Wrappers.<DepartmentInfo>lambdaQuery()
                            .eq(DepartmentInfo::getStatus, HrApplyStatusEnum.VALID.getCode())
                            .eq(DepartmentInfo::getTreeLevelNum, level)
            );

            if (departmentInfos.isEmpty()) {
                return;
            }

            List<DepartmentInfo> toUpdateList = new ArrayList<>();

            departmentInfos.forEach(
                    item -> {
                        log.info("当前计算的部门的deptId=[{}]", item.getDeptId());
                        DepartmentInfo parentDeptInfo = departmentInfoMapper.selectOne(
                                Wrappers.<DepartmentInfo>lambdaQuery()
                                        .eq(DepartmentInfo::getDeptId, item.getParentNodeId())
                                        .eq(DepartmentInfo::getStatus, HrApplyStatusEnum.VALID.getCode())
                        );

                        if (Objects.nonNull(parentDeptInfo) &&
                                StringUtils.isNotBlank(parentDeptInfo.getCompleteDeptName())) {
                            ArrayList<String> nameList = new ArrayList<String>();

                            try {
                                JsonArray jsonArray =
                                        JsonParser.parseString(parentDeptInfo.getCompleteDeptName()).getAsJsonArray();
                                jsonArray.forEach(
                                        jsonObject -> {
                                            nameList.add(jsonObject.getAsString());
                                        }
                                );
                            } catch (Exception e) {
                                log.error("计算部门的deptId=[{}]异常,原因:[{}]", item.getDeptId(), e.getMessage());
                            }

                            nameList.add(item.getCnName());

                            DepartmentInfo departmentInfo = new DepartmentInfo();
                            departmentInfo.setId(item.getId());
                            departmentInfo.setCompleteDeptName(gson.toJson(nameList));

                            toUpdateList.add(departmentInfo);
                        } else {
                            DepartmentInfo departmentInfo = new DepartmentInfo();
                            departmentInfo.setId(item.getId());
                            // 没有父节点就是取当前节点的name
                            ArrayList<String> nameList = new ArrayList<String>();
                            nameList.add(item.getCnName());

                            departmentInfo.setCompleteDeptName(gson.toJson(nameList));

                            toUpdateList.add(departmentInfo);
                        }
                    }
            );
            if (!toUpdateList.isEmpty()) {
                this.updateBatchById(toUpdateList);
            }

            // 手动释放掉内存
            toUpdateList.clear();
            departmentInfos.clear();

            calculate(level + 1);
        }
    }

    private <T> List<T> filterDepartmentList(List<T> departmentList, Function<List<T>, List<T>> function) {
        return function.apply(departmentList);
    }

    private List<DepartmentInfo> getDepartmentList(List<HrApplyDepartmentDTO> curPageQueryList) {
        List<DepartmentInfo> list = new ArrayList<>();
        curPageQueryList.forEach(
                item -> {
                    DepartmentInfo departmentInfo = new DepartmentInfo();
                    BeanUtils.copyProperties(item, departmentInfo);
                    list.add(departmentInfo);
                }
        );
        return list;
    }

    private List<DepartmentInfo> getExistList(List<DepartmentInfo> departmentInfos) {
        List<DepartmentInfo> existList = new ArrayList<>();
        departmentInfos.forEach(
                item -> {
                    LambdaQueryWrapper<DepartmentInfo> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(DepartmentInfo::getDeptId, item.getDeptId());
                    DepartmentInfo exist = departmentInfoMapper.selectOne(wrapper);
                    if (exist != null) {
                        item.setId(exist.getId());
                        existList.add(item);
                    }
                }
        );
        return existList;
    }
}
