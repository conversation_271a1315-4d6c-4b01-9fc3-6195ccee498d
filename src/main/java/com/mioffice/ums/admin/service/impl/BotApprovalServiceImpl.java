package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.mioffice.ums.admin.constants.ProcessStatusConstant;
import com.mioffice.ums.admin.entity.bo.BotApplyCardContentBo;
import com.mioffice.ums.admin.entity.bo.BotApproveCardContentBo;
import com.mioffice.ums.admin.entity.bo.BtnNotifBO;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.BotApplyApprovalPageDTO;
import com.mioffice.ums.admin.entity.dto.BotApplyDTO;
import com.mioffice.ums.admin.entity.dto.BotApplyDetailDTO;
import com.mioffice.ums.admin.entity.dto.BotApplyListDTO;
import com.mioffice.ums.admin.entity.dto.BotApprovalPassQueryDTO;
import com.mioffice.ums.admin.entity.dto.BotApprovalRejectQueryDTO;
import com.mioffice.ums.admin.entity.info.BotApplyApplyUserInfo;
import com.mioffice.ums.admin.entity.info.BotApplyRecordInfo;
import com.mioffice.ums.admin.entity.info.BotInfo;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.UserBotInfo;
import com.mioffice.ums.admin.enums.BotApplyStatusEnum;
import com.mioffice.ums.admin.enums.BotRangeScopeEnum;
import com.mioffice.ums.admin.enums.BotTypeEnum;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.LarkAuditCallBackEnum;
import com.mioffice.ums.admin.enums.LarkRobotStatusEnum;
import com.mioffice.ums.admin.enums.UserBotRoleEnum;
import com.mioffice.ums.admin.manager.SendMessage;
import com.mioffice.ums.admin.mapper.BotApplyApplyUserInfoMapper;
import com.mioffice.ums.admin.mapper.BotApplyRecordInfoMapper;
import com.mioffice.ums.admin.mapper.BotInfoMapper;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.UserBotInfoMapper;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.BotApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/31 2:18 下午
 * version: 1.0.0
 */
@Service
@Slf4j
public class BotApprovalServiceImpl implements BotApprovalService {

    @Value("${frontendUrl}")
    private String frontendUrl;

    @Value("${lark.appId}")
    private String umsBotAppId;

    private final BotApplyRecordInfoMapper botApplyRecordInfoMapper;

    private final BotApplyApplyUserInfoMapper botApplyApplyUserInfoMapper;

    private final EmployeeInfoMapper employeeInfoMapper;

    private final BotInfoMapper botInfoMapper;

    private final UserBotInfoMapper userBotInfoMapper;

    private final SendMessage sendMessage;

    private static final String LANDING_URL = "robot/mine";

    public BotApprovalServiceImpl(BotApplyRecordInfoMapper botApplyRecordInfoMapper, BotApplyApplyUserInfoMapper botApplyApplyUserInfoMapper, EmployeeInfoMapper employeeInfoMapper, BotInfoMapper botInfoMapper, UserBotInfoMapper userBotInfoMapper, SendMessage sendMessage) {
        this.botApplyRecordInfoMapper = botApplyRecordInfoMapper;
        this.botApplyApplyUserInfoMapper = botApplyApplyUserInfoMapper;
        this.employeeInfoMapper = employeeInfoMapper;
        this.botInfoMapper = botInfoMapper;
        this.userBotInfoMapper = userBotInfoMapper;
        this.sendMessage = sendMessage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResult<Object> botApply(BotApplyDTO botApplyDTO, UserBO userBO) {

        BotInfo botInfo = botInfoMapper.selectById(botApplyDTO.getBotId());
        if (!LarkRobotStatusEnum.LARK_ROBOT_ON.getCode().equals(botInfo.getBotLarkStatus())) {
            return new BaseResult<>().setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setMessage("机器人已被禁用，请联系机器人管理员确认机器人状态");
        }

        if (BotRangeScopeEnum.PUBLIC.getCode() == botInfo.getBotRangeScope()) {
            return new BaseResult<>().setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setMessage("机器人为公有机器人，无需申请即可使用");
        }

        List<String> useUsernameList = botApplyDTO.getUseUsernameList();
        Map<String, String> userNameAndNameMap = employeeInfoMapper.selectList(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .in(EmployeeInfo::getUsername, useUsernameList)
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        ).stream().collect(Collectors.toMap(EmployeeInfo::getUsername, EmployeeInfo::getName));

        List<UserBotInfo> userBotInfoList = userBotInfoMapper.selectList(
                Wrappers.<UserBotInfo>lambdaQuery().eq(UserBotInfo::getBotId, botApplyDTO.getBotId()).eq(UserBotInfo::getUserType, UserBotRoleEnum.USER_BOT_USER.getCode())
        );
        if (!userBotInfoList.isEmpty()) {
            List<String> botUserUsernameList = userBotInfoList.stream().map(UserBotInfo::getUsername).collect(Collectors.toList());
            useUsernameList = useUsernameList.stream().filter(item -> !botUserUsernameList.contains(item)).collect(Collectors.toList());
        }

        if (useUsernameList.isEmpty()) {
            return new BaseResult<>().setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setMessage("所添加的人全部为机器人使用者");
        }

        BotApplyRecordInfo botApplyRecordInfo = BotApplyRecordInfo.newCreateAndUpdateTimeInstant();
        botApplyRecordInfo.setBotId(botApplyDTO.getBotId());
        botApplyRecordInfo.setApprovalDesc("");
        botApplyRecordInfo.setApprovalName("");
        botApplyRecordInfo.setApprovalUsername("");
        botApplyRecordInfo.setApplyStatus(BotApplyStatusEnum.PROCESSING.getCode());
        botApplyRecordInfo.setApplyDesc(botApplyDTO.getApplyDesc());
        botApplyRecordInfo.setApprovalDesc("");
        botApplyRecordInfo.setCreateUsername(userBO.getUsername());
        botApplyRecordInfo.setUpdateUsername(userBO.getUsername());
        botApplyRecordInfoMapper.insert(botApplyRecordInfo);

        List<BotApplyApplyUserInfo> botApplyApplyUserInfoList = new ArrayList<>();
        useUsernameList.forEach(
                useUsername -> {
                    BotApplyApplyUserInfo botApplyApplyUserInfo = BotApplyApplyUserInfo.newCreateAndUpdateTimeInstant();
                    botApplyApplyUserInfo.setApplyUserUsername(useUsername);
                    String name = userNameAndNameMap.get(useUsername);
                    if (StringUtils.isNotBlank(name)) {
                        botApplyApplyUserInfo.setApplyUserName(name);
                    } else {
                        botApplyApplyUserInfo.setApplyUserName("");
                    }
                    botApplyApplyUserInfo.setBotApplyRecordId(botApplyRecordInfo.getId());
                    botApplyApplyUserInfoList.add(botApplyApplyUserInfo);
                }
        );
        botApplyApplyUserInfoMapper.batchInsert(botApplyApplyUserInfoList);

        sendBotApplyCard(botApplyDTO, botInfo, userNameAndNameMap, botApplyRecordInfo);
        return BaseResult.of();
    }

    private void sendBotApplyCard(BotApplyDTO botApplyDTO, BotInfo botInfo, Map<String, String> userNameAndNameMap, BotApplyRecordInfo botApplyRecordInfo) {
        List<UserBotInfo> userBotInfoList = userBotInfoMapper.selectList(
                Wrappers.<UserBotInfo>lambdaQuery()
                        .eq(UserBotInfo::getBotId, botApplyDTO.getBotId())
                        .eq(UserBotInfo::getUserType, UserBotRoleEnum.USER_BOT_MANAGER.getCode())
        );
        List<String> manageUsernameList = userBotInfoList.stream().map(UserBotInfo::getUsername).collect(Collectors.toList());
        List<UserBO> userBOList = new ArrayList<>();
        List<EmployeeInfo> employeeInfoList = employeeInfoMapper.selectList(
                Wrappers.<EmployeeInfo>lambdaQuery().in(EmployeeInfo::getUsername, manageUsernameList)
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        );
        employeeInfoList.forEach(
                item -> {
                    UserBO userBOTmp = new UserBO(item.getUsername(), item.getEmail());
                    userBOList.add(userBOTmp);
                }
        );

        BotApplyCardContentBo botApplyCardContentBo = new BotApplyCardContentBo();
        botApplyCardContentBo.setApplyDesc(botApplyRecordInfo.getApplyDesc());
        botApplyCardContentBo.setBotApplyId(botApplyRecordInfo.getId());
        botApplyCardContentBo.setCreateUsername(botApplyRecordInfo.getCreateUsername());
        botApplyCardContentBo.setName(userNameAndNameMap.get(botApplyRecordInfo.getCreateUsername()));
        botApplyCardContentBo.setPushUserList(userBOList);
        botApplyCardContentBo.setRobotName(botInfo.getBotName());
        botApplyCardContentBo.setRobotTypeDesc(BotTypeEnum.getMsgByCode(botInfo.getBotType()));
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("landingUrl", frontendUrl.concat(LANDING_URL).concat("?key=MY_AUDIT"));
        botApplyCardContentBo.setAction(paramMap);
        sendMessage.sendBotApplyCardMessage(botApplyCardContentBo);
    }

    @Override
    public IPage<BotApplyListDTO> botApplyListMy(UserBO userBO, Long page, Long size) {

        Page<BotApplyRecordInfo> dataPage = new Page<>(page, size);

        LambdaQueryWrapper<BotApplyRecordInfo> lambdaQuery = Wrappers.lambdaQuery();

        lambdaQuery.and(
                wrapper -> wrapper.eq(BotApplyRecordInfo::getCreateUsername, userBO.getUsername())
        );

        lambdaQuery.orderByDesc(BotApplyRecordInfo::getCreateTime);

        Page<BotApplyRecordInfo> botApplyRecordInfoPage = botApplyRecordInfoMapper.selectPage(dataPage, lambdaQuery);
        List<BotApplyRecordInfo> botApplyRecordInfoPageRecords = botApplyRecordInfoPage.getRecords();

        Page<BotApplyListDTO> botApplyListDTOPage = new Page<>(page, size);
        if (botApplyRecordInfoPageRecords.isEmpty()) {
            return botApplyListDTOPage;
        }

        List<Long> idList = botApplyRecordInfoPageRecords.stream().map(BotApplyRecordInfo::getId).collect(Collectors.toList());
        List<BotApplyListDTO> botApplyListDTOList = botApplyRecordInfoMapper.selectApplyPageRecords(idList);
        botApplyListDTOList.forEach(
                item -> {
                    item.setBotTypeDesc(BotTypeEnum.getMsgByCode(item.getBotType()));
                }
        );

        botApplyListDTOPage.setTotal(botApplyRecordInfoPage.getTotal());
        botApplyListDTOPage.setRecords(botApplyListDTOList);

        return botApplyListDTOPage;

    }

    @Override
    public IPage<BotApplyApprovalPageDTO> botApprovalPage(UserBO userBO, Long page, Long size) {
        Page<BotApplyApprovalPageDTO> dataPage = new Page<>(page, size);

        List<BotApplyApprovalPageDTO> botApplyApprovalPageDTOList = botApplyRecordInfoMapper.selectApproval(dataPage, userBO.getUsername());
        botApplyApprovalPageDTOList.forEach(
                item -> {
                    item.setBotTypeDesc(BotTypeEnum.getMsgByCode(item.getBotType()));
                }
        );
        dataPage.setRecords(botApplyApprovalPageDTOList);
        return dataPage;
    }

    @Override
    public BotApplyDetailDTO botApplyDetail(Long id) {

        return botApplyRecordInfoMapper.selectDetail(id);
    }

    @Override
    public BaseResult<Object> botApprovalReject(UserBO userBO, BotApprovalRejectQueryDTO botApprovalRejectQueryDTO) {
        BotApplyRecordInfo botApplyRecordInfo = BotApplyRecordInfo.newUpdateTimeInstant();
        botApplyRecordInfo.setApplyStatus(BotApplyStatusEnum.REJECT.getCode());
        botApplyRecordInfo.setApprovalDesc(botApprovalRejectQueryDTO.getRemark());
        setApprovalInfoAndUpdateUsername(userBO, botApplyRecordInfo);

        Long botApplyId = botApprovalRejectQueryDTO.getId();

        if (validateApprovalPermission(userBO, botApplyId)) {
            return new BaseResult<>(ResultCode.FORBIDDEN).setMessage("您没有权限对此进行审核");
        }

        int updateRows = botApplyRecordInfoMapper.update(
                botApplyRecordInfo, Wrappers.<BotApplyRecordInfo>lambdaQuery()
                        .eq(BotApplyRecordInfo::getId, botApplyId)
                        .eq(BotApplyRecordInfo::getApplyStatus, BotApplyStatusEnum.PROCESSING.getCode())
        );

        if (updateRows == 0) {
            return new BaseResult<>().setCode(ResultCode.OK.getCode()).setMessage("该任务已被其他人审批");
        }

        sendApplyResultCard(botApplyId, ProcessStatusConstant.ACTION_REJECT);

        return BaseResult.of();

    }

    private boolean validateApprovalPermission(UserBO userBO, Long botApplyId) {
        BotApplyRecordInfo botApplyRecordInfoTmp = botApplyRecordInfoMapper.selectById(botApplyId);
        Long botId = botApplyRecordInfoTmp.getBotId();
        UserBotInfo userBotInfo = userBotInfoMapper.selectOne(
                Wrappers.<UserBotInfo>lambdaQuery()
                        .eq(UserBotInfo::getBotId, botId)
                        .eq(UserBotInfo::getUsername, userBO.getUsername())
                        .eq(UserBotInfo::getUserType, UserBotRoleEnum.USER_BOT_MANAGER.getCode())
        );

        return Objects.isNull(userBotInfo);
    }

    private void setApprovalInfoAndUpdateUsername(UserBO userBO, BotApplyRecordInfo botApplyRecordInfo) {
        String username = userBO.getUsername();
        if (StringUtils.isNotBlank(username)) {
            botApplyRecordInfo.setApprovalUsername(userBO.getUsername());
            EmployeeInfo employeeInfo = employeeInfoMapper.selectOne(
                    Wrappers.<EmployeeInfo>lambdaQuery()
                            .eq(EmployeeInfo::getUsername, userBO.getUsername())
                            .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
            );
            botApplyRecordInfo.setApprovalName(employeeInfo.getName());
            botApplyRecordInfo.setUpdateUsername(username);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResult<Object> botApprovalPass(UserBO userBO, BotApprovalPassQueryDTO botApprovalPassQueryDTO) {

        BotApplyRecordInfo botApplyRecordInfo = BotApplyRecordInfo.newUpdateTimeInstant();
        botApplyRecordInfo.setApplyStatus(BotApplyStatusEnum.APPROVAL.getCode());
        setApprovalInfoAndUpdateUsername(userBO, botApplyRecordInfo);

        Long botApplyId = botApprovalPassQueryDTO.getId();
        if (validateApprovalPermission(userBO, botApplyId)) {
            return new BaseResult<>(ResultCode.FORBIDDEN).setMessage("您没有权限对此进行审核");
        }

        int updateRows = botApplyRecordInfoMapper.update(
                botApplyRecordInfo, Wrappers.<BotApplyRecordInfo>lambdaQuery()
                        .eq(BotApplyRecordInfo::getId, botApplyId)
                        .eq(BotApplyRecordInfo::getApplyStatus, BotApplyStatusEnum.PROCESSING.getCode())
        );

        if (updateRows == 0) {
            return new BaseResult<>().setCode(ResultCode.OK.getCode()).setMessage("该任务已被其他人审批");
        }

        BotApplyRecordInfo botApplyRecord = botApplyRecordInfoMapper.selectById(botApprovalPassQueryDTO.getId());
        List<BotApplyApplyUserInfo> botApplyApplyUserInfoList = botApplyApplyUserInfoMapper.selectList(
                Wrappers.<BotApplyApplyUserInfo>lambdaQuery().eq(BotApplyApplyUserInfo::getBotApplyRecordId, botApprovalPassQueryDTO.getId())
        );

        List<UserBotInfo> userBotInfos = userBotInfoMapper.selectList(
                Wrappers.<UserBotInfo>lambdaQuery()
                        .eq(UserBotInfo::getBotId, botApplyRecord.getBotId())
                        .eq(UserBotInfo::getUserType, UserBotRoleEnum.USER_BOT_USER.getCode())
        );
        List<String> botUseUsernameList = userBotInfos.stream().map(UserBotInfo::getUsername).collect(Collectors.toList());

        List<String> usernameList = botApplyApplyUserInfoList.stream().map(BotApplyApplyUserInfo::getApplyUserUsername).collect(Collectors.toList());
        List<String> usernameListTmp = usernameList.stream().filter(item -> !botUseUsernameList.contains(item)).collect(Collectors.toList());

        if (!usernameListTmp.isEmpty()) {
            List<UserBotInfo> userBotInfoList = new ArrayList<>();
            usernameListTmp.forEach(
                    usernameTmp -> {
                        UserBotInfo userBotInfo = UserBotInfo.newCreateAndUpdateTimeInstant();
                        userBotInfo.setUsername(usernameTmp);
                        userBotInfo.setUserType(UserBotRoleEnum.USER_BOT_USER.getCode());
                        userBotInfo.setBotId(botApplyRecord.getBotId());
                        userBotInfo.setCreateUsername(usernameTmp);
                        userBotInfo.setUpdateUsername(usernameTmp);
                        userBotInfoList.add(userBotInfo);
                    }
            );

            userBotInfoMapper.batchInsert(userBotInfoList);
        }

        sendApplyResultCard(botApplyId, ProcessStatusConstant.ACTION_AGREE);

        return BaseResult.of();

    }

    private void sendApplyResultCard(Long botApplyId, int actionAgree) {
        BotApplyRecordInfo botApplyRecord = botApplyRecordInfoMapper.selectById(botApplyId);

        BotApproveCardContentBo botApproveCardContentBo = new BotApproveCardContentBo();
        botApproveCardContentBo.setApproveName(botApplyRecord.getApprovalName());
        botApproveCardContentBo.setApproveUsername(botApplyRecord.getApprovalUsername());
        botApproveCardContentBo.setCreateUsername(botApplyRecord.getCreateUsername());
        EmployeeInfo employeeInfo = employeeInfoMapper.selectOne(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .eq(EmployeeInfo::getUsername, botApplyRecord.getCreateUsername())
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        );
        botApproveCardContentBo.setName(employeeInfo.getName());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("landingUrl", frontendUrl.concat(LANDING_URL).concat("?key=MY_APPLICATION"));
        botApproveCardContentBo.setAction(paramMap);
        botApproveCardContentBo.setProcessStatus(actionAgree);
        botApproveCardContentBo.setRefuseReason(botApplyRecord.getApprovalDesc());
        BotInfo botInfo = botInfoMapper.selectById(botApplyRecord.getBotId());
        botApproveCardContentBo.setRobotName(botInfo.getBotName());
        sendMessage.sendBotApplyResultCardMessage(botApproveCardContentBo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LarkAuditCallBackEnum larkApprovedCallBack(BtnNotifBO btnNotifBO) {
        Map<String, Object> result = btnNotifBO.getBtnActionBo().getAction().getValue();
        try {
            Long applyId = Long.valueOf(result.get("botApplyId").toString());
            String username = btnNotifBO.getBtnActionBo().getUserId();
            EmployeeInfo employeeInfo = employeeInfoMapper.selectOne(
                    Wrappers.<EmployeeInfo>lambdaQuery()
                            .eq(EmployeeInfo::getUsername, username)
                            .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
            );

            // 0.检查是机器人是否被停用
            BotApplyRecordInfo botApplyRecordInfo = botApplyRecordInfoMapper.selectById(applyId);
            BotInfo botInfo = botInfoMapper.selectById(botApplyRecordInfo.getBotId());
            if (!LarkRobotStatusEnum.LARK_ROBOT_ON.getCode().equals(botInfo.getBotLarkStatus())) {
                return LarkAuditCallBackEnum.BOT_FORBIDDEN;
            }
            // 1. 判断是否被审核
            if (StringUtils.isNotBlank(botApplyRecordInfo.getApprovalUsername())) {
                return LarkAuditCallBackEnum.HAVE_BEEN_AUDIT;
            }
            // 2. 执行审核通过
            UserBO userBO = new UserBO(username, employeeInfo.getName());
            BotApprovalPassQueryDTO botApprovalPassQueryDTO = new BotApprovalPassQueryDTO(applyId);
            botApprovalPass(userBO, botApprovalPassQueryDTO);
            // 3.返回执行状态
            return LarkAuditCallBackEnum.APPROVED;
        } catch (Exception e) {
            log.error("飞书审核app_id为[{}]的任务失败,原因:[{}],请求参数为[{}]", btnNotifBO.getAppId(), e.getMessage(), new Gson().toJson(btnNotifBO));
            throw e;
        }
    }

    @Override
    public LarkAuditCallBackEnum larkRejectedCallBack(BtnNotifBO btnNotifBO) {
        Map<String, Object> result = btnNotifBO.getBtnActionBo().getAction().getValue();
        try {
            Long applyId = Long.valueOf(result.get("botApplyId").toString());
            String username = btnNotifBO.getBtnActionBo().getUserId();
            EmployeeInfo employeeInfo = employeeInfoMapper.selectOne(
                    Wrappers.<EmployeeInfo>lambdaQuery()
                            .eq(EmployeeInfo::getUsername, username)
                            .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
            );

            // 0.检查是机器人是否被停用
            BotApplyRecordInfo botApplyRecordInfo = botApplyRecordInfoMapper.selectById(applyId);
            BotInfo botInfo = botInfoMapper.selectById(botApplyRecordInfo.getBotId());
            if (!LarkRobotStatusEnum.LARK_ROBOT_ON.getCode().equals(botInfo.getBotLarkStatus())) {
                return LarkAuditCallBackEnum.BOT_FORBIDDEN;
            }
            // 1. 判断是否被审核
            if (StringUtils.isNotBlank(botApplyRecordInfo.getApprovalUsername())) {
                return LarkAuditCallBackEnum.HAVE_BEEN_AUDIT;
            }
            // 2. 执行审核驳回
            UserBO userBO = new UserBO(username, employeeInfo.getName());
            BotApprovalRejectQueryDTO botApprovalRejectQueryDTO = new BotApprovalRejectQueryDTO(applyId, "");
            botApprovalReject(userBO, botApprovalRejectQueryDTO);
            // 3.返回执行状态
            return LarkAuditCallBackEnum.APPROVE_REJECT;
        } catch (Exception e) {
            log.error("飞书审核app_id为[{}]的任务失败,原因:[{}],请求参数为[{}]", btnNotifBO.getAppId(), e.getMessage(), new Gson().toJson(btnNotifBO));
            throw e;
        }
    }
}
