package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.admin.async.BotDashboardAsyncCalculator;
import com.mioffice.ums.admin.entity.info.BotInfo;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.LarkTaskInfo;
import com.mioffice.ums.admin.entity.vo.BotDashboardVO;
import com.mioffice.ums.admin.entity.vo.CostTimeChartVO;
import com.mioffice.ums.admin.manager.UserInfoManager;
import com.mioffice.ums.admin.manager.UserRoleManager;
import com.mioffice.ums.admin.mapper.BotInfoMapper;
import com.mioffice.ums.admin.mapper.LarkTaskInfoMapper;
import com.mioffice.ums.admin.service.BotDashboardService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <p>
 * 机器人看板Service
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/7 2:00 下午
 */
@Slf4j
@Service
public class BotDashboardServiceImpl implements BotDashboardService {

    private final BotDashboardAsyncCalculator botDashboardAsyncCalculator;

    private final UserInfoManager userInfoManager;

    private final UserRoleManager userRoleManager;

    private final BotInfoMapper botInfoMapper;

    private final LarkTaskInfoMapper larkTaskInfoMapper;

    public BotDashboardServiceImpl(BotDashboardAsyncCalculator botDashboardAsyncCalculator, UserInfoManager userInfoManager, UserRoleManager userRoleManager, BotInfoMapper botInfoMapper, LarkTaskInfoMapper larkTaskInfoMapper) {
        this.botDashboardAsyncCalculator = botDashboardAsyncCalculator;
        this.userInfoManager = userInfoManager;
        this.userRoleManager = userRoleManager;
        this.botInfoMapper = botInfoMapper;
        this.larkTaskInfoMapper = larkTaskInfoMapper;
    }

    @Override
    public BotDashboardVO getBotDashboardVO(String deptIdList, String managerUsernameList, String startTime, String endTime, String username) {
        BotDashboardVO botDashboardVO = new BotDashboardVO();
        // 1. init Future
        Future<BotDashboardVO.BotSummary> botSummaryFuture = null;
        Future<BotDashboardVO.PushScopeSummary> pushScopeSummaryFuture = null;
        Future<CostTimeChartVO> costTimeChartFuture = null;
        Future<BotDashboardVO.PushScopeChartVO> pushScopeChartFuture = null;
        Future<List<BotDashboardVO.DeptBotRank>> deptBotFuture = botDashboardAsyncCalculator.getDeptBotRankAsync();
        Future<List<BotDashboardVO.TaskCountRank>> taskCountFuture = null;

        if (StringUtils.isNotBlank(deptIdList)) {
            // 1. 只有超级管理员才能选择部门, 如果选择了部门但是没有选择管理员
            if (userRoleManager.isSuperAdmin(username)) {
                if (StringUtils.isBlank(managerUsernameList)) {
                    List<EmployeeInfo> botManagerList = userInfoManager.getBotManagerByDeptId(deptIdList);
                    List<String> botManagerUsernameList = botManagerList.stream().map(EmployeeInfo::getUsername).collect(Collectors.toList());
                    List<Long> botIdList = userInfoManager.getUserManageBotIdList(botManagerUsernameList);
                    botIdList.removeIf(id -> !userInfoManager.get1thDeptBotIdList(deptIdList).contains(id));
                    List<Long> taskIdList = getTaskIdList(botIdList);
                    botSummaryFuture = botDashboardAsyncCalculator.getBotSummaryAsync(botIdList);
                    taskCountFuture = botDashboardAsyncCalculator.getTaskCountRankAsync(botIdList);
                    pushScopeSummaryFuture = botDashboardAsyncCalculator.getPushScopeAsync(startTime, endTime, taskIdList);
                    costTimeChartFuture = botDashboardAsyncCalculator.getCostTimeChart(startTime, endTime, taskIdList);
                    pushScopeChartFuture = botDashboardAsyncCalculator.getPublishScopeChart(startTime, endTime, taskIdList);
                } else {
                    List<Long> botIdList = userInfoManager.getUserManageBotIdList(spiltStr(managerUsernameList));
                    botIdList.removeIf(id -> !userInfoManager.get1thDeptBotIdList(deptIdList).contains(id));
                    List<Long> taskIdList = getTaskIdList(botIdList);
                    botSummaryFuture = botDashboardAsyncCalculator.getBotSummaryAsync(botIdList);
                    taskCountFuture = botDashboardAsyncCalculator.getTaskCountRankAsync(botIdList);
                    pushScopeSummaryFuture = botDashboardAsyncCalculator.getPushScopeAsync(startTime, endTime, taskIdList);
                    costTimeChartFuture = botDashboardAsyncCalculator.getCostTimeChart(startTime, endTime, taskIdList);
                    pushScopeChartFuture = botDashboardAsyncCalculator.getPublishScopeChart(startTime, endTime, taskIdList);
                }
            } else {
                return botDashboardVO;
            }
        } else {
            if (userRoleManager.isSuperAdmin(username)) {
                // 1.如果是超管，部门没选直接查所有
                List<Long> botIdList = getAllBotIdList();
                List<Long> taskIdList = getTaskIdList(botIdList);
                botSummaryFuture = botDashboardAsyncCalculator.getBotSummaryAsync(botIdList);
                taskCountFuture = botDashboardAsyncCalculator.getTaskCountRankAsync(botIdList);
                pushScopeSummaryFuture = botDashboardAsyncCalculator.getPushScopeAsync(startTime, endTime, taskIdList);
                costTimeChartFuture = botDashboardAsyncCalculator.getCostTimeChart(startTime, endTime, taskIdList);
                pushScopeChartFuture = botDashboardAsyncCalculator.getPublishScopeChart(startTime, endTime, taskIdList);
            } else if (userRoleManager.isSystemAdmin(username)) {
                // 2. 如果是系统管理员
                if (StringUtils.isNotBlank(managerUsernameList)) {
                    // 2.1 如果他选择了机器人管理员
                    List<Long> botIdList = userInfoManager.getUserManageBotIdList(spiltStr(managerUsernameList));
                    botIdList.removeIf(id -> !userInfoManager.get1thDeptBotIdList(userInfoManager.getFirstDeptInfoByUsername(username).getDeptId()).contains(id));
                    List<Long> taskIdList = getTaskIdList(botIdList);
                    botSummaryFuture = botDashboardAsyncCalculator.getBotSummaryAsync(botIdList);
                    taskCountFuture = botDashboardAsyncCalculator.getTaskCountRankAsync(botIdList);
                    pushScopeSummaryFuture = botDashboardAsyncCalculator.getPushScopeAsync(startTime, endTime, taskIdList);
                    costTimeChartFuture = botDashboardAsyncCalculator.getCostTimeChart(startTime, endTime, taskIdList);
                    pushScopeChartFuture = botDashboardAsyncCalculator.getPublishScopeChart(startTime, endTime, taskIdList);
                } else {
                    // 2.2 如果没选择机器人管理员，查询他的一级部门下面的所有管理员
                    String deptId = userInfoManager.getFirstDeptInfoByUsername(username).getDeptId();
                    List<EmployeeInfo> botManagerList = userInfoManager.getBotManagerByDeptId(deptId);
                    List<String> botManagerUsernameList = botManagerList.stream().map(EmployeeInfo::getUsername).collect(Collectors.toList());
                    List<Long> botIdList = userInfoManager.getUserManageBotIdList(botManagerUsernameList);
                    botIdList.removeIf(id -> !userInfoManager.get1thDeptBotIdList(deptId).contains(id));
                    List<Long> taskIdList = getTaskIdList(botIdList);
                    botSummaryFuture = botDashboardAsyncCalculator.getBotSummaryAsync(botIdList);
                    taskCountFuture = botDashboardAsyncCalculator.getTaskCountRankAsync(botIdList);
                    pushScopeSummaryFuture = botDashboardAsyncCalculator.getPushScopeAsync(startTime, endTime, taskIdList);
                    costTimeChartFuture = botDashboardAsyncCalculator.getCostTimeChart(startTime, endTime, taskIdList);
                    pushScopeChartFuture = botDashboardAsyncCalculator.getPublishScopeChart(startTime, endTime, taskIdList);
                }
            } else if (userRoleManager.isBotManager(username)) {
                // 3. 如果为机器人管理员, 查询当前账号作为管理员的数据
                List<String> botManagerUsernameList = new ArrayList<>();
                botManagerUsernameList.add(username);
                List<Long> botIdList = userInfoManager.getUserManageBotIdList(botManagerUsernameList);
                List<Long> taskIdList = getTaskIdList(botIdList);
                botSummaryFuture = botDashboardAsyncCalculator.getBotSummaryAsync(botIdList);
                taskCountFuture = botDashboardAsyncCalculator.getTaskCountRankAsync(botIdList);
                pushScopeSummaryFuture = botDashboardAsyncCalculator.getPushScopeAsync(startTime, endTime, taskIdList);
                costTimeChartFuture = botDashboardAsyncCalculator.getCostTimeChart(startTime, endTime, taskIdList);
                pushScopeChartFuture = botDashboardAsyncCalculator.getPublishScopeChart(startTime, endTime, taskIdList);
            } else {
                return botDashboardVO;
            }
        }

        try {
            botDashboardVO.setBotSummary(botSummaryFuture.get());
            botDashboardVO.setDeptBotRankList(deptBotFuture.get());
            botDashboardVO.setTaskCountRankList(taskCountFuture.get());
            botDashboardVO.setPushScopeSummary(pushScopeSummaryFuture.get());
            botDashboardVO.setPushScopeChart(pushScopeChartFuture.get());
            botDashboardVO.setCostTimeChart(costTimeChartFuture.get());
        } catch (InterruptedException | ExecutionException e) {
            log.warn("异步计算机器人看板异常，原因[{}]", e.getMessage());
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.warn("获取机器人看板异常，原因[{}]", e.getMessage());
        }
        return botDashboardVO;
    }

    @Override
    public List<EmployeeInfo> getBotDashboardManagerList(String deptIdList, String username, String searchWord) {
        if (userRoleManager.isSuperAdmin(username)) {
            List<EmployeeInfo> employeeInfos = userInfoManager.getBotManagerByDeptId(deptIdList);
            return employeeInfos
                    .stream()
                    .filter(employeeInfo -> employeeInfo.getUsername().contains(searchWord) || employeeInfo.getName().contains(searchWord))
                    .distinct().collect(Collectors.toList());
        } else if (userRoleManager.isSystemAdmin(username)) {
            String deptId = userInfoManager.getFirstDeptInfoByUsername(username).getDeptId();
            List<EmployeeInfo> employeeInfos = userInfoManager.getBotManagerByDeptId(deptId);
            return employeeInfos
                    .stream()
                    .filter(employeeInfo -> employeeInfo.getUsername().contains(searchWord) || employeeInfo.getName().contains(searchWord))
                    .distinct().collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    private List<Long> getAllBotIdList() {
        List<BotInfo> botInfos = botInfoMapper.selectList(
                Wrappers.<BotInfo>lambdaQuery()
        );
        if (!botInfos.isEmpty()) {
            return botInfos.stream().map(BotInfo::getId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    private List<Long> getTaskIdList(List<Long> botIdList) {
        if (botIdList.isEmpty()) {
            return new ArrayList<>();
        }
        return larkTaskInfoMapper.selectList(
                Wrappers.<LarkTaskInfo>lambdaQuery()
                        .in(LarkTaskInfo::getBotId, botIdList)
                        .select(LarkTaskInfo::getTaskId)
        ).stream().map(LarkTaskInfo::getTaskId).collect(Collectors.toList());
    }

    private List<String> spiltStr(String str) {
        String[] strArr = str.split(",");
        return new ArrayList<>(Arrays.asList(strArr));
    }

}
