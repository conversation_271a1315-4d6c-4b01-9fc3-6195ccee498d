package com.mioffice.ums.admin.service;

import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.vo.AppMessageChannelVO;
import com.mioffice.ums.admin.entity.vo.AppMessageCountVO;
import com.mioffice.ums.admin.entity.vo.AppSummaryVO;
import com.mioffice.ums.admin.entity.vo.ListVO;
import com.mioffice.ums.admin.result.BaseResult;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.26
 */
public interface SysDashBoardService {

    /**
     * 获取排名前10的系统的消息
     *
     * @param userBO
     * @param beginTime
     * @param endTime
     * @return
     */
    BaseResult<ListVO<AppMessageCountVO>> getSysMessageTop(UserBO userBO, long beginTime, long endTime);

    /**
     * 获取各个渠道的消息总条数
     *
     * @param userBO
     * @param sysIdList
     * @param beginTime
     * @param endTime
     * @return
     */
    BaseResult<ListVO<AppMessageChannelVO>> getSysMessageChannelCount(UserBO userBO, List<Long> sysIdList, long beginTime, long endTime);

    /**
     * 获取系统简要列表
     *
     * @return
     */
    BaseResult<ListVO<AppSummaryVO>> getSysSummaryList();

}
