package com.mioffice.ums.admin.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mioffice.ums.admin.entity.bo.AlarmBo;
import com.mioffice.ums.admin.entity.bo.AlarmCountBo;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.bo.UserDeptBo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.entity.info.TaskMonitorInfo;
import com.mioffice.ums.admin.entity.vo.AlarmTrendVO;
import com.mioffice.ums.admin.entity.vo.TaskMonitorListVO;
import com.mioffice.ums.admin.entity.vo.TaskMonitorVO;
import com.mioffice.ums.admin.enums.TaskStatusEnum;
import com.mioffice.ums.admin.manager.UserInfoManager;
import com.mioffice.ums.admin.manager.UserRoleManager;
import com.mioffice.ums.admin.manager.warning.TaskMonitorManager;
import com.mioffice.ums.admin.mapper.EarlyWarningInfoMapper;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import com.mioffice.ums.admin.mapper.TaskMonitorInfoMapper;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.WarningService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.09
 */
@Slf4j
@Service
public class WarningServiceImpl implements WarningService {

    private final EarlyWarningInfoMapper earlyWarningInfoMapper;
    private final TaskMonitorManager taskMonitorManager;
    private final TaskMonitorInfoMapper taskMonitorInfoMapper;
    private final TaskInfoMapper taskInfoMapper;
    private final UserRoleManager userRoleManager;
    private final UserInfoManager userInfoManager;

    public WarningServiceImpl(EarlyWarningInfoMapper earlyWarningInfoMapper, TaskMonitorManager taskMonitorManager, TaskMonitorInfoMapper taskMonitorInfoMapper, TaskInfoMapper taskInfoMapper, UserRoleManager userRoleManager, UserInfoManager userInfoManager) {
        this.earlyWarningInfoMapper = earlyWarningInfoMapper;
        this.taskMonitorManager = taskMonitorManager;
        this.taskMonitorInfoMapper = taskMonitorInfoMapper;
        this.taskInfoMapper = taskInfoMapper;
        this.userRoleManager = userRoleManager;
        this.userInfoManager = userInfoManager;
    }

    @Override
    public BaseResult<AlarmTrendVO> getAlarmTrend(UserBO userBO, DateTime beginTime, DateTime endTime) {

        UserDeptBo authRole = getAuthRole(userBO);

        long days = (endTime.getTime() - beginTime.getTime()) / (24 * 3600 * 1000);
        if (days >= 356) {
            return BaseResult.of(getWarningCountByMonth(beginTime, endTime, days, authRole.getUsername(), authRole.getDeptId()));
        } else {
            return BaseResult.of(getWarningCountByDay(beginTime, endTime, days, authRole.getUsername(), authRole.getDeptId()));
        }
    }

    /**
     * 按照天
     *
     * @param beginTime
     * @param endTime
     * @param days
     * @return
     */
    private AlarmTrendVO getWarningCountByDay(DateTime beginTime, DateTime endTime, long days, String username, String deptId) {

        List<String> xAxis = new ArrayList<>();
        List<Integer> yAxis = new ArrayList<>();

        Map<String, AlarmCountBo> dateMap = earlyWarningInfoMapper
                .selectWarningCount(beginTime.getTime(), endTime.getTime(), "%Y-%m-%d", username, deptId)
                .stream().collect(Collectors.toMap(AlarmCountBo::getCreateDate, Function.identity(), (v1, v2) -> v2));
        for (long i = 0; i <= days; i++) {
            LocalDate localDate = LocalDate.parse(beginTime.toDateStr());
            LocalDate plusDays = localDate.plusDays(i);
            xAxis.add(plusDays.toString());
            if (dateMap.containsKey(plusDays.toString())) {
                yAxis.add(dateMap.get(plusDays.toString()).getCount());
            } else {
                yAxis.add(0);
            }
        }

        return new AlarmTrendVO(xAxis, yAxis);
    }

    /**
     * 按照月
     *
     * @param beginTime
     * @param endTime
     * @param days
     * @return
     */
    private AlarmTrendVO getWarningCountByMonth(DateTime beginTime, DateTime endTime, long days, String username, String deptId) {
        List<String> xAxis = new ArrayList<>();
        List<Integer> yAxis = new ArrayList<>();
        Map<String, AlarmCountBo> monthMap = earlyWarningInfoMapper.selectWarningCount(beginTime.getTime(), endTime.getTime(), "%Y-%m", username, deptId)
                .stream().collect(Collectors.toMap(AlarmCountBo::getCreateDate, Function.identity(), (v1, v2) -> v2));
        long months = days / 30;
        for (long i = 0; i <= months; i++) {
            LocalDate localDate = LocalDate.parse(beginTime.toDateStr());
            LocalDate plusMonths = localDate.plusMonths(i);
            String month = plusMonths.toString().substring(0, 7);
            xAxis.add(month);
            if (monthMap.containsKey(month)) {
                yAxis.add(monthMap.get(month).getCount());
            } else {
                yAxis.add(0);
            }
        }

        return new AlarmTrendVO(xAxis, yAxis);
    }

    @Override
    public BaseResult<IPage<AlarmBo>> getAlarmPage(UserBO userBO, Long page, Long size) {

        UserDeptBo authRole = getAuthRole(userBO);

        Page<Object> iPage = new Page<>(page, size);
        return BaseResult.of(earlyWarningInfoMapper.selectAlarmPage(iPage, authRole.getUsername(), authRole.getDeptId()));
    }

    @Override
    public BaseResult<TaskMonitorListVO> getNowMonitorList(UserBO userBO) {
        UserDeptBo authRole = getAuthRole(userBO);

        List<Long> sendingTaskIdList = taskMonitorInfoMapper.selectSendingTaskId();
        if (sendingTaskIdList.isEmpty()) {
            return BaseResult.of(new TaskMonitorListVO(new ArrayList<>()));
        }
        List<TaskInfo> taskInfoList = taskInfoMapper.selectListByMap(
                TaskStatusEnum.PUBLISH_SUCCESS.getCode(),
                sendingTaskIdList,
                authRole.getUsername(),
                authRole.getDeptId()
        );

        if (taskInfoList.isEmpty()) {
            return BaseResult.of(new TaskMonitorListVO(new ArrayList<>()));
        }

        Map<Long, TaskInfo> taskIdMap = taskInfoList.stream().collect(Collectors.toMap(TaskInfo::getId, Function.identity()));
        List<Long> taskIdList = taskInfoList.stream().map(TaskInfo::getId).collect(Collectors.toList());
        taskIdList.retainAll(sendingTaskIdList);
        List<TaskMonitorInfo> taskMonitorInfoList = taskMonitorInfoMapper.selectList(Wrappers.<TaskMonitorInfo>lambdaQuery().in(TaskMonitorInfo::getTaskId, taskIdList));

        List<TaskMonitorVO> taskMonitorVOList = new ArrayList<>();
        // 按照 taskId 进行分组
        Map<Long, List<TaskMonitorInfo>> taskIdListMap = taskMonitorInfoList.stream().collect(Collectors.groupingBy(TaskMonitorInfo::getTaskId, Collectors.mapping(Function.identity(), Collectors.toList())));
        for (Map.Entry<Long, List<TaskMonitorInfo>> entry : taskIdListMap.entrySet()) {
            Long taskId = entry.getKey();
            List<TaskMonitorInfo> monitorInfoList = entry.getValue();

            TaskMonitorVO taskMonitorVO = new TaskMonitorVO();
            taskMonitorVO.setTaskId(taskId);
            taskMonitorVO.setTitleCn(taskIdMap.get(taskId).getTitleCn());
            taskMonitorVO.setTotalCount(monitorInfoList.get(0).getAllCount());

            List<String> xAxis = new ArrayList<>();
            List<TaskMonitorVO.Detail> yAxis = new ArrayList<>();
            for (TaskMonitorInfo taskMonitorInfo : monitorInfoList) {
                Long createTime = taskMonitorInfo.getCreateTime();
                xAxis.add(DateUtil.format(DateTime.of(createTime), "HH:mm:ss"));

                TaskMonitorVO.Detail detail = new TaskMonitorVO.Detail();
                detail.setPushCount(taskMonitorInfo.getPushCount());
                detail.setPushTime(taskMonitorInfo.getCostTime());
                long oneAvg = 0;
                if (taskMonitorInfo.getPushCount() != 0) {
                    oneAvg = taskMonitorInfo.getCostTime() / taskMonitorInfo.getPushCount();
                }

                detail.setAvgOnePushTime(oneAvg);
                if (taskMonitorInfo.getTodoCount() == 0) {
                    detail.setExpectedTime(createTime);
                } else {
                    detail.setExpectedTime(createTime + (oneAvg * taskMonitorInfo.getTodoCount()));
                }

                yAxis.add(detail);
            }
            taskMonitorVO.setXAxis(xAxis);
            taskMonitorVO.setYAxis(yAxis);

            taskMonitorVOList.add(taskMonitorVO);
        }
        return BaseResult.of(new TaskMonitorListVO(taskMonitorVOList));
    }

    public UserDeptBo getAuthRole(UserBO userBO) {
        String username;
        String deptId;
        if (userRoleManager.isSystemAdmin(userBO.getUsername())) {
            deptId = userInfoManager.getFirstDeptInfoByUsername(userBO.getUsername()).getDeptId();
            username = null;
        } else if (userRoleManager.isSuperAdmin(userBO.getUsername())) {
            username = null;
            deptId = null;
        } else {
            username = userBO.getUsername();
            deptId = null;
        }

        UserDeptBo userDeptBo = new UserDeptBo();
        userDeptBo.setDeptId(deptId);
        userDeptBo.setUsername(username);

        return userDeptBo;
    }
}
