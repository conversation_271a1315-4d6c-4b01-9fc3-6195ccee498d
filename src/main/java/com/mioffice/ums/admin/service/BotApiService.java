package com.mioffice.ums.admin.service;

import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.AllBotDTO;
import com.mioffice.ums.admin.entity.dto.BotDTO;
import com.mioffice.ums.admin.entity.dto.BotUseDetailDTO;
import com.mioffice.ums.admin.entity.dto.MyBotDTO;
import com.mioffice.ums.admin.entity.dto.NotifyUrlVerifyDTO;
import com.mioffice.ums.admin.entity.dto.QueryBotDTO;
import com.mioffice.ums.admin.entity.dto.RobotVerifyDTO;
import com.mioffice.ums.admin.entity.info.BotInfo;
import com.mioffice.ums.admin.result.BaseResult;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 机器人相关API
 * <p>
 *
 * <AUTHOR> tie tou
 * @date 2020/8/8 下午7:58
 */
public interface BotApiService {

    /**
     * 获取所有机器人列表
     *
     * @param page
     * @param size
     * @param botName
     * @param deptId
     * @param owner
     * @param botStatus
     * @param botType
     * @return
     */
    BaseResult<AllBotDTO> queryAllBotsByPage(Long page, Long size, String botName, String deptId, String owner,
                                             String botStatus, String botType,Byte botTenant);

    /**
     * 添加机器人
     *
     * @param botDTO 机器人dto
     * @param username 创建人
     * @return BaseResult<BotInfo>
     */
    BaseResult<BotInfo> addRobotInfo(BotDTO botDTO, String username);

    /**
     * 验证机器人
     *
     * @param botDTO 机器人dto
     * @return BaseResult<RobotVerifyDTO>
     */
    BaseResult<RobotVerifyDTO> verify(BotDTO botDTO);

    /**
     * 机器人 禁用
     *
     * @param botDTO 机器人dto
     * @return BaseResult<BotInfo>
     */
    BaseResult<BotInfo> stopBot(BotDTO botDTO, String username);

    /**
     * 机器人 开启
     *
     * @param botDTO 机器人dto
     * @return BaseResult<BotInfo>
     */
    BaseResult<BotInfo> startBot(BotDTO botDTO, String username);

    /**
     * 机器人 维护
     *
     * @param botDTO 机器人dto
     * @param username 修改人
     * @return BaseResult<BotInfo>
     */
    BaseResult<BotInfo> updateBot(BotDTO botDTO, String username);

    /**
     * 我的bot
     *
     * @param username 当前用户
     * @return BaseResult<MyBotDTO>
     */
    BaseResult<MyBotDTO> myBot(String username);

    /**
     * 机器人使用详情列表
     *
     * @return
     */
    BaseResult<BotUseDetailDTO> queryBotUseByPage(Long page, Long size, Long botId, UserBO userBO);

    /**
     * 查询单个机器人信息
     *
     * @param id
     * @param username
     * @return
     */
    BaseResult<QueryBotDTO> queryBotById(Long id, String username);

    /**
     * 获取我能用的飞书机器人列表
     *
     * @param username 当前登陆人
     * @return BaseResult<List < BotDTO>>
     */
    BaseResult<List<BotDTO>> getMyLarkBotList(String username);

    /**
     * 获取我能用的邮件机器人列表
     *
     * @param username 当前登陆人
     * @return BaseResult<List < BotDTO>>
     */
    BaseResult<Object> getMyEmailBotList(String username);

    /**
     * 获取我能用的短信机器人列表
     *
     * @param username 当前登陆人
     * @return BaseResult<List < BotDTO>>
     */
    BaseResult<Object> getMyMessageBotList(String username);

    /**
     * 同步飞书那边机器人状态
     */
    void syncLarkBotStatus();

    /**
     * 修改机器人的日历状态为可用
     *
     * @param appId
     */
    void updateBotCalendarStatus(String appId);

    /**
     * 存放机器人的日程状态
     *
     * @param appId
     */
    void putBotCalendarCacheStatus(String appId);

    /**
     * 返回NotifyURL
     *
     * @param notifyUrlVerifyDTO
     * @return BaseResult 返回结果
     */
    BaseResult<Map<String, Object>> verifyCalendarStatus(NotifyUrlVerifyDTO notifyUrlVerifyDTO);

    /**
     * 验证日历状态
     *
     * @param notifyUrlVerifyDTO
     * @return BaseResult 返回结果
     */
    BaseResult<Map<String, Object>> getNotifyUrl(NotifyUrlVerifyDTO notifyUrlVerifyDTO);

    /**
     * 机器人停用，停用消息任务
     *
     * @param botId
     * @param updateUsername
     */
    void stopTaskByBot(Long botId, String updateUsername);

    /**
     * 机器人启用，恢复消息任务
     *
     * @param botId
     * @param updateUsername
     * @return 审核中的任务id
     */
    List<Long> startTaskByBot(Long botId, String updateUsername);

    /**
     * 根据botId获取登录人所拥有的机器人
     *
     * @param botId 机器人ID
     * @param login 登录人
     * @return List<BotInfo>
     */
    List<BotInfo> getBotList(Long botId, String login);

    /**
     * 填充botId
     *
     * @param botId botId
     * @return BaseResult<Object>
     */
    BaseResult<Object> fillBotBizId(Long botId);
}
