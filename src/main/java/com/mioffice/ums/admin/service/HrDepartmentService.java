package com.mioffice.ums.admin.service;

import com.mioffice.ums.admin.result.BaseResult;

/**
 * <p>
 * HR同步部门接口
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/10 20:49
 */
public interface HrDepartmentService {

    /**
     * 全量同步部门
     *
     * @return String
     */
    BaseResult<String> syncDepartmentWithFullLoad();

    /**
     * 增量同步部门
     *
     * @param startTime 开始时间
     * @param endTime 截止时间
     * @return String
     */
    BaseResult<String> syncDepartmentWithIncrement(String startTime, String endTime);

    void asyncCalculateCompleteDeptName();
}
