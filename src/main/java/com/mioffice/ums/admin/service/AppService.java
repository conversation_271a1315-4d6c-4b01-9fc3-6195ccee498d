package com.mioffice.ums.admin.service;

import com.mioffice.ums.admin.entity.bo.AppBo;
import com.mioffice.ums.admin.entity.bo.AppStatusBo;
import com.mioffice.ums.admin.entity.bo.AppTopicBO;
import com.mioffice.ums.admin.entity.bo.AppTopicDetailBO;
import com.mioffice.ums.admin.entity.bo.BtnNotifBO;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.AppWhiteStatusDto;
import com.mioffice.ums.admin.entity.vo.AppDetailVO;
import com.mioffice.ums.admin.result.BaseResult;

/**
 * <AUTHOR>
 * @since 2020/9/21
 */
public interface AppService {

    /**
     * apply
     *
     * @param appBo
     * @return
     */
    BaseResult<AppStatusBo> apply(AppBo appBo, UserBO userBO);

    BaseResult<AppDetailVO> detail(Long id, UserBO userBO);

    boolean stopApp(BtnNotifBO btnNotifBO);

    BaseResult<Boolean> updateWhiteStatus(AppWhiteStatusDto appWhiteStatusDto);

    BaseResult<Boolean> applyTopic(AppTopicBO appTopicBO, UserBO userBO);

    BaseResult<Boolean> cancelTopic(String appId, UserBO userBO);

    BaseResult<AppTopicDetailBO> getAppTopic(String appId);

}
