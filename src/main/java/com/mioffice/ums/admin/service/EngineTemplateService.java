package com.mioffice.ums.admin.service;

import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.AddTemplateDTO;
import com.mioffice.ums.admin.entity.dto.UpdateTemplateDTO;
import com.mioffice.ums.admin.entity.vo.AddTemplateVO;
import com.mioffice.ums.admin.entity.vo.BotListVO;
import com.mioffice.ums.admin.entity.vo.TemplateDetailVO;
import com.mioffice.ums.admin.entity.vo.TemplateListVO;
import com.mioffice.ums.admin.result.BaseResult;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/22 9:23 上午
 * version: 1.0.0
 */
public interface EngineTemplateService {

    BaseResult<TemplateDetailVO> getTemplateDetail(UserBO userBO, Long id);

    BaseResult<Object> deleteTemplate(UserBO userBO, List<Long> ids);

    BaseResult<TemplateListVO> getTemplatePage(UserBO userBO, Long page, Long size, Byte channel, Long appSysId, String templateName);

    BaseResult<AddTemplateVO> addTemplate(UserBO userBO, AddTemplateDTO addTemplateDTO);

    BaseResult<List<BotListVO>> getBotList(UserBO userBO, Long appSysId, Integer channel);

    BaseResult<Object> updateTemplate(UserBO userBO, UpdateTemplateDTO updateTemplateDTO);
}
