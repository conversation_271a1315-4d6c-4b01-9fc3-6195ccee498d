package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.admin.entity.dto.PreSendDTO;
import com.mioffice.ums.admin.entity.dto.UserInfoDTO;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.PrePushListInfo;
import com.mioffice.ums.admin.entity.info.UserRoleInfo;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.HrEmployeeTypeEnum;
import com.mioffice.ums.admin.enums.RoleTypeEnum;
import com.mioffice.ums.admin.manager.UserRoleManager;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.PrePushListInfoMapper;
import com.mioffice.ums.admin.mapper.UserRoleInfoMapper;
import com.mioffice.ums.admin.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * USER
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/13 16:20
 */
@Service
public class UserServiceImpl implements UserService {

    private final EmployeeInfoMapper employeeInfoMapper;

    private final PrePushListInfoMapper prePushListInfoMapper;

    private final UserRoleInfoMapper roleInfoMapper;

    private final UserRoleManager userRoleManager;

    public UserServiceImpl(EmployeeInfoMapper employeeInfoMapper, PrePushListInfoMapper prePushListInfoMapper,
                           UserRoleInfoMapper roleInfoMapper, UserRoleManager userRoleManager) {
        this.employeeInfoMapper = employeeInfoMapper;
        this.prePushListInfoMapper = prePushListInfoMapper;
        this.roleInfoMapper = roleInfoMapper;
        this.userRoleManager = userRoleManager;
    }

    @Override
    public List<UserInfoDTO> searchUser(String searchWord, boolean excludePartner) {
        List<UserInfoDTO> result = new ArrayList<>();

        if (StringUtils.isNotBlank(searchWord)) {

            LambdaQueryWrapper<EmployeeInfo> queryWrapper = new LambdaQueryWrapper<EmployeeInfo>();
            // 1.首先取有效的
            queryWrapper.eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode());
            // 2.根据查询的人
            queryWrapper.and(
                    wrapper -> {
                        wrapper.like(EmployeeInfo::getName, searchWord)
                                .or()
                                .like(EmployeeInfo::getUsername, searchWord);
                    }
            );
            // 是否排除合作伙伴账户
            if (excludePartner) {
                queryWrapper.ne(EmployeeInfo::getEmpType, HrEmployeeTypeEnum.PARTNER.getCode());
            } else {
                queryWrapper.eq(EmployeeInfo::getEmpType, HrEmployeeTypeEnum.PARTNER.getCode());
            }
            List<EmployeeInfo> employeeInfos = employeeInfoMapper.selectList(queryWrapper);
            employeeInfos.forEach(
                    item -> {
                        UserInfoDTO userInfoDTO = new UserInfoDTO();
                        userInfoDTO.setPersonId(item.getEmpId());
                        userInfoDTO.setName(item.getName());
                        userInfoDTO.setUsername(item.getUsername());
                        result.add(userInfoDTO);
                    }
            );
        }
        return result;
    }

    @Override
    public UserInfoDTO getLoginUser(String username) {
        EmployeeInfo employeeInfo = employeeInfoMapper.selectOne(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .eq(EmployeeInfo::getUsername, username)
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        );
        Assert.notNull(employeeInfo, "账号为:[" + username + "]" + "的人员信息不存在");
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setName(employeeInfo.getName());
        userInfoDTO.setUsername(employeeInfo.getUsername());
        userInfoDTO.setEmail(employeeInfo.getEmail());
        userInfoDTO.setDeptId(employeeInfo.getDeptId());
        userInfoDTO.setDeptLevel1(employeeInfo.getMiDeptLevel2());
        userInfoDTO.setDeptLevel1Desc(employeeInfo.getMiDeptLevel2Desc());
        userInfoDTO.setDeptLevel2(employeeInfo.getMiDeptLevel3());
        userInfoDTO.setDeptLevel2Desc(employeeInfo.getMiDeptLevel3Desc());
        userInfoDTO.setDeptLevel3(employeeInfo.getMiDeptLevel4());
        userInfoDTO.setDeptLevel3Desc(employeeInfo.getMiDeptLevel4Desc());

        // 获取登录人的角色
        List<UserRoleInfo> userRoleInfoList = roleInfoMapper.selectList(
                Wrappers.<UserRoleInfo>lambdaQuery()
                        .eq(UserRoleInfo::getUsername, username)
                        .select(UserRoleInfo::getRoleType)
        );

        if (!userRoleInfoList.isEmpty()) {
            userInfoDTO.setRoleTypeList(
                    userRoleInfoList
                            .stream()
                            .map(UserRoleInfo::getRoleType)
                            .collect(Collectors.toList())
            );
        } else {
            List<Byte> defaultRole = new ArrayList<>();
            // 添加默认角色
            defaultRole.add((byte) 0);
            userInfoDTO.setRoleTypeList(defaultRole);
        }
        // 判断有没有机器人管理员角色
        if (userRoleManager.isBotManager(username)) {
            userInfoDTO.getRoleTypeList().add(RoleTypeEnum.ROLE_BOT_ADMIN.getCode());
        }
        return userInfoDTO;
    }

    @Override
    public List<PreSendDTO> getPreSendList(Long belongTo, String loginUser) {
        List<PreSendDTO> preSendList = new ArrayList<>();
        // 1.先查询当前登录人作为预发送名单之一
        UserInfoDTO login = getLoginUser(loginUser);
        PreSendDTO loginDefault = new PreSendDTO();
        BeanUtils.copyProperties(login, loginDefault);
        preSendList.add(loginDefault);

        // 2. 查库内置的预发送名单
        List<PrePushListInfo> queryDefaultList = prePushListInfoMapper.selectList(
                Wrappers.<PrePushListInfo>lambdaQuery()
                        .eq(PrePushListInfo::getDefaultFlag, 1)
        );

        // 3. 查询某个机器人或者邮件或者SMS专门配置的预推送人员
        List<PrePushListInfo> querySettingList = prePushListInfoMapper.selectList(
                Wrappers.<PrePushListInfo>lambdaQuery()
                        .eq(PrePushListInfo::getBelongTo, belongTo)
        );

        queryDefaultList.forEach(
                prePushListInfo -> {
                    // 提取前端需要的字段值, 其他字段值暂时没有给
                    PreSendDTO preSendDTO = new PreSendDTO();
                    BeanUtils.copyProperties(prePushListInfo, preSendDTO);
                    preSendList.add(preSendDTO);
                }
        );

        querySettingList.forEach(
                prePushListInfo -> {
                    PreSendDTO preSendDTO = new PreSendDTO();
                    BeanUtils.copyProperties(prePushListInfo, preSendDTO);
                    preSendList.add(preSendDTO);
                }
        );

        return preSendList.stream().distinct().collect(Collectors.toList());
    }
}
