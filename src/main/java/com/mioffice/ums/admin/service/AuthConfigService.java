package com.mioffice.ums.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.entity.dto.RoleDTO;
import com.mioffice.ums.admin.entity.dto.UserRoleDTO;
import com.mioffice.ums.admin.entity.dto.UserRoleQueryDTO;
import com.mioffice.ums.admin.result.BaseResult;

import java.util.List;

/**
 * <p>
 * 权限角色配置
 * </p>
 *
 * <AUTHOR>
 * @date 2020/8/31 2:44 下午
 */
public interface AuthConfigService {

    /**
     * 查询role list
     *
     * @return BaseResult<List < RoleDTO>>
     */
    BaseResult<List<RoleDTO>> queryRoleList();

    /**
     * 查询用户对应的权限
     *
     * @param userRoleQueryDTO 查询dto
     * @return BaseResult<IPage < UserRoleDTO>>
     */
    BaseResult<IPage<UserRoleDTO>> queryUserRolePage(UserRoleQueryDTO userRoleQueryDTO);

    /**
     * 新增用户角色
     *
     * @param userRoleDTO dto
     * @param username 当前登录人
     * @return BaseResult<Object>
     */
    BaseResult<Object> addUserRole(UserRoleDTO userRoleDTO, String username);

    /**
     * 编辑用户角色
     *
     * @param userRoleDTO dto
     * @param username 当前登录人
     * @return BaseResult<Object>
     */
    BaseResult<Object> updateUserRole(UserRoleDTO userRoleDTO, String username);

    /**
     * 删除用户角色
     *
     * @param userRoleDTO dto
     * @return BaseResult<Object>
     */
    BaseResult<Object> deleteUserRole(UserRoleDTO userRoleDTO);
}
