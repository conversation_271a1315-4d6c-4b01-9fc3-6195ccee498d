package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.admin.entity.bo.AddTemplateRequestBO;
import com.mioffice.ums.admin.entity.bo.SmsRobotBO;
import com.mioffice.ums.admin.entity.bo.TemplateDetailBO;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.AddTemplateDTO;
import com.mioffice.ums.admin.entity.dto.UpdateTemplateDTO;
import com.mioffice.ums.admin.entity.info.BotInfo;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.vo.AddTemplateVO;
import com.mioffice.ums.admin.entity.vo.BotListVO;
import com.mioffice.ums.admin.entity.vo.TemplateDetailVO;
import com.mioffice.ums.admin.entity.vo.TemplateListVO;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.TaskChannelEnum;
import com.mioffice.ums.admin.mapper.BotInfoMapper;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.remote.grpc.AppListGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.EngineTemplateService;
import com.mioffice.ums.admin.utils.MapperUtil;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AddTemplateResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.DeleteMessageTemplateResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.GetSysBotListResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.GetTemplatePageResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.MessageTemplateDetail;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.MessageTemplateDetailResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.TemplatePage;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.UpdateTemplateRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.UpdateTemplateResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/22 9:23 上午
 * version: 1.0.0
 */
@Slf4j
@Service
public class EngineTemplateServiceImpl implements EngineTemplateService {

    private final AppListGrpcClient appListGrpcClient;

    private final EmployeeInfoMapper employeeInfoMapper;

    private final BotInfoMapper botInfoMapper;

    public EngineTemplateServiceImpl(AppListGrpcClient appListGrpcClient, EmployeeInfoMapper employeeInfoMapper, BotInfoMapper botInfoMapper) {

        this.appListGrpcClient = appListGrpcClient;
        this.employeeInfoMapper = employeeInfoMapper;
        this.botInfoMapper = botInfoMapper;
    }

    @Override
    public BaseResult<TemplateDetailVO> getTemplateDetail(UserBO userBO, Long id) {
        MessageTemplateDetailResponse response = appListGrpcClient.getMessageTemplateDetail(id);
        int code = response.getCode();
        String message = response.getMessage();
        if (code != ResultCode.OK.getCode()) {
            return new BaseResult<TemplateDetailVO>().setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setMessage(message);
        }
        MessageTemplateDetail messageTemplateDetail = response.getMessageTemplateDetail();
        TemplateDetailBO templateDetailBO = MapperUtil.INSTANCE.mapToTemplateDetailBO(messageTemplateDetail);
        String botBizId = templateDetailBO.getBotBizId();
        TemplateDetailVO templateDetailVO = new TemplateDetailVO();
        BeanUtils.copyProperties(templateDetailBO, templateDetailVO);
        List<BotInfo> botInfoList = botInfoMapper.selectList(
                Wrappers.<BotInfo>lambdaQuery().eq(BotInfo::getBotBizId, botBizId)
        );
        if (botInfoList == null || botInfoList.isEmpty()) {
            templateDetailVO.setBotInfo(new BotListVO());
            return BaseResult.of(templateDetailVO);
        }
        BotListVO botListVO = new BotListVO(botInfoList.get(0).getBotName(), botInfoList.get(0).getBotBizId());
        templateDetailVO.setBotInfo(botListVO);

        return BaseResult.of(templateDetailVO);
    }

    @Override
    public BaseResult<Object> deleteTemplate(UserBO userBO, List<Long> ids) {
        String username = userBO.getUsername();
        String name = userBO.getName();
        List<String> roleList = userBO.getRoleList();
        DeleteMessageTemplateResponse response = appListGrpcClient.deleteMessageTemplate(ids, username, name, roleList);
        int code = response.getCode();
        String message = response.getMessage();
        if (code == ResultCode.UNAUTHORIZED.getCode()) {
            return new BaseResult<>().setCode(code).setMessage("用户无权操作此记录");
        }
        if (code != ResultCode.OK.getCode()) {
            return new BaseResult<>().setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setMessage(message);
        }
        return BaseResult.of();
    }

    @Override
    public BaseResult<TemplateListVO> getTemplatePage(UserBO userBO, Long page, Long size, Byte channel, Long appSysId, String templateName) {
        if (!StringUtils.isNotBlank(templateName)) {
            templateName = "";
        }
        GetTemplatePageResponse response = appListGrpcClient.getTemplatePage(page, size, channel, appSysId, templateName);
        int code = response.getCode();
        String message = response.getMessage();
        if (code != ResultCode.OK.getCode()) {
            return new BaseResult<TemplateListVO>().setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setMessage(message);
        }
        TemplatePage templatePage = response.getTemplatePage();
        TemplateListVO templateListVO = MapperUtil.INSTANCE.mapToTemplateListVO(templatePage);
        List<TemplateListVO.TemplateListRecord> records = templateListVO.getRecords();
        if (records.isEmpty()) {
            return BaseResult.of(templateListVO);
        }

        List<String> botBizIdList = records.stream().distinct().map(TemplateListVO.TemplateListRecord::getBotBizId).collect(Collectors.toList());
        List<BotInfo> botInfoList = botInfoMapper.selectList(
                Wrappers.<BotInfo>lambdaQuery().in(BotInfo::getBotBizId, botBizIdList)
        );
        Map<String, BotInfo> botBizIdAndBotInfoMap = botInfoList.stream().collect(Collectors.toMap(BotInfo::getBotBizId, Function.identity(), (v1, v2) -> v2));

        if (channel == TaskChannelEnum.CHANNEL_SMS.getCode()) {
            records.forEach(
                    templateListRecord -> {
                        SmsRobotBO smsRobot = new SmsRobotBO();
                        BotInfo botInfo = botBizIdAndBotInfoMap.get(templateListRecord.getBotBizId());
                        smsRobot.setBotBizId(botInfo.getBotBizId());
                        smsRobot.setBotId(botInfo.getId());
                        smsRobot.setSignCode(botInfo.getBotKey());
                        smsRobot.setSignName(botInfo.getBotName());
                        // 此处为通用字段，短信时返回短信签名
                        templateListRecord.setBotAppId(smsRobot.getSignName());
                    }
            );
        }

        return BaseResult.of(templateListVO);
    }

    @Override
    public BaseResult<AddTemplateVO> addTemplate(UserBO userBO, AddTemplateDTO addTemplateDTO) {
        AddTemplateRequestBO addTemplateRequestBO = new AddTemplateRequestBO();
        BeanUtils.copyProperties(addTemplateDTO, addTemplateRequestBO);
        String username = userBO.getUsername();
        EmployeeInfo employeeInfo = employeeInfoMapper.selectOne(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .eq(EmployeeInfo::getUsername, username)
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        );
        addTemplateRequestBO.setName(employeeInfo.getName());
        addTemplateRequestBO.setUsername(employeeInfo.getUsername());
        addTemplateRequestBO.setRoleList(userBO.getRoleList());

        AddTemplateResponse addTemplateResponse = appListGrpcClient.addTemplate(addTemplateRequestBO);
        int code = addTemplateResponse.getCode();
        String message = addTemplateResponse.getMessage();
        String bizId = addTemplateResponse.getBizId();
        if (code == ResultCode.UNAUTHORIZED.getCode()) {
            return new BaseResult<AddTemplateVO>(ResultCode.FORBIDDEN).setMessage(message);
        }
        if (code != ResultCode.OK.getCode()) {
            return new BaseResult<AddTemplateVO>().setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setMessage(message);
        }
        AddTemplateVO addTemplateVO = new AddTemplateVO();
        addTemplateVO.setBizId(bizId);
        return BaseResult.of(addTemplateVO);
    }

    @Override
    public BaseResult<List<BotListVO>> getBotList(UserBO userBO, Long appSysId, Integer channel) {
        GetSysBotListResponse botList = appListGrpcClient.getBotList(appSysId, channel);
        List<String> botBizIdList = botList.getBotBizIdList();
        if (botBizIdList.isEmpty()) {
            return BaseResult.of(Collections.emptyList());
        }
        List<BotListVO> botListVOList = new ArrayList<>();
        List<BotInfo> botInfoList = botInfoMapper.selectList(
                Wrappers.<BotInfo>lambdaQuery().in(BotInfo::getBotBizId, botBizIdList)
        );
        botInfoList.forEach(
                botInfo -> botListVOList.add(new BotListVO(botInfo.getBotName(), botInfo.getBotBizId()))
        );

        return BaseResult.of(botListVOList);
    }

    @Override
    public BaseResult<Object> updateTemplate(UserBO userBO, UpdateTemplateDTO updateTemplateDTO) {

        EmployeeInfo employeeInfo = employeeInfoMapper.selectOne(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .eq(EmployeeInfo::getUsername, userBO.getUsername())
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        );

        UpdateTemplateRequest.Builder updateTemplateRequest = UpdateTemplateRequest.newBuilder();
        updateTemplateRequest.setId(updateTemplateDTO.getId());
        if (Objects.nonNull(updateTemplateDTO.getBotBizIds()) && !updateTemplateDTO.getBotBizIds().isEmpty()) {
            String botBizId = updateTemplateDTO.getBotBizIds().get(0);
            updateTemplateRequest.setBotBizId(botBizId);
            BotInfo botInfo = botInfoMapper.selectOne(Wrappers.<BotInfo>lambdaQuery().eq(BotInfo::getBotBizId, botBizId));
            updateTemplateRequest.setBotKey(botInfo.getBotKey());
        }
        if (StringUtils.isNotBlank(updateTemplateDTO.getTemplateName())) {
            updateTemplateRequest.setTemplateName(updateTemplateDTO.getTemplateName());
        }
        if (StringUtils.isNotBlank(updateTemplateDTO.getTemplateContent())) {
            updateTemplateRequest.setTemplateContent(updateTemplateDTO.getTemplateContent());
        }

        updateTemplateRequest.setUpdateUsername(userBO.getUsername());
        updateTemplateRequest.setUpdateName(employeeInfo.getName());

        UpdateTemplateResponse updateTemplateResponse = appListGrpcClient.updateTemplate(updateTemplateRequest.build());
        if (updateTemplateResponse.getCode() == ResultCode.OK.getCode()) {
            return BaseResult.of();
        } else {
            return new BaseResult<>(updateTemplateResponse.getCode(), updateTemplateResponse.getMessage());
        }
    }
}
