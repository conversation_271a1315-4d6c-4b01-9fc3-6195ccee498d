package com.mioffice.ums.admin.service.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import static com.mioffice.ums.admin.constants.RuleConventionConstants.SEND_KEY_PREFIX;
import static com.mioffice.ums.admin.constants.RuleConventionConstants.larkUserFunction;
import com.mioffice.ums.admin.entity.dto.SendUsersDTO;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.MessageButtonActions;
import com.mioffice.ums.admin.entity.info.MessageJob;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.enums.ContentFlagEnum;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.MessageChannelsEnum;
import com.mioffice.ums.admin.enums.ReceiveTypeEnum;
import com.mioffice.ums.admin.manager.SendMessage;
import com.mioffice.ums.admin.mapper.MessageButtonActionsMapper;
import com.mioffice.ums.admin.remote.grpc.AppListGrpcClient;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.EmployeeService;
import com.mioffice.ums.admin.service.MessageJobUrgeService;
import com.mioffice.ums.admin.service.MessageRuleJobService;
import com.mioffice.ums.admin.service.TaskService;
import com.mioffice.ums.admin.utils.FreeMarkerUtils;
import com.mioffice.ums.admin.utils.JsonUtils;
import com.mioffice.ums.admin.utils.MessageUtils;
import com.mioffice.ums.admin.x5client.X5HttpRequest;
import com.xiaomi.info.grpc.client.annotation.RpcClientAutowired;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageTemplateId;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUser;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUserResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.GetMessageRuleDetailResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.MessageRuleDetail;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.OpenServerBlockingClient;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.SendMessageBatchRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.SendMessageBatchResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 05 09,2023
 */
@Service
@Slf4j
public class MessageJobUrgeServiceImpl implements MessageJobUrgeService {

    @Resource
    private MessageButtonActionsMapper messageButtonActionsMapper;

    @Resource
    private MessageGrpcClient messageGrpcClient;

    @Resource
    private SendMessage sendMessage;

    @Autowired
    private TaskService taskService;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private MessageRuleJobService messageRuleJobService;

    @Resource
    private AppListGrpcClient appListGrpcClient;

    @RpcClientAutowired("open-server")
    private OpenServerBlockingClient openServerBlockingClient;

    @Resource
    private RedisTemplate redisTemplate;

    @NacosValue(value = "${ums.admin.template.urge-sub}", autoRefreshed = true)
    private String urgeSubTemplateId;

    @NacosValue(value = "${ums.admin.template.urge-success}", autoRefreshed = true)
    private Long urgeSuccessTemplateId;

    @NacosValue(value = "${ums.admin.template.urge-fail}", autoRefreshed = true)
    private Long urgeFailTemplateId;

    @Value("${cas.client-host-url}")
    private String frontHost;

    @Override
    public void urgeCustom(String managerUrgeExtraId, String appId, Long msgJobId, String username) {
        String extraId = Joiner.on("-").join("urgeCustomSuccess", msgJobId, managerUrgeExtraId);

        MessageJob job = messageRuleJobService.getById(msgJobId);
        if (Objects.isNull(job)) {
            return;
        }
        Long ruleId = job.getRuleId();

        GetMessageRuleDetailResponse response = appListGrpcClient.getMessageRuleDetail(null, ruleId);
        MessageRuleDetail detail = response.getMessageRuleDetail();

        List<MessageButtonActions> buttonActionList = messageButtonActionsMapper.selectList(
                Wrappers.<MessageButtonActions>lambdaQuery().eq(MessageButtonActions::getMsgJobId, msgJobId));

        if (CollectionUtils.isEmpty(buttonActionList)) {
            return;
        }

        buttonActionList.forEach(buttonAction -> {
            try {
                String title = buttonAction.getMessageTitle();
                String titleEn = buttonAction.getMessageTitleEn();
                String content = buttonAction.getMessageContent();
                String contentEn = buttonAction.getMessageContentEn();
                String btnText = buttonAction.getBtnText();
                String btnTextEn = buttonAction.getBtnTextEn();
                String btnUrl = buttonAction.getBtnUrl();
                Integer receiversType = buttonAction.getReceivesType();
                if (ReceiveTypeEnum.HTTP.getCode().equals(receiversType)) {
                    String url = buttonAction.getReceivesParam();
                    List<SendUsersDTO> userList = Lists.newArrayList();
                    String bizResp = StringUtils.EMPTY;
                    try {
                        bizResp = X5HttpRequest.post(appId, StringUtils.EMPTY, url, Maps.newHashMap(), false);
                        BaseResult<ArrayList<SendUsersDTO>> result =
                                JsonUtils.parse(bizResp, new TypeToken<BaseResult<ArrayList<SendUsersDTO>>>() {
                                }.getType());
                        if (result.getCode() == 0) {
                            userList.addAll(result.getData());
                        }
                        log.info("自定义催办规则业务系统回调地址:" + url + ",推送人数" + userList.size());
                    } catch (Exception e) {
                        log.error("自定义催办规则处理回调失败", e);
                        throw new RuntimeException(
                                String.format("自定义催办规则处理回调失败,jobId=[%s],resp=[%s]", msgJobId, bizResp));
                    }
                    if (CollectionUtils.isNotEmpty(userList)) {
                        Lists.partition(userList, 500)
                                .forEach(batch -> {

                                    batch.forEach(
                                            processParam(extraId,
                                                    title,
                                                    titleEn,
                                                    content,
                                                    contentEn,
                                                    btnText,
                                                    btnTextEn,
                                                    btnUrl)
                                    );

                                    send(msgJobId, extraId, batch, job, detail);

                                    if (StringUtils.isNotBlank(btnUrl)) {
                                        redisTemplate.opsForSet().add(SEND_KEY_PREFIX + extraId,
                                                batch.stream()
                                                        .map(SendUsersDTO::getOprid)
                                                        .collect(Collectors.toList())
                                                        .toArray(new String[batch.size()]));
                                    }
                                });
                    }
                }
                MessageUser.Builder builder = MessageUser.newBuilder();
                builder.setAppId(appId);
                builder.setUsername(username);
                builder.setExtraId(UUID.randomUUID().toString().replace("-", StringUtils.EMPTY));
                messageGrpcClient.sendMessage(builder.build(),
                        MessageTemplateId.newBuilder().setMessageTemplateId(urgeSuccessTemplateId).build());
            } catch (Exception e) {
                MessageUser.Builder builder = MessageUser.newBuilder();
                builder.setAppId(appId);
                builder.setUsername(username);
                builder.setExtraId(extraId.replace("urgeCustomSuccess", "urgeCustomFail"));
                messageGrpcClient.sendMessage(builder.build(),
                        MessageTemplateId.newBuilder().setMessageTemplateId(urgeFailTemplateId).build());
                String logMsg = String.format("messageJobId = [%s],自定义催办规则失败", msgJobId);
                throw new RuntimeException(logMsg, e);
            }
        });

    }

    @Override
    public void urgeManager(String managerUrgeExtraId,
                            String appId,
                            Long msgJobId,
                            Long taskId,
                            String urgeData,
                            String username) {
        String extraId;
        long timeStamp = System.currentTimeMillis();
        List<SendUsersDTO> subList = JsonUtils.parse(urgeData, new TypeToken<List<SendUsersDTO>>() {
        }.getType());
        if (CollectionUtils.isEmpty(subList)) {
            return;
        }
        subList = subList
                .stream()
                .filter(x -> employeeService.count(Wrappers.<EmployeeInfo>lambdaQuery()
                        .eq(EmployeeInfo::getUsername, x.getOprid())
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())) > 0)
                .collect(Collectors.toList());

        EmployeeInfo employeeInfo = employeeService.getOne(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .eq(EmployeeInfo::getUsername, username)
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode()));
        if (!validateArgument(appId, msgJobId, taskId, username, employeeInfo)) {
            return;
        }
        boolean isRule = Objects.nonNull(msgJobId) && Objects.isNull(taskId);
        try {
            // 上级催办【规则】
            if (isRule) {
                extraId = Joiner.on("-")
                        .join("urgeManagerByRuleSuccess", msgJobId, managerUrgeExtraId);
                urgeManagerByRule(msgJobId, extraId, subList);
            }
            // 上级催办【任务】
            else {
                extraId = Joiner.on("-").join("urgeManagerByTaskSuccess", managerUrgeExtraId);
                urgeManagerByTask(appId, taskId, extraId, subList, employeeInfo);
            }

            MessageUser.Builder builder = MessageUser.newBuilder();
            builder.setAppId(appId);
            builder.setUsername(username);
            builder.setExtraId(UUID.randomUUID().toString().replace("-", StringUtils.EMPTY));
            messageGrpcClient.sendMessage(builder.build(),
                    MessageTemplateId.newBuilder().setMessageTemplateId(urgeSuccessTemplateId).build());
        } catch (Exception e) {
            MessageUser.Builder builder = MessageUser.newBuilder();
            builder.setAppId(appId);
            builder.setUsername(username);
            String failExtraId = isRule ? Joiner.on("-")
                    .join("urgeManagerByRuleFail", msgJobId, managerUrgeExtraId) : Joiner.on(
                    "-").join("urgeManagerByTaskFail", managerUrgeExtraId);
            builder.setExtraId(failExtraId);
            messageGrpcClient.sendMessage(builder.build(),
                    MessageTemplateId.newBuilder().setMessageTemplateId(urgeFailTemplateId).build());
            String logMsg = String.format("appId = [%s],msgJobId = [%s],taskId = [%s],username = [%s],催办失败:", appId,
                    msgJobId, taskId, username);
            log.error(logMsg, e);
        }
    }

    private void urgeManagerByTask(String appId, Long parentTaskId, String extraId,
                                   List<SendUsersDTO> subList, EmployeeInfo employeeInfo) {
        TaskInfo parentTaskInfo = taskService.getById(parentTaskId);
        if (Objects.isNull(parentTaskInfo)) {
            String logMsg =
                    String.format("上级催办任务执行失败:ID无对应的催办任务 appId = [%s],taskId = [%s]", appId,
                            parentTaskId);
            log.error(logMsg);
            throw new RuntimeException(logMsg);
        }
        parentTaskInfo.setTitleCn("⚡" + employeeInfo.getName() + "提醒您⚡" + parentTaskInfo.getTitleCn());
        parentTaskInfo.setTitleEn("⚡" + employeeInfo.getName() + "remind you⚡" + parentTaskInfo.getTitleEn());
        long urgeTemplateId = sendMessage.createMessageEngineTemplate(parentTaskInfo, true);
        List<MessageUser> messageUserList =
                subList.stream()
                        .filter(p -> StringUtils.isNotBlank(p.getOprid()))
                        .map(p -> {
                            Map<String, Object> params =
                                    Objects.nonNull(p.getParam()) ? p.getParam() : Maps.newHashMap();
                            MessageUser.Builder builder = MessageUser.newBuilder();
                            builder.setAppId(appId);
                            builder.setUsername(p.getOprid());
                            builder.setPlaceholderContent(JsonUtils.toJson(params));
                            builder.setExtraId(extraId);
                            builder.setContentFlag(ContentFlagEnum.LARK_CONTENT.getFlag());
                            return builder.build();
                        })
                        .collect(Collectors.toList());
        // 发送
        MessageUserResponse messageUserResponse = messageGrpcClient.sendMessageBatch(messageUserList,
                MessageTemplateId.newBuilder().setMessageTemplateId(urgeTemplateId).build());
        if (messageUserResponse.getCode() == ResultCode.OK.getCode()) {
            String logMsg = String.format("上级催办任务执行成功 appId = [%s],taskId = [%s]", appId, parentTaskId);
            log.info(logMsg);
        } else {
            String logMsg = String.format("上级催办任务调用ums引擎发送消息失败 code = [%d], message = [%s]",
                    messageUserResponse.getCode(), messageUserResponse.getMessage());
            log.error(logMsg);
            throw new RuntimeException(logMsg);
        }
    }

    private void urgeManagerByRule(Long msgJobId, String extraId, List<SendUsersDTO> subList) {
        MessageJob job = messageRuleJobService.getById(msgJobId);
        if (Objects.isNull(job)) {
            return;
        }
        Long ruleId = job.getRuleId();

        GetMessageRuleDetailResponse response = appListGrpcClient.getMessageRuleDetail(null, ruleId);
        MessageRuleDetail detail = response.getMessageRuleDetail();

        List<MessageButtonActions> buttonActionList = messageButtonActionsMapper.selectList(
                Wrappers.<MessageButtonActions>lambdaQuery()
                        .eq(MessageButtonActions::getMsgJobId, msgJobId));

        if (CollectionUtils.isEmpty(buttonActionList)) {
            return;
        }

        buttonActionList.forEach(buttonAction -> {
            String title = buttonAction.getMessageTitle();
            String titleEn = buttonAction.getMessageTitleEn();
            String content = buttonAction.getMessageContent();
            String contentEn = buttonAction.getMessageContentEn();
            String btnText = buttonAction.getBtnText();
            String btnTextEn = buttonAction.getBtnTextEn();
            String btnUrl = buttonAction.getBtnUrl();

            subList.forEach(
                    processParam(extraId,
                            title,
                            titleEn,
                            content,
                            contentEn,
                            btnText,
                            btnTextEn,
                            btnUrl)
            );

            send(msgJobId, extraId, subList, job, detail);

            if (StringUtils.isNotBlank(btnUrl)) {
                redisTemplate.opsForSet().add(SEND_KEY_PREFIX + extraId,
                        subList.stream()
                                .map(SendUsersDTO::getOprid)
                                .collect(Collectors.toList())
                                .toArray(new String[subList.size()]));
            }

        });
    }

    private void send(Long msgJobId,
                      String extraId,
                      List<SendUsersDTO> subList,
                      MessageJob job,
                      MessageRuleDetail detail) {
        SendMessageBatchRequest.Builder builder = SendMessageBatchRequest.newBuilder();
        builder.setAppId(detail.getAppId());
        builder.setBotBizId(job.getLarkBotBizId());
        builder.setChannel(MessageChannelsEnum.LARK.getChannel());
        builder.setTemplateBizId(urgeSubTemplateId);
        builder.setExtraId(extraId);
        builder.addAllUserList(larkUserFunction.apply(subList, detail.getPlaceholder()));

        // 发送
        SendMessageBatchResponse resp = openServerBlockingClient.sendMessageBatch(builder.build());
        if (resp.getCode() == ResultCode.OK.getCode()) {
            String logMsg = String.format("messageJobId = [%s],催办规则执行成功", msgJobId);
            log.info(logMsg);
        } else {
            String logMsg = String.format("催办规则调用ums引擎发送消息失败 code = [%d], message = [%s]",
                    resp.getCode(), resp.getDesc());
            // 调用引擎失败异常，抛异常中断
            log.error(logMsg);
            throw new RuntimeException(logMsg);
        }
    }

    private Consumer<SendUsersDTO> processParam(String extraId,
                                                String title,
                                                String titleEn,
                                                String content,
                                                String contentEn,
                                                String btnText,
                                                String btnTextEn,
                                                String btnUrl) {
        return sender -> {
            Map<String, Object> param =
                    Objects.nonNull(sender.getParam()) ? sender.getParam() : Maps.newHashMap();
            try {
                param.put("title",
                        FreeMarkerUtils.render(title, param).orElse(StringUtils.EMPTY));
                param.put("titleEn",
                        FreeMarkerUtils.render(titleEn, param).orElse(StringUtils.EMPTY));
                param.put("content",
                        FreeMarkerUtils.render(content, param).orElse(StringUtils.EMPTY));
                param.put("contentEn",
                        FreeMarkerUtils.render(contentEn, param).orElse(StringUtils.EMPTY));
                param.put("btnText",
                        FreeMarkerUtils.render(btnText, param).orElse(StringUtils.EMPTY));
                param.put("btnTextEn",
                        FreeMarkerUtils.render(btnTextEn, param).orElse(StringUtils.EMPTY));
                if (StringUtils.isNotBlank(btnUrl)) {
                    String sigUrl = MessageUtils.getSigUrl(frontHost, extraId, sender.getOprid(), btnUrl);
                    param.put("btnUrl", sigUrl);
                }
            } catch (Exception e) {
                log.error("催办规则模板渲染失败", e);
                throw new RuntimeException("催办规则模板渲染失败", e);
            }
            sender.setParam(param);
        };
    }

    private boolean validateArgument(String appId,
                                     Long msgJobId,
                                     Long taskId,
                                     String username,
                                     EmployeeInfo employeeInfo) {
        if (Objects.isNull(employeeInfo)) {
            MessageUser.Builder builder = MessageUser.newBuilder();
            builder.setAppId(appId);
            builder.setUsername(username);
            builder.setExtraId(UUID.randomUUID().toString().replace("-", ""));
            messageGrpcClient.sendMessage(builder.build(),
                    MessageTemplateId.newBuilder().setMessageTemplateId(urgeFailTemplateId).build());
            String logMsg =
                    String.format("appId = [%s],msgJobId = [%s],taskId = [%s],username = [%s],催办失败:催办人不存在",
                            appId, msgJobId, taskId, username);
            log.error(logMsg);
            return false;
        }
        if (Objects.isNull(msgJobId) && Objects.isNull(taskId)) {
            MessageUser.Builder builder = MessageUser.newBuilder();
            builder.setAppId(appId);
            builder.setUsername(username);
            builder.setExtraId(UUID.randomUUID().toString().replace("-", ""));
            messageGrpcClient.sendMessage(builder.build(),
                    MessageTemplateId.newBuilder().setMessageTemplateId(urgeFailTemplateId).build());
            String logMsg = String.format(
                    "appId = [%s],msgJobId = [%s],taskId = [%s],username = [%s],催办失败:催办规则ID和催办任务ID都没传",
                    appId, msgJobId, taskId, username);
            log.error(logMsg);
            return false;
        }
        if (Objects.nonNull(msgJobId) && Objects.nonNull(taskId)) {
            MessageUser.Builder builder = MessageUser.newBuilder();
            builder.setAppId(appId);
            builder.setUsername(username);
            builder.setExtraId(UUID.randomUUID().toString().replace("-", ""));
            messageGrpcClient.sendMessage(builder.build(),
                    MessageTemplateId.newBuilder().setMessageTemplateId(urgeFailTemplateId).build());
            String logMsg = String.format(
                    "appId = [%s],msgJobId = [%s],taskId = [%s],username = [%s],催办失败:催办规则ID和催办任务ID都传了",
                    appId, msgJobId, taskId, username);
            log.error(logMsg);
            return false;
        }
        return true;
    }

}
