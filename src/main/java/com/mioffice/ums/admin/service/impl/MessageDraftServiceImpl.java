package com.mioffice.ums.admin.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.MessageDraftDTO;
import com.mioffice.ums.admin.entity.dto.MessageDraftQueryDTO;
import com.mioffice.ums.admin.entity.info.PublishScopeInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.enums.TaskStatusEnum;
import com.mioffice.ums.admin.manager.DeptManager;
import com.mioffice.ums.admin.mapper.DepartmentInfoMapper;
import com.mioffice.ums.admin.mapper.PublishScopeInfoMapper;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import com.mioffice.ums.admin.service.MessageDraftService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 草稿箱
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/14 10:12
 */
@Service
public class MessageDraftServiceImpl implements MessageDraftService {

    private final TaskInfoMapper taskInfoMapper;

    private final PublishScopeInfoMapper scopeInfoMapper;

    private final DeptManager deptManager;

    public MessageDraftServiceImpl(TaskInfoMapper taskInfoMapper, PublishScopeInfoMapper scopeInfoMapper, DepartmentInfoMapper departmentInfoMapper, DeptManager deptManager) {
        this.taskInfoMapper = taskInfoMapper;
        this.scopeInfoMapper = scopeInfoMapper;
        this.deptManager = deptManager;
    }

    @Override
    public IPage<MessageDraftDTO> draftPage(MessageDraftQueryDTO messageDraftQueryDTO, UserBO userBO) {
        // page
        Page<TaskInfo> taskInfoPage = new Page<>(messageDraftQueryDTO.getPage(), messageDraftQueryDTO.getSize());

        LambdaQueryWrapper<TaskInfo> lambdaQuery = Wrappers.lambdaQuery();

        // 发布渠道
        if (StringUtils.isNotBlank(messageDraftQueryDTO.getChannel())) {
            String[] channelArray = messageDraftQueryDTO.getChannel().split(",");
            List<Byte> channelList = new ArrayList<>();
            for (String channel : channelArray) {
                channelList.add(Byte.valueOf(channel));
            }
            lambdaQuery.in(TaskInfo::getChannel, channelList);
        }

        // 推送范围
        if (StringUtils.isNotBlank(messageDraftQueryDTO.getPublishScope())) {
            String[] publishScopeArray = messageDraftQueryDTO.getPublishScope().split(",");
            List<Byte> publishScopeList = new ArrayList<>();
            for (String publishScope : publishScopeArray) {
                publishScopeList.add(Byte.valueOf(publishScope));
            }
            lambdaQuery.in(TaskInfo::getPublishScope, publishScopeList);
        }

        // 消息主题/或者消息ID
        if (StringUtils.isNotBlank(messageDraftQueryDTO.getKey())) {
            lambdaQuery.and(
                    wrapper ->
                            wrapper.like(TaskInfo::getBizId, messageDraftQueryDTO.getKey())
                                    .or()
                                    .like(TaskInfo::getTitleCn, messageDraftQueryDTO.getKey())
            );
        }

        // 创建时间开始
        if (StringUtils.isNotBlank(messageDraftQueryDTO.getBeginDate())) {
            DateTime dateTime = DateUtil.beginOfSecond(DateUtil.parseDateTime(messageDraftQueryDTO.getBeginDate()));
            lambdaQuery.ge(TaskInfo::getCreateTime, dateTime.getTime());
        }

        // 创建时间结束
        if (StringUtils.isNotBlank(messageDraftQueryDTO.getEndDate())) {
            DateTime dateTime = DateUtil.beginOfSecond(DateUtil.parseDateTime(messageDraftQueryDTO.getEndDate()));
            lambdaQuery.le(TaskInfo::getCreateTime, dateTime.getTime());
        }

        // 条件为草稿, 创建用户为当前登录用户, 按照创建时间倒序排列
        lambdaQuery.eq(TaskInfo::getTaskStatus, TaskStatusEnum.DRAFT.getCode());
        lambdaQuery.eq(TaskInfo::getCreateUsername, userBO.getUsername());
        lambdaQuery.eq(TaskInfo::getParentTaskId,0L);
        lambdaQuery.orderByDesc(TaskInfo::getCreateTime);

        IPage<TaskInfo> taskPage = taskInfoMapper.selectPage(taskInfoPage, lambdaQuery);
        // 组装成一个新Page
        IPage<MessageDraftDTO> draftPage = new Page<>();

        List<MessageDraftDTO> draftRecords = new ArrayList<>();

        List<TaskInfo> records = taskPage.getRecords();
        if (records.isEmpty()) {
            draftPage.setRecords(draftRecords);
            draftPage.setCurrent(messageDraftQueryDTO.getPage());
            draftPage.setPages(0L);
            draftPage.setSize(messageDraftQueryDTO.getSize());
            draftPage.setTotal(0L);
            return draftPage;
        }

        taskPage.getRecords().forEach(
                taskInfo -> {
                    MessageDraftDTO messageDraftDTO = new MessageDraftDTO();
                    BeanUtils.copyProperties(taskInfo, messageDraftDTO);
                    messageDraftDTO.setTaskId(taskInfo.getId());

                    if (messageDraftDTO.getPublishScope() != null) {
                        Byte publishScope = messageDraftDTO.getPublishScope();
                        if (publishScope.intValue() == (PublishScopeEnum.DEPT_PUSH.getCode())) {
                            // 取出该任务推送范围为部门的所有范围信息
                            List<PublishScopeInfo> deptScopeList = scopeInfoMapper.selectList(
                                    Wrappers.<PublishScopeInfo>lambdaQuery()
                                            .eq(PublishScopeInfo::getScopeType, PublishScopeEnum.DEPT_PUSH.getCode())
                                            .eq(PublishScopeInfo::getTaskId, taskInfo.getId())
                            );
                            // 取出所有的DeptId
                            List<String> deptIds = deptScopeList.stream()
                                    .map(PublishScopeInfo::getScopeKey)
                                    .collect(Collectors.toList());
                            // 查出所有的部门完整信息, 如果有二级部门只展示一级部门信息
                            if (!deptIds.isEmpty()) {
                                messageDraftDTO.setPublishScopeDesc(deptManager.getFirstLevelDeptString(deptIds));
                            }
                        } else if (publishScope.intValue() == (PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode())) {
                            messageDraftDTO.setPublishScopeDesc(PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getMsg());
                        } else if (publishScope.intValue() == (PublishScopeEnum.ALL_PUSH.getCode())) {
                            messageDraftDTO.setPublishScopeDesc(PublishScopeEnum.ALL_PUSH.getMsg());
                        } else if (publishScope.intValue() == (PublishScopeEnum.CHOOSE_PUSH.getCode())) {
                            messageDraftDTO.setPublishScopeDesc(PublishScopeEnum.CHOOSE_PUSH.getMsg());
                        } else if (publishScope.intValue() == (PublishScopeEnum.GROUP_PUSH.getCode())) {
                            messageDraftDTO.setPublishScopeDesc(PublishScopeEnum.GROUP_PUSH.getMsg());
                        }
                    }

                    draftRecords.add(messageDraftDTO);
                }
        );
        draftPage.setRecords(draftRecords);
        draftPage.setCurrent(taskPage.getCurrent());
        draftPage.setPages(taskPage.getPages());
        draftPage.setSize(taskPage.getSize());
        draftPage.setTotal(taskPage.getTotal());
        return draftPage;
    }

    @Override
    public int deleteDraftTask(MessageDraftQueryDTO messageDraftQueryDTO, UserBO userBO) {

        TaskInfo draftTask = taskInfoMapper.selectOne(
                Wrappers.<TaskInfo>lambdaQuery()
                        .eq(TaskInfo::getId, messageDraftQueryDTO.getTaskId()));

        if (draftTask != null && draftTask.getCreateUsername().equals(userBO.getUsername())) {
            return taskInfoMapper.delete(
                    Wrappers.<TaskInfo>lambdaUpdate()
                            .eq(TaskInfo::getId, messageDraftQueryDTO.getTaskId()));
        }
        return -1;
    }
}
