package com.mioffice.ums.admin.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.nacos.shaded.com.google.common.base.Strings;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mioffice.ums.admin.entity.dto.HrApplyPageDTO;
import com.mioffice.ums.admin.entity.dto.HrApplyUserDTO;
import com.mioffice.ums.admin.entity.dto.IdmUserDTO;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.SyncRecordLatestTime;
import com.mioffice.ums.admin.enums.SyncRecordBelongToEnum;
import com.mioffice.ums.admin.enums.SyncRecordTypeEnum;
import com.mioffice.ums.admin.manager.HrApplyManager;
import com.mioffice.ums.admin.manager.IdmApplyManager;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.SyncRecordLatestTimeMapper;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.HrEmployeeService;
import com.mioffice.ums.admin.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

/**
 * <p>
 * HR同步接口
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/10 18:41
 */
@Slf4j
@Service
public class HrEmployeeServiceImpl extends ServiceImpl<EmployeeInfoMapper, EmployeeInfo> implements HrEmployeeService {

    private final EmployeeInfoMapper employeeMapper;

    private final HrApplyManager hrApplyManager;

    private final IdmApplyManager idmApplyManager;

    private final SyncRecordLatestTimeMapper syncRecordLatestTimeMapper;

    public HrEmployeeServiceImpl(EmployeeInfoMapper employeeMapper, HrApplyManager hrApplyManager,
                                 SyncRecordLatestTimeMapper syncRecordLatestTimeMapper,
                                 IdmApplyManager idmApplyManager) {
        this.employeeMapper = employeeMapper;
        this.hrApplyManager = hrApplyManager;
        this.idmApplyManager = idmApplyManager;
        this.syncRecordLatestTimeMapper = syncRecordLatestTimeMapper;
    }

    /**
     * 全量同步部门
     */
    @Override
    public BaseResult<String> syncEmployeeWithFullLoad() {
        HrApplyPageDTO hrApplyPageDTO = HrApplyPageDTO.getFullLoadInstant();
        return startSync(hrApplyPageDTO);
    }

    /**
     * 增量同步部门
     */
    @Override
    public BaseResult<String> syncEmployeeWithIncrement(String startTime, String endTime) {
        LocalDateTime now = LocalDateTime.now();
        long currentTimeMillis = now.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        //判断库里有无数据, 没有数据来一下全量
        long databaseCount = employeeMapper.selectCount(Wrappers.lambdaQuery());
        if (databaseCount == 0) {
            return this.syncEmployeeWithFullLoad();
        } else {
            HrApplyPageDTO hrApplyPageDTO = new HrApplyPageDTO();
            if (!StringUtils.isEmpty(startTime) && !StringUtils.isEmpty(endTime)) {
                hrApplyPageDTO.setStartTime(startTime);
                hrApplyPageDTO.setEndTime(endTime);
            } else {

                // 从库里面查出最后一次同步时间
                LambdaQueryWrapper<SyncRecordLatestTime> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(SyncRecordLatestTime::getBelongTo, SyncRecordBelongToEnum.HR_APPLY.getName());
                wrapper.eq(SyncRecordLatestTime::getSyncType, SyncRecordTypeEnum.SYNC_EMPLOYEE.getName());
                SyncRecordLatestTime asyncRecordLatestTime = syncRecordLatestTimeMapper.selectOne(wrapper);
                // 如果库没有记录, 就同步昨天0:00:00 至 昨天23:59:59
                if (Objects.isNull(asyncRecordLatestTime)) {
                    // 昨天开始
                    String yesterdayBegin = DateTimeUtil.getYesterdayBegin();
                    // 昨天结束
                    String theEnd = LocalDateTimeUtil.format(now, DatePattern.NORM_DATETIME_PATTERN);

                    hrApplyPageDTO.setStartTime(yesterdayBegin);
                    hrApplyPageDTO.setEndTime(theEnd);
                    // 并将这次同步时间记录到库
                    SyncRecordLatestTime recordLatestTime = new SyncRecordLatestTime();
                    recordLatestTime.setBelongTo(SyncRecordBelongToEnum.HR_APPLY.getName());
                    recordLatestTime.setSyncType(SyncRecordTypeEnum.SYNC_EMPLOYEE.getName());
                    recordLatestTime.setLatestTime(currentTimeMillis);
                    recordLatestTime.setCreateTime(System.currentTimeMillis());
                    recordLatestTime.setUpdateTime(System.currentTimeMillis());
                    syncRecordLatestTimeMapper.insert(recordLatestTime);
                } else {
                    // 如果有记录
                    String startTime2 = DateTimeUtil.getStringTime(asyncRecordLatestTime.getLatestTime(),
                            DateTimeUtil.DATE_TIME_PATTERN);

                    String endTime2 = LocalDateTimeUtil.format(now, DatePattern.NORM_DATETIME_PATTERN);

                    hrApplyPageDTO.setStartTime(startTime2);
                    hrApplyPageDTO.setEndTime(endTime2);

                    // 更新一下这次同步的时间
                    LambdaQueryWrapper<SyncRecordLatestTime> updateWrapper = new LambdaQueryWrapper<>();
                    updateWrapper.eq(SyncRecordLatestTime::getId, asyncRecordLatestTime.getId());
                    asyncRecordLatestTime.setLatestTime(currentTimeMillis);
                    asyncRecordLatestTime.setUpdateTime(System.currentTimeMillis());
                    syncRecordLatestTimeMapper.update(asyncRecordLatestTime, updateWrapper);
                }
            }
            return startSync(hrApplyPageDTO);
        }
    }

    private BaseResult<String> startSync(HrApplyPageDTO hrApplyPageDTO) {
        log.info("===============拉取HR人员开始===============");
        int updateCount = 0;
        int insertCount = 0;
        Integer curPage = hrApplyPageDTO.getPageNum();
        while (true) {
            try {
                log.info("当前查询第[{}]页", curPage);
                List<HrApplyUserDTO> curPageResultList = hrApplyManager.getEmployeeList(hrApplyPageDTO);
                if (curPageResultList.isEmpty()) {
                    break;
                }
                List<EmployeeInfo> curPageEmployeeList = this.convertFromEmployeeList(curPageResultList);
                List<EmployeeInfo> updateList = filterList(curPageEmployeeList, this::getExistList);
                // 拆出来需要update的记录，剩下就是需要insert的记录
                curPageEmployeeList.removeAll(updateList);
                if (!updateList.isEmpty()) {
                    this.updateBatchById(updateList);
                }
                if (!curPageEmployeeList.isEmpty()) {
                    this.saveBatch(curPageEmployeeList);
                }
                insertCount += curPageEmployeeList.size();
                updateCount += updateList.size();
            } catch (RuntimeException e) {
                log.error("===============拉取HR人员出错===============,当前页为[" + curPage + "]", e);
            } finally {
                curPage = curPage + 1;
                hrApplyPageDTO.setPageNum(curPage);
            }
        }
        log.info("===============拉取HR人员结束===============,当前插入数据:[" + insertCount + "]条，当前更新数据:[" +
                updateCount + "]条");
        // 异步同步IDM合作伙伴
        ThreadUtil.execute(this::syncIdmPartnerUser);
        return BaseResult.of("当前插入数据:[" + insertCount + "]条，当前更新数据:[" + updateCount + "]条");
    }

    private <T> List<T> filterList(List<T> employeeList, Function<List<T>, List<T>> function) {
        return function.apply(employeeList);
    }

    /**
     * HrApplyUserDTO转换EmployeeInfo
     *
     * @param curPageQueryList
     * @return
     */
    private List<EmployeeInfo> convertFromEmployeeList(List<HrApplyUserDTO> curPageQueryList) {
        List<EmployeeInfo> list = new ArrayList<>();
        curPageQueryList.forEach(
                item -> {
                    EmployeeInfo employee = new EmployeeInfo();
                    BeanUtils.copyProperties(item, employee);
                    employee.setCreateTime(System.currentTimeMillis());
                    employee.setUpdateTime(System.currentTimeMillis());
                    list.add(employee);
                }
        );
        return list;
    }

    /**
     * IdmUserDTO转换EmployeeInfo
     *
     * @param curPageQueryList
     * @return
     */
    private List<EmployeeInfo> convertFromPartnerList(List<IdmUserDTO> curPageQueryList) {
        List<EmployeeInfo> list = new ArrayList<>();
        curPageQueryList.forEach(
                item -> {
                    EmployeeInfo employee = new EmployeeInfo();
                    BeanUtils.copyProperties(item, employee);
                    employee.setCreateTime(System.currentTimeMillis());
                    employee.setUpdateTime(System.currentTimeMillis());
                    employee.setHrStatus(Strings.nullToEmpty(employee.getHrStatus()));
                    employee.setMiDeptLevel2(Strings.nullToEmpty(employee.getMiDeptLevel2()));
                    employee.setMiDeptLevel2Desc(Strings.nullToEmpty(employee.getMiDeptLevel2Desc()));
                    employee.setMiDeptLevel3(Strings.nullToEmpty(employee.getMiDeptLevel3()));
                    employee.setMiDeptLevel3Desc(Strings.nullToEmpty(employee.getMiDeptLevel3Desc()));
                    employee.setMiDeptLevel4(Strings.nullToEmpty(employee.getMiDeptLevel4()));
                    employee.setMiDeptLevel4Desc(Strings.nullToEmpty(employee.getMiDeptLevel4Desc()));

                    list.add(employee);
                }
        );
        return list;
    }

    private List<EmployeeInfo> getExistList(List<EmployeeInfo> employeeList) {
        List<EmployeeInfo> existList = new ArrayList<>();
        employeeList.forEach(
                item -> {
                    LambdaQueryWrapper<EmployeeInfo> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(EmployeeInfo::getEmpId, item.getEmpId());
                    EmployeeInfo exist = employeeMapper.selectOne(wrapper);
                    if (exist != null) {
                        item.setId(exist.getId());
                        existList.add(item);
                    }
                }
        );
        return existList;
    }

    /**
     * 同步Idm合作伙伴数据
     */
    @Override
    public void syncIdmPartnerUser() {
        log.info("===============拉取合作伙伴开始===============");
        int updateCount = 0;
        int insertCount = 0;
        int curPage = 1;
        while (true) {
            try {
                log.info("当前查询第[{}]页", curPage);
                List<IdmUserDTO> curPageResultList = idmApplyManager.getIdmPartnerUserList(curPage);
                if (curPageResultList.isEmpty()) {
                    break;
                }
                List<EmployeeInfo> curPagePartnerList = this.convertFromPartnerList(curPageResultList);
                List<EmployeeInfo> updateList = filterList(curPagePartnerList, this::getExistList);
                // 拆出来需要update的记录，剩下就是需要insert的记录
                curPagePartnerList.removeAll(updateList);
                if (!updateList.isEmpty()) {
                    this.updateBatchById(updateList);
                }
                if (!curPagePartnerList.isEmpty()) {
                    this.saveBatch(curPagePartnerList);
                }
                insertCount += curPagePartnerList.size();
                updateCount += updateList.size();
            } catch (RuntimeException e) {
                log.error("拉取合作伙伴出错,当前页为[" + curPage + "]", e);
            } finally {
                curPage = curPage + 1;
            }
        }
        log.info("===============拉取合作伙伴结束===============,当前插入数据:[" + insertCount + "]条，当前更新数据:[" +
                updateCount + "]条");
    }

}
