package com.mioffice.ums.admin.service;

import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.ImportPageDTO;
import com.mioffice.ums.admin.entity.dto.NameListAddDTO;
import com.mioffice.ums.admin.entity.dto.NameListDeleteDTO;
import com.mioffice.ums.admin.entity.info.ParseTmpInfo;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.result.BaseResult;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/11 1:09 上午
 * version: 1.0.0
 */
public interface ImportNameListService {

    BaseResult<Object> upload(UserBO userBO, MultipartFile file, String taskId);

    BaseResult<Object> rate(String excelId);

    BaseResult<ImportPageDTO<ParseTmpInfo>> page(Long page, Long size, String excelId, String key, PublishScopeEnum publishScopeEnum);

    BaseResult<Object> delete(NameListDeleteDTO nameListDeleteDTO);

    BaseResult<Object> add(NameListAddDTO nameListAddDTO);

    BaseResult<Map<String, Object>> download(String excelId) throws IOException;

}
