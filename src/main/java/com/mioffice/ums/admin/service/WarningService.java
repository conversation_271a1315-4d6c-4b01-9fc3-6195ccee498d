package com.mioffice.ums.admin.service;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.entity.bo.AlarmBo;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.vo.AlarmTrendVO;
import com.mioffice.ums.admin.entity.vo.TaskMonitorListVO;
import com.mioffice.ums.admin.result.BaseResult;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.09
 */
public interface WarningService {

    /**
     * 预警趋势
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    BaseResult<AlarmTrendVO> getAlarmTrend(UserBO userBO, DateTime beginTime, DateTime endTime);

    BaseResult<IPage<AlarmBo>> getAlarmPage(UserBO userBO, Long page, Long size);

    BaseResult<TaskMonitorListVO> getNowMonitorList(UserBO userBO);

}
