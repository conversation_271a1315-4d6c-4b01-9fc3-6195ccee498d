package com.mioffice.ums.admin.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.admin.entity.bo.ChannelCostTimeFromSqlBO;
import com.mioffice.ums.admin.entity.bo.CostTimeFromSqlBO;
import com.mioffice.ums.admin.entity.bo.DateAndExtraIdListBO;
import com.mioffice.ums.admin.entity.bo.ScopeAndExtraListBO;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.bo.UserInfoBaseBO;
import com.mioffice.ums.admin.entity.info.DepartmentInfo;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.vo.ChannelCostTimeY;
import com.mioffice.ums.admin.entity.vo.CostTimeYAxis;
import com.mioffice.ums.admin.entity.vo.MessageDashBoardVO;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.manager.UserInfoManager;
import com.mioffice.ums.admin.manager.UserRoleManager;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.MessageDashBoardService;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.ChannelAndCostTime;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.ChannelAndCostTimeByDate;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.CostTimeByDateAndExtraIdResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageAverageCostTimeResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageScopeAndAverageCostTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/7 7:40 下午
 * version: 1.0.0
 */
@Service
@Slf4j
public class MessageDashBoardServiceImpl implements MessageDashBoardService {

    private final EmployeeInfoMapper employeeInfoMapper;

    private final TaskInfoMapper taskInfoMapper;

    private final UserInfoManager userInfoManager;

    private final UserRoleManager userRoleManager;

    private final MessageGrpcClient messageGrpcClient;

    public MessageDashBoardServiceImpl(EmployeeInfoMapper employeeInfoMapper, TaskInfoMapper taskInfoMapper, UserInfoManager userInfoManager, UserRoleManager userRoleManager, MessageGrpcClient messageGrpcClient) {
        this.employeeInfoMapper = employeeInfoMapper;
        this.taskInfoMapper = taskInfoMapper;
        this.userInfoManager = userInfoManager;
        this.userRoleManager = userRoleManager;
        this.messageGrpcClient = messageGrpcClient;
    }

    @Override
    public BaseResult<MessageDashBoardVO> getDataAnalysisMessage(UserBO userBO, String deptIdList, String publishUsernameList, String start, String end) {

        boolean isSystemAdmin = userRoleManager.isSystemAdmin(userBO.getUsername());

        boolean isSuperAdmin = userRoleManager.isSuperAdmin(userBO.getUsername());

        if (!isSuperAdmin && !isSystemAdmin) {
            return new BaseResult<MessageDashBoardVO>().setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setMessage("当前登录用户既不是系统普通管理员，也不是超级管理员");
        }

        MessageDashBoardVO messageDashBoardVO = new MessageDashBoardVO();

        List<String> deptIds = new ArrayList<>();
        List<String> publishUsernames = new ArrayList<>();
        if (StringUtils.isNotBlank(publishUsernameList)) {
            publishUsernames = Arrays.asList(publishUsernameList.split(","));
        }
        Date startDate = DateUtil.parse(start, "yyyy-MM-dd");

        Date endDate = DateUtil.parse(end, "yyyy-MM-dd");

        long betweenDay = DateUtil.between(startDate, endDate, DateUnit.DAY);

        if (isSuperAdmin) {
            if (StringUtils.isNotBlank(deptIdList)) {
                deptIds = Arrays.asList(deptIdList.split(","));
            }
            getMessageDashBoardVO(messageDashBoardVO, deptIds, publishUsernames, startDate, endDate, betweenDay);
            return BaseResult.of(messageDashBoardVO);
        }

        EmployeeInfo employeeInfo = employeeInfoMapper.selectOne(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .eq(EmployeeInfo::getUsername, userBO.getUsername())
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        );

        String miDeptLevel2 = employeeInfo.getMiDeptLevel2();
        deptIds.add(miDeptLevel2);

        getMessageDashBoardVO(messageDashBoardVO, deptIds, publishUsernames, startDate, endDate, betweenDay);

        return BaseResult.of(messageDashBoardVO);
    }

    @Override
    public BaseResult<List<UserInfoBaseBO>> getPublisherSystemAdmin(UserBO userBO, String searchWord) {
        DepartmentInfo deptInfoByUsername = userInfoManager.getFirstDeptInfoByUsername(userBO.getUsername());
        List<EmployeeInfo> employeeInfoList = userInfoManager.getTaskReleaseByDeptId(deptInfoByUsername.getDeptId(), searchWord);
        List<UserInfoBaseBO> userInfoBaseBOList = new ArrayList<>();
        employeeInfoList.forEach(
                employeeInfo -> {
                    UserInfoBaseBO userInfoBaseBO = new UserInfoBaseBO();
                    userInfoBaseBO.setName(employeeInfo.getName());
                    userInfoBaseBO.setUsername(employeeInfo.getUsername());
                    userInfoBaseBOList.add(userInfoBaseBO);
                }
        );
        return BaseResult.of(userInfoBaseBOList);
    }

    @Override
    public BaseResult<List<UserInfoBaseBO>> getPublisherSuperAdmin(UserBO userBO, String deptIds, String searchWord) {
        List<EmployeeInfo> employeeInfoList = userInfoManager.getTaskReleaseByDeptId(deptIds, searchWord);
        List<UserInfoBaseBO> userInfoBaseBOList = new ArrayList<>();
        employeeInfoList.forEach(
                employeeInfo -> {
                    UserInfoBaseBO userInfoBaseBO = new UserInfoBaseBO();
                    userInfoBaseBO.setName(employeeInfo.getName());
                    userInfoBaseBO.setUsername(employeeInfo.getUsername());
                    userInfoBaseBOList.add(userInfoBaseBO);
                }
        );
        return BaseResult.of(userInfoBaseBOList);
    }

    private void getMessageDashBoardVO(MessageDashBoardVO messageDashBoardVO, List<String> deptIds, List<String> publishUsernames, Date startDate, Date endDate, long betweenDay) {

        CompletableFuture<MessageDashBoardVO> futureMessageDashBoardCountSummary = CompletableFuture.supplyAsync(
                () -> taskInfoMapper.selectMessageDashBoardCount(deptIds, publishUsernames, DateUtil.beginOfDay(startDate).getTime(), DateUtil.endOfDay(endDate).getTime())
        );
        CompletableFuture<MessageDashBoardVO> futureMessageDashBoardChannelCostTime = CompletableFuture.supplyAsync(
                () -> setSummaryChannelCostTimeMessageStatistics(deptIds, publishUsernames, startDate, endDate)
        );
        CompletableFuture<MessageDashBoardVO> futureMessageDashBoardMessageStatusStatistics = CompletableFuture.supplyAsync(
                () -> taskInfoMapper.selectMessageStatusStatistics(deptIds, publishUsernames, DateUtil.beginOfDay(startDate).getTime(), DateUtil.endOfDay(endDate).getTime())
        );
        CompletableFuture<MessageDashBoardVO> futureMessageDashBoardCostTime;

        LocalDate endDay = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        List<String> axisList = new ArrayList<>();
        if (betweenDay > 31) {
            List<CostTimeFromSqlBO> costTimeFromSqlBOByMonth = taskInfoMapper.selectMessageDashBoardGroupByMonth(deptIds, publishUsernames, DateUtil.beginOfDay(startDate).getTime(), DateUtil.endOfDay(endDate).getTime());
            axisList.add(endDay.toString().substring(0, 7));
            for (int i = 1; i < 12; i++) {
                LocalDate localDate = endDay.minusMonths(i);
                axisList.add(localDate.toString().substring(0, 7));
            }
            Collections.reverse(axisList);
            List<DateAndExtraIdListBO> dateAndExtraIdListBOS = taskInfoMapper.selectExtraIdListByMonth(deptIds, publishUsernames, DateUtil.beginOfDay(startDate).getTime(), DateUtil.endOfDay(endDate).getTime(), axisList);
            futureMessageDashBoardCostTime = CompletableFuture.supplyAsync(
                    () -> setAverageCostTime(axisList, costTimeFromSqlBOByMonth, dateAndExtraIdListBOS)
            );
        } else if (betweenDay > 7) {
            List<CostTimeFromSqlBO> costTimeFromSqlBOByDay = taskInfoMapper.selectMessageDashBoardGroupByDay(deptIds, publishUsernames, DateUtil.beginOfDay(startDate).getTime(), DateUtil.endOfDay(endDate).getTime());
            axisList.add(endDay.toString());
            for (int i = 1; i < 30; i++) {
                axisList.add(endDay.minusDays(i).toString());
            }
            Collections.reverse(axisList);
            List<DateAndExtraIdListBO> dateAndExtraIdListBOS = taskInfoMapper.selectExtraIdListByDay(deptIds, publishUsernames, DateUtil.beginOfDay(startDate).getTime(), DateUtil.endOfDay(endDate).getTime(), axisList);
            futureMessageDashBoardCostTime = CompletableFuture.supplyAsync(
                    () -> setAverageCostTime(axisList, costTimeFromSqlBOByDay, dateAndExtraIdListBOS)
            );
        } else {
            List<CostTimeFromSqlBO> costTimeFromSqlBOByDay = taskInfoMapper.selectMessageDashBoardGroupByDay(deptIds, publishUsernames, DateUtil.beginOfDay(startDate).getTime(), DateUtil.endOfDay(endDate).getTime());
            axisList.add(endDay.toString());
            for (int i = 1; i < 7; i++) {
                axisList.add(endDay.minusDays(i).toString());
            }
            Collections.reverse(axisList);
            List<DateAndExtraIdListBO> dateAndExtraIdListBOS = taskInfoMapper.selectExtraIdListByDay(deptIds, publishUsernames, DateUtil.beginOfDay(startDate).getTime(), DateUtil.endOfDay(endDate).getTime(), axisList);
            futureMessageDashBoardCostTime = CompletableFuture.supplyAsync(
                    () -> setAverageCostTime(axisList, costTimeFromSqlBOByDay, dateAndExtraIdListBOS)
            );
        }

        try {
            StopWatch sw = new StopWatch(UUID.randomUUID().toString());
            sw.start("获取CountSummary");
            MessageDashBoardVO messageDashBoardVOSummary = futureMessageDashBoardCountSummary.get();
            sw.stop();
            sw.start("获取MessageStatusStatistics");
            MessageDashBoardVO messageDashBoardMessageStatusStatistics = futureMessageDashBoardMessageStatusStatistics.get();
            sw.stop();
            sw.start("获取ChannelCostTime");
            MessageDashBoardVO messageDashBoardVOChannelCostTime = futureMessageDashBoardChannelCostTime.get();
            sw.stop();
            sw.start("获取CostTime");
            MessageDashBoardVO messageDashBoardCostTime = futureMessageDashBoardCostTime.get();
            sw.stop();
            log.info(sw.prettyPrint());
            messageDashBoardVO.setSummary(messageDashBoardVOSummary.getSummary());
            messageDashBoardVO.setMessageStatusStatistics(messageDashBoardMessageStatusStatistics.getMessageStatusStatistics());
            messageDashBoardVO.setChannelCostTime(messageDashBoardVOChannelCostTime.getChannelCostTime());
            messageDashBoardVO.setCostTime(messageDashBoardCostTime.getCostTime());
        } catch (Exception e) {
            log.error("获取消息看板异常", e);
        }
    }

    private MessageDashBoardVO setAverageCostTime(List<String> axisList, List<CostTimeFromSqlBO> costTimeFromSqlBOByMonth, List<DateAndExtraIdListBO> dateAndExtraIdListBOS) {
        List<CostTimeYAxis.YAxisData> lark = new ArrayList<>();
        List<CostTimeYAxis.YAxisData> email = new ArrayList<>();
        List<CostTimeYAxis.YAxisData> message = new ArrayList<>();
        MessageDashBoardVO.CostTime costTime = new MessageDashBoardVO.CostTime();
        CostTimeYAxis costTimeYAxis = new CostTimeYAxis();
        costTimeYAxis.setLark(lark);
        costTimeYAxis.setEmail(email);
        costTimeYAxis.setMessage(message);
        CostTimeByDateAndExtraIdResponse averageCostTimeByDateAndExtraIdBatchResponse = messageGrpcClient.getAverageCostTimeByDateAndExtraIdBatch(dateAndExtraIdListBOS);
        List<ChannelAndCostTimeByDate> channelAndCostTimeByDateList = averageCostTimeByDateAndExtraIdBatchResponse.getChannelAndCostTimeByDateList();
        axisList.forEach(
                axis -> {
                    CostTimeYAxis.YAxisData larkYAxisData = new CostTimeYAxis.YAxisData();
                    CostTimeYAxis.YAxisData emailYAxisData = new CostTimeYAxis.YAxisData();
                    CostTimeYAxis.YAxisData messageYAxisData = new CostTimeYAxis.YAxisData();
                    channelAndCostTimeByDateList.forEach(
                            channelAndCostTimeByDate -> {
                                if (axis.equals(channelAndCostTimeByDate.getDate())) {
                                    List<ChannelAndCostTime> channelAndCostTimeList = channelAndCostTimeByDate.getChannelAndCostTimeList();
                                    channelAndCostTimeList.forEach(
                                            channelAndCostTime -> {
                                                if ("1".equals(channelAndCostTime.getChannel())) {
                                                    larkYAxisData.setCostTime(channelAndCostTime.getAverageCostTime());
                                                } else if ("2".equals(channelAndCostTime.getChannel())) {
                                                    emailYAxisData.setCostTime(channelAndCostTime.getAverageCostTime());
                                                } else if ("3".equals(channelAndCostTime.getChannel())) {
                                                    messageYAxisData.setCostTime(channelAndCostTime.getAverageCostTime());
                                                }
                                            }
                                    );
                                }
                            }
                    );

                    costTimeFromSqlBOByMonth.forEach(
                            costTimeFromSqlBO -> {
                                if (axis.equals(costTimeFromSqlBO.getTime())) {
                                    larkYAxisData.setCount(costTimeFromSqlBO.getLark().getCount());

                                    emailYAxisData.setCount(costTimeFromSqlBO.getEmail().getCount());

                                    messageYAxisData.setCount(costTimeFromSqlBO.getMessage().getCount());

                                }
                            }
                    );
                    if (larkYAxisData.getCount() == null && larkYAxisData.getCostTime() == null) {
                        larkYAxisData.setCount(0L);
                        larkYAxisData.setCostTime(0L);
                    }
                    if (emailYAxisData.getCount() == null && emailYAxisData.getCostTime() == null) {
                        emailYAxisData.setCount(0L);
                        emailYAxisData.setCostTime(0L);
                    }
                    if (messageYAxisData.getCount() == null && messageYAxisData.getCostTime() == null) {
                        messageYAxisData.setCount(0L);
                        messageYAxisData.setCostTime(0L);
                    }
                    if (larkYAxisData.getCostTime() == null) {
                        larkYAxisData.setCostTime(0L);
                    }
                    if (emailYAxisData.getCostTime() == null) {
                        emailYAxisData.setCostTime(0L);
                    }
                    if (messageYAxisData.getCostTime() == null) {
                        messageYAxisData.setCostTime(0L);
                    }
                    lark.add(larkYAxisData);
                    email.add(emailYAxisData);
                    message.add(messageYAxisData);
                }
        );
        costTimeYAxis.setLark(lark);
        costTimeYAxis.setEmail(email);
        costTimeYAxis.setMessage(message);
        costTime.setXAxis(axisList);
        costTime.setYAxis(costTimeYAxis);
        MessageDashBoardVO messageDashBoardVO = new MessageDashBoardVO();
        messageDashBoardVO.setCostTime(costTime);
        return messageDashBoardVO;
    }

    private MessageDashBoardVO setCostTime(List<String> axisList, List<CostTimeFromSqlBO> costTimeFromSqlBOByMonth, List<DateAndExtraIdListBO> dateAndExtraIdListBOS) {
        List<CostTimeYAxis.YAxisData> lark = new ArrayList<>();
        List<CostTimeYAxis.YAxisData> email = new ArrayList<>();
        List<CostTimeYAxis.YAxisData> message = new ArrayList<>();
        MessageDashBoardVO.CostTime costTime = new MessageDashBoardVO.CostTime();
        CostTimeYAxis costTimeYAxis = new CostTimeYAxis();
        costTimeYAxis.setLark(lark);
        costTimeYAxis.setEmail(email);
        costTimeYAxis.setMessage(message);
        axisList.forEach(
                axis -> {
                    List<ScopeAndExtraListBO> scopeAndExtraListBOList = new ArrayList<>();
                    CostTimeYAxis.YAxisData larkYAxisData = new CostTimeYAxis.YAxisData();
                    CostTimeYAxis.YAxisData emailYAxisData = new CostTimeYAxis.YAxisData();
                    CostTimeYAxis.YAxisData messageYAxisData = new CostTimeYAxis.YAxisData();
                    dateAndExtraIdListBOS.forEach(
                            dateAndExtraIdListBO -> {
                                if (axis.equals(dateAndExtraIdListBO.getDate())) {
                                    if (dateAndExtraIdListBO.getChannel() == 1) {
                                        List<String> larkExtraIdList = dateAndExtraIdListBO.getExtraIdList();
                                        scopeAndExtraListBOList.add(new ScopeAndExtraListBO("lark", larkExtraIdList));
                                    } else if (dateAndExtraIdListBO.getChannel() == 2) {
                                        List<String> emailExtraIdList = dateAndExtraIdListBO.getExtraIdList();
                                        scopeAndExtraListBOList.add(new ScopeAndExtraListBO("email", emailExtraIdList));
                                    } else if (dateAndExtraIdListBO.getChannel() == 3) {
                                        List<String> messageExtraIdList = dateAndExtraIdListBO.getExtraIdList();
                                        scopeAndExtraListBOList.add(new ScopeAndExtraListBO("message", messageExtraIdList));
                                    }
                                    MessageAverageCostTimeResponse messageAverageCostTimeResponse = messageGrpcClient.getMessageAverageCostTimeByExtraIdBatch(scopeAndExtraListBOList);
                                    List<MessageScopeAndAverageCostTime> messageScopeAndAverageCostTimeListList = messageAverageCostTimeResponse.getMessageScopeAndAverageCostTimeListList();
                                    messageScopeAndAverageCostTimeListList.forEach(
                                            messageScopeAndAverageCostTime -> {
                                                if ("lark".equals(messageScopeAndAverageCostTime.getScope())) {
                                                    larkYAxisData.setCostTime(messageScopeAndAverageCostTime.getAverageCostTime());
                                                } else if ("email".equals(messageScopeAndAverageCostTime.getScope())) {
                                                    emailYAxisData.setCostTime(messageScopeAndAverageCostTime.getAverageCostTime());
                                                } else if ("message".equals(messageScopeAndAverageCostTime.getScope())) {
                                                    messageYAxisData.setCostTime(messageScopeAndAverageCostTime.getAverageCostTime());
                                                }
                                            }
                                    );
                                }
                            }
                    );
                    costTimeFromSqlBOByMonth.forEach(
                            costTimeFromSqlBO -> {
                                if (axis.equals(costTimeFromSqlBO.getTime())) {
                                    larkYAxisData.setCount(costTimeFromSqlBO.getLark().getCount());

                                    emailYAxisData.setCount(costTimeFromSqlBO.getEmail().getCount());

                                    messageYAxisData.setCount(costTimeFromSqlBO.getMessage().getCount());

                                }
                            }
                    );
                    if (larkYAxisData.getCount() == null && larkYAxisData.getCostTime() == null) {
                        larkYAxisData.setCount(0L);
                        larkYAxisData.setCostTime(0L);
                    }
                    if (emailYAxisData.getCount() == null && emailYAxisData.getCostTime() == null) {
                        emailYAxisData.setCount(0L);
                        emailYAxisData.setCostTime(0L);
                    }
                    if (messageYAxisData.getCount() == null && messageYAxisData.getCostTime() == null) {
                        messageYAxisData.setCount(0L);
                        messageYAxisData.setCostTime(0L);
                    }
                    lark.add(larkYAxisData);
                    email.add(emailYAxisData);
                    message.add(messageYAxisData);
                }
        );
        costTimeYAxis.setLark(lark);
        costTimeYAxis.setEmail(email);
        costTimeYAxis.setMessage(message);
        costTime.setXAxis(axisList);
        costTime.setYAxis(costTimeYAxis);
        MessageDashBoardVO messageDashBoardVO = new MessageDashBoardVO();
        messageDashBoardVO.setCostTime(costTime);
        return messageDashBoardVO;
    }

    private MessageDashBoardVO setSummaryChannelCostTimeMessageStatistics(List<String> deptIds, List<String> publishUsernames, Date startDate, Date endDate) {

        List<String> x = new ArrayList<>();
        x.add("小米办公");
        x.add("邮件");
        x.add("短信");

        List<Byte> xByteList = new ArrayList<>();
        xByteList.add((byte) 1);
        xByteList.add((byte) 2);
        xByteList.add((byte) 3);

        List<ChannelCostTimeY.Y> deptChannel = new ArrayList<>();
        List<ChannelCostTimeY.Y> allChannel = new ArrayList<>();
        List<ChannelCostTimeY.Y> customChannel = new ArrayList<>();
        List<ChannelCostTimeY.Y> chooseChannel = new ArrayList<>();
        List<ChannelCostTimeY.Y> larkGroupChannel = new ArrayList<>();
        List<ChannelCostTimeFromSqlBO> channelCostTimeFromSqlBOS = taskInfoMapper.selectMessageDashBoardChannelCostTime(deptIds, publishUsernames, DateUtil.beginOfDay(startDate).getTime(), DateUtil.endOfDay(endDate).getTime());
        xByteList.forEach(
                xByte -> {
                    ChannelCostTimeY.Y allY = new ChannelCostTimeY.Y();
                    ChannelCostTimeY.Y deptY = new ChannelCostTimeY.Y();
                    ChannelCostTimeY.Y customY = new ChannelCostTimeY.Y();
                    ChannelCostTimeY.Y chooseY = new ChannelCostTimeY.Y();
                    ChannelCostTimeY.Y larkGroupY = new ChannelCostTimeY.Y();
                    channelCostTimeFromSqlBOS.forEach(
                            channelCostTimeFromSqlBO -> {
                                if (xByte.equals(channelCostTimeFromSqlBO.getChannel())) {
                                    allY.setCount(channelCostTimeFromSqlBO.getAll().getCount());

                                    deptY.setCount(channelCostTimeFromSqlBO.getDept().getCount());

                                    customY.setCount(channelCostTimeFromSqlBO.getCustom().getCount());

                                    chooseY.setCount(channelCostTimeFromSqlBO.getChoose().getCount());

                                    larkGroupY.setCount(channelCostTimeFromSqlBO.getLarkGroup().getCount());
                                }
                            }
                    );
                    allChannel.add(allY);
                    deptChannel.add(deptY);
                    customChannel.add(customY);
                    chooseChannel.add(chooseY);
                    larkGroupChannel.add(larkGroupY);
                }
        );

        MessageDashBoardVO.ChannelCostTime channelCostTime = new MessageDashBoardVO.ChannelCostTime();
        ChannelCostTimeY channelCostTimeY = new ChannelCostTimeY();

        channelCostTimeY.setAll(allChannel);
        channelCostTimeY.setDept(deptChannel);
        channelCostTimeY.setCustom(customChannel);
        channelCostTimeY.setChoose(chooseChannel);
        channelCostTimeY.setLarkGroup(larkGroupChannel);
        channelCostTime.setX(x);
        channelCostTime.setY(channelCostTimeY);
        MessageDashBoardVO messageDashBoardVO = new MessageDashBoardVO();
        messageDashBoardVO.setChannelCostTime(channelCostTime);
        return messageDashBoardVO;
    }

}