package com.mioffice.ums.admin.service;

import com.mioffice.ums.admin.entity.dto.AddWhiteListDTO;
import com.mioffice.ums.admin.entity.dto.DelWhiteListDTO;
import com.mioffice.ums.admin.entity.dto.QueryAppWhiteListDTO;
import com.mioffice.ums.admin.entity.vo.AppWhiteListPageVo;
import com.mioffice.ums.admin.result.BaseResult;

/**
 * AppWhiteListService
 *
 * <AUTHOR>
 * @since 2021-12-23
 */
public interface AppWhiteListService {

    BaseResult<AppWhiteListPageVo> queryAppWhiteList(QueryAppWhiteListDTO appWhiteListDTO);

    BaseResult<Integer> addAppWhiteList(AddWhiteListDTO addWhiteListDTO);

    BaseResult<Integer> delAppWhiteList(DelWhiteListDTO delWhiteListDTO);
}
