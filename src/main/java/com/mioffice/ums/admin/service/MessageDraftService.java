package com.mioffice.ums.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.MessageDraftDTO;
import com.mioffice.ums.admin.entity.dto.MessageDraftQueryDTO;

/**
 * <p>
 * 草稿箱
 * </p>
 *
 * @author: shi tie tou
 * @date: 2020/8/14 1:55
 */
public interface MessageDraftService {

    /**
     * 查询草稿箱列表
     *
     * @param messageDraftQueryDTO dto
     * @return IPage<TaskInfo>
     */
    IPage<MessageDraftDTO> draftPage(MessageDraftQueryDTO messageDraftQueryDTO, UserBO userBO);

    /**
     * 删除Draft
     *
     * @param messageDraftQueryDTO dto
     * @return int
     */
    int deleteDraftTask(MessageDraftQueryDTO messageDraftQueryDTO, UserBO userBO);
}
