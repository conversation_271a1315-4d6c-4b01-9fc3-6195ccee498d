package com.mioffice.ums.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.entity.vo.BaseTaskVO;
import com.mioffice.ums.admin.result.BaseResult;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/19 6:25 下午
 * version: 1.0.0
 */
public interface TaskService extends IService<TaskInfo> {

    BaseResult<Object> taskDelete(List<Long> ids);

    /**
     * 获取任务及子任务的extraId
     * @param taskInfo
     * @return
     */
    List<String> getTaskExtraIdList(TaskInfo taskInfo);

    /**
     * 是否加推任务
     * @param taskInfo
     * @return
     */
    boolean isRush(TaskInfo taskInfo);

    List<Long> selectTaskIdSystemAdmin(String deptId);

    BaseTaskVO selectTaskDetailCommon(Long taskId);

}
