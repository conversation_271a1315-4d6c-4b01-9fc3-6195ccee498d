package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonParser;
import com.mi.oa.infra.oaucf.bpm.rep.ApprovalTaskResp;
import com.mi.oa.infra.oaucf.bpm.rep.UserTaskSignType;
import static com.mi.oa.infra.oaucf.bpm.rep.UserTaskSignType.PARALLEL_ONE;
import com.mi.oa.infra.oaucf.bpm.service.ApprovalService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.mioffice.ums.admin.constants.ProcessStatusConstant;
import com.mioffice.ums.admin.entity.bo.ProcessDefinition;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.ApprovalConfigDTO;
import com.mioffice.ums.admin.entity.dto.ApprovalNodeDTO;
import com.mioffice.ums.admin.entity.dto.DepartmentSummaryDTO;
import com.mioffice.ums.admin.entity.dto.SpecialApprovalNodeDTO;
import com.mioffice.ums.admin.entity.dto.TaskApprovalNodeQueryDTO;
import com.mioffice.ums.admin.entity.info.ApprovalInfo;
import com.mioffice.ums.admin.entity.info.BotInfo;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.LarkGroupPushInfo;
import com.mioffice.ums.admin.entity.info.OperationInfo;
import com.mioffice.ums.admin.entity.info.ParseTmpInfo;
import com.mioffice.ums.admin.entity.info.ProcessInstanceInfo;
import com.mioffice.ums.admin.entity.info.ProcessTaskInfo;
import com.mioffice.ums.admin.entity.info.PublishScopeInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.entity.info.UserBotInfo;
import com.mioffice.ums.admin.entity.vo.ApprovalTimeLine;
import com.mioffice.ums.admin.entity.vo.TaskApprovalNodeVO;
import com.mioffice.ums.admin.entity.vo.hrod.HrodUser;
import com.mioffice.ums.admin.enums.ApprovalNodeEnum;
import static com.mioffice.ums.admin.enums.ApprovalNodeEnum.APPROVAL_TYPE_CUSTOMIZE;
import static com.mioffice.ums.admin.enums.ApprovalNodeEnum.APPROVAL_TYPE_LEADER;
import static com.mioffice.ums.admin.enums.ApprovalNodeEnum.APPROVAL_TYPE_LEVEL_1_LEADER;
import static com.mioffice.ums.admin.enums.ApprovalNodeEnum.APPROVAL_TYPE_LEVEL_2_LEADER;
import static com.mioffice.ums.admin.enums.ApprovalNodeEnum.APPROVAL_TYPE_MANAGERS;
import static com.mioffice.ums.admin.enums.ApprovalNodeEnum.APPROVAL_TYPE_OPERATORS;
import com.mioffice.ums.admin.enums.BotTenantEnum;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.HrEmployeeTypeEnum;
import com.mioffice.ums.admin.enums.ParseDataStatusEnum;
import com.mioffice.ums.admin.enums.ProcessAuditEnum;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.enums.TaskChannelEnum;
import com.mioffice.ums.admin.enums.UserBotRoleEnum;
import com.mioffice.ums.admin.manager.DeptManager;
import com.mioffice.ums.admin.manager.PermissionManager;
import com.mioffice.ums.admin.manager.ProcessDefinitionResource;
import com.mioffice.ums.admin.manager.ReportLineManager;
import com.mioffice.ums.admin.manager.UserInfoManager;
import com.mioffice.ums.admin.mapper.ApprovalInfoMapper;
import com.mioffice.ums.admin.mapper.BotInfoMapper;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.LarkGroupPushInfoMapper;
import com.mioffice.ums.admin.mapper.OperationInfoMapper;
import com.mioffice.ums.admin.mapper.ParseTmpInfoMapper;
import com.mioffice.ums.admin.mapper.ProcessInstanceInfoMapper;
import com.mioffice.ums.admin.mapper.ProcessTaskInfoMapper;
import com.mioffice.ums.admin.mapper.PublishScopeInfoMapper;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import com.mioffice.ums.admin.mapper.UserBotInfoMapper;
import com.mioffice.ums.admin.message.member.ChooseIdEnum;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.ApprovalConfigService;
import com.mioffice.ums.admin.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 审核配置
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/1 4:25 下午
 */
@Slf4j
@Service
public class ApprovalConfigServiceImpl extends ServiceImpl<ApprovalInfoMapper, ApprovalInfo>
        implements ApprovalConfigService {

    public static final byte SPECIAL_CHANNEL = 99;

    private final UserBotInfoMapper userBotInfoMapper;

    private final BotInfoMapper botInfoMapper;

    private final UserInfoManager userInfoManager;

    private static final String CUSTOMIZE = "customize";

    private final ApprovalInfoMapper approvalInfoMapper;

    private final ProcessTaskInfoMapper processTaskInfoMapper;

    private final PublishScopeInfoMapper publishScopeInfoMapper;

    private final ProcessInstanceInfoMapper processInstanceInfoMapper;

    private final OperationInfoMapper operationInfoMapper;

    private final EmployeeInfoMapper employeeInfoMapper;

    private final DeptManager deptManager;

    private final ParseTmpInfoMapper parseTmpInfoMapper;

    private final ReportLineManager reportLineManager;

    private final PermissionManager permissionManager;

    private final LarkGroupPushInfoMapper larkGroupPushInfoMapper;

    private final TaskInfoMapper taskInfoMapper;

    private final ApprovalService approvalService;

    public ApprovalConfigServiceImpl(UserBotInfoMapper userBotInfoMapper, UserInfoManager userInfoManager,
                                     ApprovalInfoMapper approvalInfoMapper, ProcessTaskInfoMapper processTaskInfoMapper,
                                     PublishScopeInfoMapper publishScopeInfoMapper,
                                     ProcessInstanceInfoMapper processInstanceInfoMapper,
                                     OperationInfoMapper operationInfoMapper, ReportLineManager reportLineManager,
                                     EmployeeInfoMapper employeeInfoMapper, DeptManager deptManager,
                                     ParseTmpInfoMapper parseTmpInfoMapper,
                                     BotInfoMapper botInfoMapper,
                                     PermissionManager permissionManager,
                                     LarkGroupPushInfoMapper larkGroupPushInfoMapper, TaskInfoMapper taskInfoMapper,
                                     ApprovalService approvalService) {
        this.botInfoMapper = botInfoMapper;
        this.userBotInfoMapper = userBotInfoMapper;
        this.userInfoManager = userInfoManager;
        this.approvalInfoMapper = approvalInfoMapper;
        this.employeeInfoMapper = employeeInfoMapper;
        this.deptManager = deptManager;
        this.parseTmpInfoMapper = parseTmpInfoMapper;
        this.processTaskInfoMapper = processTaskInfoMapper;
        this.publishScopeInfoMapper = publishScopeInfoMapper;
        this.processInstanceInfoMapper = processInstanceInfoMapper;
        this.operationInfoMapper = operationInfoMapper;
        this.reportLineManager = reportLineManager;
        this.permissionManager = permissionManager;
        this.larkGroupPushInfoMapper = larkGroupPushInfoMapper;
        this.taskInfoMapper = taskInfoMapper;
        this.approvalService = approvalService;
    }

    @Override
    public BaseResult<ApprovalConfigDTO> getApprovalConfig() {

        // 1.查出邮件，sms，消息办公 三条常规审批记录，都只有唯一一条
        Map<Byte, ApprovalInfo> approvalInfoMap = approvalInfoMapper.selectList(
                        Wrappers.<ApprovalInfo>lambdaQuery()
                                .ne(ApprovalInfo::getChannel, SPECIAL_CHANNEL)
                ).stream()
                .collect(Collectors.toMap(ApprovalInfo::getChannel, Function.identity(), (x1, x2) -> x2));

        // 1.1 查出特殊审批的记录，多条
        List<ApprovalInfo> specialApprovalInfoList = approvalInfoMapper.selectList(
                Wrappers.<ApprovalInfo>lambdaQuery().eq(ApprovalInfo::getChannel, SPECIAL_CHANNEL));

        ApprovalConfigDTO approvalConfigDTO = new ApprovalConfigDTO();
        // 2.设置Threshold,因为公用一个threshold，所以取一个就行
        approvalConfigDTO.setThreshold(approvalInfoMap.get(TaskChannelEnum.CHANNEL_LARK.getCode()).getThreshold());
        approvalConfigDTO.setBaseThreshold(
                approvalInfoMap.get(TaskChannelEnum.CHANNEL_LARK.getCode()).getBaseThreshold());
        // 3.设置流程图，同上取一个就行
        approvalConfigDTO.setFlowChartUrl(
                approvalInfoMap.get(TaskChannelEnum.CHANNEL_LARK.getCode()).getFlowChartUrl());
        // 4.设置邮件 SMS EMAIL 以及Special审批的 审批节点
        approvalConfigDTO.setEmailApprovalMetaNode(
                getApprovalDTO(approvalInfoMap.get(TaskChannelEnum.CHANNEL_EMAIL.getCode())));
        approvalConfigDTO.setLarkApprovalMetaNode(
                getApprovalDTO(approvalInfoMap.get(TaskChannelEnum.CHANNEL_LARK.getCode())));
        approvalConfigDTO.setSmsApprovalMetaNode(
                getApprovalDTO(approvalInfoMap.get(TaskChannelEnum.CHANNEL_SMS.getCode())));
        approvalConfigDTO.setEmailApprovalNodeList(getApproveNodeList(TaskChannelEnum.CHANNEL_EMAIL.getCode()));
        approvalConfigDTO.setLarkApprovalNodeList(getApproveNodeList(TaskChannelEnum.CHANNEL_LARK.getCode()));
        approvalConfigDTO.setSmsApprovalNodeList(getApproveNodeList(TaskChannelEnum.CHANNEL_SMS.getCode()));
        approvalConfigDTO.setSpecialApproveMetaNode(getSpecialApprovalDTO(specialApprovalInfoList));
        approvalConfigDTO.setSpecialApproveNodeList(getApproveNodeList(SPECIAL_CHANNEL));
        return BaseResult.of(approvalConfigDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResult<Object> updateApprovalConfig(ApprovalConfigDTO approvalConfigDTO, String username) {
        this.goToUpdateApproval(approvalConfigDTO, username);
        return BaseResult.of();
    }

    @Override
    public BaseResult<Map<String, Object>> getApprovalInfo(Byte channel) {
        // 因为共用一套审批阈值，所以取其中一个值就行
        ApprovalInfo approvalInfo = approvalInfoMapper.selectOne(
                Wrappers.<ApprovalInfo>lambdaQuery()
                        .eq(ApprovalInfo::getChannel, TaskChannelEnum.CHANNEL_LARK.getCode()));
        Map<String, Object> result = new HashMap<>(16);
        result.put("threshold", approvalInfo.getThreshold());
        result.put("flowChartUrl", approvalInfo.getFlowChartUrl());
        return BaseResult.of(result);
    }

    @Override
    public BaseResult<List<ApprovalTimeLine>> getApprovalTimeLine(Long taskId, UserBO userBO) {
        // 防止越权
        boolean haveAccess = permissionManager.isHaveMessagePermission(userBO, taskId);
        if (!haveAccess) {
            return new BaseResult<>(ResultCode.FORBIDDEN);
        }
        // 0.获取审批实例
        ProcessInstanceInfo processInstanceInfo = processInstanceInfoMapper.selectOne(
                Wrappers.<ProcessInstanceInfo>lambdaQuery()
                        .eq(ProcessInstanceInfo::getBizId, taskId)
                        .orderByDesc(ProcessInstanceInfo::getCreateTime)
                        .last("limit 1")
        );
        if (Objects.isNull(processInstanceInfo)) {
            return BaseResult.of();
        }
        // 1. 审批流程
        String approvalNode = processInstanceInfo.getApprovalNode();
        // 2. 审批节点人物
        String variables = processInstanceInfo.getVariables();
        // 3. 将 审批流程 和 审批节点 转换成 具体对象
        Map<String, Object> variablesMap = JsonUtils.toMap(variables);
        ProcessDefinition processDefinition = JsonUtils.parse(approvalNode, ProcessDefinition.class);
        if (Objects.isNull(processDefinition)) {
            log.warn("获取task id = [{}]的审批流程异常", taskId);
            return BaseResult.of();
        }
        // 4. 获取当前审批节点
        List<ProcessTaskInfo> processTaskInfos = processTaskInfoMapper.selectList(
                Wrappers.<ProcessTaskInfo>lambdaQuery()
                        .eq(ProcessTaskInfo::getInstanceId, processInstanceInfo.getId())
        );
        // 5. 获取审核级别
        ProcessAuditEnum auditLevel = getAuditLevelByTaskId(taskId);
        TaskInfo taskInfo = taskInfoMapper.selectById(taskId);
        boolean isGroupPush = taskInfo.getPublishScope() == PublishScopeEnum.GROUP_PUSH.getCode().intValue();
        // 6. 构造返回对象
        List<ApprovalTimeLine> approvalTimeLines = new ArrayList<>();
        ProcessDefinition.Task curTask = processDefinition.getStartTask();

        int auditCnt = 1;
        while (curTask != null) {
            ProcessDefinition.Task finalCurTask = curTask;
            ApprovalTimeLine approvalTimeLine = new ApprovalTimeLine();
            // 审批节点
            approvalTimeLine.setApproveNode(curTask.getTaskName());
            // 审批人
            String assignees = curTask.getAssignees();
            Assert.hasLength(assignees, "获取审批节点审批人为空");
            String curVariables = String.valueOf(variablesMap.get(extractVariable(assignees)));
            String[] curVariablesArr = curVariables.split(",");
            List<String> usernameList = new ArrayList<>(Arrays.asList(curVariablesArr));
            approvalTimeLine.setApprovalUser(userInfoManager.getUserInfoList(usernameList));
            // 审批任务
            List<ProcessTaskInfo> curProcessTaskList = processTaskInfos.stream()
                    .filter(processTaskInfo -> processTaskInfo.getTaskId()
                            .equals(String.valueOf(finalCurTask.getTaskId())))
                    .collect(Collectors.toList());
            if (curProcessTaskList.isEmpty()) {
                approvalTimeLine.setApproveStatus(ProcessStatusConstant.NO_ACTION);
            } else {
                Integer approvalStatus = curProcessTaskList.get(0).getApproveResultValue();
                if (approvalStatus != ProcessStatusConstant.NO_ACTION) {
                    approvalTimeLine.setTimeStamp(curProcessTaskList.get(0).getApproveTime().getTime());
                }
                approvalTimeLine.setApproveStatus(approvalStatus);
            }
            approvalTimeLines.add(approvalTimeLine);
            if (isGroupPush) {
                curTask = null;
            } else if (taskInfo.getChannel() != TaskChannelEnum.CHANNEL_LARK.getCode()) {
                if (auditLevel.getType() == ProcessAuditEnum.FIRST_LEVEL_AUDIT.getType() && auditCnt == 1) {
                    curTask = null;
                } else if (auditLevel.getType() == ProcessAuditEnum.SECOND_LEVEL_AUDIT.getType() && auditCnt == 2) {
                    curTask = null;
                } else {
                    curTask = curTask.getNextTask();
                }
            } else if (taskInfo.getChannel() == TaskChannelEnum.CHANNEL_LARK.getCode()) {
                if (auditLevel.getType() == ProcessAuditEnum.FIRST_LEVEL_AUDIT.getType() && auditCnt == 2) {
                    curTask = null;
                } else if (auditLevel.getType() == ProcessAuditEnum.SECOND_LEVEL_AUDIT.getType() && auditCnt == 3) {
                    curTask = null;
                } else {
                    curTask = curTask.getNextTask();
                }
            } else {
                curTask = curTask.getNextTask();
            }
            auditCnt++;
        }
        return BaseResult.of(approvalTimeLines);
    }

    @Override
    public BaseResult<List<ApprovalTimeLine>> getBpmApprovalTimeLine(Long taskId, UserBO userBO) {
        // 防止越权
        boolean haveAccess = permissionManager.isHaveMessagePermission(userBO, taskId);
        if (!haveAccess) {
            return new BaseResult<>(ResultCode.FORBIDDEN);
        }

        List<ApprovalTimeLine> result = new ArrayList<>();

        TaskInfo taskInfo = taskInfoMapper.selectById(taskId);

        BaseResp<List<ApprovalTaskResp>> resp = approvalService.list(taskInfo.getBpmInstanceId(), true);

        List<String> definitionKeyList =
                resp.getData().stream().map(ApprovalTaskResp::getTaskDefinitionKey).distinct()
                        .collect(Collectors.toList());

        Map<String, List<ApprovalTaskResp>> definitionKey2TaskList =
                resp.getData().stream().collect(Collectors.groupingBy(ApprovalTaskResp::getTaskDefinitionKey));

        definitionKeyList.forEach(definitionKey -> {
            List<ApprovalTaskResp> taskList = definitionKey2TaskList.get(definitionKey);
            UserTaskSignType taskSignType = taskList.get(0).getSignType();
            if (PARALLEL_ONE.equals(taskSignType)) {
                ApprovalTimeLine approvalTimeLine = new ApprovalTimeLine();
                approvalTimeLine.setApproveNode(taskList.get(0).getTaskName() + "(或签)");
                List<EmployeeInfo> approverList =
                        userInfoManager.getUserInfoList(
                                taskList.stream().map(x -> x.getAssignee().getUserName()).collect(Collectors.toList()));
                approvalTimeLine.setApprovalUser(
                        CollectionUtils.isNotEmpty(approverList) ? approverList : Collections.emptyList());
                approvalTimeLine.setApproveStatus(
                        taskList.stream().anyMatch(x -> Objects.nonNull(x.getEndTime())) ? 1 : 0);
                if (approvalTimeLine.getApproveStatus() == 1) {
                    List<ApprovalTaskResp> doneTaskList =
                            taskList.stream().filter(x -> Objects.nonNull(x.getEndTime()))
                                    .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(doneTaskList)) {
                        approvalTimeLine.setTimeStamp(doneTaskList.get(0).getEndTime().toInstant().toEpochMilli());
                    }
                }
                result.add(approvalTimeLine);
            } else {
                taskList.forEach(x -> {
                    ApprovalTimeLine approvalTimeLine = new ApprovalTimeLine();
                    approvalTimeLine.setApproveNode(x.getTaskName());
                    EmployeeInfo employeeInfo = userInfoManager.getUserInfo(x.getAssignee().getUserName());
                    approvalTimeLine.setApprovalUser(
                            Objects.nonNull(employeeInfo) ? Collections.singletonList(employeeInfo) :
                                    Collections.emptyList());
                    approvalTimeLine.setApproveStatus(Objects.nonNull(x.getEndTime()) ? 1 : 0);
                    if (approvalTimeLine.getApproveStatus() == 1) {
                        approvalTimeLine.setTimeStamp(x.getEndTime().toInstant().toEpochMilli());
                    }
                    result.add(approvalTimeLine);
                });
            }
        });
        return BaseResult.of(result);
    }

    @Override
    public BaseResult<TaskApprovalNodeVO> getApproval(TaskApprovalNodeQueryDTO approvalNodeQueryDTO,
                                                      String createUserName) {

        String approvalProcess = "";
        String approvalMetaProcess = "";
        if (approvalNodeQueryDTO.getChannel() == TaskChannelEnum.CHANNEL_LARK.getCode()) {
            ApprovalInfo specialApprovalInfo = approvalInfoMapper.selectOne(
                    Wrappers.<ApprovalInfo>lambdaQuery()
                            .eq(ApprovalInfo::getChannel, ProcessDefinitionResource.SPECIAL_RESOURCE_CHANNEL)
                            .eq(ApprovalInfo::getBotId, approvalNodeQueryDTO.getBotId())
            );
            if (specialApprovalInfo != null) {
                approvalProcess = specialApprovalInfo.getApprovalProcessNode();
                approvalMetaProcess = specialApprovalInfo.getApprovalMetaNode();
            } else {
                ApprovalInfo approvalInfo = approvalInfoMapper.selectOne(
                        Wrappers.<ApprovalInfo>lambdaQuery()
                                .eq(ApprovalInfo::getChannel, TaskChannelEnum.CHANNEL_LARK.getCode()));
                approvalProcess = approvalInfo.getApprovalProcessNode();
                approvalMetaProcess = approvalInfo.getApprovalMetaNode();
            }
        } else if (approvalNodeQueryDTO.getChannel() == TaskChannelEnum.CHANNEL_SMS.getCode()) {
            ApprovalInfo approvalInfo = approvalInfoMapper.selectOne(
                    Wrappers.<ApprovalInfo>lambdaQuery()
                            .eq(ApprovalInfo::getChannel, TaskChannelEnum.CHANNEL_SMS.getCode()));
            approvalProcess = approvalInfo.getApprovalProcessNode();
            approvalMetaProcess = approvalInfo.getApprovalMetaNode();
        } else if (approvalNodeQueryDTO.getChannel() == TaskChannelEnum.CHANNEL_EMAIL.getCode()) {
            ApprovalInfo approvalInfo = approvalInfoMapper.selectOne(
                    Wrappers.<ApprovalInfo>lambdaQuery()
                            .eq(ApprovalInfo::getChannel, TaskChannelEnum.CHANNEL_EMAIL.getCode()));
            approvalProcess = approvalInfo.getApprovalProcessNode();
            approvalMetaProcess = approvalInfo.getApprovalMetaNode();
        }
        Assert.hasLength(approvalProcess, "审批流程为空");
        Assert.hasLength(approvalMetaProcess, "审批流程为空");

        TaskApprovalNodeVO taskApprovalNodeVO = new TaskApprovalNodeVO();
        List<TaskApprovalNodeVO.ApprovalNodeDetail> approvalNodeDetailList = new ArrayList<>();

        JsonArray jsonArray = new JsonParser().parse(approvalMetaProcess).getAsJsonArray();

        long allCount = this.allScopeCount(approvalNodeQueryDTO);
        ProcessAuditEnum auditType = getAuditLevelByCount(allCount);
        boolean isGroupPush =
                approvalNodeQueryDTO.getPublishScope() == PublishScopeEnum.GROUP_PUSH.getCode().intValue();
        if (isGroupPush) {
            // 群推送只走一个节点
            JsonArray newJsonArray = new JsonArray();
            newJsonArray.add(jsonArray.get(0));
            jsonArray = newJsonArray;
        } else if (approvalNodeQueryDTO.getChannel() != TaskChannelEnum.CHANNEL_LARK.getCode()) {
            // 如果不是飞书消息，一级审核只走一个节点，二级审核只走二个节点，三级审核走全部
            if (auditType.getType() == ProcessAuditEnum.FIRST_LEVEL_AUDIT.getType()) {
                JsonArray newJsonArray = new JsonArray();
                newJsonArray.add(jsonArray.get(0));
                jsonArray = newJsonArray;
            } else if (auditType.getType() == ProcessAuditEnum.SECOND_LEVEL_AUDIT.getType()) {
                if (jsonArray.size() >= 2) {
                    JsonArray newJsonArray = new JsonArray();
                    newJsonArray.add(jsonArray.get(0));
                    newJsonArray.add(jsonArray.get(1));
                    jsonArray = newJsonArray;
                }
            }
        } else {
            // 飞书消息，一级审核走二个节点，二级审核只走三个节点，三级审核走全部
            // change:feat/bpm——都需要走【信息部运营】审核
            if (auditType.getType() == ProcessAuditEnum.FIRST_LEVEL_AUDIT.getType()) {
                if (jsonArray.size() >= 2) {
                    JsonArray newJsonArray = new JsonArray();
                    newJsonArray.add(jsonArray.get(0));
                    newJsonArray.add(jsonArray.get(1));
                    jsonArray = newJsonArray;
                }
            } else if (auditType.getType() == ProcessAuditEnum.SECOND_LEVEL_AUDIT.getType()) {
                if (jsonArray.size() >= 3) {
                    JsonArray newJsonArray = new JsonArray();
                    newJsonArray.add(jsonArray.get(0));
                    newJsonArray.add(jsonArray.get(1));
                    newJsonArray.add(jsonArray.get(2));
                    jsonArray = newJsonArray;
                }
            }
        }
        jsonArray.forEach(
                jsonObject -> {
                    TaskApprovalNodeVO.ApprovalNodeDetail approvalNodeDetail =
                            new TaskApprovalNodeVO.ApprovalNodeDetail();
                    ApprovalNodeDTO approvalNodeDTO = new Gson().fromJson(jsonObject, ApprovalNodeDTO.class);
                    approvalNodeDetail.setApprovalNode(approvalNodeDTO.getDesc());
                    approvalNodeDetail.setApproveKey(approvalNodeDTO.getName());
                    approvalNodeDetail.setApprovalUser(
                            getApprovalUserList(approvalNodeDTO.getName(), createUserName,
                                    approvalNodeQueryDTO.getBotId(),
                                    approvalNodeDTO));
                    approvalNodeDetailList.add(approvalNodeDetail);
                }
        );
        taskApprovalNodeVO.setApprovalNodeDetail(approvalNodeDetailList);
        return BaseResult.of(taskApprovalNodeVO);
    }

    @Override
    public BaseResult<TaskApprovalNodeVO> getBpmApproval(TaskApprovalNodeQueryDTO approvalNodeQueryDTO,
                                                         String createUserName) {
        TaskApprovalNodeVO resultVO = new TaskApprovalNodeVO();
        List<TaskApprovalNodeVO.ApprovalNodeDetail> resultDetail = new ArrayList<>();

        boolean isWhiteBot = false;
        ApprovalInfo specialApprovalInfo = approvalInfoMapper.selectOne(
                Wrappers.<ApprovalInfo>lambdaQuery()
                        .eq(ApprovalInfo::getChannel, ProcessDefinitionResource.SPECIAL_RESOURCE_CHANNEL)
                        .eq(ApprovalInfo::getBotId, approvalNodeQueryDTO.getBotId())
        );
        if (specialApprovalInfo != null) {
            isWhiteBot = true;
        }

        boolean isGroupPush =
                approvalNodeQueryDTO.getPublishScope() == PublishScopeEnum.GROUP_PUSH.getCode().intValue();
        if (isGroupPush) {
            // 飞书的群推送
            // 白名单：机器人管理员->自定义审批
            if (isWhiteBot) {
                String approvalMetaProcess = specialApprovalInfo.getApprovalMetaNode();
                Assert.hasLength(approvalMetaProcess, "审批流程为空");
                // 机器人管理员
                resultDetail.add(getBotManagerApprovalNodeDetail(approvalNodeQueryDTO.getBotId()));
                // 自定义审批
                resultDetail.add(getCustomApprovalNodeDetail(approvalMetaProcess, createUserName));
            } else {
                // 非白名单：机器人管理员->直属主管
                // 机器人管理员
                resultDetail.add(getBotManagerApprovalNodeDetail(approvalNodeQueryDTO.getBotId()));
                // 直属主管
                resultDetail.add(getDirectLeaderApprovalNodeDetail(createUserName));
            }
        } else if (approvalNodeQueryDTO.getChannel() != TaskChannelEnum.CHANNEL_LARK.getCode()) {
            // 如果是邮件或者短信 审批到二级部门主管->信息部运营
            // 直到二级部门主管
            resultDetail.add(getLevelNLeaderApprovalNodeDetail(createUserName, 2));
            // 信息部运营
            resultDetail.add(getOperatorApprovalNodeDetail());
        } else {
            // 飞书消息
            // 白名单：机器人管理员->自定义审批
            if (isWhiteBot) {
                String approvalMetaProcess = specialApprovalInfo.getApprovalMetaNode();
                Assert.hasLength(approvalMetaProcess, "审批流程为空");
                // 机器人管理员
                resultDetail.add(getBotManagerApprovalNodeDetail(approvalNodeQueryDTO.getBotId()));
                // 自定义审批
                resultDetail.add(getCustomApprovalNodeDetail(approvalMetaProcess, createUserName));
            } else {
                // 非白名单：按照审批人数判断审批链
                long allCount = this.allScopeCount(approvalNodeQueryDTO);
                ProcessAuditEnum auditType = getAuditLevelByCount(allCount);

                // ＜1000人：机器人管理员->直属主管
                if (auditType.getType() == ProcessAuditEnum.FIRST_LEVEL_AUDIT.getType()) {
                    // 机器人管理员
                    resultDetail.add(getBotManagerApprovalNodeDetail(approvalNodeQueryDTO.getBotId()));
                    // 直属主管
                    resultDetail.add(getDirectLeaderApprovalNodeDetail(createUserName));
                }
                // 非全员：机器人管理员->审批到二级部门主管->信息部运营
                else if (auditType.getType() == ProcessAuditEnum.SECOND_LEVEL_AUDIT.getType()) {
                    // 机器人管理员
                    resultDetail.add(getBotManagerApprovalNodeDetail(approvalNodeQueryDTO.getBotId()));
                    // 直到二级部门主管
                    resultDetail.add(getLevelNLeaderApprovalNodeDetail(createUserName, 2));
                    // 信息部运营
                    resultDetail.add(getOperatorApprovalNodeDetail());
                }
                // 全员：机器人管理员->审批到一级部门主管->信息部运营
                else {
                    // 机器人管理员
                    resultDetail.add(getBotManagerApprovalNodeDetail(approvalNodeQueryDTO.getBotId()));
                    // 直到一级部门主管
                    resultDetail.add(getLevelNLeaderApprovalNodeDetail(createUserName, 1));
                    // 信息部运营
                    resultDetail.add(getOperatorApprovalNodeDetail());
                }
            }
        }
        resultVO.setApprovalNodeDetail(resultDetail);
        return BaseResult.of(resultVO);
    }

    @Override
    public List<String> getCustomApprover(String metaProcess, String createUserName) {
        List<String> customApproverList = new ArrayList<>();

        List<ApprovalNodeDTO> approvalNodeList = JacksonUtils.json2List(metaProcess, ApprovalNodeDTO.class);
        //过滤掉机器人管理员
        approvalNodeList =
                approvalNodeList.stream()
                        .filter(x -> !ApprovalNodeEnum.APPROVAL_TYPE_MANAGERS.getName().equals(x.getName())).collect(
                                Collectors.toList());
        //如果包含二级部门主管则过滤掉直属主管(因为获取的是直到二级主管的汇报线已包括直属主管)
        if (approvalNodeList.stream()
                .anyMatch(x -> ApprovalNodeEnum.APPROVAL_TYPE_LEVEL_2_LEADER.getName().equals(x.getName()))) {
            approvalNodeList =
                    approvalNodeList.stream()
                            .filter(x -> !ApprovalNodeEnum.APPROVAL_TYPE_LEADER.getName().equals(x.getName())).collect(
                                    Collectors.toList());
        }
        //如果包含一级部门主管则过滤掉直属主管进而二级部门主管（因为获取的是直到一级主管的汇报线已包括直属主管和二级部门主管）
        if (approvalNodeList.stream()
                .anyMatch(x -> ApprovalNodeEnum.APPROVAL_TYPE_LEVEL_1_LEADER.getName().equals(x.getName()))) {
            approvalNodeList =
                    approvalNodeList.stream()
                            .filter(x -> (!ApprovalNodeEnum.APPROVAL_TYPE_LEADER.getName().equals(x.getName())) &&
                                    (!ApprovalNodeEnum.APPROVAL_TYPE_LEVEL_2_LEADER.getName()
                                            .equals(x.getName()))).collect(
                                    Collectors.toList());
        }

        approvalNodeList.forEach(x -> {
            if (ApprovalNodeEnum.APPROVAL_TYPE_LEADER.getName().equals(x.getName())) {
                String leader = reportLineManager.getDirectLeader(createUserName, "").getSupUserName();
                customApproverList.add(StringUtils.isBlank(leader) ? createUserName : leader);
            } else if (ApprovalNodeEnum.APPROVAL_TYPE_LEVEL_2_LEADER.getName().equals(x.getName())) {
                List<String> untilLevel2ApproverList =
                        reportLineManager.getUntilLevelNLeader(createUserName, 2).stream().map(HrodUser::getOprId)
                                .collect(
                                        Collectors.toList());
                customApproverList.addAll(untilLevel2ApproverList);
            } else if (ApprovalNodeEnum.APPROVAL_TYPE_LEVEL_1_LEADER.getName().equals(x.getName())) {
                List<String> untilLevel1ApproverList =
                        reportLineManager.getUntilLevelNLeader(createUserName, 1).stream().map(HrodUser::getOprId)
                                .collect(
                                        Collectors.toList());
                customApproverList.addAll(untilLevel1ApproverList);
            } else if (APPROVAL_TYPE_CUSTOMIZE.getName().equals(x.getName())) {
                List<String> specifiedApproverList =
                        x.getCustomizeUserList().stream().map(ApprovalNodeDTO.Customize::getUsername).distinct()
                                .collect(
                                        Collectors.toList());
                customApproverList.addAll(specifiedApproverList);
            } else if (ApprovalNodeEnum.APPROVAL_TYPE_OPERATORS.getName().equals(x.getName())) {
                List<OperationInfo> operationInfoList = operationInfoMapper.selectList(Wrappers.lambdaQuery());
                List<String> operationUsernameList = operationInfoList.stream().map(OperationInfo::getOperationUsername)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(operationUsernameList)) {
                    customApproverList.add(operationUsernameList.get(0));
                }
            }
        });
        return customApproverList;
    }

    private List<EmployeeInfo> getApprovalUserList(String approvalName, String createUsername, Long botId,
                                                   ApprovalNodeDTO approvalNode) {
        if (APPROVAL_TYPE_MANAGERS.getName().equals(approvalName)) {
            return userInfoManager.getUserInfoList(this.getBotManagerList(botId));
        } else if (APPROVAL_TYPE_LEADER.getName().equals(approvalName)) {
            EmployeeInfo employeeInfo =
                    userInfoManager.getUserInfo(reportLineManager.getDirectLeader(createUsername, "").getSupUserName());
            List<EmployeeInfo> employeeInfos = new ArrayList<>();
            employeeInfos.add(employeeInfo);
            return employeeInfos;
        } else if (ApprovalNodeEnum.APPROVAL_TYPE_LEVEL_2_LEADER.getName().equals(approvalName)) {
            EmployeeInfo employeeInfo = userInfoManager.getUserInfo(
                    reportLineManager.get2thDeptLeader(createUsername, "").getSupUserName());
            List<EmployeeInfo> employeeInfos = new ArrayList<>();
            employeeInfos.add(employeeInfo);
            return employeeInfos;
        } else if (APPROVAL_TYPE_CUSTOMIZE.getName().equals(approvalName)) {
            List<ApprovalNodeDTO.Customize> customizes = approvalNode.getCustomizeUserList();
            List<String> usernameList = customizes.stream().map(ApprovalNodeDTO.Customize::getUsername).distinct()
                    .collect(Collectors.toList());
            return userInfoManager.getUserInfoList(usernameList);
        } else if (ApprovalNodeEnum.APPROVAL_TYPE_OPERATORS.getName().equals(approvalName)) {
            List<OperationInfo> operationInfoList = operationInfoMapper.selectList(Wrappers.lambdaQuery());
            if (operationInfoList.isEmpty()) {
                throw new IllegalArgumentException("创建审批失败，运营的人员为空");
            }
            List<String> operationUsernameList =
                    operationInfoList.stream().map(OperationInfo::getOperationUsername).collect(Collectors.toList());
            return userInfoManager.getUserInfoList(operationUsernameList);
        } else if (ApprovalNodeEnum.APPROVAL_TYPE_LEVEL_1_LEADER.getName().equals(approvalName)) {
            EmployeeInfo employeeInfo =
                    userInfoManager.getUserInfo(reportLineManager.getDeptVp(createUsername, "").getSupUserName());
            List<EmployeeInfo> employeeInfos = new ArrayList<>();
            employeeInfos.add(employeeInfo);
            return employeeInfos;
        }
        throw new RuntimeException("审批人为空");
    }

    private List<String> getBotManagerList(long botId) {
        List<UserBotInfo> managerBotInfoList = userBotInfoMapper.selectList(
                Wrappers.<UserBotInfo>lambdaQuery()
                        .eq(UserBotInfo::getBotId, botId)
                        .eq(UserBotInfo::getUserType, UserBotRoleEnum.USER_BOT_MANAGER.getCode())
        );
        if (managerBotInfoList.isEmpty()) {
            throw new IllegalArgumentException("创建审批失败，机器人管理员为空");
        }
        return managerBotInfoList.stream().map(UserBotInfo::getUsername).collect(Collectors.toList());
    }

    /**
     * 提取 ${assignees}中的内容
     *
     * @param expression 表达式
     * @return
     */
    private String extractVariable(String expression) {
        String pattern = "\\$\\{.*\\}";
        Pattern p = Pattern.compile(pattern);
        Matcher m = p.matcher(expression);
        if (m.find()) {
            String param = m.group(0);
            return param.substring(2, param.length() - 1);
        }
        return "";
    }

    private ProcessAuditEnum getAuditLevelByTaskId(long taskId) {
        List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery()
                        .eq(PublishScopeInfo::getTaskId, taskId)
        );
        long allCount = 0L;
        if (!publishScopeInfoList.isEmpty()) {
            allCount = publishScopeInfoList.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).orElse(0L);
        }
        // 1.因为邮件，SMS,飞书共用一个阈值，所以取一个就行
        ApprovalInfo approvalInfo = approvalInfoMapper.selectOne(
                Wrappers.<ApprovalInfo>lambdaQuery()
                        .eq(ApprovalInfo::getChannel, TaskChannelEnum.CHANNEL_LARK.getCode()));

        long advancedThreshold = approvalInfo.getThreshold();
        long baseThreshold = approvalInfo.getBaseThreshold();

        if (allCount < baseThreshold) {
            return ProcessAuditEnum.FIRST_LEVEL_AUDIT;
        }

        if (allCount < advancedThreshold) {
            return ProcessAuditEnum.SECOND_LEVEL_AUDIT;
        }

        return ProcessAuditEnum.THIRD_LEVEL_AUDIT;
    }

    private List<ApprovalNodeDTO> getApprovalDTO(ApprovalInfo approvalInfo) {
        List<ApprovalNodeDTO> approvalNodeDTOList = new ArrayList<>();
        if (Objects.isNull(approvalInfo) || Objects.isNull(approvalInfo.getApprovalMetaNode())) {
            return approvalNodeDTOList;
        }
        JsonArray jsonArray = new JsonParser().parse(approvalInfo.getApprovalMetaNode()).getAsJsonArray();
        Gson gson = new Gson();
        jsonArray.forEach(
                jsonObject -> {
                    ApprovalNodeDTO approvalNodeDTO = gson.fromJson(jsonObject, ApprovalNodeDTO.class);
                    approvalNodeDTOList.add(approvalNodeDTO);
                }
        );
        return approvalNodeDTOList;
    }

    private List<SpecialApprovalNodeDTO> getSpecialApprovalDTO(List<ApprovalInfo> approvalInfoList) {
        List<SpecialApprovalNodeDTO> specialApprovalNodeDTOList = new ArrayList<>();
        if (Objects.isNull(approvalInfoList) || approvalInfoList.isEmpty()) {
            return specialApprovalNodeDTOList;
        }
        approvalInfoList.forEach(
                approvalInfo -> {
                    List<ApprovalNodeDTO> approvalNodeDTOList = new ArrayList<>();
                    SpecialApprovalNodeDTO specialApprovalNodeDTO = new SpecialApprovalNodeDTO();
                    specialApprovalNodeDTO.setBotId(approvalInfo.getBotId());
                    JsonArray jsonArray = new JsonParser().parse(approvalInfo.getApprovalMetaNode()).getAsJsonArray();
                    Gson gson = new Gson();
                    jsonArray.forEach(
                            jsonObject -> {
                                ApprovalNodeDTO approvalNodeDTO = gson.fromJson(jsonObject, ApprovalNodeDTO.class);
                                approvalNodeDTOList.add(approvalNodeDTO);
                            }
                    );
                    specialApprovalNodeDTO.setSpecialApprovalNodeList(approvalNodeDTOList);
                    specialApprovalNodeDTOList.add(specialApprovalNodeDTO);
                }
        );
        return specialApprovalNodeDTOList;
    }

    private List<ApprovalConfigDTO.ApproveNode> getApproveNodeList(byte channel) {
        List<ApprovalNodeEnum> approvalNodeEnumList = new ArrayList<>();
        approvalNodeEnumList.add(ApprovalNodeEnum.APPROVAL_TYPE_OPERATORS);
        approvalNodeEnumList.add(ApprovalNodeEnum.APPROVAL_TYPE_LEVEL_1_LEADER);
        approvalNodeEnumList.add(APPROVAL_TYPE_CUSTOMIZE);
        approvalNodeEnumList.add(APPROVAL_TYPE_LEADER);
        approvalNodeEnumList.add(ApprovalNodeEnum.APPROVAL_TYPE_LEVEL_2_LEADER);
        if (channel == TaskChannelEnum.CHANNEL_LARK.getCode()) {
            approvalNodeEnumList.add(APPROVAL_TYPE_MANAGERS);
        } else if (channel == TaskChannelEnum.CHANNEL_EMAIL.getCode()) {
            // TODO
        } else if (channel == TaskChannelEnum.CHANNEL_SMS.getCode()) {
            // TODO
        } else if (channel == SPECIAL_CHANNEL) {
            approvalNodeEnumList.add(APPROVAL_TYPE_MANAGERS);
        }
        List<ApprovalConfigDTO.ApproveNode> approvalNodeList = new ArrayList<>();
        approvalNodeEnumList.forEach(
                approvalNodeEnum -> {
                    ApprovalConfigDTO.ApproveNode approveNode = ApprovalConfigDTO.newApproveNodeInstance();
                    approveNode.setDesc(approvalNodeEnum.getDesc());
                    approveNode.setName(approvalNodeEnum.getName());
                    approveNode.setType(approvalNodeEnum.getType());
                    approvalNodeList.add(approveNode);
                }
        );
        return approvalNodeList;
    }

    private void goToUpdateApproval(ApprovalConfigDTO approvalConfigDTO, String username) {
        Gson gson = new Gson();
        long curTime = System.currentTimeMillis();
        // 1.更新邮件审核对象
        String emailApprovalProcessNode = getApprovalProcessNode(approvalConfigDTO.getEmailApprovalMetaNode());
        String emailApprovalMetaNode = gson.toJson(approvalConfigDTO.getEmailApprovalMetaNode());
        ApprovalInfo emailApprovalInfo = new ApprovalInfo();
        emailApprovalInfo.setChannel(TaskChannelEnum.CHANNEL_EMAIL.getCode());
        emailApprovalInfo.setThreshold(approvalConfigDTO.getThreshold());
        emailApprovalInfo.setBaseThreshold(approvalConfigDTO.getBaseThreshold());
        emailApprovalInfo.setFlowChartUrl(approvalConfigDTO.getFlowChartUrl());
        emailApprovalInfo.setApprovalMetaNode(emailApprovalMetaNode);
        emailApprovalInfo.setApprovalProcessNode(emailApprovalProcessNode);
        emailApprovalInfo.setUpdateTime(curTime);
        emailApprovalInfo.setUpdateUsername(username);
        approvalInfoMapper.update(emailApprovalInfo,
                Wrappers.<ApprovalInfo>lambdaUpdate()
                        .eq(ApprovalInfo::getChannel, emailApprovalInfo.getChannel())
        );
        // 2.更新飞书对象
        String larkApprovalProcessNode = getApprovalProcessNode(approvalConfigDTO.getLarkApprovalMetaNode());
        String larkApprovalMetaNode = gson.toJson(approvalConfigDTO.getLarkApprovalMetaNode());
        ApprovalInfo larkApprovalInfo = new ApprovalInfo();
        larkApprovalInfo.setChannel(TaskChannelEnum.CHANNEL_LARK.getCode());
        larkApprovalInfo.setBaseThreshold(approvalConfigDTO.getBaseThreshold());
        larkApprovalInfo.setThreshold(approvalConfigDTO.getThreshold());
        larkApprovalInfo.setFlowChartUrl(approvalConfigDTO.getFlowChartUrl());
        larkApprovalInfo.setApprovalMetaNode(larkApprovalMetaNode);
        larkApprovalInfo.setApprovalProcessNode(larkApprovalProcessNode);
        larkApprovalInfo.setUpdateUsername(username);
        larkApprovalInfo.setUpdateTime(curTime);
        approvalInfoMapper.update(larkApprovalInfo,
                Wrappers.<ApprovalInfo>lambdaUpdate()
                        .eq(ApprovalInfo::getChannel, larkApprovalInfo.getChannel())
        );

        // 3.更新短信对像
        String smsApprovalProcessNode = getApprovalProcessNode(approvalConfigDTO.getSmsApprovalMetaNode());
        String smsApprovalMetaNode = gson.toJson(approvalConfigDTO.getSmsApprovalMetaNode());
        ApprovalInfo smsApprovalInfo = new ApprovalInfo();
        smsApprovalInfo.setChannel(TaskChannelEnum.CHANNEL_SMS.getCode());
        smsApprovalInfo.setThreshold(approvalConfigDTO.getThreshold());
        smsApprovalInfo.setBaseThreshold(approvalConfigDTO.getBaseThreshold());
        smsApprovalInfo.setFlowChartUrl(approvalConfigDTO.getFlowChartUrl());
        smsApprovalInfo.setApprovalMetaNode(smsApprovalMetaNode);
        smsApprovalInfo.setApprovalProcessNode(smsApprovalProcessNode);
        smsApprovalInfo.setUpdateTime(curTime);
        smsApprovalInfo.setUpdateUsername(username);
        approvalInfoMapper.update(smsApprovalInfo,
                Wrappers.<ApprovalInfo>lambdaUpdate()
                        .eq(ApprovalInfo::getChannel, smsApprovalInfo.getChannel())
        );

        // 4. 更新特殊审批
        List<SpecialApprovalNodeDTO> specialApprovalNodeList = approvalConfigDTO.getSpecialApproveMetaNode();
        if (Objects.nonNull(specialApprovalNodeList)) {
            if (specialApprovalNodeList.isEmpty()) {
                approvalInfoMapper.delete(
                        Wrappers.<ApprovalInfo>lambdaUpdate()
                                .eq(ApprovalInfo::getChannel, SPECIAL_CHANNEL)
                );
            } else {
                // 4.0 先判断有没有重复的
                boolean noRepeatBotId = noRepeatBotId(specialApprovalNodeList);
                Assert.isTrue(noRepeatBotId, "存在相同机器人特殊审批流程");
                // 4.1 先删除 他们的已经存在的
                approvalInfoMapper.delete(
                        Wrappers.<ApprovalInfo>lambdaUpdate()
                                .eq(ApprovalInfo::getChannel, SPECIAL_CHANNEL)
                );
                // 4.2 重新保存新的
                List<ApprovalInfo> toInsertApprovalInfoList = new ArrayList<>();
                specialApprovalNodeList.forEach(
                        specialApprovalNodeDTO -> {
                            ApprovalInfo approvalInfo = new ApprovalInfo();
                            approvalInfo.setApprovalProcessNode(
                                    getApprovalProcessNode(specialApprovalNodeDTO.getSpecialApprovalNodeList()));
                            approvalInfo.setApprovalMetaNode(
                                    gson.toJson(specialApprovalNodeDTO.getSpecialApprovalNodeList()));
                            approvalInfo.setChannel(SPECIAL_CHANNEL);
                            approvalInfo.setBotId(specialApprovalNodeDTO.getBotId());
                            approvalInfo.setThreshold(approvalConfigDTO.getThreshold());
                            approvalInfo.setBaseThreshold(approvalConfigDTO.getBaseThreshold());
                            approvalInfo.setFlowChartUrl(approvalConfigDTO.getFlowChartUrl());
                            approvalInfo.setCreateTime(curTime);
                            approvalInfo.setCreateUsername(username);
                            approvalInfo.setUpdateTime(curTime);
                            approvalInfo.setUpdateUsername(username);
                            toInsertApprovalInfoList.add(approvalInfo);
                        }
                );
                if (!toInsertApprovalInfoList.isEmpty()) {
                    this.saveBatch(toInsertApprovalInfoList);
                }
            }
        }
    }

    private String getApprovalProcessNode(List<ApprovalNodeDTO> approveNodes) {

        ProcessDefinition messageProcess = new ProcessDefinition();
        messageProcess.setProcessId(ProcessDefinitionResource.PROCESS_MESSAGE_RELEASE);
        messageProcess.setProcessName(ProcessDefinitionResource.PROCESS_MESSAGE_TOPIC);

        ProcessDefinition.Task startTask = null;
        ProcessDefinition.Task preTask = null;
        int customizeIndex = 1;
        for (int i = 0; i < approveNodes.size(); i++) {
            ApprovalNodeDTO approvalNodeDTO = approveNodes.get(i);
            Assert.hasLength(approvalNodeDTO.getName(), "更新异常,审批节点参数为空");
            Assert.hasLength(approvalNodeDTO.getDesc(), "更新异常,审批节点参数为空");
            if (i == 0) {
                startTask = new ProcessDefinition.Task();
                startTask.setTaskId(String.valueOf(i + 1));
                startTask.setTaskName(approvalNodeDTO.getDesc());
                if (CUSTOMIZE.equals(approvalNodeDTO.getName().trim())) {
                    startTask.setAssignees(
                            "${" + approvalNodeDTO.getName().trim().concat(String.valueOf(customizeIndex++)) + "}");
                } else {
                    startTask.setAssignees("${" + approvalNodeDTO.getName().trim() + "}");
                }
                preTask = startTask;
            } else {
                ProcessDefinition.Task task = new ProcessDefinition.Task();
                task.setTaskName(approvalNodeDTO.getDesc());
                task.setTaskId(String.valueOf(i + 1));
                if (CUSTOMIZE.equals(approvalNodeDTO.getName().trim())) {
                    task.setAssignees(
                            "${" + approvalNodeDTO.getName().trim().concat(String.valueOf(customizeIndex++)) + "}");
                } else {
                    task.setAssignees("${" + approvalNodeDTO.getName().trim() + "}");
                }
                preTask.setNextTask(task);
                preTask = task;
            }
        }

        messageProcess.setStartTask(startTask);
        return new Gson().toJson(messageProcess);

    }

    private boolean noRepeatBotId(List<SpecialApprovalNodeDTO> specialApprovalNodeList) {
        List<Long> botIdList = specialApprovalNodeList.stream().map(SpecialApprovalNodeDTO::getBotId).distinct()
                .collect(Collectors.toList());
        return botIdList.size() == specialApprovalNodeList.size();
    }

    private int allScopeCount(TaskApprovalNodeQueryDTO approvalNodeQueryDTO) {

        int allCount = 0;

        PublishScopeEnum publishScopeEnum = PublishScopeEnum.getByCode(approvalNodeQueryDTO.getPublishScope());

        List<String> scopeKeyList = approvalNodeQueryDTO.getScopeKey();

        // 是否要排除掉合作伙伴账号
        boolean excludePartner = true;
        BotInfo botInfo = botInfoMapper.selectById(approvalNodeQueryDTO.getBotId());
        BotTenantEnum botTenantEnum = BotTenantEnum.getByCode(botInfo.getBotTenant());
        if (BotTenantEnum.BOT_TENANT_PARTNER.equals(botTenantEnum)) {
            excludePartner = false;
        }

        switch (publishScopeEnum) {
            case ALL_PUSH:
                LambdaQueryWrapper<EmployeeInfo> lambdaQueryWrapper = Wrappers.<EmployeeInfo>lambdaQuery()
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                        .ne(EmployeeInfo::getUsername, "");
                if (excludePartner) {
                    lambdaQueryWrapper.ne(EmployeeInfo::getEmpType, HrEmployeeTypeEnum.PARTNER.getCode());
                } else {
                    lambdaQueryWrapper.eq(EmployeeInfo::getEmpType, HrEmployeeTypeEnum.PARTNER.getCode());
                }
                return employeeInfoMapper.selectCount(lambdaQueryWrapper);
            case DEPT_PUSH:
                if (scopeKeyList == null || scopeKeyList.isEmpty()) {
                    return allCount;
                }
                scopeKeyList = deptManager.filterDeptIds(scopeKeyList);
                List<DepartmentSummaryDTO> deptInfoList = deptManager.getDeptInfo(scopeKeyList, excludePartner);
                Map<String, DepartmentSummaryDTO> deptIdMap = deptInfoList.stream().collect(
                        Collectors.toMap(DepartmentSummaryDTO::getDeptId, Function.identity(), (v1, v2) -> v2));

                for (String scopeKey : scopeKeyList) {
                    DepartmentSummaryDTO departmentSummaryDTO = deptIdMap.get(scopeKey);
                    if (Objects.nonNull(departmentSummaryDTO)) {
                        allCount += departmentSummaryDTO.getCount();
                    }
                }
                log.info("allScopeCount allCount = {}", allCount);
                return allCount;
            case CUSTOM_PUSH_EMPLOYEE:
                if (scopeKeyList == null || scopeKeyList.isEmpty()) {
                    return allCount;
                }
                return parseTmpInfoMapper.selectCount(
                        Wrappers.<ParseTmpInfo>lambdaQuery()
                                .eq(ParseTmpInfo::getExcelId, scopeKeyList.get(0))
                                .eq(ParseTmpInfo::getPublishScope, PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode())
                                .in(ParseTmpInfo::getDataStatus, ParseDataStatusEnum.WAIT_PROCESS.getCode(),
                                        ParseDataStatusEnum.PROCESSED.getCode())
                );
            case CHOOSE_PUSH:
                if (scopeKeyList == null || scopeKeyList.isEmpty()) {
                    return allCount;
                }
                Set<Integer> chooseIdList = scopeKeyList.stream().map(Integer::valueOf).collect(Collectors.toSet());
                List<Consumer<LambdaQueryWrapper<EmployeeInfo>>> consumerList = chooseIdList.stream()
                        .map(ChooseIdEnum::getById)
                        .map(ChooseIdEnum::getConsumer)
                        .collect(Collectors.toList());

                LambdaQueryWrapper<EmployeeInfo> lambdaQueryWrapper1 =
                        Wrappers.<EmployeeInfo>lambdaQuery()
                                .and(p -> consumerList.forEach(p::or))
                                .ne(EmployeeInfo::getUsername, "");
                if (excludePartner) {
                    lambdaQueryWrapper1.ne(EmployeeInfo::getEmpType, HrEmployeeTypeEnum.PARTNER.getCode());
                } else {
                    lambdaQueryWrapper1.eq(EmployeeInfo::getEmpType, HrEmployeeTypeEnum.PARTNER.getCode());
                }
                return employeeInfoMapper.selectCount(lambdaQueryWrapper1);
            case GROUP_PUSH:
                return larkGroupPushInfoMapper.selectCount(
                        Wrappers.<LarkGroupPushInfo>lambdaQuery()
                                .eq(LarkGroupPushInfo::getTagId, scopeKeyList.get(0))
                );
            default:
                log.warn("范围类型错误");
                return 0;
        }

    }

    private ProcessAuditEnum getAuditLevelByCount(long allCount) {

        // 1.因为邮件，SMS,飞书共用一个阈值，所以取一个就行
        ApprovalInfo approvalInfo = approvalInfoMapper.selectOne(
                Wrappers.<ApprovalInfo>lambdaQuery()
                        .eq(ApprovalInfo::getChannel, TaskChannelEnum.CHANNEL_LARK.getCode()));

        long advancedThreshold = approvalInfo.getThreshold();
        long baseThreshold = approvalInfo.getBaseThreshold();

        if (allCount < baseThreshold) {
            return ProcessAuditEnum.FIRST_LEVEL_AUDIT;
        }

        if (allCount < advancedThreshold) {
            return ProcessAuditEnum.SECOND_LEVEL_AUDIT;
        }

        return ProcessAuditEnum.THIRD_LEVEL_AUDIT;

    }

    /**
     * 获取机器人管理员审批节点
     *
     * @param botId 机器人ID
     * @return
     */
    private TaskApprovalNodeVO.ApprovalNodeDetail getBotManagerApprovalNodeDetail(long botId) {
        TaskApprovalNodeVO.ApprovalNodeDetail botManagerApprovalNodeDetail =
                new TaskApprovalNodeVO.ApprovalNodeDetail();
        botManagerApprovalNodeDetail.setApprovalNode(APPROVAL_TYPE_MANAGERS.getDesc());
        botManagerApprovalNodeDetail.setApproveKey(APPROVAL_TYPE_MANAGERS.getName());
        List<EmployeeInfo> approvalUserList =
                userInfoManager.getUserInfoList(this.getBotManagerList(botId));
        botManagerApprovalNodeDetail.setApprovalUser(approvalUserList);
        return botManagerApprovalNodeDetail;
    }

    /**
     * 获取直属主管审批节点
     *
     * @param userName 用户域账号
     * @return
     */
    private TaskApprovalNodeVO.ApprovalNodeDetail getDirectLeaderApprovalNodeDetail(String userName) {
        TaskApprovalNodeVO.ApprovalNodeDetail directLeaderApprovalNode =
                new TaskApprovalNodeVO.ApprovalNodeDetail();
        directLeaderApprovalNode.setApprovalNode(APPROVAL_TYPE_LEADER.getDesc());
        directLeaderApprovalNode.setApproveKey(APPROVAL_TYPE_LEADER.getName());
        EmployeeInfo employeeInfo =
                userInfoManager.getUserInfo(reportLineManager.getDirectLeader(userName, "").getSupUserName());
        directLeaderApprovalNode.setApprovalUser(Objects.nonNull(employeeInfo) ?
                Collections.singletonList(employeeInfo) : Collections.emptyList());
        return directLeaderApprovalNode;
    }

    /**
     * 获取直到N级部门主管审批节点
     *
     * @param userName 用户域账号
     * @param level 几级主管
     * @return
     */
    private TaskApprovalNodeVO.ApprovalNodeDetail getLevelNLeaderApprovalNodeDetail(String userName, int level) {
        TaskApprovalNodeVO.ApprovalNodeDetail untilLevelNLeaderApprovalNode =
                new TaskApprovalNodeVO.ApprovalNodeDetail();
        if (level == 1) {
            untilLevelNLeaderApprovalNode.setApprovalNode(APPROVAL_TYPE_LEVEL_1_LEADER.getDesc());
            untilLevelNLeaderApprovalNode.setApproveKey(APPROVAL_TYPE_LEVEL_1_LEADER.getName());
        } else if (level == 2) {
            untilLevelNLeaderApprovalNode.setApprovalNode(APPROVAL_TYPE_LEVEL_2_LEADER.getDesc());
            untilLevelNLeaderApprovalNode.setApproveKey(APPROVAL_TYPE_LEVEL_2_LEADER.getName());
        }
        List<EmployeeInfo> approvalUserList =
                userInfoManager.getUserInfoList(reportLineManager.getUntilLevelNLeader(userName, level).stream().map(
                        HrodUser::getOprId).collect(Collectors.toList()));
        untilLevelNLeaderApprovalNode.setApprovalUser(approvalUserList);
        return untilLevelNLeaderApprovalNode;
    }

    /**
     * 获取信息部运营审批节点
     *
     * @return
     */
    private TaskApprovalNodeVO.ApprovalNodeDetail getOperatorApprovalNodeDetail() {
        TaskApprovalNodeVO.ApprovalNodeDetail operatorApprovalNode = new TaskApprovalNodeVO.ApprovalNodeDetail();
        operatorApprovalNode.setApprovalNode(APPROVAL_TYPE_OPERATORS.getDesc());
        operatorApprovalNode.setApproveKey(APPROVAL_TYPE_OPERATORS.getName());
        List<EmployeeInfo> operationInfoList =
                userInfoManager.getUserInfoList(operationInfoMapper.selectList(Wrappers.lambdaQuery()).stream()
                        .map(OperationInfo::getOperationUsername).collect(Collectors.toList()));
        operatorApprovalNode.setApprovalUser(operationInfoList);
        return operatorApprovalNode;
    }

    /**
     * 获取自定义审批节点
     *
     * @param metaProcess 自定义审批步骤
     * @param userName 用户域账号
     * @return
     */
    private TaskApprovalNodeVO.ApprovalNodeDetail getCustomApprovalNodeDetail(String metaProcess, String userName) {
        TaskApprovalNodeVO.ApprovalNodeDetail customApprovalNode = new TaskApprovalNodeVO.ApprovalNodeDetail();
        customApprovalNode.setApprovalNode(APPROVAL_TYPE_CUSTOMIZE.getDesc());
        customApprovalNode.setApproveKey(APPROVAL_TYPE_CUSTOMIZE.getName());
        List<EmployeeInfo> approvalUserList = userInfoManager.getUserInfoList(getCustomApprover(metaProcess, userName));
        customApprovalNode.setApprovalUser(approvalUserList);
        return customApprovalNode;
    }

}
