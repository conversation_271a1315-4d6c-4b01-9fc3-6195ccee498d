package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.gson.reflect.TypeToken;
import com.mioffice.ums.admin.entity.bo.BtnNotifBO;
import com.mioffice.ums.admin.entity.bo.CalendarBO;
import com.mioffice.ums.admin.entity.bo.CalendarListBO;
import com.mioffice.ums.admin.entity.info.LarkTaskInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.enums.CalendarRoleEnum;
import com.mioffice.ums.admin.exception.AppNotException;
import com.mioffice.ums.admin.manager.CalendarManager;
import com.mioffice.ums.admin.mapper.LarkTaskInfoMapper;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.ScheduleService;
import com.mioffice.ums.admin.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/19 4:49 下午
 * version: 1.0.0
 */
@Slf4j
@Service
public class ScheduleServiceImpl implements ScheduleService {

    private final TaskInfoMapper taskInfoMapper;
    private final LarkTaskInfoMapper larkTaskInfoMapper;
    private final CalendarManager calendarManager;
    private final MessageGrpcClient messageGrpcClient;

    public ScheduleServiceImpl(TaskInfoMapper taskInfoMapper, LarkTaskInfoMapper larkTaskInfoMapper, CalendarManager calendarManager, MessageGrpcClient messageGrpcClient) {
        this.taskInfoMapper = taskInfoMapper;
        this.larkTaskInfoMapper = larkTaskInfoMapper;
        this.calendarManager = calendarManager;
        this.messageGrpcClient = messageGrpcClient;
    }

    @Override
    public boolean addSchedule(BtnNotifBO btnNotifBO) {

        String taskId = btnNotifBO.getBtnActionBo().getAction().getValue().get("taskId").toString();

        TaskInfo taskInfo = taskInfoMapper.selectById(taskId);
        if (Objects.nonNull(taskInfo)) {
            LarkTaskInfo larkTaskInfo = larkTaskInfoMapper.selectOne(
                    Wrappers.<LarkTaskInfo>lambdaQuery().eq(LarkTaskInfo::getTaskId, taskId)
            );
            if (Objects.nonNull(larkTaskInfo)) {
                try {
                    long startTime = larkTaskInfo.getScheduleStartTime() / 1000;
                    long endTime = larkTaskInfo.getScheduleEndTime() / 1000;
                    BaseResult baseResult = calendarManager.getCalendarList();
                    List<CalendarListBO> calendarListBOList = JsonUtils.parse(JsonUtils.toJson(baseResult.getData()), new TypeToken<List<CalendarListBO>>() {
                    }.getType());
                    if (calendarListBOList.isEmpty()) {
                        CalendarBO calendarBO = new CalendarBO("日历", "日历", false, CalendarRoleEnum.READER.getRole());
                        BaseResult calendar = calendarManager.createCalendar(calendarBO);
                        CalendarBO calendarBOTmp = JsonUtils.parse(JsonUtils.toJson(calendar.getData()), new TypeToken<CalendarBO>() {
                        }.getType());
                        createSchedule(btnNotifBO, larkTaskInfo, startTime, endTime, calendarBOTmp.getId());
                    } else {
                        CalendarListBO calendarListBO = calendarListBOList.get(0);
                        createSchedule(btnNotifBO, larkTaskInfo, startTime, endTime, calendarListBO.getId());
                    }
                    return true;
                } catch (AppNotException e) {
                    log.error("创建日程失败，taskId = [{}]", taskId, e);
                }
            }
        }

        return false;
    }

    private void createSchedule(BtnNotifBO btnNotifBO, LarkTaskInfo larkTaskInfo, long startTime, long endTime, String id)
            throws AppNotException {
        List<Map<String, Object>> attendList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("employee_id", btnNotifBO.getBtnActionBo().getUserId());
        attendList.add(map);
        calendarManager.createSchedule(startTime, endTime, larkTaskInfo.getScheduleDescription(), larkTaskInfo.getScheduleDescription(), "default", attendList, id);
    }

}
