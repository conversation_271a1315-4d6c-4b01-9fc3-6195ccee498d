package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.admin.entity.info.PublishScopeInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.mapper.PublishScopeInfoMapper;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.mioffice.ums.admin.service.StorePushDataService;
import com.mioffice.ums.admin.utils.DateTimeUtil;
import com.mioffice.ums.admin.utils.JsonUtils;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageNumberAndTime;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageNumberAndTimeResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageRead;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageReadResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * @since 2021/9/14 下午5:07
 * version: 1.0.0
 */
@Slf4j
@Service
public class StorePushDataServiceImpl implements StorePushDataService {
    private final TaskInfoMapper taskInfoMapper;
    private final MessageGrpcClient messageGrpcClient;
    private final PublishScopeInfoMapper publishScopeInfoMapper;

    public StorePushDataServiceImpl(
            TaskInfoMapper taskInfoMapper,
            MessageGrpcClient messageGrpcClient,
            PublishScopeInfoMapper publishScopeInfoMapper
    ) {
        this.taskInfoMapper = taskInfoMapper;
        this.messageGrpcClient = messageGrpcClient;
        this.publishScopeInfoMapper = publishScopeInfoMapper;
    }

    @Override
    public void storePushDataIntoTaskInfoTable(Long beginTimeMills, Long endTimeMills) {
        LocalDateTime beginLocalDateTime;
        LocalDateTime endLocalDateTime;
        if (0 == beginTimeMills && 0 == endTimeMills) {
            LocalDate localDate = LocalDate.now().minusDays(4);
            beginLocalDateTime = LocalDateTime.of(localDate, LocalTime.MIN);
            endLocalDateTime = LocalDateTime.of(localDate, LocalTime.MAX);
        } else {
            beginLocalDateTime =
                    LocalDateTime.of(Instant.ofEpochMilli(beginTimeMills).atZone(ZoneOffset.ofHours(8)).toLocalDate(),
                            LocalTime.MIN);
            endLocalDateTime =
                    LocalDateTime.of(Instant.ofEpochMilli(endTimeMills).atZone(ZoneOffset.ofHours(8)).toLocalDate(),
                            LocalTime.MAX);
        }

        Long beginTime = DateTimeUtil.getLongTimeWithDateTime(beginLocalDateTime);
        Long endTime = DateTimeUtil.getLongTimeWithDateTime(endLocalDateTime);
        List<TaskInfo> taskInfoList = taskInfoMapper.selectList(
                Wrappers.<TaskInfo>lambdaQuery()
                        .ge(TaskInfo::getUpdateTime, beginTime)
                        .le(TaskInfo::getUpdateTime, endTime)
        );

        List<TaskInfo> taskInfosTmp = new ArrayList<>();
        for (TaskInfo taskInfo : taskInfoList) {
            TaskInfo taskInfoTmp = new TaskInfo();
            taskInfoTmp.setId(taskInfo.getId());
            taskInfoTmp.setExtraId(taskInfo.getExtraId());
            PublishScopeEnum publishScopeEnum = PublishScopeEnum.getByCode(taskInfo.getPublishScope());
            switch (publishScopeEnum) {
                case DEPT_PUSH:
                    setDeptTaskPushData(taskInfoTmp);
                    break;
                case CUSTOM_PUSH_EMPLOYEE:
                    setCustomTaskPushData(taskInfoTmp);
                    break;
                case CHOOSE_PUSH:
                case ALL_PUSH:
                    setAllTaskPushData(taskInfoTmp, publishScopeEnum);
                    break;
                case GROUP_PUSH:
                    setLarkGroupPushData(taskInfoTmp);
                    break;
                default:
                    break;
            }
            taskInfosTmp.add(taskInfoTmp);
        }
        if (CollectionUtils.isNotEmpty(taskInfosTmp)) {
            taskInfoMapper.updateBatchSelective(taskInfosTmp);
            log.info("taskInfosTmp = [{}]", JsonUtils.toJson(taskInfosTmp));
        }
    }

    private void setDeptTaskPushData(TaskInfo taskInfo) {
        innerSet(taskInfo);
    }

    private void setCustomTaskPushData(TaskInfo taskInfo) {
        if (Objects.isNull(taskInfo.getParentTaskId()) || taskInfo.getParentTaskId() == 0) {
            setParentTask(taskInfo);
        } else {
            List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                    Wrappers.<PublishScopeInfo>lambdaQuery().eq(PublishScopeInfo::getTaskId, taskInfo.getId())
            );

            if (publishScopeInfoList.isEmpty()) {
                taskInfo.setTaskTotalCount(0L);
            } else {
                taskInfo.setTaskTotalCount(publishScopeInfoList.get(0).getAllCount());
            }
            setRushTask(taskInfo);
        }
    }

    private void setAllTaskPushData(TaskInfo taskInfo, PublishScopeEnum publishScopeEnum) {
        // 组聚合条数
        if (Objects.isNull(taskInfo.getParentTaskId()) || taskInfo.getParentTaskId() == 0) {
            setParentTask(taskInfo);
        } else {
            List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                    Wrappers.<PublishScopeInfo>lambdaQuery().eq(PublishScopeInfo::getTaskId, taskInfo.getId())
            );
            long allCount = 0;
            if (PublishScopeEnum.ALL_PUSH.getCode().equals(publishScopeEnum.getCode())) {
                if (!publishScopeInfoList.isEmpty()) {
                    allCount = publishScopeInfoList.get(0).getAllCount();
                }
            } else if (PublishScopeEnum.CHOOSE_PUSH.getCode().equals(publishScopeEnum.getCode())) {
                allCount =
                        publishScopeInfoList.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).orElse(0L);
            }
            taskInfo.setTaskTotalCount(allCount);
            // 消耗时间和条数相关
            setRushTask(taskInfo);
        }
    }

    private void setLarkGroupPushData(TaskInfo taskInfo) {
        innerSet(taskInfo);
    }

    private void innerSet(TaskInfo taskInfo) {
        if (Objects.isNull(taskInfo.getParentTaskId()) || taskInfo.getParentTaskId() == 0) {
            setParentTask(taskInfo);
        } else {
            List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                    Wrappers.<PublishScopeInfo>lambdaQuery().eq(PublishScopeInfo::getTaskId, taskInfo.getId())
            );
            long allCount = 0;
            if (!publishScopeInfoList.isEmpty()) {
                allCount =
                        publishScopeInfoList.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).orElse(0L);
            }
            taskInfo.setTaskTotalCount(allCount);
            setRushTask(taskInfo);
        }
    }

    private void setParentTask(TaskInfo taskInfo) {
        List<String> subExtraIdList =
                taskInfoMapper.selectList(Wrappers.<TaskInfo>lambdaQuery().eq(TaskInfo::getParentTaskId,
                        taskInfo.getId())).stream().map(TaskInfo::getExtraId).collect(
                        Collectors.toList());
        MessageReadResponse messageReadResponse =
                messageGrpcClient.getTaskReadData(taskInfo.getExtraId(), subExtraIdList);
        MessageRead messageRead = messageReadResponse.getMessageRead();
        taskInfo.setTaskPushCount(messageRead.getSendCount());
        taskInfo.setTaskReadCount(messageRead.getReadCount());
        taskInfo.setTaskTotalCount(messageRead.getTotalCount());
        taskInfo.setTaskPushAllTime(messageRead.getCostTime());
        taskInfo.setTaskRetractCount(messageRead.getRetractCount());
    }

    private void setRushTask(TaskInfo taskInfo) {
        MessageNumberAndTimeResponse messageNumberByExtraIdBatch =
                messageGrpcClient.getMessageNumberByExtraIdBatch(Collections.singletonList(taskInfo.getExtraId()));
        if (messageNumberByExtraIdBatch.getMessageNumberAndTimeListList().isEmpty()) {
            taskInfo.setTaskPushCount(0L);
            taskInfo.setTaskPushAllTime(0L);
            taskInfo.setTaskReadCount(0L);
        }
        MessageNumberAndTime messageNumberAndTime =
                messageNumberByExtraIdBatch.getMessageNumberAndTimeListList().get(0);
        taskInfo.setTaskPushCount(messageNumberAndTime.getPushCount());
        taskInfo.setTaskPushAllTime(messageNumberAndTime.getCostTime());
        taskInfo.setTaskReadCount(messageNumberAndTime.getReadCount());
        taskInfo.setTaskRetractCount(messageNumberAndTime.getRetractCount());
    }
}
