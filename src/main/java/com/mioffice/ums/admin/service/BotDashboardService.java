package com.mioffice.ums.admin.service;

import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.vo.BotDashboardVO;

import java.util.List;

/**
 * <p>
 * 机器人看板
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/7 11:30 上午
 */
public interface BotDashboardService {

    /**
     * 获取机器人看板
     *
     * @param deptIdList 部门id列表的字符串形式
     * @param managerUsernameList 管理员列表的字符串形式
     * @param startTime 开始时间
     * @param endTime 开始时间
     * @param username 登录用户
     * @return BotDashboardDTO
     */
    BotDashboardVO getBotDashboardVO(String deptIdList, String managerUsernameList, String startTime, String endTime, String username);

    /**
     * 获取机器人看板的管理员列表
     *
     * @param deptIdList 部门ID list
     * @param username 登录用户
     * @param searchWord 搜索词
     * @return List<EmployeeInfo>
     */
    List<EmployeeInfo> getBotDashboardManagerList(String deptIdList, String username, String searchWord);

}
