package com.mioffice.ums.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mioffice.ums.admin.entity.dto.IdDTO;
import com.mioffice.ums.admin.entity.dto.MessageRuleJobDTO;
import com.mioffice.ums.admin.entity.dto.MessageRuleV2JobDTO;
import com.mioffice.ums.admin.entity.info.MessageJob;
import com.mioffice.ums.admin.result.BaseResult;

/**
 * 消息规则小
 */
public interface MessageRuleJobService extends IService<MessageJob> {

    /**
     * messageRuleJob 消息任务实体
     */
    BaseResult<Long> ruleJobAdd(MessageRuleJobDTO messageRuleJob);

    /**
     * messageRuleJob 消息任务实体
     */
    BaseResult<Long> ruleJobUpdate(MessageRuleJobDTO messageRuleJob);

    /**
     * @param idDTO 规则ID
     */
    BaseResult<MessageRuleJobDTO> ruleJobDetail(IdDTO idDTO);

    BaseResult<Object> delRuleJob(IdDTO idDTO);

    BaseResult<Object> stopRuleJob(IdDTO idDTO);

    BaseResult<Object> startRuleJob(IdDTO idDTO);

    //--------------------v2-----------------------------

    BaseResult<Long> ruleJobAddV2(MessageRuleV2JobDTO messageRuleJob);

    BaseResult<Long> ruleJobUpdateV2(MessageRuleV2JobDTO messageRuleJob);

}
