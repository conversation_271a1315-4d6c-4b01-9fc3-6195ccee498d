package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mioffice.ums.admin.entity.bo.LarkGroupBO;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.BotGroupPushListAddDTO;
import com.mioffice.ums.admin.entity.info.BotInfo;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.LarkGroupPushInfo;
import com.mioffice.ums.admin.entity.vo.BotGroupListResultVO;
import com.mioffice.ums.admin.enums.GroupOwnerTypeEnum;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.mapper.BotInfoMapper;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.LarkGroupPushInfoMapper;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.BotGroupApiService;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.GroupListResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.GroupRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * @since 2020/11/26 下午3:53
 * version: 1.0.0
 */
@Slf4j
@Service
public class BotGroupApiServiceImpl implements BotGroupApiService {

    @Value("${lark.appId}")
    private String umsBotAppId;

    private final BotInfoMapper botInfoMapper;

    private final EmployeeInfoMapper employeeInfoMapper;

    private final LarkGroupPushInfoMapper larkGroupPushInfoMapper;

    private final MessageGrpcClient messageGrpcClient;

    public BotGroupApiServiceImpl(
            BotInfoMapper botInfoMapper,
            EmployeeInfoMapper employeeInfoMapper, LarkGroupPushInfoMapper larkGroupPushInfoMapper, MessageGrpcClient messageGrpcClient
    ) {
        this.botInfoMapper = botInfoMapper;
        this.employeeInfoMapper = employeeInfoMapper;
        this.larkGroupPushInfoMapper = larkGroupPushInfoMapper;
        this.messageGrpcClient = messageGrpcClient;
    }

    @Override
    public BaseResult<BotGroupListResultVO> fetchBotGroupList(Long botId, UserBO userBO) {
        BotGroupListResultVO botGroupListResultVO = new BotGroupListResultVO();

        BotInfo botInfo = botInfoMapper.selectById(botId);
        if (Objects.isNull(botInfo)) {
            return new BaseResult<BotGroupListResultVO>(ResultCode.PARAM_ERROR).setMessage("不存在机器人");
        }

        String botKey = botInfo.getBotKey();

        List<LarkGroupBO> larkGroupBOS = new ArrayList<>();
        try {
            GroupListResponse groupListResponse = messageGrpcClient.fetchGroupList(botKey, umsBotAppId);
            int code = groupListResponse.getCode();
            if (code == ResultCode.OK.getCode()) {
                List<GroupRecord> groupList = groupListResponse.getGroupList();
                if (groupList.isEmpty()) {
                    return BaseResult.of(botGroupListResultVO);
                }
                List<String> userIdList = groupList.stream().map(GroupRecord::getOwnerUserId).distinct().collect(Collectors.toList());
                List<EmployeeInfo> employeeInfos = employeeInfoMapper.selectList(
                        Wrappers.<EmployeeInfo>lambdaQuery()
                                .in(EmployeeInfo::getUsername, userIdList)
                                .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                );
                Map<String, String> usernameAndNameMap = employeeInfos.stream().distinct().collect(Collectors.toMap(EmployeeInfo::getUsername, EmployeeInfo::getName));
                groupList.forEach(
                        group -> {
                            LarkGroupBO larkGroupBO = new LarkGroupBO();
                            larkGroupBO.setChatId(group.getChatId());
                            larkGroupBO.setOwnerUserId(group.getOwnerUserId());
                            larkGroupBO.setName(Optional.ofNullable(group.getName()).orElse(""));
                            if ("robot".equals(group.getOwnerName())) {
                                larkGroupBO.setOwnerType(GroupOwnerTypeEnum.ROBOT.getType());
                                larkGroupBO.setOwnerName("");
                            } else {
                                larkGroupBO.setOwnerType(GroupOwnerTypeEnum.PERSON.getType());
                                larkGroupBO.setOwnerName(usernameAndNameMap.getOrDefault(group.getOwnerUserId(), ""));
                            }
                            larkGroupBOS.add(larkGroupBO);
                        }
                );
            } else {
                String message = groupListResponse.getMessage();
                log.info("获取botId = [{}] 的群列表时发生了异常，异常信息为[{}]", botId, message);
                return new BaseResult<BotGroupListResultVO>(ResultCode.SERVER_INNER_ERROR).setMessage(message);
            }
        } catch (Exception e) {
            log.info("获取botId = [{}] 的群列表时发生了异常，异常信息为", botId, e);
        }
        botGroupListResultVO.setGroups(larkGroupBOS);
        return BaseResult.of(botGroupListResultVO);
    }

    @Override
    public BaseResult<IPage<LarkGroupPushInfo>> page(Long page, Long size, String tagId, String key) {
        // page
        Page<LarkGroupPushInfo> dataPage = new Page<>(page, size);

        LambdaQueryWrapper<LarkGroupPushInfo> lambdaQuery = Wrappers.lambdaQuery();

        if (!StringUtils.isNotBlank(tagId)) {
            return BaseResult.of(dataPage);
        }

        lambdaQuery.eq(LarkGroupPushInfo::getTagId, tagId);

        if (StringUtils.isNotBlank(key)) {
            lambdaQuery.and(wrapper -> wrapper.like(LarkGroupPushInfo::getChatName, key));
        }

        lambdaQuery.orderByDesc(LarkGroupPushInfo::getId);

        IPage<LarkGroupPushInfo> larkGroupPushInfoIPage = larkGroupPushInfoMapper.selectPage(dataPage, lambdaQuery);

        List<LarkGroupPushInfo> records = larkGroupPushInfoIPage.getRecords();

        long total = larkGroupPushInfoIPage.getTotal();

        for (int i = 0; i < records.size(); i++) {
            LarkGroupPushInfo larkGroupPushInfo = records.get(i);
            larkGroupPushInfo.setIndexNo((int) (total - size * (page - 1) - i));
        }

        return BaseResult.of(larkGroupPushInfoIPage);
    }

    @Override
    public BaseResult<Object> delete(String tagId, List<Integer> idList) {
        larkGroupPushInfoMapper.delete(
                Wrappers.<LarkGroupPushInfo>lambdaQuery().eq(LarkGroupPushInfo::getTagId, tagId).in(LarkGroupPushInfo::getId, idList)
        );
        return BaseResult.of();
    }

    @Override
    public BaseResult<Object> add(BotGroupPushListAddDTO botGroupPushListAddDTO) {
        if (!StringUtils.isNotBlank(botGroupPushListAddDTO.getTagId())) {
            String tagIdNew = UUID.randomUUID().toString();
            botGroupPushListAddDTO.setTagId(tagIdNew);
        }
        return saveImportData(botGroupPushListAddDTO);
    }

    private BaseResult<Object> saveImportData(BotGroupPushListAddDTO botGroupPushListAddDTO) {
        List<BotGroupPushListAddDTO.BotGroupRecord> botGroupRecordList = botGroupPushListAddDTO.getBotGroupRecordList();
        String tagId = botGroupPushListAddDTO.getTagId();
        List<LarkGroupPushInfo> larkGroupPushInfoList = new ArrayList<>();
        long time = System.currentTimeMillis();
        botGroupRecordList.forEach(
                botGroupRecord -> {
                    LarkGroupPushInfo larkGroupPushInfo = new LarkGroupPushInfo();
                    larkGroupPushInfo.setChatId(botGroupRecord.getChatId());
                    larkGroupPushInfo.setChatName(botGroupRecord.getName());
                    larkGroupPushInfo.setOwnerUserType(botGroupRecord.getOwnerType());
                    larkGroupPushInfo.setOwnerName(botGroupRecord.getOwnerName());
                    larkGroupPushInfo.setOwnerUserId(botGroupRecord.getOwnerUserId());
                    larkGroupPushInfo.setTagId(tagId);
                    larkGroupPushInfo.setCreateTime(time);
                    larkGroupPushInfo.setUpdateTime(time);
                    larkGroupPushInfoList.add(larkGroupPushInfo);
                }
        );
        try {
            larkGroupPushInfoMapper.batchInsert(larkGroupPushInfoList);
        } catch (Exception e) {
            log.info("tagId = [{}]的群组推送插入失败，失败原因", tagId, e);
        }
        return BaseResult.of(botGroupPushListAddDTO.getTagId());
    }

}
