package com.mioffice.ums.admin.service;

import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.bo.UsernameAndNameBO;
import com.mioffice.ums.admin.entity.vo.AppSystemListVO;
import com.mioffice.ums.admin.result.BaseResult;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/18 9:47 上午
 * version: 1.0.0
 */
public interface AppSystemListService {
    /**
     * 获取系统列表页
     *
     * @param userBO
     * @param page
     * @param size
     * @param appName
     * @param managerUsernameList
     * @param applyUsername
     * @param channel
     * @param appSysStatus
     * @param beginDate
     * @param endDate
     * @return
     */
    BaseResult<AppSystemListVO> getAppPage(UserBO userBO, Long page, Long size, String appName, String managerUsernameList, String applyUsername, String channel, String appSysStatus, String beginDate, String endDate);

    BaseResult<Object> appStart(UserBO userBO, Long id);

    BaseResult<Object> appStop(UserBO userBO, Long id);

    BaseResult<Object> appUseApply(UserBO userBO, Long id);

    BaseResult<List<UsernameAndNameBO>> getManagers(UserBO userBO, String searchWord);

    BaseResult<List<UsernameAndNameBO>> getAppApplyUsers(UserBO userBO, String searchWord);
}
