package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mioffice.ums.admin.entity.info.EmailTaskInfo;
import com.mioffice.ums.admin.entity.info.LarkTaskInfo;
import com.mioffice.ums.admin.entity.info.SmsTaskInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.entity.vo.BaseTaskVO;
import com.mioffice.ums.admin.mapper.EmailTaskInfoMapper;
import com.mioffice.ums.admin.mapper.LarkTaskInfoMapper;
import com.mioffice.ums.admin.mapper.SmsTaskInfoMapper;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/19 6:25 下午
 * version: 1.0.0
 */
@Slf4j
@Service
public class TaskServiceImpl extends ServiceImpl<TaskInfoMapper,TaskInfo> implements TaskService {

    @Resource
    private TaskInfoMapper taskInfoMapper;

    @Resource
    private EmailTaskInfoMapper emailTaskInfoMapper;

    @Resource
    private LarkTaskInfoMapper larkTaskInfoMapper;

    @Resource
    private SmsTaskInfoMapper smsTaskInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResult<Object> taskDelete(List<Long> ids) {
        taskInfoMapper.deleteBatchIds(ids);
        List<LarkTaskInfo> larkTaskInfoList = larkTaskInfoMapper.selectList(Wrappers.<LarkTaskInfo>lambdaQuery().in(LarkTaskInfo::getTaskId, ids));
        if (!larkTaskInfoList.isEmpty()) {
            List<Long> larkTaskInfoIdList = larkTaskInfoList.stream().map(LarkTaskInfo::getId).collect(Collectors.toList());
            larkTaskInfoMapper.deleteBatchIds(larkTaskInfoIdList);
        }
        List<EmailTaskInfo> emailTaskInfoList = emailTaskInfoMapper.selectList(Wrappers.<EmailTaskInfo>lambdaQuery().in(EmailTaskInfo::getTaskId, ids));
        if (!emailTaskInfoList.isEmpty()) {
            List<Long> emailTaskInfoIdList = emailTaskInfoList.stream().map(EmailTaskInfo::getId).collect(Collectors.toList());
            emailTaskInfoMapper.deleteBatchIds(emailTaskInfoIdList);
        }
        List<SmsTaskInfo> smsTaskInfoList = smsTaskInfoMapper.selectList(Wrappers.<SmsTaskInfo>lambdaQuery().in(SmsTaskInfo::getTaskId, ids));
        if (!smsTaskInfoList.isEmpty()) {
            List<Long> smsTaskInfoIdList = smsTaskInfoList.stream().map(SmsTaskInfo::getId).collect(Collectors.toList());
            smsTaskInfoMapper.deleteBatchIds(smsTaskInfoIdList);
        }
        log.info("ids = [{}]的批量任务删除成功", ids.toString());
        return BaseResult.of();
    }

    @Override
    public List<String> getTaskExtraIdList(TaskInfo taskInfo) {
        List<String> extraIdList = new ArrayList<>(Collections.singletonList(taskInfo.getExtraId()));
        if (!isRush(taskInfo)) {
            List<String> rushTaskExtraIdList =
                    taskInfoMapper.selectList(
                                    Wrappers.<TaskInfo>lambdaQuery().eq(TaskInfo::getParentTaskId, taskInfo.getId()))
                            .stream()
                            .map(TaskInfo::getExtraId)
                            .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(rushTaskExtraIdList)) {
                extraIdList.addAll(rushTaskExtraIdList);
            }
        }
        return extraIdList;
    }

    @Override
    public boolean isRush(TaskInfo taskInfo) {
        return Objects.nonNull(taskInfo.getParentTaskId()) && taskInfo.getParentTaskId() != 0;
    }

    @Override
    public List<Long> selectTaskIdSystemAdmin(String deptId) {
        return taskInfoMapper.selectTaskIdSystemAdmin(deptId);
    }

    @Override
    public BaseTaskVO selectTaskDetailCommon(Long taskId) {
        return taskInfoMapper.selectTaskDetailCommon(taskId);
    }
}
