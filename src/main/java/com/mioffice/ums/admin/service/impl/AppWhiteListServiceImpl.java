package com.mioffice.ums.admin.service.impl;

import com.mioffice.ums.admin.entity.dto.AddWhiteListDTO;
import com.mioffice.ums.admin.entity.dto.DelWhiteListDTO;
import com.mioffice.ums.admin.entity.dto.QueryAppWhiteListDTO;
import com.mioffice.ums.admin.entity.vo.AppWhiteListPageVo;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.AppWhiteListService;
import com.mioffice.ums.admin.utils.MapperUtil;
import com.xiaomi.info.grpc.client.annotation.RpcClientAutowired;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AddWhiteListRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AddWhiteListResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.DeleteWhiteListRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.DeleteWhiteListResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.OpenServerBlockingClient;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.QueryWhiteListRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.QueryWhiteListResponse;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * AppWhiteListServiceImpl
 *
 * <AUTHOR>
 * @since 2021-12-23
 */
@Service
public class AppWhiteListServiceImpl implements AppWhiteListService {
    @RpcClientAutowired("open-server")
    private OpenServerBlockingClient openServerBlockingClient;

    @Override
    public BaseResult<AppWhiteListPageVo> queryAppWhiteList(QueryAppWhiteListDTO appWhiteListDTO) {
        QueryWhiteListRequest queryWhiteListRequest = MapperUtil.INSTANCE.mapToQueryWhiteListRequest(appWhiteListDTO);
        QueryWhiteListResponse response = openServerBlockingClient.queryAppWhiteList(queryWhiteListRequest);
        if (response.getCode() != ResultCode.OK.getCode()) {
            return new BaseResult<>(response.getCode(), response.getDesc());
        }
        AppWhiteListPageVo appWhiteListPageVo = MapperUtil.INSTANCE.mapToAppWhiteListPageVo(response.getWhiteListPage());
        return BaseResult.of(appWhiteListPageVo);
    }

    @Override
    public BaseResult<Integer> addAppWhiteList(@Valid @RequestBody AddWhiteListDTO addWhiteListDTO) {
        AddWhiteListRequest addWhiteListRequest = MapperUtil.INSTANCE.mapToAddWhiteListRequest(addWhiteListDTO);
        AddWhiteListResponse response = openServerBlockingClient.addAppWhiteList(addWhiteListRequest);
        if (response.getCode() != ResultCode.OK.getCode()) {
            return new BaseResult<>(response.getCode(), response.getDesc());
        }
        return BaseResult.of(response.getCount());
    }

    @Override
    public BaseResult<Integer> delAppWhiteList(DelWhiteListDTO delWhiteListDTO) {
        DeleteWhiteListRequest deleteWhiteListRequest = MapperUtil.INSTANCE.mapToDelWhiteListRequest(delWhiteListDTO);
        DeleteWhiteListResponse response = openServerBlockingClient.deleteAppWhiteList(deleteWhiteListRequest);
        if (response.getCode() != ResultCode.OK.getCode()) {
            return new BaseResult<>(response.getCode(), response.getDesc());
        }
        return BaseResult.of(response.getCount());
    }

}
