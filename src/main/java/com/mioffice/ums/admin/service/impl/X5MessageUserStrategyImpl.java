package com.mioffice.ums.admin.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mioffice.ums.admin.entity.dto.MessageUserDTO;
import com.mioffice.ums.admin.service.MessageUserStrategy;
import com.mioffice.ums.admin.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @create 12 07,2022
 */
@Slf4j
public class X5MessageUserStrategyImpl<T> implements MessageUserStrategy<T> {

    protected String callback;
    protected String appId;
    protected String appSecret;

    private Object param;

    private final int HTTP_SUCCESS_CODE = 200;

    public X5MessageUserStrategyImpl(String callback, String appId, String appSecret, Object param) {
        this.callback = callback;
        this.appId = appId;
        this.appSecret = appSecret;
        this.param = param;
    }

    @Override
    public T fetch() {
        return null;
    }

    public static List<Map<String, Object>> getMessageEntry(String callback) {
        if (StringUtils.isNotBlank(callback)) {
            switch (callback) {
                case "http://www.mi.com/care/birth/personal":
                    return getPersonal();
                case "http://www.mi.com/care/birth/leader/month":
                    return getLeaderMonth();
                case "http://www.mi.com/care/birth/leader/daily":
                    return getLeaderDaily();
            }
        }
        return Lists.newArrayList();
    }

    private static List<Map<String, Object>> getPersonal() {
        List<MessageUserDTO> list = Lists.newArrayList();

        MessageUserDTO dto1 = new MessageUserDTO();
        dto1.setUsername("wangwei77");
        dto1.setMobile("17600777850");
        dto1.setMail("<EMAIL>");
        Map<String, String> param = Maps.newHashMap();
        param.put("name", "王巍");
        param.put("url", "http://www.mi.com");
        param.put("timestamp", UUID.randomUUID().toString());
        dto1.setParam(param);

        MessageUserDTO dto2 = new MessageUserDTO();
        dto2.setUsername("haofeng");
        dto2.setMobile("17600777850");
        dto2.setMail("<EMAIL>");
        Map<String, String> param2 = Maps.newHashMap();
        param2.put("timestamp", UUID.randomUUID().toString());
        param2.put("name", "郝枫");
        dto2.setParam(param2);

        MessageUserDTO dto3 = new MessageUserDTO();
        dto3.setUsername("v-huangjunwei");
        dto3.setMobile("17600777850");
        dto3.setMail("<EMAIL>");
        Map<String, String> param3 = Maps.newHashMap();
        param3.put("name", "黄俊伟");
        param3.put("timestamp", UUID.randomUUID().toString());
        dto3.setParam(param3);

        list.add(dto1);
        list.add(dto2);
        list.add(dto3);
        return JsonUtils.parse(JsonUtils.toJson(list), List.class);
    }

    private static List<Map<String, Object>> getLeaderMonth() {
        List<MessageUserDTO> list = Lists.newArrayList();

        MessageUserDTO dto1 = new MessageUserDTO();
        dto1.setUsername("wangwei77");
        dto1.setMobile("17600777850");
        dto1.setMail("<EMAIL>");
        Map<String, String> param = Maps.newHashMap();
        param.put("birthList", "12月1日 郝枫,黄俊伟\\n12月15日 张三,李四\\n12月31日 小王,小周");
        dto1.setParam(param);

        MessageUserDTO dto2 = new MessageUserDTO();
        dto2.setUsername("haofeng");
        dto2.setMobile("17600777850");
        dto2.setMail("<EMAIL>");
        Map<String, String> param2 = Maps.newHashMap();
        param2.put("birthList", "12月1日 王巍,黄俊伟\\n12月15日 张三,李四,王麻子\\n12月31日 小王,小周");
        param2.put("timestamp", UUID.randomUUID().toString());
        dto2.setParam(param2);

        MessageUserDTO dto3 = new MessageUserDTO();
        dto3.setUsername("v-huangjunwei");
        dto3.setMobile("17600777850");
        dto3.setMail("<EMAIL>");
        Map<String, String> param3 = Maps.newHashMap();
        param3.put("birthList", "12月1日 郝枫,王巍\\n12月15日 张三,李四,王麻子\\n12月31日 大大,达达");
        param3.put("timestamp", UUID.randomUUID().toString());
        dto3.setParam(param3);
        list.add(dto1);
        list.add(dto2);
        list.add(dto3);

        return JsonUtils.parse(JsonUtils.toJson(list), List.class);

    }

    private static List<Map<String, Object>> getLeaderDaily() {
        List<MessageUserDTO> list = Lists.newArrayList();
        MessageUserDTO dto1 = new MessageUserDTO();
        dto1.setUsername("wangwei77");
        dto1.setMobile("17600777850");
        dto1.setMail("<EMAIL>");
        Map<String, String> param = Maps.newHashMap();
        param.put("birthList", "林克,达玛拉");
        param.put("timestamp", UUID.randomUUID().toString());
        dto1.setParam(param);

        MessageUserDTO dto2 = new MessageUserDTO();
        dto2.setUsername("haofeng");
        dto2.setMobile("17600777850");
        dto2.setMail("<EMAIL>");
        Map<String, String> param2 = Maps.newHashMap();
        param2.put("birthList", "王巍,黄俊伟");
        param2.put("timestamp", UUID.randomUUID().toString());
        dto2.setParam(param2);

        MessageUserDTO dto3 = new MessageUserDTO();
        dto3.setUsername("v-huangjunwei");
        dto3.setMobile("17600777850");
        dto3.setMail("<EMAIL>");
        Map<String, String> param3 = Maps.newHashMap();
        param3.put("birthList", "郝枫,王巍");
        param3.put("timestamp", UUID.randomUUID().toString());
        dto3.setParam(param3);
        list.add(dto1);
        list.add(dto2);
        list.add(dto3);
        return JsonUtils.parse(JsonUtils.toJson(list), List.class);
    }

}
