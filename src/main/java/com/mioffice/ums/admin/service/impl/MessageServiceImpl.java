package com.mioffice.ums.admin.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import static com.alibaba.excel.EasyExcelFactory.write;
import static com.alibaba.excel.EasyExcelFactory.writerSheet;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Splitter;
import com.google.gson.JsonArray;
import com.google.gson.JsonParser;
import static com.mioffice.ums.admin.constants.RuleConventionConstants.UNREAD_PUSH_TASK_TITLE_PREFIX_CN;
import static com.mioffice.ums.admin.constants.RuleConventionConstants.UNREAD_PUSH_TASK_TITLE_PREFIX_EN;
import com.mioffice.ums.admin.entity.bo.ExtraContentBO;
import com.mioffice.ums.admin.entity.bo.TaskMessageCustomListExportBO;
import com.mioffice.ums.admin.entity.bo.TaskResultBo;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.BaseTaskDTO;
import com.mioffice.ums.admin.entity.dto.DepartmentSummaryDTO;
import com.mioffice.ums.admin.entity.dto.EmailTaskDTO;
import com.mioffice.ums.admin.entity.dto.LarkTaskDTO;
import com.mioffice.ums.admin.entity.dto.LarkTaskDetailDTO;
import com.mioffice.ums.admin.entity.dto.MessagePrePushDTO;
import com.mioffice.ums.admin.entity.dto.MyTaskRecordDTO;
import com.mioffice.ums.admin.entity.dto.PushResultDTO;
import com.mioffice.ums.admin.entity.dto.ScopeKeyDeptsDTO;
import com.mioffice.ums.admin.entity.dto.SendUsersDTO;
import com.mioffice.ums.admin.entity.dto.SmsTaskDTO;
import com.mioffice.ums.admin.entity.dto.TaskDetailCustomListDTO;
import com.mioffice.ums.admin.entity.dto.TaskDetailDeptListDTO;
import com.mioffice.ums.admin.entity.dto.TaskHistoryPageDTO;
import com.mioffice.ums.admin.entity.dto.TaskHistoryPageRecordDTO;
import com.mioffice.ums.admin.entity.dto.TaskMessageCustomListRecordDTO;
import com.mioffice.ums.admin.entity.dto.TaskMessageDeptListRecordDTO;
import com.mioffice.ums.admin.entity.dto.TaskMessageDeptListRecordExportDTO;
import com.mioffice.ums.admin.entity.dto.TaskMessageLarkGroupListRecordDTO;
import com.mioffice.ums.admin.entity.dto.TaskResultDTO;
import com.mioffice.ums.admin.entity.dto.TaskSummaryDTO;
import com.mioffice.ums.admin.entity.info.BotInfo;
import com.mioffice.ums.admin.entity.info.DepartmentInfo;
import com.mioffice.ums.admin.entity.info.EmailTaskInfo;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.LarkGroupPushInfo;
import com.mioffice.ums.admin.entity.info.LarkTaskInfo;
import com.mioffice.ums.admin.entity.info.ParseTmpInfo;
import com.mioffice.ums.admin.entity.info.PrePushListInfo;
import com.mioffice.ums.admin.entity.info.PublishScopeInfo;
import com.mioffice.ums.admin.entity.info.SmsTaskInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.entity.info.UserBotInfo;
import com.mioffice.ums.admin.entity.vo.BaseTaskVO;
import com.mioffice.ums.admin.entity.vo.TaskDetailLarkGroupListVO;
import com.mioffice.ums.admin.entity.vo.TaskEmailDetailVO;
import com.mioffice.ums.admin.entity.vo.TaskLarkDetailVO;
import com.mioffice.ums.admin.entity.vo.TaskSmsDetailVO;
import com.mioffice.ums.admin.enums.BotTenantEnum;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.HrEmployeeTypeEnum;
import com.mioffice.ums.admin.enums.LarkButtonEnum;
import com.mioffice.ums.admin.enums.LarkRobotStatusEnum;
import com.mioffice.ums.admin.enums.ParseDataStatusEnum;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.enums.TaskChannelEnum;
import com.mioffice.ums.admin.enums.TaskStatusEnum;
import com.mioffice.ums.admin.enums.TaskSubmitTypeEnum;
import com.mioffice.ums.admin.enums.UmsRobotStatusEnum;
import com.mioffice.ums.admin.enums.UserBotRoleEnum;
import com.mioffice.ums.admin.manager.DeptManager;
import com.mioffice.ums.admin.manager.IdManager;
import com.mioffice.ums.admin.manager.MiCloudFdsManager;
import com.mioffice.ums.admin.manager.PermissionManager;
import com.mioffice.ums.admin.manager.ProcessManager;
import com.mioffice.ums.admin.manager.SendMessage;
import com.mioffice.ums.admin.manager.UserRoleManager;
import com.mioffice.ums.admin.manager.warning.TaskMonitorManager;
import com.mioffice.ums.admin.mapper.BotInfoMapper;
import com.mioffice.ums.admin.mapper.DepartmentInfoMapper;
import com.mioffice.ums.admin.mapper.EmailTaskInfoMapper;
import com.mioffice.ums.admin.mapper.LarkGroupPushInfoMapper;
import com.mioffice.ums.admin.mapper.LarkTaskInfoMapper;
import com.mioffice.ums.admin.mapper.ParseTmpInfoMapper;
import com.mioffice.ums.admin.mapper.PrePushListInfoMapper;
import com.mioffice.ums.admin.mapper.PublishScopeInfoMapper;
import com.mioffice.ums.admin.mapper.SmsTaskInfoMapper;
import com.mioffice.ums.admin.mapper.UserBotInfoMapper;
import com.mioffice.ums.admin.message.member.ChooseIdEnum;
import com.mioffice.ums.admin.process.MessageProcess;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.EmployeeService;
import com.mioffice.ums.admin.service.MessageService;
import com.mioffice.ums.admin.service.TaskService;
import com.mioffice.ums.admin.utils.JsonUtils;
import com.mioffice.ums.admin.utils.MapperUtil;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.CommonResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.GroupListResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.GroupRecord;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageGroupDeptResultPage;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageGroupDeptResultRecord;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageNumberAndTime;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageNumberAndTimeResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageReadResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageRecord;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageResultSingleForCustomResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageResultSingleForDeptResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUnReadDetailResponse;
import com.xiaomi.infra.galaxy.fds.model.FDSObjectMetadata;
import static java.util.stream.Collectors.groupingBy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/13 7:57 下午
 * version: 1.0.0
 */
@Service
@Slf4j
public class MessageServiceImpl implements MessageService {

    @NacosValue(value = "${ums.admin.bpm-start-taskid:}", autoRefreshed = true)
    private String bpmStartTaskId;

    @NacosValue(value = "${ums.admin.executives:}", autoRefreshed = true)
    private String executives;

    @NacosValue(value = "${ums.admin.rush-time-limit-switch:}", autoRefreshed = true)
    private String rushTimeLimitSwitch;

    @Value("${lark.appId}")
    private String umsBotAppId;

    private static final String UMS_ADMIN_PATH_STR = "umsAdmin/";
    private static final String MS_EXCEL_HEADER = "application/vnd.ms-excel";
    private static final String EXCEL_EXTENSION_STR = ".xlsx";
    private final IdManager idManager;
    private final LarkTaskInfoMapper larkTaskInfoMapper;
    private final EmailTaskInfoMapper emailTaskInfoMapper;
    private final SmsTaskInfoMapper smsTaskInfoMapper;
    private final PublishScopeInfoMapper publishScopeInfoMapper;
    private final MessageGrpcClient messageGrpcClient;
    private final SendMessage sendMessage;
    private final DepartmentInfoMapper departmentInfoMapper;
    private final BotInfoMapper botInfoMapper;
    private final UserBotInfoMapper userBotInfoMapper;
    private final ParseTmpInfoMapper parseTmpInfoMapper;
    private final ProcessManager processManager;
    private final MiCloudFdsManager miCloudFdsManager;
    private final DeptManager deptManager;
    private final UserRoleManager userRoleManager;
    private final PermissionManager permissionManager;
    /**
     * 消息发布结果 callback
     */
    private final Consumer<TaskResultBo> onSendMessageResultTask;
    private final PrePushListInfoMapper prePushListInfoMapper;
    private final MessageProcess messageProcess;
    private final LarkGroupPushInfoMapper larkGroupPushInfoMapper;
    private final TaskService taskService;
    private final EmployeeService employeeService;

    public MessageServiceImpl(
            IdManager idManager,
            LarkTaskInfoMapper larkTaskInfoMapper,
            PublishScopeInfoMapper publishScopeInfoMapper,
            MessageGrpcClient messageGrpcClient,
            SendMessage sendMessage,
            ProcessManager processManager,
            ParseTmpInfoMapper parseTmpInfoMapper,
            UserBotInfoMapper userBotInfoMapper,
            DepartmentInfoMapper departmentInfoMapper,
            BotInfoMapper botInfoMapper,
            MiCloudFdsManager miCloudFdsManager,
            DeptManager deptManager,
            PrePushListInfoMapper prePushListInfoMapper,
            SmsTaskInfoMapper smsTaskInfoMapper,
            EmailTaskInfoMapper emailTaskInfoMapper,
            UserRoleManager userRoleManager,
            PermissionManager permissionManager,
            MessageProcess messageProcess,
            TaskMonitorManager taskMonitorManager,
            LarkGroupPushInfoMapper larkGroupPushInfoMapper,
            TaskService taskService,
            EmployeeService employeeService) {
        this.idManager = idManager;
        this.larkTaskInfoMapper = larkTaskInfoMapper;
        this.publishScopeInfoMapper = publishScopeInfoMapper;
        this.messageGrpcClient = messageGrpcClient;
        this.sendMessage = sendMessage;
        this.userRoleManager = userRoleManager;
        this.permissionManager = permissionManager;
        this.taskService = taskService;
        this.employeeService = employeeService;

        onSendMessageResultTask = taskResultBo -> {
            TaskInfo updateTaskInfo = TaskInfo.newUpdateTimeInstant();
            updateTaskInfo.setId(taskResultBo.getTaskId());
            updateTaskInfo.setExtraId(taskResultBo.getExtraId());
            updateTaskInfo.setTaskEngineStatus(TaskStatusEnum.PUBLISHING.getCode());
            updateTaskInfo.setTaskStatus(taskResultBo.getTaskStatusEnum().getCode());
            updateTaskInfo.setErrorLog(taskResultBo.getMsg());

            // add to task monitor
            if (TaskStatusEnum.PUBLISH_SUCCESS.equals(taskResultBo.getTaskStatusEnum())) {
                log.info("任务进入预警监控队列，extraId = [{}]", updateTaskInfo.getExtraId());
                taskMonitorManager.put(updateTaskInfo);
                updateTaskInfo.setPublishSuccessTime(System.currentTimeMillis());
            }
            taskService.updateById(updateTaskInfo);
        };

        this.departmentInfoMapper = departmentInfoMapper;
        this.botInfoMapper = botInfoMapper;
        this.processManager = processManager;
        this.parseTmpInfoMapper = parseTmpInfoMapper;
        this.userBotInfoMapper = userBotInfoMapper;
        this.miCloudFdsManager = miCloudFdsManager;
        this.deptManager = deptManager;
        this.prePushListInfoMapper = prePushListInfoMapper;
        this.emailTaskInfoMapper = emailTaskInfoMapper;
        this.smsTaskInfoMapper = smsTaskInfoMapper;
        this.messageProcess = messageProcess;
        this.larkGroupPushInfoMapper = larkGroupPushInfoMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResult<TaskResultDTO> createLarkTask(UserBO userBO, LarkTaskDTO larkTaskDTO) {

        // 判断机器人是否可用
        assertBotStatusOn(larkTaskDTO.getBotId());

        TaskInfo taskInfo = this.makeNewTaskInfo(userBO, larkTaskDTO, TaskChannelEnum.CHANNEL_LARK);
        taskService.save(taskInfo);

        LarkTaskInfo larkTaskInfo = this.makeNewLarkTaskInfo(taskInfo.getId(), larkTaskDTO);
        larkTaskInfoMapper.insert(larkTaskInfo);

        // 根据范围判断是否要入  publish_scope_info
        this.addPublishScope(taskInfo.getId(), larkTaskDTO);
        calAllCount(taskInfo.getId(), PublishScopeEnum.getByCode(taskInfo.getPublishScope()), null);

        if (larkTaskDTO.getSubmitType() == TaskSubmitTypeEnum.SUBMIT.getType()) {
            if (!messageProcess.createProcess(TaskChannelEnum.CHANNEL_LARK, taskInfo, userBO.getUsername())) {
                throw new IllegalArgumentException("创建审批审批失败");
            }
        }

        TaskResultDTO taskResultDTO = new TaskResultDTO();
        taskResultDTO.setTaskId(taskInfo.getId());
        taskResultDTO.setTaskStatus(taskInfo.getTaskStatus());

        return BaseResult.of(taskResultDTO);
    }

    @Override
    public BaseResult<TaskResultDTO> createEmailTask(UserBO userBO, EmailTaskDTO emailTaskDTO) {

        TaskInfo taskInfo = this.makeNewTaskInfo(userBO, emailTaskDTO, TaskChannelEnum.CHANNEL_EMAIL);
        taskService.save(taskInfo);

        EmailTaskInfo emailTaskInfo = this.makeNewEmailTaskInfo(taskInfo.getId(), emailTaskDTO);
        emailTaskInfoMapper.insert(emailTaskInfo);

        // 根据范围判断是否要入  publish_scope_info
        this.addPublishScope(taskInfo.getId(), emailTaskDTO);
        calAllCount(taskInfo.getId(), PublishScopeEnum.getByCode(taskInfo.getPublishScope()), null);

        if (emailTaskDTO.getSubmitType() == TaskSubmitTypeEnum.SUBMIT.getType()) {
            if (!messageProcess.createProcess(TaskChannelEnum.CHANNEL_EMAIL, taskInfo, userBO.getUsername())) {
                throw new IllegalArgumentException("创建审批审批失败");
            }
        }

        TaskResultDTO taskResultDTO = new TaskResultDTO();
        taskResultDTO.setTaskId(taskInfo.getId());
        taskResultDTO.setTaskStatus(taskInfo.getTaskStatus());

        return BaseResult.of(taskResultDTO);
    }

    @Override
    public BaseResult<TaskResultDTO> createSmsTask(UserBO userBO, SmsTaskDTO smsTaskDTO) {

        TaskInfo taskInfo = this.makeNewTaskInfo(userBO, smsTaskDTO, TaskChannelEnum.CHANNEL_SMS);
        taskService.save(taskInfo);

        SmsTaskInfo smsTaskInfo = this.makeNewSmsTaskInfo(taskInfo.getId(), smsTaskDTO);
        smsTaskInfoMapper.insert(smsTaskInfo);

        // 根据范围判断是否要入  publish_scope_info
        this.addPublishScope(taskInfo.getId(), smsTaskDTO);
        calAllCount(taskInfo.getId(), PublishScopeEnum.getByCode(taskInfo.getPublishScope()), null);

        if (smsTaskDTO.getSubmitType() == TaskSubmitTypeEnum.SUBMIT.getType()) {
            if (!messageProcess.createProcess(TaskChannelEnum.CHANNEL_SMS, taskInfo, userBO.getUsername())) {
                throw new IllegalArgumentException("创建审批审批失败");
            }
        }

        TaskResultDTO taskResultDTO = new TaskResultDTO();
        taskResultDTO.setTaskId(taskInfo.getId());
        taskResultDTO.setTaskStatus(taskInfo.getTaskStatus());

        return BaseResult.of(taskResultDTO);
    }

    private void assertBotStatusOn(Long botId) {
        BotInfo botInfo = botInfoMapper.selectById(botId);
        if (Objects.isNull(botInfo) ||
                UmsRobotStatusEnum.getByCode(botInfo.getBotLarkStatus()) != UmsRobotStatusEnum.UMS_ROBOT_ON) {
            throw new IllegalArgumentException("机器人未启用或不存在");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResult<TaskResultDTO> updateLarkTask(UserBO userBO, LarkTaskDTO larkTaskDTO) {

        // 判断消息是否发布
        assertMessageStatusAndBelong(larkTaskDTO.getTaskId(), userBO.getUsername());

        TaskInfo updateTaskInfo = this.makeUpdateTaskInfo(userBO, larkTaskDTO, TaskChannelEnum.CHANNEL_LARK);
        boolean res = taskService.updateById(updateTaskInfo);
        if (!res) {
            throw new IllegalArgumentException("任务不存在，更新失败");
        }

        LarkTaskInfo updateLarkTaskInfo = this.makeUpdateLarkTaskInfo(larkTaskDTO);
        larkTaskInfoMapper.update(
                updateLarkTaskInfo,
                Wrappers.<LarkTaskInfo>lambdaUpdate().eq(LarkTaskInfo::getTaskId, larkTaskDTO.getTaskId())
        );

        // 机器人是否可用
        LarkTaskInfo larkTaskInfo = larkTaskInfoMapper.selectOne(
                Wrappers.<LarkTaskInfo>lambdaQuery().eq(LarkTaskInfo::getTaskId, larkTaskDTO.getTaskId()));
        assertBotStatusOn(larkTaskInfo.getBotId());

        // 根据范围判断是否要入  publish_scope_info
        this.updatePublishScope(larkTaskDTO.getTaskId(), larkTaskDTO);
        calAllCount(larkTaskDTO.getTaskId(), PublishScopeEnum.getByCode(larkTaskDTO.getPublishScope()), null);

        if (larkTaskDTO.getSubmitType() == TaskSubmitTypeEnum.SUBMIT.getType()) {
            TaskInfo taskInfo = taskService.getById(larkTaskDTO.getTaskId());

            // 中断现有审批
            CompletableFuture.runAsync(() -> {
                if (!messageProcess.isApprovalFinish(taskInfo)) {
                    if (!messageProcess.terminate(userBO.getUsername(), taskInfo)) {
                        log.error("取消审批失败");
                    }
                }
            });

            // 创建新审批
            taskInfo.setBpmInstanceId(idManager.createExtraId());
            if (!messageProcess.createProcess(TaskChannelEnum.CHANNEL_LARK, taskInfo, userBO.getUsername())) {
                throw new IllegalArgumentException("创建审批失败");
            }
            taskInfo.setTaskStatus(TaskStatusEnum.APPROVING.getCode());
            taskService.updateById(taskInfo);
        }

        TaskResultDTO taskResultDTO = new TaskResultDTO();
        taskResultDTO.setTaskId(updateTaskInfo.getId());
        taskResultDTO.setTaskStatus(updateTaskInfo.getTaskStatus());

        return BaseResult.of(taskResultDTO);

    }

    @Override
    public BaseResult<TaskResultDTO> updateEmailTask(UserBO userBO, EmailTaskDTO emailTaskDTO) {

        // 判断消息是否发布
        assertMessageStatusAndBelong(emailTaskDTO.getTaskId(), userBO.getUsername());

        TaskInfo updateTaskInfo = this.makeUpdateTaskInfo(userBO, emailTaskDTO, TaskChannelEnum.CHANNEL_EMAIL);
        boolean res = taskService.updateById(updateTaskInfo);
        if (!res) {
            throw new IllegalArgumentException("任务不存在，更新失败");
        }

        EmailTaskInfo updateLarkTaskInfo = this.makeUpdateEmailTaskInfo(emailTaskDTO);
        emailTaskInfoMapper.update(
                updateLarkTaskInfo,
                Wrappers.<EmailTaskInfo>lambdaUpdate().eq(EmailTaskInfo::getTaskId, emailTaskDTO.getTaskId())
        );

        // 根据范围判断是否要入  publish_scope_info
        this.updatePublishScope(emailTaskDTO.getTaskId(), emailTaskDTO);
        calAllCount(emailTaskDTO.getTaskId(), PublishScopeEnum.getByCode(emailTaskDTO.getPublishScope()), null);

        if (emailTaskDTO.getSubmitType() == TaskSubmitTypeEnum.SUBMIT.getType()) {
            TaskInfo taskInfo = taskService.getById(emailTaskDTO.getTaskId());

            // 中断现有审批
            CompletableFuture.runAsync(() -> {
                if (!messageProcess.isApprovalFinish(taskInfo)) {
                    if (!messageProcess.terminate(userBO.getUsername(), taskInfo)) {
                        log.error("取消审批失败");
                    }
                }
            });

            // 创建新审批
            taskInfo.setBpmInstanceId(idManager.createExtraId());
            if (!messageProcess.createProcess(TaskChannelEnum.CHANNEL_EMAIL, taskInfo, userBO.getUsername())) {
                throw new IllegalArgumentException("创建审批审批失败");
            }
            taskInfo.setTaskStatus(TaskStatusEnum.APPROVING.getCode());
            taskService.updateById(taskInfo);
        }

        TaskResultDTO taskResultDTO = new TaskResultDTO();
        taskResultDTO.setTaskId(updateTaskInfo.getId());
        taskResultDTO.setTaskStatus(updateTaskInfo.getTaskStatus());

        return BaseResult.of(taskResultDTO);
    }

    @Override
    public BaseResult<TaskResultDTO> updateSmsTask(UserBO userBO, SmsTaskDTO smsTaskDTO) {

        // 判断消息是否已发
        assertMessageStatusAndBelong(smsTaskDTO.getTaskId(), userBO.getUsername());

        TaskInfo updateTaskInfo = this.makeUpdateTaskInfo(userBO, smsTaskDTO, TaskChannelEnum.CHANNEL_SMS);
        boolean res = taskService.updateById(updateTaskInfo);
        if (!res) {
            throw new IllegalArgumentException("任务不存在，更新失败");
        }

        SmsTaskInfo updateSmsTaskInfo = this.makeUpdateSmsTaskInfo(smsTaskDTO);
        smsTaskInfoMapper.update(
                updateSmsTaskInfo,
                Wrappers.<SmsTaskInfo>lambdaUpdate().eq(SmsTaskInfo::getTaskId, smsTaskDTO.getTaskId())
        );

        // 根据范围判断是否要入  publish_scope_info
        this.updatePublishScope(smsTaskDTO.getTaskId(), smsTaskDTO);
        calAllCount(smsTaskDTO.getTaskId(), PublishScopeEnum.getByCode(smsTaskDTO.getPublishScope()), null);

        if (smsTaskDTO.getSubmitType() == TaskSubmitTypeEnum.SUBMIT.getType()) {
            TaskInfo taskInfo = taskService.getById(smsTaskDTO.getTaskId());

            // 中断现有审批
            CompletableFuture.runAsync(() -> {
                if (!messageProcess.isApprovalFinish(taskInfo)) {
                    if (!messageProcess.terminate(userBO.getUsername(), taskInfo)) {
                        log.error("取消审批失败");
                    }
                }
            });

            // 创建新审批
            taskInfo.setBpmInstanceId(idManager.createExtraId());
            if (!messageProcess.createProcess(TaskChannelEnum.CHANNEL_SMS, taskInfo, userBO.getUsername())) {
                throw new IllegalArgumentException("创建新审批失败");
            }
            taskInfo.setTaskStatus(TaskStatusEnum.APPROVING.getCode());
            taskService.updateById(taskInfo);
        }

        TaskResultDTO taskResultDTO = new TaskResultDTO();
        taskResultDTO.setTaskId(updateTaskInfo.getId());
        taskResultDTO.setTaskStatus(updateTaskInfo.getTaskStatus());

        return BaseResult.of(taskResultDTO);
    }

    @Override
    public BaseResult<TaskResultDTO> rushLarkTask(UserBO userBO, LarkTaskDTO larkTaskDTO) {
        //高管名单
        List<String> executiveUsernames = Splitter.on(",").splitToList(executives);
        List<String> excludeUsernames = new ArrayList<>(executiveUsernames);
        long allCount = 0L;

        if (PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE == PublishScopeEnum.getByCode(larkTaskDTO.getPublishScope())) {
            String scopeKey = CollectionUtils.isNotEmpty(larkTaskDTO.getScopeKey()) ? larkTaskDTO.getScopeKey().get(0) :
                    StringUtils.EMPTY;
            if (StringUtils.isNotBlank(scopeKey)) {
                allCount = parseTmpInfoMapper.selectCount(
                        Wrappers.<ParseTmpInfo>lambdaQuery()
                                .eq(ParseTmpInfo::getExcelId, scopeKey)
                                .eq(ParseTmpInfo::getPublishScope, PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode())
                                .in(ParseTmpInfo::getDataStatus, ParseDataStatusEnum.WAIT_PROCESS.getCode(),
                                        ParseDataStatusEnum.PROCESSED.getCode())
                );
            }
        } else if (PublishScopeEnum.CUSTOM_PUSH_MANAGER ==
                PublishScopeEnum.getByCode(larkTaskDTO.getPublishScope())) {
            String scopeKey = CollectionUtils.isNotEmpty(larkTaskDTO.getScopeKey()) ? larkTaskDTO.getScopeKey().get(0) :
                    StringUtils.EMPTY;
            if (StringUtils.isNotBlank(scopeKey)) {
                List<SendUsersDTO> subList = parseTmpInfoMapper.selectList(
                                Wrappers.<ParseTmpInfo>lambdaQuery()
                                        .eq(ParseTmpInfo::getExcelId, scopeKey)
                                        .eq(ParseTmpInfo::getPublishScope, PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode())
                                        .in(ParseTmpInfo::getDataStatus, ParseDataStatusEnum.WAIT_PROCESS.getCode(),
                                                ParseDataStatusEnum.PROCESSED.getCode())
                        )
                        .stream()
                        .map(MapperUtil.INSTANCE::parseTmpInfo2SendUsersDTO)
                        .collect(Collectors.toList());
                allCount = employeeService.convert2LeaderList(subList).size();
            }
        } else if (PublishScopeEnum.UNREAD_PUSH == PublishScopeEnum.getByCode(larkTaskDTO.getPublishScope())) {
            TaskInfo parentTaskInfo = taskService.getById(larkTaskDTO.getTaskId());

            String scopeKey = CollectionUtils.isNotEmpty(larkTaskDTO.getScopeKey()) ? larkTaskDTO.getScopeKey().get(0) :
                    StringUtils.EMPTY;
            if (StringUtils.isNotBlank(scopeKey)) {
                Set<String> unreadExcludeUsernames = parseTmpInfoMapper.selectList(
                        Wrappers.<ParseTmpInfo>lambdaQuery()
                                .eq(ParseTmpInfo::getExcelId, scopeKey)
                                .eq(ParseTmpInfo::getPublishScope, PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode())
                                .in(ParseTmpInfo::getDataStatus, ParseDataStatusEnum.WAIT_PROCESS.getCode(),
                                        ParseDataStatusEnum.PROCESSED.getCode())
                ).stream().map(ParseTmpInfo::getUsername).collect(Collectors.toSet());
                if (!unreadExcludeUsernames.isEmpty()) {
                    excludeUsernames.addAll(unreadExcludeUsernames);
                }
            }

            List<String> subExtraIdList =
                    taskService.list(Wrappers.<TaskInfo>lambdaQuery().eq(TaskInfo::getParentTaskId,
                                    parentTaskInfo.getId()))
                            .stream()
                            .map(TaskInfo::getExtraId)
                            .collect(Collectors.toList());

            MessageUnReadDetailResponse messageUnReadDetailResponse =
                    messageGrpcClient.getPageUnRead(StringUtils.EMPTY,
                            1L,
                            10L,
                            parentTaskInfo.getExtraId(),
                            subExtraIdList,
                            excludeUsernames);
            allCount = messageUnReadDetailResponse.getMessageUnreadDetail().getTotal();
        }

        Assert.isTrue(allCount > 0L, "推送人数为0，请修改!");
        // 判断机器人是否可用
        assertBotStatusOn(larkTaskDTO.getBotId());
        TaskInfo taskInfo = this.makeNewTaskInfo(userBO, larkTaskDTO, TaskChannelEnum.CHANNEL_LARK);
        // 设置父任务ID
        taskInfo.setParentTaskId(larkTaskDTO.getTaskId());
        taskService.save(taskInfo);

        LarkTaskInfo larkTaskInfo = this.makeNewLarkTaskInfo(taskInfo.getId(), larkTaskDTO);
        larkTaskInfoMapper.insert(larkTaskInfo);

        addPublishScope(taskInfo.getId(), larkTaskDTO);
        calAllCount(taskInfo.getId(), PublishScopeEnum.getByCode(taskInfo.getPublishScope()), allCount);

        if (larkTaskDTO.getSubmitType() == TaskSubmitTypeEnum.SUBMIT.getType()) {
            if (!messageProcess.createProcess(TaskChannelEnum.CHANNEL_LARK, taskInfo, userBO.getUsername())) {
                throw new IllegalArgumentException("创建审批审批失败");
            }
        }

        TaskResultDTO taskResultDTO = new TaskResultDTO();
        taskResultDTO.setTaskId(taskInfo.getId());
        taskResultDTO.setTaskStatus(taskInfo.getTaskStatus());

        return BaseResult.of(taskResultDTO);
    }

    /**
     * 计算总的人数
     */
    private boolean calAllCount(Long taskId, PublishScopeEnum publishScopeEnum, Long count) {
        boolean scopeChanged = false;
        //高管名单
        List<String> executiveUsernames = Splitter.on(",").splitToList(executives);

        long allCount = 0L;

        List<PublishScopeInfo> scopeInfoList = publishScopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery()
                        .eq(PublishScopeInfo::getTaskId, taskId)
        );

        if (scopeInfoList.isEmpty()) {
            scopeChanged = false;
        }

        boolean excludePartner = true;
        LarkTaskInfo larkTaskInfo =
                larkTaskInfoMapper.selectOne(Wrappers.<LarkTaskInfo>lambdaQuery().eq(LarkTaskInfo::getTaskId, taskId));
        if (Objects.nonNull(larkTaskInfo)) {
            BotInfo botInfo = botInfoMapper.selectById(larkTaskInfo.getBotId());
            BotTenantEnum botTenantEnum = BotTenantEnum.getByCode(botInfo.getBotTenant());
            if (BotTenantEnum.BOT_TENANT_PARTNER == botTenantEnum) {
                excludePartner = false;
            }
        }

        List<String> scopeKeyList =
                scopeInfoList.stream().map(PublishScopeInfo::getScopeKey).collect(Collectors.toList());

        switch (publishScopeEnum) {
            case ALL_PUSH:
                LambdaQueryWrapper<EmployeeInfo> lambdaQueryWrapper = Wrappers.<EmployeeInfo>lambdaQuery()
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                        .ne(EmployeeInfo::getUsername, "");
                if (excludePartner) {
                    lambdaQueryWrapper.ne(EmployeeInfo::getEmpType, HrEmployeeTypeEnum.PARTNER.getCode());
                } else {
                    lambdaQueryWrapper.eq(EmployeeInfo::getEmpType, HrEmployeeTypeEnum.PARTNER.getCode());
                }
                allCount = employeeService.count(lambdaQueryWrapper);
                if (scopeInfoList.get(0).getAllCount() != allCount) {
                    scopeInfoList.get(0).setAllCount(allCount);
                    scopeChanged = true;
                }
                break;
            case DEPT_PUSH:
                List<DepartmentSummaryDTO> deptInfoList = deptManager.getDeptInfo(scopeKeyList, excludePartner);
                Map<String, DepartmentSummaryDTO> deptIdMap = deptInfoList.stream().collect(
                        Collectors.toMap(DepartmentSummaryDTO::getDeptId, Function.identity(), (v1, v2) -> v2));
                for (PublishScopeInfo publishScopeInfo : scopeInfoList) {
                    DepartmentSummaryDTO departmentSummaryDTO = deptIdMap.get(publishScopeInfo.getScopeKey());
                    if (Objects.nonNull(departmentSummaryDTO)) {
                        if (!publishScopeInfo.getAllCount().equals(departmentSummaryDTO.getCount())) {
                            publishScopeInfo.setAllCount(departmentSummaryDTO.getCount());
                            scopeChanged = true;
                        }
                    }
                }
                break;
            case CUSTOM_PUSH_EMPLOYEE:
                if (Objects.isNull(count)) {
                    allCount = parseTmpInfoMapper.selectList(
                                    Wrappers.<ParseTmpInfo>lambdaQuery()
                                            .eq(ParseTmpInfo::getExcelId, scopeKeyList.get(0))
                                            .eq(ParseTmpInfo::getPublishScope, PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode())
                                            .in(ParseTmpInfo::getDataStatus, ParseDataStatusEnum.WAIT_PROCESS.getCode(),
                                                    ParseDataStatusEnum.PROCESSED.getCode())
                            ).stream().filter(parseTmpInfo -> employeeService.count(Wrappers.<EmployeeInfo>lambdaQuery()
                                    .eq(EmployeeInfo::getUsername, parseTmpInfo.getUsername())) > 0)
                            .count();
                } else {
                    allCount = count;
                }
                if (scopeInfoList.get(0).getAllCount() != allCount) {
                    scopeInfoList.get(0).setAllCount(allCount);
                    scopeChanged = true;
                }
                break;
            case CHOOSE_PUSH:
                Map<Integer, Consumer<LambdaQueryWrapper<EmployeeInfo>>> idAndQueryMap = scopeKeyList.stream()
                        .map(Integer::valueOf)
                        .map(ChooseIdEnum::getById)
                        .collect(Collectors.toMap(ChooseIdEnum::getId, ChooseIdEnum::getConsumer, (v1, v2) -> v2));

                Map<Integer, Long> idAndCount = new HashMap<>();
                for (Map.Entry<Integer, Consumer<LambdaQueryWrapper<EmployeeInfo>>> entry : idAndQueryMap.entrySet()) {
                    LambdaQueryWrapper<EmployeeInfo> lambdaQueryWrapper1 = Wrappers.<EmployeeInfo>lambdaQuery()
                            .ne(EmployeeInfo::getUsername, "")
                            .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                            .and(entry.getValue());
                    if (excludePartner) {
                        lambdaQueryWrapper1.ne(EmployeeInfo::getEmpType, HrEmployeeTypeEnum.PARTNER.getCode());
                    } else {
                        lambdaQueryWrapper1.eq(EmployeeInfo::getEmpType, HrEmployeeTypeEnum.PARTNER.getCode());
                    }
                    idAndCount.put(entry.getKey(), (long) employeeService.count(lambdaQueryWrapper1));
                }

                for (PublishScopeInfo publishScopeInfo : scopeInfoList) {
                    Integer scopeKey = Integer.valueOf(publishScopeInfo.getScopeKey());
                    if (idAndCount.containsKey(scopeKey)) {
                        if (!publishScopeInfo.getAllCount().equals(idAndCount.get(scopeKey))) {
                            publishScopeInfo.setAllCount(idAndCount.get(scopeKey));
                            scopeChanged = true;
                        }
                    }
                }
                break;
            case GROUP_PUSH:
                allCount = larkGroupPushInfoMapper.selectCount(
                        Wrappers.<LarkGroupPushInfo>lambdaQuery()
                                .eq(LarkGroupPushInfo::getTagId, scopeInfoList.get(0).getScopeKey())
                );
                if (scopeInfoList.get(0).getAllCount() != allCount) {
                    scopeInfoList.get(0).setAllCount(allCount);
                    scopeChanged = true;
                }
                break;
            case CUSTOM_PUSH_MANAGER:
                if (Objects.isNull(count)) {
                    List<SendUsersDTO> subList = parseTmpInfoMapper.selectList(
                                    Wrappers.<ParseTmpInfo>lambdaQuery()
                                            .eq(ParseTmpInfo::getExcelId, scopeKeyList.get(0))
                                            .eq(ParseTmpInfo::getPublishScope, PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode())
                                            .in(ParseTmpInfo::getDataStatus, ParseDataStatusEnum.WAIT_PROCESS.getCode(),
                                                    ParseDataStatusEnum.PROCESSED.getCode())
                            )
                            .stream()
                            .filter(parseTmpInfo -> employeeService.count(Wrappers.<EmployeeInfo>lambdaQuery()
                                    .eq(EmployeeInfo::getUsername, parseTmpInfo.getUsername())) > 0)
                            .map(MapperUtil.INSTANCE::parseTmpInfo2SendUsersDTO)
                            .collect(Collectors.toList());
                    allCount = employeeService.convert2LeaderList(subList).size();
                } else {
                    allCount = count;
                }
                if (scopeInfoList.get(0).getAllCount() != allCount) {
                    scopeInfoList.get(0).setAllCount(allCount);
                    scopeChanged = true;
                }
                break;
            case UNREAD_PUSH:
                if (Objects.isNull(count)) {
                    TaskInfo rushTaskInfo = taskService.getById(taskId);
                    if (Objects.isNull(rushTaskInfo.getParentTaskId())) {
                        log.error("parent taskId null");
                        return false;
                    }
                    TaskInfo parentTaskInfo = taskService.getById(rushTaskInfo.getParentTaskId());
                    if (Objects.isNull(parentTaskInfo)) {
                        log.error("parent taskInfo null");
                        return false;
                    }
                    List<String> excludeUsernames = new ArrayList<>(executiveUsernames);
                    if (StringUtils.isNotBlank(scopeInfoList.get(0).getScopeKey())) {
                        Set<String> unreadExcludeUsernames = parseTmpInfoMapper.selectList(
                                Wrappers.<ParseTmpInfo>lambdaQuery()
                                        .eq(ParseTmpInfo::getExcelId, scopeInfoList.get(0).getScopeKey())
                                        .eq(ParseTmpInfo::getPublishScope,
                                                PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode())
                                        .in(ParseTmpInfo::getDataStatus, ParseDataStatusEnum.WAIT_PROCESS.getCode(),
                                                ParseDataStatusEnum.PROCESSED.getCode())
                        ).stream().map(ParseTmpInfo::getUsername).collect(Collectors.toSet());
                        if (!unreadExcludeUsernames.isEmpty()) {
                            excludeUsernames.addAll(unreadExcludeUsernames);
                        }
                    }
                    List<String> subExtraIdList =
                            taskService.list(Wrappers.<TaskInfo>lambdaQuery().eq(TaskInfo::getParentTaskId,
                                            parentTaskInfo.getId()))
                                    .stream()
                                    .map(TaskInfo::getExtraId)
                                    .collect(Collectors.toList());
                    MessageUnReadDetailResponse messageUnReadDetailResponse =
                            messageGrpcClient.getPageUnRead(StringUtils.EMPTY,
                                    1L,
                                    10L,
                                    parentTaskInfo.getExtraId(),
                                    subExtraIdList,
                                    excludeUsernames);
                    allCount = messageUnReadDetailResponse.getMessageUnreadDetail().getTotalWithoutSearch();
                } else {
                    allCount = count;
                }
                log.info("unread_old_account:{},unread_new_account:{}", scopeInfoList.get(0).getAllCount(), allCount);
                if (scopeInfoList.get(0).getAllCount() != allCount) {
                    scopeInfoList.get(0).setAllCount(allCount);
                    scopeChanged = true;
                }
                break;
            default:
                log.warn("范围类型错误");
                break;
        }
        if (scopeChanged) {
            publishScopeInfoMapper.updateBatch(scopeInfoList);
        }
        return scopeChanged;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void triggerSendTask() {

        List<TaskInfo> taskInfoList = taskService.list(
                Wrappers.<TaskInfo>lambdaQuery()
                        .select(TaskInfo::getId)
                        .eq(TaskInfo::getTaskStatus, TaskStatusEnum.APPROVED.getCode())
                        .le(TaskInfo::getPublishTime, System.currentTimeMillis())
        );

        if (taskInfoList.isEmpty()) {
            return;
        }

        List<Long> idLIst = taskInfoList.stream().map(TaskInfo::getId).collect(Collectors.toList());
        TaskInfo updateTask = TaskInfo.newUpdateTimeInstant();
        updateTask.setTaskStatus(TaskStatusEnum.PUBLISHING.getCode());
        taskService.update(updateTask, Wrappers.<TaskInfo>lambdaUpdate().in(TaskInfo::getId, idLIst));

        for (TaskInfo taskInfo : taskInfoList) {
            log.info("触发发送消息任务, taskId = [{}]", taskInfo.getId());
            sendMessage.syncSendMessageTask(taskInfo.getId(), onSendMessageResultTask);
        }
    }

    @Override
    public void triggerRetrySendExceptionTask() {

        List<TaskInfo> taskInfoList = taskService.list(
                Wrappers.<TaskInfo>lambdaQuery()
                        .select(TaskInfo::getId)
                        .eq(TaskInfo::getTaskStatus, TaskStatusEnum.PUBLISH_EXCEPTION.getCode())
        );

        if (taskInfoList.isEmpty()) {
            return;
        }

        for (TaskInfo taskInfo : taskInfoList) {
            log.info("触发发送消息异常的任务, taskId = [{}]", taskInfo.getId());
            sendMessage.syncSendMessageTask(taskInfo.getId(), onSendMessageResultTask);
        }

    }

    @Override
    public void triggerRetrySendingTimeoutTask() {

        Long timeout = System.currentTimeMillis() - 30 * 60 * 1000;

        List<TaskInfo> taskInfoSendingList = taskService.list(
                Wrappers.<TaskInfo>lambdaQuery()
                        .eq(TaskInfo::getTaskStatus, TaskStatusEnum.PUBLISHING.getCode())
                        .lt(TaskInfo::getUpdateTime, timeout)
        );

        if (taskInfoSendingList.isEmpty()) {
            return;
        }

        for (TaskInfo taskInfo : taskInfoSendingList) {
            sendMessage.sendByChannel(taskInfo, taskInfo.getEngineTemplateId(), this.onSendMessageResultTask);
        }
    }

    private void assertMessageStatusAndBelong(Long taskId, String username) {
        TaskInfo taskInfo = taskService.getById(taskId);

        // 已发布的消息不能更改
        if (taskInfo.getTaskStatus() == TaskStatusEnum.PUBLISHING.getCode() ||
                taskInfo.getTaskStatus() == TaskStatusEnum.PUBLISH_SUCCESS.getCode()) {
            // 不能修改
            throw new RuntimeException("该消息已发布不能修改");
        }

        // 只有创建者才能修改改消息
        if (!username.equals(taskInfo.getCreateUsername())) {
            throw new RuntimeException("无权修改该消息");
        }
    }

    /**
     * 添加范围
     * <p>
     * 只有 部门、自定义名单、筛选规则
     *
     * @param taskId
     * @param larkTaskDTO
     */
    private void addPublishScope(Long taskId, BaseTaskDTO larkTaskDTO) {
        PublishScopeEnum publishScopeEnum = PublishScopeEnum.getByCode(larkTaskDTO.getPublishScope());

        List<PublishScopeInfo> list = new ArrayList<>();
        if (publishScopeEnum == PublishScopeEnum.DEPT_PUSH
                || publishScopeEnum == PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE
                || publishScopeEnum == PublishScopeEnum.CHOOSE_PUSH
                || publishScopeEnum == PublishScopeEnum.GROUP_PUSH
                || publishScopeEnum == PublishScopeEnum.CUSTOM_PUSH_MANAGER
                || publishScopeEnum == PublishScopeEnum.UNREAD_PUSH) {

            List<String> scopeKeyList;
            try {
                scopeKeyList = larkTaskDTO.getScopeKey();
            } catch (Exception e) {
                log.info("scopeKey json 解析错误", e);
                throw new IllegalArgumentException("scopeKey格式非法");
            }

            if (scopeKeyList.isEmpty()) {
                throw new IllegalArgumentException("未选择发送人员");
            }

            // 部门推送的话，需要二级部门去重
            if (publishScopeEnum == PublishScopeEnum.DEPT_PUSH) {
                scopeKeyList = deptManager.filterDeptIds(scopeKeyList);
            }

            for (String scopeKey : scopeKeyList) {
                PublishScopeInfo publishScopeInfo = PublishScopeInfo.newCreateAndUpdateTimeInstant();
                publishScopeInfo.setTaskId(taskId);
                publishScopeInfo.setScopeKey(scopeKey);
                publishScopeInfo.setScopeType(publishScopeEnum.getCode());
                publishScopeInfo.setAllCount(0L);
                list.add(publishScopeInfo);
            }

            publishScopeInfoMapper.batchInsert(list);
        } else {
            PublishScopeInfo publishScopeInfo = PublishScopeInfo.newCreateAndUpdateTimeInstant();
            publishScopeInfo.setTaskId(taskId);
            publishScopeInfo.setScopeType(publishScopeEnum.getCode());
            publishScopeInfo.setAllCount(0L);
            publishScopeInfoMapper.insert(publishScopeInfo);
        }
    }

    private void updatePublishScope(Long taskId, BaseTaskDTO baseTaskDTO) {

        if (Objects.isNull(baseTaskDTO.getPublishScope())) {
            return;
        }

        publishScopeInfoMapper.delete(
                Wrappers.<PublishScopeInfo>lambdaUpdate()
                        .eq(PublishScopeInfo::getTaskId, taskId)
        );

        this.addPublishScope(taskId, baseTaskDTO);
    }

    private TaskInfo makeNewTaskInfo(UserBO userBO, BaseTaskDTO baseTaskDTO, TaskChannelEnum taskChannelEnum) {
        TaskInfo taskInfo = TaskInfo.newCreateAndUpdateTimeInstant();

        taskInfo.setChannel(taskChannelEnum.getCode());
        taskInfo.setBizId(idManager.createBizId());
        taskInfo.setExtraId(idManager.createExtraId());
        taskInfo.setCreateUsername(userBO.getUsername());
        taskInfo.setUpdateUsername(userBO.getUsername());

        long time = DateUtil.parse(baseTaskDTO.getPublishDate(), DatePattern.NORM_DATETIME_FORMAT).getTime();
        taskInfo.setPublishTime(time);

        taskInfo.setPublishScope(baseTaskDTO.getPublishScope());

        TaskSubmitTypeEnum taskSubmitTypeEnum = TaskSubmitTypeEnum.getByType(baseTaskDTO.getSubmitType());
        if (taskSubmitTypeEnum == TaskSubmitTypeEnum.DRAFT) {
            taskInfo.setTaskStatus(TaskStatusEnum.DRAFT.getCode());
        } else {
            taskInfo.setTaskStatus(TaskStatusEnum.APPROVING.getCode());
        }

        taskInfo.setTitleCn(PublishScopeEnum.UNREAD_PUSH.getCode().equals(taskInfo.getPublishScope()) ?
                UNREAD_PUSH_TASK_TITLE_PREFIX_CN + baseTaskDTO.getTitleCn() : baseTaskDTO.getTitleCn());
        taskInfo.setTitleEn(PublishScopeEnum.UNREAD_PUSH.getCode().equals(taskInfo.getPublishScope()) ?
                UNREAD_PUSH_TASK_TITLE_PREFIX_EN + baseTaskDTO.getTitleEn() : baseTaskDTO.getTitleEn());
        taskInfo.setContentCn(baseTaskDTO.getContentCn());
        taskInfo.setContentEn(baseTaskDTO.getContentEn());
        taskInfo.setBpmInstanceId(idManager.createExtraId());

        return taskInfo;
    }

    private TaskInfo makeUpdateTaskInfo(UserBO userBO, BaseTaskDTO baseTaskDTO, TaskChannelEnum taskChannelEnum) {

        TaskInfo updateTaskInfo = TaskInfo.newUpdateTimeInstant();
        updateTaskInfo.setId(baseTaskDTO.getTaskId());
        updateTaskInfo.setChannel(taskChannelEnum.getCode());
        updateTaskInfo.setUpdateUsername(userBO.getUsername());

        if (Objects.nonNull(baseTaskDTO.getParentTaskId())) {
            updateTaskInfo.setParentTaskId(baseTaskDTO.getParentTaskId());
        }

        if (StringUtils.isNotBlank(baseTaskDTO.getPublishDate())) {
            long time = DateUtil.parse(baseTaskDTO.getPublishDate(), DatePattern.NORM_DATETIME_FORMAT).getTime();
            updateTaskInfo.setPublishTime(time);
        }

        updateTaskInfo.setPublishScope(baseTaskDTO.getPublishScope());

        TaskSubmitTypeEnum taskSubmitTypeEnum = TaskSubmitTypeEnum.getByType(baseTaskDTO.getSubmitType());
        if (taskSubmitTypeEnum == TaskSubmitTypeEnum.DRAFT) {
            updateTaskInfo.setTaskStatus(TaskStatusEnum.DRAFT.getCode());
        } else {
            updateTaskInfo.setTaskStatus(TaskStatusEnum.APPROVING.getCode());
        }

        updateTaskInfo.setTitleCn(PublishScopeEnum.UNREAD_PUSH.getCode().equals(updateTaskInfo.getPublishScope()) ?
                UNREAD_PUSH_TASK_TITLE_PREFIX_CN + baseTaskDTO.getTitleCn() : baseTaskDTO.getTitleCn());
        updateTaskInfo.setTitleEn(PublishScopeEnum.UNREAD_PUSH.getCode().equals(updateTaskInfo.getPublishScope()) ?
                UNREAD_PUSH_TASK_TITLE_PREFIX_EN + baseTaskDTO.getTitleEn() : baseTaskDTO.getTitleEn());
        updateTaskInfo.setContentCn(baseTaskDTO.getContentCn());
        updateTaskInfo.setContentEn(baseTaskDTO.getContentEn());

        return updateTaskInfo;
    }

    private LarkTaskInfo makeNewLarkTaskInfo(Long taskId, LarkTaskDTO larkTaskDTO) {
        LarkTaskInfo larkTaskInfo = LarkTaskInfo.newCreateAndUpdateTimeInstant();

        larkTaskInfo.setTaskId(taskId);
        larkTaskInfo.setBotId(larkTaskDTO.getBotId());
        larkTaskInfo.setLarkMessageType(larkTaskDTO.getLarkMessageType());

        larkTaskInfo.setButton(larkTaskDTO.getButton());
        LarkButtonEnum larkButtonEnum = LarkButtonEnum.getByType(larkTaskDTO.getButton());
        if (larkButtonEnum == LarkButtonEnum.SCHEDULE) {
            // 日程
            DateTime dateTime = DateUtil.parse(larkTaskDTO.getScheduleStartDate(), DatePattern.NORM_DATETIME_PATTERN);
            larkTaskInfo.setScheduleStartTime(dateTime.getTime());

            dateTime = DateUtil.parse(larkTaskDTO.getScheduleEndDate(), DatePattern.NORM_DATETIME_PATTERN);
            larkTaskInfo.setScheduleEndTime(dateTime.getTime());

            larkTaskInfo.setScheduleDescription(larkTaskDTO.getScheduleDescription());
        } else if (larkButtonEnum == LarkButtonEnum.LAND_PAGE) {
            larkTaskInfo.setButtonName(larkTaskDTO.getButtonName());
            larkTaskInfo.setButtonNameEn(larkTaskDTO.getButtonNameEn());
            // 落地页
            larkTaskInfo.setLandingPageUrl(larkTaskDTO.getLandingPageUrl());
        }

        // at 配置
        larkTaskInfo.setAtId(larkTaskDTO.getAtId());

        return larkTaskInfo;
    }

    private EmailTaskInfo makeNewEmailTaskInfo(Long taskId, EmailTaskDTO emailTaskDTO) {
        EmailTaskInfo emailTaskInfo = EmailTaskInfo.newCreateAndUpdateTimeInstant();
        BeanUtils.copyProperties(emailTaskDTO, emailTaskInfo);
        emailTaskInfo.setButtonExtra(JsonUtils.toJson(emailTaskDTO.getButtonExtra()));
        emailTaskInfo.setTaskId(taskId);
        return emailTaskInfo;
    }

    private SmsTaskInfo makeNewSmsTaskInfo(Long taskId, SmsTaskDTO smsTaskDTO) {
        SmsTaskInfo smsTaskInfo = SmsTaskInfo.newCreateAndUpdateTimeInstant();

        BeanUtils.copyProperties(smsTaskDTO, smsTaskInfo);
        smsTaskInfo.setTaskId(taskId);

        return smsTaskInfo;
    }

    private LarkTaskInfo makeUpdateLarkTaskInfo(LarkTaskDTO larkTaskDTO) {

        LarkTaskInfo larkTaskInfo = LarkTaskInfo.newUpdateTimeInstant();

        larkTaskInfo.setBotId(larkTaskDTO.getBotId());
        larkTaskInfo.setLarkMessageType(larkTaskDTO.getLarkMessageType());

        larkTaskInfo.setButton(larkTaskDTO.getButton());
        LarkButtonEnum larkButtonEnum = LarkButtonEnum.getByType(larkTaskDTO.getButton());
        if (larkButtonEnum == LarkButtonEnum.SCHEDULE) {

            DateTime dateTime = DateUtil.parse(larkTaskDTO.getScheduleStartDate(), DatePattern.NORM_DATETIME_PATTERN);
            larkTaskInfo.setScheduleStartTime(dateTime.getTime());

            dateTime = DateUtil.parse(larkTaskDTO.getScheduleEndDate(), DatePattern.NORM_DATETIME_PATTERN);
            larkTaskInfo.setScheduleEndTime(dateTime.getTime());

            larkTaskInfo.setScheduleDescription(larkTaskDTO.getScheduleDescription());
        } else if (larkButtonEnum == LarkButtonEnum.LAND_PAGE) {
            larkTaskInfo.setButtonName(larkTaskDTO.getButtonName());
            larkTaskInfo.setButtonNameEn(larkTaskDTO.getButtonNameEn());
            // 落地页
            larkTaskInfo.setLandingPageUrl(larkTaskDTO.getLandingPageUrl());
        }

        return larkTaskInfo;
    }

    private EmailTaskInfo makeUpdateEmailTaskInfo(EmailTaskDTO emailTaskDTO) {
        EmailTaskInfo emailTaskInfo = EmailTaskInfo.newUpdateTimeInstant();
        BeanUtils.copyProperties(emailTaskDTO, emailTaskInfo);
        if (Objects.nonNull(emailTaskDTO.getButtonExtra())) {
            emailTaskInfo.setButtonExtra(JsonUtils.toJson(emailTaskDTO.getButtonExtra()));
        }
        return emailTaskInfo;
    }

    private SmsTaskInfo makeUpdateSmsTaskInfo(SmsTaskDTO smsTaskDTO) {
        SmsTaskInfo smsTaskInfo = SmsTaskInfo.newUpdateTimeInstant();
        BeanUtils.copyProperties(smsTaskDTO, smsTaskInfo);
        return smsTaskInfo;
    }

    @Override
    public BaseResult<IPage<MyTaskRecordDTO>> queryMyTaskByPage(Long page, Long size, String key, List<Byte> channel,
                                                                List<Byte> taskStatus, String beginDate, String endDate,
                                                                UserBO userBO, List<Byte> publishScope) {
        IPage<TaskInfo> taskInfoIPage =
                filterTaskInfoByPage(page, size, key, channel, new ArrayList<>(taskStatus), beginDate, endDate, userBO,
                        publishScope);

        IPage<MyTaskRecordDTO> myTaskRecordDTOIPage = new Page<>(page, size);

        if (taskInfoIPage.getRecords().isEmpty()) {
            myTaskRecordDTOIPage.setCurrent(page);
            myTaskRecordDTOIPage.setPages(0L);
            myTaskRecordDTOIPage.setSize(size);
            myTaskRecordDTOIPage.setTotal(0L);
            myTaskRecordDTOIPage.setRecords(new ArrayList<>());
            return BaseResult.of(myTaskRecordDTOIPage);
        }

        List<MyTaskRecordDTO> myTaskRecordDTOList = getMyTaskRecordDTOList(taskInfoIPage.getRecords(), false);

        myTaskRecordDTOList.forEach(myTaskRecordDTO -> {
            myTaskRecordDTO.setRushList(getMyRushTaskRecordDTOList(myTaskRecordDTO.getTaskId()));
        });

        myTaskRecordDTOIPage.setCurrent(taskInfoIPage.getCurrent());
        myTaskRecordDTOIPage.setPages(taskInfoIPage.getPages());
        myTaskRecordDTOIPage.setSize(taskInfoIPage.getSize());
        myTaskRecordDTOIPage.setTotal(taskInfoIPage.getTotal());
        myTaskRecordDTOIPage.setRecords(myTaskRecordDTOList);

        return BaseResult.of(myTaskRecordDTOIPage);
    }

    private List<MyTaskRecordDTO> getMyTaskRecordDTOList(List<TaskInfo> taskInfoList, boolean isRush) {
        List<MyTaskRecordDTO> result = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(taskInfoList)) {
            List<Long> taskIdList = taskInfoList.stream().map(TaskInfo::getId).collect(Collectors.toList());
            List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                    Wrappers.<PublishScopeInfo>lambdaQuery().in(PublishScopeInfo::getTaskId, taskIdList)
            );

            Map<Long, PushResultDTO> taskIdAndPushResultCombineMap =
                    getTaskPushResultMap(publishScopeInfoList, taskInfoList, isRush);

            taskInfoList.forEach(
                    taskInfo -> {
                        MyTaskRecordDTO myTaskRecordDTO = new MyTaskRecordDTO();
                        myTaskRecordDTO.setBizId(taskInfo.getBizId());
                        myTaskRecordDTO.setChannel(taskInfo.getChannel());
                        myTaskRecordDTO.setCreateTime(taskInfo.getCreateTime());
                        myTaskRecordDTO.setPublishScope(taskInfo.getPublishScope());
                        if (taskInfo.getPublishScope().equals(PublishScopeEnum.DEPT_PUSH.getCode())) {
                            List<String> deptIdList = publishScopeInfoList.stream()
                                    .filter(record -> record.getTaskId().equals(taskInfo.getId()))
                                    .map(PublishScopeInfo::getScopeKey).distinct().collect(Collectors.toList());
                            String deptName = deptManager.getFirstLevelDeptString(deptIdList);
                            myTaskRecordDTO.setPublishScopeDesc(deptName);
                        } else {
                            myTaskRecordDTO.setPublishScopeDesc(
                                    isRush ? "【加推】" +
                                            PublishScopeEnum.getMsgByCode(taskInfo.getPublishScope()) :
                                            PublishScopeEnum.getMsgByCode(taskInfo.getPublishScope()));
                        }
                        myTaskRecordDTO.setTaskId(taskInfo.getId());
                        myTaskRecordDTO.setTaskStatus(taskInfo.getTaskStatus());
                        myTaskRecordDTO.setTitleCn(taskInfo.getTitleCn());
                        myTaskRecordDTO.setTitleEn(taskInfo.getTitleEn());
                        PushResultDTO pushResult = taskIdAndPushResultCombineMap.get(taskInfo.getId());
                        if (taskInfo.getTaskStatus() != TaskStatusEnum.RETRACTED.getCode()) {
                            if (taskInfo.getTaskStatus() == TaskStatusEnum.PUBLISH_SUCCESS.getCode() &&
                                    taskInfo.getTaskEngineStatus() == TaskStatusEnum.PUBLISHING.getCode()) {
                                myTaskRecordDTO.setTaskStatus(TaskStatusEnum.PUBLISHING.getCode());
                            } else if (taskInfo.getTaskEngineStatus() == TaskStatusEnum.PUBLISH_SUCCESS.getCode()) {
                                myTaskRecordDTO.setTaskStatus(TaskStatusEnum.PUBLISH_SUCCESS.getCode());
                            }
                        }
                        myTaskRecordDTO.setPushResult(pushResult);
                        myTaskRecordDTO.setIsSuccess(pushResult.getAllCount().equals(pushResult.getPushCount()));
                        if (myTaskRecordDTO.getTaskStatus().equals(TaskStatusEnum.PUBLISHING.getCode())) {
                            myTaskRecordDTO.setIsSuccess(null);
                        }

                        myTaskRecordDTO.setRushable(!isRush &&
                                TaskChannelEnum.CHANNEL_LARK.getCode() == taskInfo.getChannel() &&
                                !PublishScopeEnum.GROUP_PUSH.getCode().equals(taskInfo.getPublishScope()) &&
                                TaskStatusEnum.PUBLISH_SUCCESS.getCode() == myTaskRecordDTO.getTaskStatus()
                                && ("on".equals(rushTimeLimitSwitch) ?
                                (System.currentTimeMillis() - taskInfo.getPublishSuccessTime() >
                                        TimeUnit.HOURS.toMillis(24) &&
                                        System.currentTimeMillis() - taskInfo.getPublishTime() <
                                                TimeUnit.DAYS.toMillis(7)) : true)
                        );
                        result.add(myTaskRecordDTO);
                    }
            );
        }
        return result;
    }

    private List<MyTaskRecordDTO> getMyRushTaskRecordDTOList(Long taskId) {
        List<TaskInfo> rushTaskList =
                taskService.list(Wrappers.<TaskInfo>lambdaQuery().eq(TaskInfo::getParentTaskId,
                        taskId).gt(TaskInfo::getTaskStatus, TaskStatusEnum.DRAFT.getCode()));
        return getMyTaskRecordDTOList(rushTaskList, true);
    }

    private Map<Long, PushResultDTO> getTaskDetailFromLocal(List<TaskInfo> taskInfoList,
                                                            List<PublishScopeInfo> publishScopeInfoList) {
        Map<Long, PushResultDTO> taskIdAndPushResultMap = new HashMap<>();

        if (publishScopeInfoList.isEmpty()) {
            return taskIdAndPushResultMap;
        }

        Map<Long, Long> taskIdAndAllCountMap = new HashMap<>();
        publishScopeInfoList.stream().collect(groupingBy(PublishScopeInfo::getTaskId)).forEach(
                (k, v) -> taskIdAndAllCountMap.put(k,
                        v.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).get())
        );

        taskInfoList.forEach(
                taskInfo -> {
                    Long allCount = taskIdAndAllCountMap.get(taskInfo.getId());
                    PushResultDTO pushResult = new PushResultDTO();
                    if (Objects.nonNull(allCount)) {
                        pushResult.setAllCount(allCount);
                    } else {
                        pushResult.setAllCount(0L);
                    }
                    pushResult.setPushCount(0L);
                    pushResult.setTodoCount(0L);
                    pushResult.setReadCount(0L);
                    pushResult.setRetractCount(0L);
                    taskIdAndPushResultMap.put(taskInfo.getId(), pushResult);
                }
        );

        return taskIdAndPushResultMap;
    }

    @Deprecated
    private Map<Long, PushResultDTO> getTaskDetailFromEngine(List<TaskInfo> taskInfoList,
                                                             List<PublishScopeInfo> publishScopeInfoList) {
        Map<Long, PushResultDTO> taskIdAndPushResultMap = new HashMap<>();
        // 消耗时间和条数相关
        MessageNumberAndTimeResponse numberAndTimeResponse = messageGrpcClient.getMessageNumberByExtraIdBatch(
                taskInfoList.stream().map(TaskInfo::getExtraId).collect(Collectors.toList()));
        if (numberAndTimeResponse.getMessageNumberAndTimeListList().isEmpty()) {
            return taskIdAndPushResultMap;
        }

        if (publishScopeInfoList.isEmpty()) {
            return taskIdAndPushResultMap;
        }

        Map<Long, Long> taskIdAndAllCountMap = new HashMap<>();
        publishScopeInfoList.stream().collect(groupingBy(PublishScopeInfo::getTaskId)).forEach(
                (k, v) -> taskIdAndAllCountMap.put(k,
                        v.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).get())
        );

        List<MessageNumberAndTime> messageNumberAndTimeListList =
                numberAndTimeResponse.getMessageNumberAndTimeListList();
        messageNumberAndTimeListList.forEach(
                messageNumberAndTime -> {
                    taskInfoList.forEach(
                            taskInfo -> {
                                if (taskInfo.getExtraId().equals(messageNumberAndTime.getExtraId())) {
                                    PushResultDTO pushResult = new PushResultDTO();
                                    Long allCount = taskIdAndAllCountMap.get(taskInfo.getId());
                                    if (Objects.nonNull(allCount)) {
                                        pushResult.setAllCount(allCount);
                                    } else {
                                        pushResult.setAllCount(0L);
                                    }
                                    pushResult.setTodoCount(messageNumberAndTime.getTodoCount());
                                    pushResult.setPushCount(messageNumberAndTime.getPushCount());
                                    taskIdAndPushResultMap.put(taskInfo.getId(), pushResult);
                                }
                            }
                    );
                }
        );

        return taskIdAndPushResultMap;
    }

    private Map<Long, PushResultDTO> getTaskPushData(List<TaskInfo> taskInfoList,
                                                     List<PublishScopeInfo> publishScopeInfoList,
                                                     boolean isRush) {
        Map<Long, PushResultDTO> taskIdAndPushResultMap = new HashMap<>();
        Map<Long, PushResultDTO> newTaskIdAndPushResultMap = new HashMap<>();
        long theTimeMillis = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(7);

        Map<Long, Long> taskIdAndAllCountMap = new HashMap<>();
        publishScopeInfoList.stream().collect(groupingBy(PublishScopeInfo::getTaskId)).forEach(
                (k, v) -> taskIdAndAllCountMap.put(k,
                        v.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).get())
        );

        // 此处oldTaskInfos指超过7天的任务, newTaskInfos指7天内的任务
        List<TaskInfo> oldTaskInfos = new ArrayList<>();
        List<TaskInfo> newTaskInfos = new ArrayList<>();
        for (TaskInfo taskInfo : taskInfoList) {
            if (taskInfo.getUpdateTime() < theTimeMillis) {
                oldTaskInfos.add(taskInfo);
            } else {
                newTaskInfos.add(taskInfo);
            }
        }

        if (CollectionUtils.isNotEmpty(newTaskInfos)) {
            newTaskIdAndPushResultMap = getTaskIdAndPushResultMapFromEngine(taskIdAndAllCountMap, newTaskInfos, isRush);
        }

        oldTaskInfos.forEach(
                oldTaskInfo -> {
                    PushResultDTO pushResult = new PushResultDTO();
                    pushResult.setAllCount(oldTaskInfo.getTaskTotalCount());
                    pushResult.setPushCount(oldTaskInfo.getTaskPushCount());
                    pushResult.setTodoCount(pushResult.getAllCount() - pushResult.getPushCount());
                    pushResult.setReadCount(oldTaskInfo.getTaskReadCount());
                    pushResult.setRetractCount(oldTaskInfo.getTaskRetractCount());
                    taskIdAndPushResultMap.put(oldTaskInfo.getId(), pushResult);
                }
        );

        taskIdAndPushResultMap.putAll(newTaskIdAndPushResultMap);

        return taskIdAndPushResultMap;
    }

    private Map<Long, PushResultDTO> getTaskIdAndPushResultMapFromEngine(Map<Long, Long> taskIdAndAllCountMap,
                                                                         List<TaskInfo> newTaskInfos,
                                                                         boolean isRush) {
        Map<Long, PushResultDTO> taskIdAndPushResultMap = new HashMap<>();

        if (isRush) {
            // 消耗时间和条数相关
            MessageNumberAndTimeResponse numberAndTimeResponse = messageGrpcClient.getMessageNumberByExtraIdBatch(
                    newTaskInfos.stream()
                            .map(TaskInfo::getExtraId)
                            .collect(Collectors.toList())
            );
            if (numberAndTimeResponse.getMessageNumberAndTimeListList().isEmpty()) {
                return taskIdAndPushResultMap;
            }
            List<MessageNumberAndTime> messageNumberAndTimeListList =
                    numberAndTimeResponse.getMessageNumberAndTimeListList();
            messageNumberAndTimeListList.forEach(
                    messageNumberAndTime -> newTaskInfos.forEach(
                            taskInfo -> {
                                if (taskInfo.getExtraId().equals(messageNumberAndTime.getExtraId())) {
                                    PushResultDTO pushResult = new PushResultDTO();
                                    Long allCount = taskIdAndAllCountMap.get(taskInfo.getId());
                                    if (Objects.nonNull(allCount)) {
                                        pushResult.setAllCount(allCount);
                                    } else {
                                        pushResult.setAllCount(0L);
                                    }
                                    pushResult.setTodoCount(messageNumberAndTime.getTodoCount());
                                    pushResult.setPushCount(messageNumberAndTime.getPushCount());
                                    pushResult.setReadCount(messageNumberAndTime.getReadCount());
                                    pushResult.setRetractCount(messageNumberAndTime.getRetractCount());
                                    taskIdAndPushResultMap.put(taskInfo.getId(), pushResult);
                                }
                            }
                    )
            );
        } else {
            newTaskInfos.forEach(taskInfo -> {
                List<String> rushTaskExtraIdList =
                        taskService.list(Wrappers.<TaskInfo>lambdaQuery().eq(TaskInfo::getParentTaskId,
                                        taskInfo.getId()))
                                .stream()
                                .map(TaskInfo::getExtraId)
                                .collect(Collectors.toList());
                MessageReadResponse messageReadResponse = messageGrpcClient.getTaskReadData(taskInfo.getExtraId(),
                        rushTaskExtraIdList);
                PushResultDTO pushResult = new PushResultDTO();
                pushResult.setPushCount(messageReadResponse.getMessageRead().getSendCount());
                pushResult.setReadCount(messageReadResponse.getMessageRead().getReadCount());
                pushResult.setTodoCount(messageReadResponse.getMessageRead().getTodoCount());
                pushResult.setAllCount(messageReadResponse.getMessageRead().getTotalCount());
                pushResult.setRetractCount(messageReadResponse.getMessageRead().getRetractCount());
                taskIdAndPushResultMap.put(taskInfo.getId(), pushResult);
            });
        }
        return taskIdAndPushResultMap;
    }

    private Map<Long, PushResultDTO> getTaskPushResultMap(List<PublishScopeInfo> publishScopeInfoList,
                                                          List<TaskInfo> taskInfoList, boolean isRush) {
        Map<Long, PushResultDTO> taskIdAndPushResultCombineMap = new HashMap<>();
        if (!taskInfoList.isEmpty()) {
            List<TaskInfo> getNumberFromLocalAllTaskInfoList = taskInfoList.stream().filter(
                    p -> p.getTaskStatus() != TaskStatusEnum.PUBLISH_SUCCESS.getCode() &&
                            p.getTaskStatus() != TaskStatusEnum.INTERRUPT.getCode() &&
                            p.getTaskStatus() != TaskStatusEnum.RETRACTED.getCode()
            ).collect(Collectors.toList());
            if (!getNumberFromLocalAllTaskInfoList.isEmpty()) {
                List<Long> fromLocalAllTaskIdList =
                        getNumberFromLocalAllTaskInfoList.stream().map(TaskInfo::getId).collect(Collectors.toList());
                List<PublishScopeInfo> fromLocalAllPublishScopeInfoList =
                        publishScopeInfoList.stream().filter(item -> fromLocalAllTaskIdList.contains(item.getTaskId()))
                                .collect(Collectors.toList());
                Map<Long, PushResultDTO> taskIdAndPushResultMap =
                        getTaskDetailFromLocal(getNumberFromLocalAllTaskInfoList, fromLocalAllPublishScopeInfoList);
                taskIdAndPushResultCombineMap.putAll(taskIdAndPushResultMap);
            }
            List<TaskInfo> getNumberFromEngineAllTaskInfoList = taskInfoList.stream().filter(
                    p -> p.getTaskStatus() == TaskStatusEnum.PUBLISH_SUCCESS.getCode() ||
                            p.getTaskStatus() == TaskStatusEnum.INTERRUPT.getCode() ||
                            p.getTaskStatus() == TaskStatusEnum.RETRACTED.getCode()
            ).collect(Collectors.toList());
            if (!getNumberFromEngineAllTaskInfoList.isEmpty()) {
                List<Long> fromEngineAllTaskIdList =
                        getNumberFromEngineAllTaskInfoList.stream().map(TaskInfo::getId).collect(Collectors.toList());
                List<PublishScopeInfo> fromEngineAllPublishScopeInfoList =
                        publishScopeInfoList.stream().filter(item -> fromEngineAllTaskIdList.contains(item.getTaskId()))
                                .collect(Collectors.toList());
                Map<Long, PushResultDTO> taskIdAndPushResultMap =
                        getTaskPushData(getNumberFromEngineAllTaskInfoList, fromEngineAllPublishScopeInfoList, isRush);
                taskIdAndPushResultCombineMap.putAll(taskIdAndPushResultMap);
            }
        }
        return taskIdAndPushResultCombineMap;
    }

    private IPage<TaskInfo> filterTaskInfoByPage(Long page, Long size, String key, List<Byte> channel,
                                                 List<Byte> taskStatusList, String beginDate, String endDate,
                                                 UserBO userBO, List<Byte> publishScope) {
        // page
        Page<TaskInfo> dataPage = new Page<>(page, size);

        LambdaQueryWrapper<TaskInfo> lambdaQuery = Wrappers.lambdaQuery();

        lambdaQuery.eq(TaskInfo::getCreateUsername, userBO.getUsername());

        lambdaQuery.eq(TaskInfo::getParentTaskId, 0L);

        lambdaQuery.gt(TaskInfo::getTaskStatus, TaskStatusEnum.DRAFT.getCode());

        // key
        if (StringUtils.isNotBlank(key)) {
            lambdaQuery
                    .and(wrapper -> wrapper
                            .like(TaskInfo::getTitleCn, key)
                            .or()
                            .like(TaskInfo::getBizId, key)
                    );
        }
        // channel
        if (!channel.isEmpty()) {
            lambdaQuery
                    .and(wrapper -> wrapper
                            .in(TaskInfo::getChannel, channel)
                    );
        }
        // taskStatus
        setTaskStatusFilter(taskStatusList, lambdaQuery);
        // beginDate
        if (StringUtils.isNotBlank(beginDate)) {
            DateTime dateTime = DateUtil.beginOfSecond(DateUtil.parseDateTime(beginDate));
            lambdaQuery.ge(TaskInfo::getPublishTime, dateTime.getTime());
            log.info("开始时间 = [{}]", dateTime.getTime());
        }
        // endDate
        if (StringUtils.isNotBlank(endDate)) {
            DateTime dateTime = DateUtil.beginOfSecond(DateUtil.parseDateTime(endDate));
            lambdaQuery.le(TaskInfo::getPublishTime, dateTime.getTime());
            log.info("结束时间 = [{}]", dateTime.getTime());
        }
        // publishScope
        if (!publishScope.isEmpty()) {
            lambdaQuery.in(TaskInfo::getPublishScope, publishScope);
        }

        lambdaQuery.orderByDesc(TaskInfo::getCreateTime);
        return taskService.page(dataPage, lambdaQuery);
    }

    @Override
    public BaseResult queryTaskMessageByPage(Long page, int size, Long taskId, UserBO userBO) {
        TaskInfo taskInfo = taskService.getById(taskId);

        boolean haveMessagePermission = permissionManager.isHaveMessagePermission(userBO, taskId);
        if (!haveMessagePermission) {
            return new BaseResult<>(ResultCode.FORBIDDEN).setMessage("您没有权限访问此接口");
        }

        PublishScopeEnum publishScopeEnum = PublishScopeEnum.getByCode(taskInfo.getPublishScope());

        TaskStatusEnum taskStatusEnum = TaskStatusEnum.getByCode(taskInfo.getTaskStatus());
        switch (taskStatusEnum) {
            case DRAFT:
            case CANCEL:
            case APPROVED:
            case APPROVING:
            case APPROVE_BACK:
            case PUBLISH_FAILED:
            case PUBLISH_EXCEPTION:
            case PUBLISHING:
            case BOT_STOP:
                return getTaskDetailByPage(page, size, taskInfo, publishScopeEnum);
            case INTERRUPT:
            case PUBLISH_SUCCESS:
                return getTaskDetailFromEngineByPage(page, size, taskInfo, publishScopeEnum);
            default:
                break;
        }

        return BaseResult.of();

    }

    private BaseResult getTaskDetailByPage(Long page, int size, TaskInfo taskInfo, PublishScopeEnum publishScopeEnum) {

        switch (publishScopeEnum) {
            case DEPT_PUSH:
                return getTaskDetailDeptGroup(page, size, taskInfo);
            case CUSTOM_PUSH_MANAGER:
            case CUSTOM_PUSH_EMPLOYEE:
                return getTaskDetailCustom(page, size, taskInfo);
            case UNREAD_PUSH:
            case CHOOSE_PUSH:
            case ALL_PUSH:
                return getTaskDetailAll(page, size, taskInfo, publishScopeEnum);
            case GROUP_PUSH:
                return getTaskDetailLarkGroup(page, size, taskInfo);
            default:
                break;
        }
        return new BaseResult<>(ResultCode.PARAM_ERROR).setMessage("任务发布范围有误");
    }

    private BaseResult getTaskDetailFromEngineByPage(Long page,
                                                     int size,
                                                     TaskInfo taskInfo,
                                                     PublishScopeEnum publishScopeEnum) {
        switch (publishScopeEnum) {
            case DEPT_PUSH:
                return getTaskDetailDeptGroupFromEngine(page, size, taskInfo);
            case CUSTOM_PUSH_MANAGER:
            case CUSTOM_PUSH_EMPLOYEE:
                return getTaskDetailCustomFromEngine(page, size, taskInfo);
            case UNREAD_PUSH:
            case CHOOSE_PUSH:
            case ALL_PUSH:
                return getTaskDetailAllFromEngine(page, size, taskInfo, publishScopeEnum);
            case GROUP_PUSH:
                return getTaskDetailLarkGroupFromEngine(page, size, taskInfo);
            default:
                break;
        }
        return new BaseResult<>(ResultCode.PARAM_ERROR).setMessage("任务发布范围有误");
    }

    private BaseResult<TaskDetailLarkGroupListVO> getTaskDetailLarkGroup(Long page, int size, TaskInfo taskInfo) {
        TaskDetailLarkGroupListVO taskDetailLarkGroupListVO = TaskDetailLarkGroupListVO.newEmpty(page, (long) size);
        List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery().eq(PublishScopeInfo::getTaskId, taskInfo.getId())
        );

        long allCount = 0;
        if (!publishScopeInfoList.isEmpty()) {
            allCount = publishScopeInfoList.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).orElse(0L);
        }

        PublishScopeInfo publishScopeInfo = publishScopeInfoList.get(0);

        // page
        Page<LarkGroupPushInfo> dataPage = new Page<>(page, size);

        LambdaQueryWrapper<LarkGroupPushInfo> lambdaQuery = Wrappers.lambdaQuery();

        lambdaQuery.and(
                wrapper -> wrapper.eq(LarkGroupPushInfo::getTagId, publishScopeInfo.getScopeKey())
        );

        IPage<LarkGroupPushInfo> larkGroupPushInfoIPage = larkGroupPushInfoMapper.selectPage(dataPage, lambdaQuery);

        List<LarkGroupPushInfo> larkGroupPushInfoList = larkGroupPushInfoIPage.getRecords();

        List<TaskMessageLarkGroupListRecordDTO> taskMessageLarkGroupListRecordDTOList = new ArrayList<>();
        larkGroupPushInfoList.forEach(
                larkGroupPushInfo -> {
                    TaskMessageLarkGroupListRecordDTO taskMessageLarkGroupListRecordDTO =
                            new TaskMessageLarkGroupListRecordDTO();
                    taskMessageLarkGroupListRecordDTO.setChatId(larkGroupPushInfo.getChatId());
                    taskMessageLarkGroupListRecordDTO.setChatName(larkGroupPushInfo.getChatName());
                    taskMessageLarkGroupListRecordDTO.setOwnerUserId(larkGroupPushInfo.getOwnerUserId());
                    taskMessageLarkGroupListRecordDTO.setOwnerName(larkGroupPushInfo.getOwnerName());
                    taskMessageLarkGroupListRecordDTO.setErrorLog("");
                    taskMessageLarkGroupListRecordDTO.setIsSuccess(false);
                    taskMessageLarkGroupListRecordDTO.setPushResDesc("未推送");
                    taskMessageLarkGroupListRecordDTOList.add(taskMessageLarkGroupListRecordDTO);
                }
        );

        taskDetailLarkGroupListVO.setAllCount(allCount);
        taskDetailLarkGroupListVO.setCurrent(larkGroupPushInfoIPage.getCurrent());
        taskDetailLarkGroupListVO.setSize(larkGroupPushInfoIPage.getSize());
        taskDetailLarkGroupListVO.setPages(larkGroupPushInfoIPage.getPages());
        taskDetailLarkGroupListVO.setTotal(larkGroupPushInfoIPage.getTotal());
        taskDetailLarkGroupListVO.setPushCount(0L);
        taskDetailLarkGroupListVO.setPushAllTime(0L);
        taskDetailLarkGroupListVO.setReadCount(0L);
        taskDetailLarkGroupListVO.setRetractCount(0L);
        taskDetailLarkGroupListVO.setRecords(taskMessageLarkGroupListRecordDTOList);
        return BaseResult.of(taskDetailLarkGroupListVO);

    }

    private BaseResult<TaskDetailLarkGroupListVO> getTaskDetailLarkGroupFromEngine(Long page, int size,
                                                                                   TaskInfo taskInfo) {
        TaskDetailLarkGroupListVO taskDetailLarkGroupListVO = TaskDetailLarkGroupListVO.newEmpty(page, (long) size);
        if (System.currentTimeMillis() - taskInfo.getUpdateTime() > TimeUnit.DAYS.toMillis(7)) {
            taskDetailLarkGroupListVO.setRecords(Collections.emptyList());
            taskDetailLarkGroupListVO.setPushAllTime(taskInfo.getTaskPushAllTime());
            taskDetailLarkGroupListVO.setAllCount(taskInfo.getTaskTotalCount());
            taskDetailLarkGroupListVO.setPushCount(taskInfo.getTaskPushCount());
            taskDetailLarkGroupListVO.setReadCount(taskInfo.getTaskReadCount());
            taskDetailLarkGroupListVO.setRetractCount(taskInfo.getTaskRetractCount());
            return BaseResult.of(taskDetailLarkGroupListVO);
        }

        List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery().eq(PublishScopeInfo::getTaskId, taskInfo.getId())
        );

        PublishScopeInfo publishScopeInfo = publishScopeInfoList.get(0);

        if (taskService.isRush(taskInfo)) {
            long allCount = 0;
            if (!publishScopeInfoList.isEmpty()) {
                allCount =
                        publishScopeInfoList.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).orElse(0L);
            }
            MessageNumberAndTimeResponse messageNumberByExtraIdBatch =
                    messageGrpcClient.getMessageNumberByExtraIdBatch(Collections.singletonList(taskInfo.getExtraId()));
            List<MessageNumberAndTime> messageNumberAndTimeList =
                    messageNumberByExtraIdBatch.getMessageNumberAndTimeListList();
            taskDetailLarkGroupListVO.setAllCount(allCount);
            taskDetailLarkGroupListVO.setPushCount(messageNumberAndTimeList.get(0).getPushCount());
            taskDetailLarkGroupListVO.setPushAllTime(messageNumberAndTimeList.get(0).getCostTime());
            taskDetailLarkGroupListVO.setReadCount(messageNumberAndTimeList.get(0).getReadCount());
            taskDetailLarkGroupListVO.setRetractCount(messageNumberAndTimeList.get(0).getRetractCount());
        } else {
            List<String> subExtraIdList =
                    taskService.list(Wrappers.<TaskInfo>lambdaQuery().eq(TaskInfo::getParentTaskId,
                                    taskInfo.getId()))
                            .stream()
                            .map(TaskInfo::getExtraId)
                            .collect(Collectors.toList());
            MessageReadResponse messageReadResponse =
                    messageGrpcClient.getTaskReadData(taskInfo.getExtraId(), subExtraIdList);
            taskDetailLarkGroupListVO.setAllCount(messageReadResponse.getMessageRead().getTotalCount());
            taskDetailLarkGroupListVO.setPushCount(messageReadResponse.getMessageRead().getSendCount());
            taskDetailLarkGroupListVO.setReadCount(messageReadResponse.getMessageRead().getReadCount());
            taskDetailLarkGroupListVO.setPushAllTime(messageReadResponse.getMessageRead().getCostTime());
            taskDetailLarkGroupListVO.setRetractCount(messageReadResponse.getMessageRead().getRetractCount());
        }

        MessageResultSingleForCustomResponse messagePageByExtraId =
                messageGrpcClient.getMessagePageByExtraId(taskInfo.getExtraId(), page, size);

        taskDetailLarkGroupListVO.setCurrent(messagePageByExtraId.getMessageResultPage().getCurrentPage());
        taskDetailLarkGroupListVO.setTotal(messagePageByExtraId.getMessageResultPage().getTotal());
        taskDetailLarkGroupListVO.setSize(messagePageByExtraId.getMessageResultPage().getSize());

        List<MessageRecord> recordsList = messagePageByExtraId.getMessageResultPage().getRecordsList();
        // page
        Page<LarkGroupPushInfo> dataPage = new Page<>(page, size);

        LambdaQueryWrapper<LarkGroupPushInfo> lambdaQuery = Wrappers.lambdaQuery();

        lambdaQuery.and(
                wrapper -> wrapper.eq(LarkGroupPushInfo::getTagId, publishScopeInfo.getScopeKey())
        );

        IPage<LarkGroupPushInfo> larkGroupPushInfoIPage = larkGroupPushInfoMapper.selectPage(dataPage, lambdaQuery);
        List<LarkGroupPushInfo> records = larkGroupPushInfoIPage.getRecords();
        Map<String, String> chatIdAndChatNameMap = records.stream()
                .collect(Collectors.toMap(LarkGroupPushInfo::getChatId, LarkGroupPushInfo::getChatName));
        Map<String, String> chatIdAndOwnerNameMap = records.stream()
                .collect(Collectors.toMap(LarkGroupPushInfo::getChatId, LarkGroupPushInfo::getOwnerName));
        Map<String, String> chatIdAndOwnerUserIdMap = records.stream()
                .collect(Collectors.toMap(LarkGroupPushInfo::getChatId, LarkGroupPushInfo::getOwnerUserId));

        List<TaskMessageLarkGroupListRecordDTO> taskMessageLarkGroupListRecordDTOList = new ArrayList<>();
        recordsList.forEach(
                messageRecord -> {
                    TaskMessageLarkGroupListRecordDTO taskMessageLarkGroupListRecordDTO =
                            new TaskMessageLarkGroupListRecordDTO();
                    taskMessageLarkGroupListRecordDTO.setChatId(messageRecord.getChatId());
                    taskMessageLarkGroupListRecordDTO.setChatName(chatIdAndChatNameMap.get(messageRecord.getChatId()));
                    taskMessageLarkGroupListRecordDTO.setOwnerUserId(
                            chatIdAndOwnerUserIdMap.getOrDefault(messageRecord.getChatId(), ""));
                    taskMessageLarkGroupListRecordDTO.setOwnerName(
                            chatIdAndOwnerNameMap.getOrDefault(messageRecord.getChatId(), ""));
                    // 1-发送中，2-发送成功，3-发送失败，4-消息中断
                    switch (messageRecord.getMessageStatus()) {
                        case 1:
                            taskMessageLarkGroupListRecordDTO.setIsSuccess(true);
                            taskMessageLarkGroupListRecordDTO.setPushResDesc("发送中");
                            break;
                        case 2:
                            taskMessageLarkGroupListRecordDTO.setIsSuccess(true);
                            taskMessageLarkGroupListRecordDTO.setPushResDesc("成功");
                            break;
                        case 3:
                            taskMessageLarkGroupListRecordDTO.setIsSuccess(false);
                            taskMessageLarkGroupListRecordDTO.setPushResDesc("失败");
                            break;
                        case 4:
                            taskMessageLarkGroupListRecordDTO.setIsSuccess(false);
                            taskMessageLarkGroupListRecordDTO.setPushResDesc("中断");
                            break;
                        default:
                            break;
                    }
                    boolean isSuccess = taskMessageLarkGroupListRecordDTO.getIsSuccess();
                    if (!isSuccess) {
                        taskMessageLarkGroupListRecordDTO.setErrorLog(messageRecord.getErrorLog());
                    }
                    taskMessageLarkGroupListRecordDTOList.add(taskMessageLarkGroupListRecordDTO);
                }
        );

        taskDetailLarkGroupListVO.setRecords(taskMessageLarkGroupListRecordDTOList);

        return BaseResult.of(taskDetailLarkGroupListVO);
    }

    @Override
    public BaseResult<TaskHistoryPageDTO> queryTaskHistoryByPage(Long page, Long size, String key, String channel,
                                                                 List<Byte> taskStatus, String beginDate,
                                                                 String endDate, String publishScope,
                                                                 List<String> createUsernameList, UserBO userBO) {
        IPage<TaskInfo> taskInfoIPage =
                filterTaskInfo(page, size, key, channel, new ArrayList<>(taskStatus), beginDate, endDate, publishScope,
                        createUsernameList, userBO);

        if (taskInfoIPage.getRecords().isEmpty()) {
            TaskHistoryPageDTO taskHistoryPageDTO = new TaskHistoryPageDTO();
            taskHistoryPageDTO.setCurrent(page);
            taskHistoryPageDTO.setPages(0L);
            taskHistoryPageDTO.setSearchCount(true);
            taskHistoryPageDTO.setSize(size);
            taskHistoryPageDTO.setTotal(0L);
            taskHistoryPageDTO.setRecords(new ArrayList<>());
            return BaseResult.of(taskHistoryPageDTO);
        }

        List<TaskHistoryPageRecordDTO> taskHistoryPageRecordDTOList =
                getTaskHistoryPageRecordDTOList(taskInfoIPage.getRecords(), false);
        taskHistoryPageRecordDTOList.forEach(taskHistoryPageRecordDTO -> {
            taskHistoryPageRecordDTO.setRushList(
                    getRePushTaskHistoryPageRecordDTOList(taskHistoryPageRecordDTO.getTaskId()));
        });

        TaskHistoryPageDTO taskHistoryPageDTO = new TaskHistoryPageDTO();
        taskHistoryPageDTO.setCurrent(taskInfoIPage.getCurrent());
        taskHistoryPageDTO.setPages(taskInfoIPage.getPages());
        taskHistoryPageDTO.setSearchCount(taskInfoIPage.isSearchCount());
        taskHistoryPageDTO.setSize(taskInfoIPage.getSize());
        taskHistoryPageDTO.setTotal(taskInfoIPage.getTotal());
        taskHistoryPageDTO.setRecords(taskHistoryPageRecordDTOList);

        return BaseResult.of(taskHistoryPageDTO);
    }

    private List<TaskHistoryPageRecordDTO> getTaskHistoryPageRecordDTOList(List<TaskInfo> taskInfoList,
                                                                           boolean isRush) {
        List<TaskHistoryPageRecordDTO> result = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(taskInfoList)) {
            List<Long> taskIdList = taskInfoList.stream().map(TaskInfo::getId).collect(Collectors.toList());
            List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                    Wrappers.<PublishScopeInfo>lambdaQuery().in(PublishScopeInfo::getTaskId, taskIdList)
            );

            Map<Long, PushResultDTO> taskIdAndPushResultCombineMap =
                    getTaskPushResultMap(publishScopeInfoList, taskInfoList, isRush);

            taskInfoList.forEach(
                    taskInfo -> {
                        TaskHistoryPageRecordDTO taskHistoryPageRecordDTO = new TaskHistoryPageRecordDTO();
                        taskHistoryPageRecordDTO.setTaskId(taskInfo.getId());
                        taskHistoryPageRecordDTO.setBizId(taskInfo.getBizId());
                        taskHistoryPageRecordDTO.setTitleCn(taskInfo.getTitleCn());
                        taskHistoryPageRecordDTO.setTitleEn(taskInfo.getTitleEn());
                        EmployeeInfo employeeInfo = employeeService.getOne(
                                Wrappers.<EmployeeInfo>lambdaQuery()
                                        .eq(EmployeeInfo::getUsername, taskInfo.getCreateUsername())
                                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                        );
                        if (!Objects.isNull(employeeInfo)) {
                            taskHistoryPageRecordDTO.setCreateName(employeeInfo.getName());
                            taskHistoryPageRecordDTO.setCreateUserEmail(employeeInfo.getEmail());
                        }
                        taskHistoryPageRecordDTO.setCreateUsername(taskInfo.getCreateUsername());
                        taskHistoryPageRecordDTO.setChannel(taskInfo.getChannel());
                        taskHistoryPageRecordDTO.setPublishScope(taskInfo.getPublishScope());
                        if (taskInfo.getPublishScope().equals(PublishScopeEnum.DEPT_PUSH.getCode())) {
                            List<String> deptIdList = publishScopeInfoList.stream()
                                    .filter(publishScopeInfo -> publishScopeInfo.getTaskId().equals(taskInfo.getId()))
                                    .map(PublishScopeInfo::getScopeKey).distinct().collect(Collectors.toList());
                            String deptName = deptManager.getFirstLevelDeptString(deptIdList);
                            taskHistoryPageRecordDTO.setPublishScopeDesc(deptName);
                        } else {
                            taskHistoryPageRecordDTO.setPublishScopeDesc(
                                    isRush ?
                                            "【加推】" + PublishScopeEnum.getMsgByCode(taskInfo.getPublishScope()) :
                                            PublishScopeEnum.getMsgByCode(taskInfo.getPublishScope()));
                        }
                        taskHistoryPageRecordDTO.setPublishTime(taskInfo.getPublishTime());
                        taskHistoryPageRecordDTO.setCreateTime(taskInfo.getCreateTime());
                        taskHistoryPageRecordDTO.setTaskStatus(taskInfo.getTaskStatus());
                        PushResultDTO pushResult = taskIdAndPushResultCombineMap.get(taskInfo.getId());
                        if (taskInfo.getTaskStatus() != TaskStatusEnum.RETRACTED.getCode()) {
                            if (taskInfo.getTaskStatus() == TaskStatusEnum.PUBLISH_SUCCESS.getCode() &&
                                    taskInfo.getTaskEngineStatus() == TaskStatusEnum.PUBLISHING.getCode()) {
                                taskHistoryPageRecordDTO.setTaskStatus(TaskStatusEnum.PUBLISHING.getCode());
                            } else if (taskInfo.getTaskEngineStatus() == TaskStatusEnum.PUBLISH_SUCCESS.getCode()) {
                                taskHistoryPageRecordDTO.setTaskStatus(TaskStatusEnum.PUBLISH_SUCCESS.getCode());
                            }
                        }
                        taskHistoryPageRecordDTO.setPushResult(pushResult);
                        taskHistoryPageRecordDTO.setIsSuccess(
                                pushResult.getAllCount().equals(pushResult.getPushCount()));

                        taskHistoryPageRecordDTO.setRushable(!isRush &&
                                TaskChannelEnum.CHANNEL_LARK.getCode() == taskInfo.getChannel() &&
                                !PublishScopeEnum.GROUP_PUSH.getCode().equals(taskInfo.getPublishScope()) &&
                                TaskStatusEnum.PUBLISH_SUCCESS.getCode() == taskHistoryPageRecordDTO.getTaskStatus()
                                && ("on".equals(rushTimeLimitSwitch) ?
                                (System.currentTimeMillis() - taskInfo.getPublishSuccessTime() >
                                        TimeUnit.HOURS.toMillis(24) &&
                                        System.currentTimeMillis() - taskInfo.getPublishTime() <
                                                TimeUnit.DAYS.toMillis(7)) : true)
                        );
                        result.add(taskHistoryPageRecordDTO);
                    }
            );
        }
        return result;
    }

    private List<TaskHistoryPageRecordDTO> getRePushTaskHistoryPageRecordDTOList(Long taskId) {
        List<TaskInfo> rePushTaskList =
                taskService.list(Wrappers.<TaskInfo>lambdaQuery().eq(TaskInfo::getParentTaskId,
                        taskId).gt(TaskInfo::getTaskStatus, TaskStatusEnum.DRAFT.getCode()));
        return getTaskHistoryPageRecordDTOList(rePushTaskList, true);
    }

    private IPage<TaskInfo> filterTaskInfo(Long page, Long size, String key, String channel, List<Byte> taskStatusList,
                                           String beginDate, String endDate, String publishScope,
                                           List<String> createUsernameList, UserBO userBO) {
        // page
        Page<TaskInfo> dataPage = new Page<>(page, size);

        LambdaQueryWrapper<TaskInfo> lambdaQuery = Wrappers.lambdaQuery();

        if (!(userRoleManager.isSuperAdmin(userBO.getUsername()))) {

            if (!(userRoleManager.isSystemAdmin(userBO.getUsername()))) {
                return getTaskInfoIPage(page, size);
            }

            EmployeeInfo employeeInfo = employeeService.getOne(
                    Wrappers.<EmployeeInfo>lambdaQuery()
                            .eq(EmployeeInfo::getUsername, userBO.getUsername())
                            .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
            );

            List<Long> taskIds = taskService.selectTaskIdSystemAdmin(employeeInfo.getMiDeptLevel2());

            if (taskIds.isEmpty()) {
                return getTaskInfoIPage(page, size);
            }

            lambdaQuery.and(
                    wrapper -> wrapper.in(TaskInfo::getId, taskIds)
            );

        }

        lambdaQuery.eq(TaskInfo::getParentTaskId, 0L);

        lambdaQuery.and(
                wrapper -> wrapper.gt(TaskInfo::getTaskStatus, TaskStatusEnum.DRAFT.getCode())
        );

        // key
        if (StringUtils.isNotBlank(key)) {
            lambdaQuery
                    .and(wrapper -> wrapper
                            .like(TaskInfo::getTitleCn, key)
                            .or()
                            .like(TaskInfo::getBizId, key)
                    );
        }
        // channel
        if (!channel.isEmpty()) {
            lambdaQuery
                    .and(wrapper -> wrapper
                            .in(TaskInfo::getChannel, channel)
                    );
        }
        // createUsernameList
        if (!createUsernameList.isEmpty()) {
            lambdaQuery
                    .and(wrapper -> wrapper
                            .in(TaskInfo::getCreateUsername, createUsernameList)
                    );
        }

        // taskStatus
        setTaskStatusFilter(taskStatusList, lambdaQuery);
        // beginDate
        if (StringUtils.isNotBlank(beginDate)) {
            DateTime dateTime = DateUtil.beginOfSecond(DateUtil.parseDateTime(beginDate));
            lambdaQuery.ge(TaskInfo::getPublishTime, dateTime.getTime());
            log.info("开始时间=[{}]", dateTime.getTime());
        }
        // endDate
        if (StringUtils.isNotBlank(endDate)) {
            DateTime dateTime = DateUtil.beginOfSecond(DateUtil.parseDateTime(endDate));
            lambdaQuery.le(TaskInfo::getPublishTime, dateTime.getTime());
            log.info("结束时间=[{}]", dateTime.getTime());
        }
        // publishScope
        if (!publishScope.isEmpty()) {
            lambdaQuery.in(TaskInfo::getPublishScope, publishScope);
        }

        lambdaQuery.orderByDesc(TaskInfo::getPublishTime);
        return taskService.page(dataPage, lambdaQuery);
    }

    private void setTaskStatusFilter(List<Byte> taskStatusList, LambdaQueryWrapper<TaskInfo> lambdaQuery) {
        if (!taskStatusList.isEmpty()) {
            if (taskStatusList.contains(TaskStatusEnum.PUBLISHING.getCode())) {
                if (taskStatusList.size() > 1) {
                    lambdaQuery.and(wrapper -> wrapper.eq(TaskInfo::getTaskEngineStatus,
                            TaskStatusEnum.PUBLISHING.getCode()).or().in(TaskInfo::getTaskStatus, taskStatusList));
                } else {
                    lambdaQuery.and(wrapper -> wrapper.eq(TaskInfo::getTaskEngineStatus,
                            TaskStatusEnum.PUBLISHING.getCode()));
                }
            } else {
                lambdaQuery.and(wrapper -> wrapper.in(TaskInfo::getTaskStatus, taskStatusList)
                        .ne(TaskInfo::getTaskEngineStatus, TaskStatusEnum.PUBLISHING.getCode()));
            }
//            List<Byte> taskEngineStatusList = new ArrayList<>();
//            if (taskStatusList.contains(TaskStatusEnum.PUBLISHING.getCode())) {
//                taskEngineStatusList.add(TaskStatusEnum.PUBLISHING.getCode());
//                taskStatusList.remove((Byte) TaskStatusEnum.PUBLISHING.getCode());
//            }
//            if (taskStatusList.contains(TaskStatusEnum.PUBLISH_SUCCESS.getCode())) {
//                taskEngineStatusList.add(TaskStatusEnum.PUBLISH_SUCCESS.getCode());
//                taskStatusList.remove((Byte) TaskStatusEnum.PUBLISH_SUCCESS.getCode());
//            }
//
//            if (!taskStatusList.isEmpty() && !taskEngineStatusList.isEmpty()) {
//                lambdaQuery.and(wrapper -> wrapper.in(TaskInfo::getTaskStatus, taskStatusList)
//                        .or()
//                        .in(TaskInfo::getTaskEngineStatus, taskEngineStatusList));
//            } else if (!taskStatusList.isEmpty()) {
//                lambdaQuery.and(wrapper -> wrapper.in(TaskInfo::getTaskStatus, taskStatusList));
//            } else if (!taskEngineStatusList.isEmpty()) {
//                lambdaQuery.and(wrapper -> wrapper.in(TaskInfo::getTaskEngineStatus, taskEngineStatusList));
//            }
        }
    }

    private IPage<TaskInfo> getTaskInfoIPage(Long page, Long size) {
        IPage<TaskInfo> taskInfoIPage = new Page<>();
        taskInfoIPage.setTotal(0L);
        taskInfoIPage.setPages(0L);
        taskInfoIPage.setSize(size);
        taskInfoIPage.setCurrent(page);
        taskInfoIPage.setRecords(Collections.emptyList());
        return taskInfoIPage;
    }

    private BaseResult<TaskDetailDeptListDTO> getTaskDetailDeptGroup(Long page, int size, TaskInfo taskInfo) {

        TaskDetailDeptListDTO taskDetailDeptListDTO = new TaskDetailDeptListDTO();

        // page
        Page<PublishScopeInfo> dataPage = new Page<>(page, size);

        LambdaQueryWrapper<PublishScopeInfo> lambdaQuery = Wrappers.lambdaQuery();

        lambdaQuery.and(
                wrapper -> wrapper.eq(PublishScopeInfo::getTaskId, taskInfo.getId())
        );

        IPage<PublishScopeInfo> publishScopeInfoPage = publishScopeInfoMapper.selectPage(dataPage, lambdaQuery);

        List<PublishScopeInfo> publishScopeInfoRecordList = publishScopeInfoPage.getRecords();

        List<String> deptIdList = publishScopeInfoRecordList
                .stream()
                .distinct()
                .map(PublishScopeInfo::getScopeKey)
                .collect(Collectors.toList());

        Map<String, DepartmentInfo> deptIdAndDepartmentInfoMap = departmentInfoMapper.selectList(
                        Wrappers.<DepartmentInfo>lambdaQuery()
                                .in(DepartmentInfo::getDeptId, deptIdList)
                )
                .stream()
                .collect(Collectors.toMap(DepartmentInfo::getDeptId, Function.identity(), (x, y) -> y));

        List<TaskMessageDeptListRecordDTO> taskMessageDeptListRecordDTOList = new ArrayList<>();
        publishScopeInfoRecordList.forEach(
                publishScopeInfoRecord -> {
                    TaskMessageDeptListRecordDTO taskMessageDeptListRecordDTO = new TaskMessageDeptListRecordDTO();
                    taskMessageDeptListRecordDTO.setIsSuccess(null);
                    DepartmentInfo departmentInfo =
                            deptIdAndDepartmentInfoMap.get(publishScopeInfoRecord.getScopeKey());
                    if (Objects.nonNull(departmentInfo)) {
                        JsonArray jsonArray =
                                new JsonParser().parse(departmentInfo.getCompleteDeptName()).getAsJsonArray();
                        int deptNameSize = jsonArray.size();
                        if (deptNameSize == 3) {
                            taskMessageDeptListRecordDTO.setMiDeptLevel2(jsonArray.get(0).getAsString());
                            taskMessageDeptListRecordDTO.setMiDeptLevel3(jsonArray.get(1).getAsString());
                            taskMessageDeptListRecordDTO.setMiDeptLevel4(jsonArray.get(2).getAsString());
                        } else if (deptNameSize == 2) {
                            taskMessageDeptListRecordDTO.setMiDeptLevel2(jsonArray.get(0).getAsString());
                            taskMessageDeptListRecordDTO.setMiDeptLevel3(jsonArray.get(1).getAsString());
                        } else if (deptNameSize == 1) {
                            taskMessageDeptListRecordDTO.setMiDeptLevel2(jsonArray.get(0).getAsString());
                        }
                    }
                    PushResultDTO pushResult = new PushResultDTO();
                    pushResult.setPushCount(0L);
                    pushResult.setTodoCount(publishScopeInfoRecord.getAllCount());
                    pushResult.setAllCount(publishScopeInfoRecord.getAllCount());
                    pushResult.setReadCount(0L);
                    pushResult.setRetractCount(0L);
                    taskMessageDeptListRecordDTO.setPushResult(pushResult);
                    taskMessageDeptListRecordDTOList.add(taskMessageDeptListRecordDTO);
                }
        );

        List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery().eq(PublishScopeInfo::getTaskId, taskInfo.getId())
        );

        long allCount = 0;
        if (!publishScopeInfoList.isEmpty()) {
            allCount = publishScopeInfoList.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).orElse(0L);
        }

        taskDetailDeptListDTO.setCurrent(page);
        taskDetailDeptListDTO.setTotal(publishScopeInfoPage.getTotal());
        taskDetailDeptListDTO.setSize((long) size);
        taskDetailDeptListDTO.setAllCount(allCount);
        taskDetailDeptListDTO.setPushCount(0L);
        taskDetailDeptListDTO.setPushAllTime(0L);
        taskDetailDeptListDTO.setReadCount(0L);
        taskDetailDeptListDTO.setRetractCount(0L);
        taskDetailDeptListDTO.setRecords(taskMessageDeptListRecordDTOList);
        return BaseResult.of(taskDetailDeptListDTO);
    }

    private BaseResult<TaskDetailDeptListDTO> getTaskDetailDeptGroupFromEngine(Long page, int size, TaskInfo taskInfo) {

        TaskDetailDeptListDTO taskDetailDeptListDTOTmp = TaskDetailDeptListDTO.newEmpty(page, (long) size);
        if (System.currentTimeMillis() - taskInfo.getUpdateTime() > TimeUnit.DAYS.toMillis(7)) {
            taskDetailDeptListDTOTmp.setRecords(Collections.emptyList());
            taskDetailDeptListDTOTmp.setPushAllTime(taskInfo.getTaskPushAllTime());
            taskDetailDeptListDTOTmp.setAllCount(taskInfo.getTaskTotalCount());
            taskDetailDeptListDTOTmp.setPushCount(taskInfo.getTaskPushCount());
            taskDetailDeptListDTOTmp.setReadCount(taskInfo.getTaskReadCount());
            taskDetailDeptListDTOTmp.setRetractCount(taskInfo.getTaskRetractCount());
            return BaseResult.of(taskDetailDeptListDTOTmp);
        }

        // 组聚合条数
        List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery().eq(PublishScopeInfo::getTaskId, taskInfo.getId())
        );

        if (taskService.isRush(taskInfo)) {
            // 消耗时间和条数相关
            MessageNumberAndTimeResponse numberAndTimeResponse =
                    messageGrpcClient.getMessageNumberByExtraIdBatch(Collections.singletonList(taskInfo.getExtraId()));
            if (numberAndTimeResponse.getMessageNumberAndTimeListList().isEmpty()) {
                return BaseResult.of(taskDetailDeptListDTOTmp);
            }
            MessageNumberAndTime messageNumberAndTime = numberAndTimeResponse.getMessageNumberAndTimeListList().get(0);
            taskDetailDeptListDTOTmp.setPushCount(messageNumberAndTime.getPushCount());
            taskDetailDeptListDTOTmp.setPushAllTime(messageNumberAndTime.getCostTime());
            taskDetailDeptListDTOTmp.setReadCount(messageNumberAndTime.getReadCount());
            taskDetailDeptListDTOTmp.setRetractCount(messageNumberAndTime.getRetractCount());
            setAllCount(taskDetailDeptListDTOTmp, publishScopeInfoList);
        } else {
            List<String> subExtraIdList =
                    taskService.list(Wrappers.<TaskInfo>lambdaQuery().eq(TaskInfo::getParentTaskId,
                                    taskInfo.getId()))
                            .stream()
                            .map(TaskInfo::getExtraId)
                            .collect(Collectors.toList());
            MessageReadResponse messageReadResponse = messageGrpcClient.getTaskReadData(taskInfo.getExtraId(),
                    subExtraIdList);
            taskDetailDeptListDTOTmp.setPushCount(messageReadResponse.getMessageRead().getSendCount());
            taskDetailDeptListDTOTmp.setReadCount(messageReadResponse.getMessageRead().getReadCount());
            taskDetailDeptListDTOTmp.setRetractCount(messageReadResponse.getMessageRead().getRetractCount());
            taskDetailDeptListDTOTmp.setAllCount(messageReadResponse.getMessageRead().getTotalCount());
            taskDetailDeptListDTOTmp.setPushAllTime(messageReadResponse.getMessageRead().getCostTime());
        }

        List<String> deptIdList =
                publishScopeInfoList.stream().map(PublishScopeInfo::getScopeKey).collect(Collectors.toList());

        List<DepartmentInfo> departmentInfoList = departmentInfoMapper.selectList(
                Wrappers.<DepartmentInfo>lambdaQuery().in(DepartmentInfo::getDeptId, deptIdList)
        );
        List<String> deptLevel2IdList = departmentInfoList.stream().filter(p -> p.getTreeLevelNum() == 1).distinct()
                .map(DepartmentInfo::getDeptId).collect(Collectors.toList());

        List<String> deptLevel3IdList = departmentInfoList.stream().filter(p -> p.getTreeLevelNum() == 2).distinct()
                .map(DepartmentInfo::getDeptId).collect(Collectors.toList());

        List<String> deptLevel4IdList = departmentInfoList.stream().filter(p -> p.getTreeLevelNum() == 3).distinct()
                .map(DepartmentInfo::getDeptId).collect(Collectors.toList());

        if (deptLevel2IdList.isEmpty() && deptLevel3IdList.isEmpty() && deptLevel4IdList.isEmpty()) {
            return BaseResult.of(taskDetailDeptListDTOTmp);
        }

        MessageResultSingleForDeptResponse messageResultSingleForDeptResponse =
                messageGrpcClient.getMessageGroupDeptByExtraId(taskInfo.getExtraId(), deptLevel2IdList,
                        deptLevel3IdList, deptLevel4IdList, page, size);
        MessageGroupDeptResultPage messageGroupDeptResultPage =
                messageResultSingleForDeptResponse.getMessageGroupDeptResultPage();
        taskDetailDeptListDTOTmp.setTotal(messageGroupDeptResultPage.getTotal());

        List<MessageGroupDeptResultRecord> resultRecordList = messageGroupDeptResultPage.getRecordsList();
        List<TaskMessageDeptListRecordDTO> taskMessageDeptListRecordDTOList = new ArrayList<>();
        resultRecordList.forEach(
                messageRecord -> {
                    TaskMessageDeptListRecordDTO taskMessageDeptListRecordDTO =
                            buildDeptListRecordDTOFromEngine(messageRecord);
                    taskMessageDeptListRecordDTOList.add(taskMessageDeptListRecordDTO);
                }
        );
        taskDetailDeptListDTOTmp.setRecords(taskMessageDeptListRecordDTOList);
        return BaseResult.of(taskDetailDeptListDTOTmp);
    }

    private void setAllCount(TaskDetailDeptListDTO taskDetailDeptListDTOTmp,
                             List<PublishScopeInfo> publishScopeInfoList) {
        long allCount = 0;
        if (!publishScopeInfoList.isEmpty()) {
            allCount = publishScopeInfoList.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).orElse(0L);
        }
        taskDetailDeptListDTOTmp.setAllCount(allCount);
    }

    private TaskMessageDeptListRecordDTO buildDeptListRecordDTOFromEngine(MessageGroupDeptResultRecord messageRecord) {
        TaskMessageDeptListRecordDTO taskMessageDeptListRecordDTO = new TaskMessageDeptListRecordDTO();
        taskMessageDeptListRecordDTO.setMiDeptLevel2(messageRecord.getMiDeptLevel2Desc());
        taskMessageDeptListRecordDTO.setMiDeptLevel3(messageRecord.getMiDeptLevel3Desc());
        taskMessageDeptListRecordDTO.setMiDeptLevel4(messageRecord.getMiDeptLevel4Desc());
        PushResultDTO pushResultDTO = new PushResultDTO();
        pushResultDTO.setAllCount(messageRecord.getAllCount());
        pushResultDTO.setPushCount(messageRecord.getPushCount());
        pushResultDTO.setTodoCount(messageRecord.getTodoCount());
        pushResultDTO.setRetractCount(messageRecord.getRetractCount());
        taskMessageDeptListRecordDTO.setPushResult(pushResultDTO);
        taskMessageDeptListRecordDTO.setIsSuccess(pushResultDTO.getAllCount().equals(pushResultDTO.getPushCount()));
        return taskMessageDeptListRecordDTO;
    }

    private BaseResult<TaskDetailDeptListDTO> getTaskDetailAllFromEngine(Long page, int size, TaskInfo taskInfo,
                                                                         PublishScopeEnum publishScopeEnum) {
        TaskDetailDeptListDTO taskDetailDeptListDTO = TaskDetailDeptListDTO.newEmpty(page, (long) size);
        if (System.currentTimeMillis() - taskInfo.getUpdateTime() > TimeUnit.DAYS.toMillis(7)) {
            taskDetailDeptListDTO.setRecords(Collections.emptyList());
            taskDetailDeptListDTO.setPushAllTime(taskInfo.getTaskPushAllTime());
            taskDetailDeptListDTO.setAllCount(taskInfo.getTaskTotalCount());
            taskDetailDeptListDTO.setPushCount(taskInfo.getTaskPushCount());
            taskDetailDeptListDTO.setReadCount(taskDetailDeptListDTO.getReadCount());
            taskDetailDeptListDTO.setRetractCount(taskDetailDeptListDTO.getRetractCount());
            return BaseResult.of(taskDetailDeptListDTO);
        }

        if (taskService.isRush(taskInfo)) {
            // 消耗时间和条数相关
            MessageNumberAndTimeResponse numberAndTimeResponse =
                    messageGrpcClient.getMessageNumberByExtraIdBatch(Collections.singletonList(taskInfo.getExtraId()));
            if (numberAndTimeResponse.getMessageNumberAndTimeListList().isEmpty()) {
                return BaseResult.of(TaskDetailDeptListDTO.newEmpty(page, (long) size));
            }
            MessageNumberAndTime messageNumberAndTime = numberAndTimeResponse.getMessageNumberAndTimeListList().get(0);
            taskDetailDeptListDTO.setPushCount(messageNumberAndTime.getPushCount());
            taskDetailDeptListDTO.setPushAllTime(messageNumberAndTime.getCostTime());
            taskDetailDeptListDTO.setReadCount(messageNumberAndTime.getReadCount());
            taskDetailDeptListDTO.setRetractCount(messageNumberAndTime.getRetractCount());
            // 组聚合条数
            List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                    Wrappers.<PublishScopeInfo>lambdaQuery().eq(PublishScopeInfo::getTaskId, taskInfo.getId())
            );
            long allCount = 0;
            if (PublishScopeEnum.CHOOSE_PUSH.getCode().equals(publishScopeEnum.getCode())) {
                allCount =
                        publishScopeInfoList.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).orElse(0L);
            } else {
                if (!publishScopeInfoList.isEmpty()) {
                    allCount = publishScopeInfoList.get(0).getAllCount();
                }
            }
            taskDetailDeptListDTO.setAllCount(allCount);
        } else {
            List<String> subExtraIdList =
                    taskService.list(Wrappers.<TaskInfo>lambdaQuery().eq(TaskInfo::getParentTaskId,
                                    taskInfo.getId()))
                            .stream()
                            .map(TaskInfo::getExtraId)
                            .collect(Collectors.toList());
            MessageReadResponse messageReadResponse =
                    messageGrpcClient.getTaskReadData(taskInfo.getExtraId(), subExtraIdList);
            taskDetailDeptListDTO.setAllCount(messageReadResponse.getMessageRead().getTotalCount());
            taskDetailDeptListDTO.setPushCount(messageReadResponse.getMessageRead().getSendCount());
            taskDetailDeptListDTO.setReadCount(messageReadResponse.getMessageRead().getReadCount());
            taskDetailDeptListDTO.setRetractCount(messageReadResponse.getMessageRead().getRetractCount());
            taskDetailDeptListDTO.setPushAllTime(messageReadResponse.getMessageRead().getCostTime());
        }
        return BaseResult.of(taskDetailDeptListDTO);
    }

    private BaseResult<TaskDetailDeptListDTO> getTaskDetailAll(Long page, int size, TaskInfo taskInfo,
                                                               PublishScopeEnum publishScopeEnum) {
        TaskDetailDeptListDTO taskDetailDeptListDTO = TaskDetailDeptListDTO.newEmpty(page, (long) size);

        List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery().eq(PublishScopeInfo::getTaskId, taskInfo.getId())
        );
        long allCount = 0;
        if (PublishScopeEnum.ALL_PUSH.getCode().equals(publishScopeEnum.getCode()) ||
                PublishScopeEnum.UNREAD_PUSH.getCode().equals(publishScopeEnum.getCode())) {
            if (!publishScopeInfoList.isEmpty()) {
                allCount = publishScopeInfoList.get(0).getAllCount();
            }
        } else if (PublishScopeEnum.CHOOSE_PUSH.getCode().equals(publishScopeEnum.getCode())) {
            allCount = publishScopeInfoList.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).orElse(0L);
        }
        taskDetailDeptListDTO.setAllCount(allCount);

        return BaseResult.of(taskDetailDeptListDTO);
    }

    private BaseResult<TaskDetailCustomListDTO> getTaskDetailCustomFromEngine(Long page, int size, TaskInfo taskInfo) {
        TaskDetailCustomListDTO taskDetailCustomListDTO = new TaskDetailCustomListDTO();
        if (System.currentTimeMillis() - taskInfo.getUpdateTime() > TimeUnit.DAYS.toMillis(7)) {
            taskDetailCustomListDTO.setRecords(Collections.emptyList());
            taskDetailCustomListDTO.setPushAllTime(taskInfo.getTaskPushAllTime());
            taskDetailCustomListDTO.setAllCount(taskInfo.getTaskTotalCount());
            taskDetailCustomListDTO.setPushCount(taskInfo.getTaskPushCount());
            taskDetailCustomListDTO.setReadCount(taskInfo.getTaskReadCount());
            taskDetailCustomListDTO.setRetractCount(taskInfo.getTaskRetractCount());
            return BaseResult.of(taskDetailCustomListDTO);
        }

        if (taskService.isRush(taskInfo)) {
            MessageNumberAndTimeResponse messageNumberByExtraIdBatch =
                    messageGrpcClient.getMessageNumberByExtraIdBatch(Collections.singletonList(taskInfo.getExtraId()));
            List<MessageNumberAndTime> messageNumberAndTimeList =
                    messageNumberByExtraIdBatch.getMessageNumberAndTimeListList();

            taskDetailCustomListDTO.setAllCount(messageNumberAndTimeList.get(0).getAllCount());
            taskDetailCustomListDTO.setPushCount(messageNumberAndTimeList.get(0).getPushCount());
            taskDetailCustomListDTO.setPushAllTime(messageNumberAndTimeList.get(0).getCostTime());
            taskDetailCustomListDTO.setReadCount(messageNumberAndTimeList.get(0).getReadCount());
            taskDetailCustomListDTO.setRetractCount(messageNumberAndTimeList.get(0).getRetractCount());
        } else {
            List<String> subExtraIdList =
                    taskService.list(Wrappers.<TaskInfo>lambdaQuery().eq(TaskInfo::getParentTaskId,
                                    taskInfo.getId()))
                            .stream()
                            .map(TaskInfo::getExtraId)
                            .collect(Collectors.toList());
            MessageReadResponse messageReadResponse =
                    messageGrpcClient.getTaskReadData(taskInfo.getExtraId(), subExtraIdList);
            taskDetailCustomListDTO.setAllCount(messageReadResponse.getMessageRead().getTotalCount());
            taskDetailCustomListDTO.setPushCount(messageReadResponse.getMessageRead().getSendCount());
            taskDetailCustomListDTO.setReadCount(messageReadResponse.getMessageRead().getReadCount());
            taskDetailCustomListDTO.setPushAllTime(messageReadResponse.getMessageRead().getCostTime());
            taskDetailCustomListDTO.setRetractCount(messageReadResponse.getMessageRead().getRetractCount());
        }

        MessageResultSingleForCustomResponse messagePageByExtraId =
                messageGrpcClient.getMessagePageByExtraId(taskInfo.getExtraId(), page, size);

        taskDetailCustomListDTO.setCurrent(messagePageByExtraId.getMessageResultPage().getCurrentPage());
        taskDetailCustomListDTO.setTotal(messagePageByExtraId.getMessageResultPage().getTotal());
        taskDetailCustomListDTO.setSize(messagePageByExtraId.getMessageResultPage().getSize());
        List<MessageRecord> recordsList = messagePageByExtraId.getMessageResultPage().getRecordsList();
        List<TaskMessageCustomListRecordDTO> taskMessageCustomListRecordDTOList = new ArrayList<>();
        recordsList.forEach(
                messageRecord -> {
                    TaskMessageCustomListRecordDTO taskMessageCustomListRecordDTO =
                            new TaskMessageCustomListRecordDTO();
                    EmployeeInfo employeeInfo =
                            employeeService.getOne(Wrappers.<EmployeeInfo>lambdaQuery().eq(EmployeeInfo::getUsername,
                                    messageRecord.getUsername()).last("limit 1"));
                    taskMessageCustomListRecordDTO.setName(Objects.nonNull(employeeInfo) ? employeeInfo.getName() :
                            StringUtils.EMPTY);
                    taskMessageCustomListRecordDTO.setUsername(messageRecord.getUsername());
                    taskMessageCustomListRecordDTO.setUserEmail(messageRecord.getEmail());
                    setNameAndDeptIdAndDeptName(messageRecord, taskMessageCustomListRecordDTO);
                    // 1-发送中，2-发送成功，3-发送失败，4-消息中断
                    setIsSuccessAndPushResDesc(messageRecord, taskMessageCustomListRecordDTO);
                    boolean isSuccess = taskMessageCustomListRecordDTO.getIsSuccess();
                    if (!isSuccess) {
                        taskMessageCustomListRecordDTO.setErrorLog(messageRecord.getErrorLog());
                    }
                    taskMessageCustomListRecordDTOList.add(taskMessageCustomListRecordDTO);
                }
        );
        taskDetailCustomListDTO.setRecords(taskMessageCustomListRecordDTOList);

        return BaseResult.of(taskDetailCustomListDTO);
    }

    private BaseResult<TaskDetailCustomListDTO> getTaskDetailCustom(Long page, int size, TaskInfo taskInfo) {

        //高管名单
        List<String> executiveUsernames = Splitter.on(",").splitToList(executives);

        TaskDetailCustomListDTO taskDetailCustomListDTO = TaskDetailCustomListDTO.newEmpty(page, (long) size);
        if (Objects.isNull(taskInfo)) {
            return BaseResult.of(taskDetailCustomListDTO);
        }

        List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery().eq(PublishScopeInfo::getTaskId, taskInfo.getId())
        );

        if (publishScopeInfoList.isEmpty()) {
            return BaseResult.of(taskDetailCustomListDTO);
        }
        PublishScopeInfo publishScopeInfo = publishScopeInfoList.get(0);

        String scopeKey = publishScopeInfo.getScopeKey();

        if (StringUtils.isNotBlank(scopeKey)) {

            // page
            Page<ParseTmpInfo> dataPage = new Page<>(page, size);

            LambdaQueryWrapper<ParseTmpInfo> lambdaQuery = Wrappers.lambdaQuery();

            lambdaQuery.eq(ParseTmpInfo::getExcelId, scopeKey);

            lambdaQuery.eq(ParseTmpInfo::getPublishScope, taskInfo.getPublishScope());

            lambdaQuery.notIn(ParseTmpInfo::getUsername, executiveUsernames);

            lambdaQuery.lt(ParseTmpInfo::getDataStatus, ParseDataStatusEnum.EMPLOYEE_NOT_EXIST.getCode());

            IPage<ParseTmpInfo> parseTmpInfoPage = parseTmpInfoMapper.selectPage(dataPage, lambdaQuery);

            List<ParseTmpInfo> parseTmpInfoList = parseTmpInfoPage.getRecords();

            if (parseTmpInfoList.isEmpty()) {
                return BaseResult.of(taskDetailCustomListDTO);
            }

            List<TaskMessageCustomListRecordDTO> taskMessageCustomListRecordList = new ArrayList<>();

            parseTmpInfoList.forEach(
                    parseTmpInfo -> {
                        TaskMessageCustomListRecordDTO taskMessageCustomListRecordDTO =
                                new TaskMessageCustomListRecordDTO();
                        taskMessageCustomListRecordDTO.setIsSuccess(false);
                        taskMessageCustomListRecordDTO.setUserEmail(parseTmpInfo.getEmail());
                        taskMessageCustomListRecordDTO.setUsername(parseTmpInfo.getUsername());
                        taskMessageCustomListRecordDTO.setName(parseTmpInfo.getName());
                     /*   taskMessageCustomListRecordDTO.setDeptId(parseTmpInfo.getDeptId());
                        taskMessageCustomListRecordDTO.setDeptName(parseTmpInfo.getDeptDesc());*/
                        taskMessageCustomListRecordDTO.setPushResDesc("未推送");
                        taskMessageCustomListRecordDTO.setErrorLog("未推送");
                        taskMessageCustomListRecordList.add(taskMessageCustomListRecordDTO);
                    }
            );

            taskDetailCustomListDTO.setTotal(parseTmpInfoPage.getTotal());
            taskDetailCustomListDTO.setCurrent(parseTmpInfoPage.getCurrent());
            taskDetailCustomListDTO.setSize(parseTmpInfoPage.getSize());
            taskDetailCustomListDTO.setPages(parseTmpInfoPage.getPages());
            taskDetailCustomListDTO.setAllCount(parseTmpInfoPage.getTotal());
            taskDetailCustomListDTO.setPushCount(0L);
            taskDetailCustomListDTO.setPushAllTime(0L);
            taskDetailCustomListDTO.setReadCount(0L);
            taskDetailCustomListDTO.setRetractCount(0L);
            taskDetailCustomListDTO.setRecords(taskMessageCustomListRecordList);

            return BaseResult.of(taskDetailCustomListDTO);
        }

        return BaseResult.of(taskDetailCustomListDTO);
    }

    @Override
    public BaseResult<Object> getSummaryList(UserBO userBO) {

        List<TaskInfo> taskInfoList = taskService.list(
                Wrappers.<TaskInfo>lambdaQuery()
                        .eq(TaskInfo::getCreateUsername, userBO.getUsername())
                        .in(TaskInfo::getTaskStatus, TaskStatusEnum.PUBLISH_SUCCESS.getCode())
                        .orderByDesc(TaskInfo::getId)
        );

        List<TaskSummaryDTO> list = new ArrayList<>();
        for (TaskInfo taskInfo : taskInfoList) {

            TaskChannelEnum channelEnum = TaskChannelEnum.getByCode(taskInfo.getChannel());
            if (channelEnum == TaskChannelEnum.NO) {
                continue;
            }
            String name = String.format("%s【%s】", taskInfo.getTitleCn(), channelEnum.getMsg());
            list.add(new TaskSummaryDTO(taskInfo.getId(), name));
        }

        Map<String, Object> data = new HashMap<>(1);
        data.put("list", list);

        return BaseResult.of(data);
    }

    @Override
    public BaseResult<Object> prePushMsg(UserBO userBO, MessagePrePushDTO messagePrePushDTO) {

        TaskInfo taskInfo = taskService.getById(messagePrePushDTO.getTaskId());
        if (Objects.isNull(taskInfo)) {
            throw new IllegalArgumentException("消息任务未保存");
        }

        // 鉴权，无权访问
        if (!(userBO.getRoleList().contains("ROLE_SYS_SUPER_ADMIN") ||
                userBO.getRoleList().contains("ROLE_SYS_ADMIN") ||
                userBO.getRoleList().contains("ROLE_OPERATOR") ||
                userBO.getRoleList().contains("ROLE_BOT_ADMIN") ||
                permissionManager.checkHaveAccessAudit(userBO.getUsername(), taskInfo.getId()) ||
                taskInfo.getCreateUsername().equals(userBO.getUsername()))) {
            return new BaseResult<>(ResultCode.FORBIDDEN);
        }

        if (!checkPreScope(userBO, messagePrePushDTO)) {
            return new BaseResult<>(ResultCode.PARAM_ERROR.getCode(), "预推送名单存在不合规人员");
        }

        TaskChannelEnum channelEnum = TaskChannelEnum.getByCode(taskInfo.getChannel());
        switch (channelEnum) {
            case CHANNEL_LARK:
                return preLarkPushMsg(userBO, taskInfo, messagePrePushDTO);
            case CHANNEL_EMAIL:
                return preEmailPushMsg(userBO, taskInfo, messagePrePushDTO);
            case CHANNEL_SMS:
                return preSmPushMsg(userBO, taskInfo, messagePrePushDTO);
            default:
                throw new IllegalArgumentException("消息渠道不存在");
        }

    }

    private boolean checkPreScope(UserBO userBO, MessagePrePushDTO messagePrePushDTO) {
        // 2. 检查机器人的预推送名单是否在库里
        boolean isAllowedPush = true;

        List<String> preSendList = messagePrePushDTO.getUsernameList();

        List<PrePushListInfo> defaultPreSendList = prePushListInfoMapper.selectList(Wrappers.lambdaQuery());
        List<String> defaultPreSendUsernameList;
        if (!defaultPreSendList.isEmpty()) {
            // 检查库里的推送名单是否包含 前端传来的预推送名单
            defaultPreSendUsernameList =
                    defaultPreSendList.stream().map(PrePushListInfo::getUsername).collect(Collectors.toList());
            defaultPreSendUsernameList.add(userBO.getUsername());
        } else {
            // 如果库里为空,
            defaultPreSendUsernameList = Collections.singletonList(userBO.getUsername());
        }
        isAllowedPush = defaultPreSendUsernameList.containsAll(preSendList);

        return isAllowedPush;
    }

    private BaseResult<Object> preLarkPushMsg(UserBO userBO, TaskInfo taskInfo, MessagePrePushDTO messagePrePushDTO) {
        // 查询对应的飞书任务
        LarkTaskInfo larkTaskInfo = larkTaskInfoMapper.selectOne(
                Wrappers.<LarkTaskInfo>lambdaQuery()
                        .eq(LarkTaskInfo::getTaskId, taskInfo.getId())
        );
        // 1.检查机器人是否启用
        BotInfo botInfo = botInfoMapper.selectById(larkTaskInfo.getBotId());
        if (botInfo.getBotStatus() == UmsRobotStatusEnum.UMS_ROBOT_ON.getCode().intValue()
                && botInfo.getBotLarkStatus() == LarkRobotStatusEnum.LARK_ROBOT_ON.getCode().intValue()
        ) {
            sendMessage.preSendMessageTask(taskInfo, messagePrePushDTO.getUsernameList());
            return BaseResult.of();
        } else {
            return new BaseResult<>(ResultCode.SERVER_INNER_ERROR.getCode(), "机器人当前状态为未启用");
        }
    }

    private BaseResult<Object> preEmailPushMsg(UserBO userBO, TaskInfo taskInfo, MessagePrePushDTO messagePrePushDTO) {
        sendMessage.preSendMessageTask(taskInfo, messagePrePushDTO.getUsernameList());
        return BaseResult.of();
    }

    private BaseResult<Object> preSmPushMsg(UserBO userBO, TaskInfo taskInfo, MessagePrePushDTO messagePrePushDTO) {
        sendMessage.preSendMessageTask(taskInfo, messagePrePushDTO.getUsernameList());
        return BaseResult.of();
    }

    @Override
    public BaseResult<Object> stopMessageTask(UserBO userBO, TaskResultDTO taskResultDTO) {

        Long taskId = taskResultDTO.getTaskId();
        // 查询是否越权操作
        boolean haveAccess = permissionManager.isHaveMessagePermission(userBO, taskId);
        if (!haveAccess) {
            return new BaseResult<>(ResultCode.FORBIDDEN);
        }

        TaskInfo taskInfo = taskService.getById(taskId);

        if (taskResultDTO.getTaskStatus() == TaskStatusEnum.CANCEL.getCode()) {
            if (taskInfo.getTaskStatus() == TaskStatusEnum.PUBLISHING.getCode() ||
                    taskInfo.getTaskStatus() == TaskStatusEnum.PUBLISH_SUCCESS.getCode()) {
                throw new IllegalArgumentException("该消息已发布不能取消");
            } else {
                if (taskId < Long.parseLong(bpmStartTaskId)) {
                    processManager.terminate(taskId, userBO.getUsername());
                } else {
                    if (!messageProcess.isApprovalFinish(taskInfo)) {
                        if (!messageProcess.terminate(taskInfo.getCreateUsername(), taskInfo)) {
                            log.error("取消流程失败");
                        }
                    }
                }
            }
        }

        TaskInfo updateTaskInfo = TaskInfo.newUpdateTimeInstant();
        updateTaskInfo.setId(taskId);
        updateTaskInfo.setTaskStatus(taskResultDTO.getTaskStatus());
        updateTaskInfo.setTaskEngineStatus(TaskStatusEnum.NO.getCode());
        updateTaskInfo.setUpdateUsername(userBO.getUsername());
        taskService.updateById(updateTaskInfo);

        if (taskResultDTO.getTaskStatus() == TaskStatusEnum.INTERRUPT.getCode()) {
            // 中断
            messageGrpcClient.interruptMessageByExtraId(taskInfo.getExtraId());
        }
        return BaseResult.of();
    }

    @Override
    public BaseResult<LarkTaskDetailDTO> taskLarkDetail(Long taskId, UserBO userBO) {

        TaskInfo taskInfo = taskService.getOne(
                Wrappers.<TaskInfo>lambdaQuery().eq(TaskInfo::getId, taskId)
        );

        List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery().eq(PublishScopeInfo::getTaskId, taskId)
        );

        LarkTaskDetailDTO larkTaskDetailDTO = new LarkTaskDetailDTO();
        List<Byte> userType = new ArrayList<>();
        if (!Objects.isNull(taskInfo)) {
            larkTaskDetailDTO.setTaskId(taskId);
            larkTaskDetailDTO.setTitleCn(taskInfo.getTitleCn());
            larkTaskDetailDTO.setTitleEn(taskInfo.getTitleEn());
            larkTaskDetailDTO.setChannel(taskInfo.getChannel());
            larkTaskDetailDTO.setPublishScope(taskInfo.getPublishScope());
            larkTaskDetailDTO.setCreateTime(taskInfo.getCreateTime());
            larkTaskDetailDTO.setPublishTime(taskInfo.getPublishTime());
            larkTaskDetailDTO.setTaskStatus(taskInfo.getTaskStatus());
            setTaskStatus(taskInfo, larkTaskDetailDTO);
            if (userBO.getUsername().equals(taskInfo.getCreateUsername())) {
                userType.add((byte) 1);
            }
            larkTaskDetailDTO.setContentCn(taskInfo.getContentCn());
            larkTaskDetailDTO.setContentEn(taskInfo.getContentEn());
            if (!publishScopeInfoList.isEmpty()) {
                List<String> deptIdS =
                        publishScopeInfoList.stream().map(PublishScopeInfo::getScopeKey).collect(Collectors.toList());
                larkTaskDetailDTO.setScopeKey(deptIdS);
                if (taskInfo.getPublishScope().equals(PublishScopeEnum.DEPT_PUSH.getCode())) {
                    List<ScopeKeyDeptsDTO> scopeKeyDeptsDTOS = departmentInfoMapper.selectList(
                                    Wrappers.<DepartmentInfo>lambdaQuery().in(DepartmentInfo::getDeptId, deptIdS)
                            ).stream().map(p -> new ScopeKeyDeptsDTO(p.getDeptId(), p.getCnName())).distinct()
                            .collect(Collectors.toList());
                    larkTaskDetailDTO.setScopeKeyDepts(scopeKeyDeptsDTOS);
                }
            }

            larkTaskDetailDTO.setCreateUsername(taskInfo.getCreateUsername());
            EmployeeInfo employeeInfo = employeeService.getOne(
                    Wrappers.<EmployeeInfo>lambdaQuery()
                            .eq(EmployeeInfo::getUsername, taskInfo.getCreateUsername())
                            .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
            );
            if (!Objects.isNull(employeeInfo)) {
                larkTaskDetailDTO.setCreateName(employeeInfo.getName());
                larkTaskDetailDTO.setCreateUserEmail(employeeInfo.getEmail());
            }
        }

        LarkTaskInfo larkTaskInfo = larkTaskInfoMapper.selectOne(
                Wrappers.<LarkTaskInfo>lambdaQuery().eq(LarkTaskInfo::getTaskId, taskId)
        );

        if (Objects.nonNull(larkTaskInfo)) {
            larkTaskDetailDTO.setLarkMessageType(larkTaskInfo.getLarkMessageType());
            larkTaskDetailDTO.setLandingPageUrl(larkTaskInfo.getLandingPageUrl());
            larkTaskDetailDTO.setButton(larkTaskInfo.getButton());
            larkTaskDetailDTO.setScheduleStartTime(larkTaskInfo.getScheduleStartTime());
            larkTaskDetailDTO.setScheduleEndTime(larkTaskInfo.getScheduleEndTime());
            larkTaskDetailDTO.setScheduleDescription(larkTaskInfo.getScheduleDescription());
            BotInfo botInfo = botInfoMapper.selectById(larkTaskInfo.getBotId());
            if (Objects.nonNull(botInfo)) {
                larkTaskDetailDTO.setBotId(botInfo.getId());
                larkTaskDetailDTO.setBotName(botInfo.getBotName());
            }
        }
        if (permissionManager.checkNowHaveAccessAudit(userBO.getUsername(), taskId)) {
            userType.add((byte) 1);
        }
        larkTaskDetailDTO.setUserType(userType);
        return BaseResult.of(larkTaskDetailDTO);
    }

    @Override
    public BaseResult<TaskResultDTO> taskLarkRetract(Long taskId, UserBO userBO) {
        TaskInfo taskInfo = taskService.getById(taskId);

        LarkTaskInfo larkTaskInfo =
                larkTaskInfoMapper.selectOne(Wrappers.<LarkTaskInfo>lambdaQuery().eq(LarkTaskInfo::getTaskId,
                        taskId));

        if (Objects.isNull(taskInfo) || Objects.isNull(larkTaskInfo)) {
            throw new IllegalArgumentException("没有此任务信息");
        }

        boolean haveMessagePermission = permissionManager.isHaveMessagePermission(userBO, taskId);
        if (!haveMessagePermission) {
            throw new IllegalArgumentException("您没有权限访问此接口");
        }

        BotInfo botInfo = botInfoMapper.selectById(larkTaskInfo.getBotId());
        if (Objects.isNull(botInfo)) {
            throw new IllegalArgumentException("该任务对应的机器人不存在");
        }

        if (TaskStatusEnum.PUBLISH_SUCCESS.getCode() != taskInfo.getTaskStatus() &&
                TaskStatusEnum.PUBLISHING.getCode() != taskInfo.getTaskStatus()) {
            throw new IllegalArgumentException("只有发布成功或者发布中的任务才可以撤回");
        }

        CommonResponse commonResponse = messageGrpcClient.retractTask(taskInfo.getExtraId());
        if (commonResponse.getCode() != ResultCode.OK.getCode()) {
            throw new RuntimeException("撤回失败");
        }

        taskInfo.setTaskStatus(TaskStatusEnum.RETRACTED.getCode());
        taskService.updateById(taskInfo);

        TaskResultDTO taskResultDTO = new TaskResultDTO();
        taskResultDTO.setTaskId(taskInfo.getId());
        taskResultDTO.setTaskStatus(taskInfo.getTaskStatus());

        return BaseResult.of(taskResultDTO);
    }

    private void setTaskStatus(TaskInfo taskInfo, LarkTaskDetailDTO larkTaskDetailDTO) {
        if (taskInfo.getTaskEngineStatus() == TaskStatusEnum.PUBLISHING.getCode()) {
            larkTaskDetailDTO.setTaskStatus(TaskStatusEnum.PUBLISHING.getCode());
        } else if (taskInfo.getTaskEngineStatus() == TaskStatusEnum.PUBLISH_SUCCESS.getCode()) {
            larkTaskDetailDTO.setTaskStatus(TaskStatusEnum.PUBLISH_SUCCESS.getCode());
        }
    }

    @Override
    public BaseResult<Object> taskDetail(Long taskId, UserBO userBO) {

        BaseResult<Object> result = new BaseResult<>();

        BaseTaskVO baseTaskVO = taskService.selectTaskDetailCommon(taskId);

        if (Objects.isNull(baseTaskVO)) {
            return new BaseResult<>().setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setMessage("任务不存在");
        }

        List<Byte> userType = new ArrayList<>();
        if (userBO.getUsername().equals(baseTaskVO.getCreateUsername()) ||
                userRoleManager.isSuperAdmin(userBO.getUsername())) {
            userType.add((byte) 1);
        }
        try {
            if (permissionManager.checkNowHaveAccessAudit(userBO.getUsername(), taskId)) {
                userType.add((byte) 1);
            }
            if (permissionManager.checkHaveAccessAudit(userBO.getUsername(), taskId)) {
                userType.add((byte) 1);
            }
        } catch (Exception e) {
            log.info("查询任务id = [{}]的任务审核权限时发生异常", taskId, e);
        }

        if (baseTaskVO.getTaskStatus() <= TaskStatusEnum.APPROVED.getCode()) {
            if (!PublishScopeEnum.GROUP_PUSH.getCode().equals(baseTaskVO.getPublishScope())) {
                if (calAllCount(taskId, PublishScopeEnum.getByCode(baseTaskVO.getPublishScope()), null)) {
                    baseTaskVO.setPublishScopeChanged(true);
                }
            } else {
                LarkTaskInfo larkTaskInfo =
                        larkTaskInfoMapper.selectOne(Wrappers.<LarkTaskInfo>lambdaQuery().eq(LarkTaskInfo::getTaskId,
                                taskId));

                if (Objects.nonNull(larkTaskInfo)) {
                    BotInfo botInfo = botInfoMapper.selectById(larkTaskInfo.getBotId());
                    if (Objects.nonNull(botInfo)) {
                        List<LarkGroupPushInfo> localLarkGroupPushInfoList =
                                larkGroupPushInfoMapper.selectList(Wrappers.<LarkGroupPushInfo>lambdaQuery()
                                        .in(LarkGroupPushInfo::getTagId, baseTaskVO.getScopeKey()));

                        GroupListResponse groupListResponse = messageGrpcClient.fetchGroupList(botInfo.getBotKey(),
                                umsBotAppId);

                        int code = groupListResponse.getCode();
                        if (code == ResultCode.OK.getCode()) {

                            List<GroupRecord> groupList = groupListResponse.getGroupList();
                            Set<String> factGroupSet =
                                    groupList.stream()
                                            .map(GroupRecord::getChatId)
                                            .collect(Collectors.toSet());

                            List<Long> toDelIds =
                                    localLarkGroupPushInfoList
                                            .stream()
                                            .filter(larkGroupPushInfo -> !factGroupSet.contains(
                                                    larkGroupPushInfo.getChatId()))
                                            .map(LarkGroupPushInfo::getId)
                                            .collect(Collectors.toList());

                            if (!toDelIds.isEmpty()) {
                                larkGroupPushInfoMapper.deleteBatchIds(toDelIds);
                                baseTaskVO.setPublishScopeChanged(true);
                            }
                        }

                        List<PublishScopeInfo> scopeInfoList = publishScopeInfoMapper.selectList(
                                Wrappers.<PublishScopeInfo>lambdaQuery()
                                        .eq(PublishScopeInfo::getTaskId, taskId)
                        );
                        long allCount = larkGroupPushInfoMapper.selectCount(
                                Wrappers.<LarkGroupPushInfo>lambdaQuery()
                                        .in(LarkGroupPushInfo::getTagId, baseTaskVO.getScopeKey())
                        );
                        scopeInfoList.get(0).setAllCount(allCount);
                        publishScopeInfoMapper.updateBatch(scopeInfoList);
                    }
                }
            }
        }
        if (baseTaskVO.getPublishScopeChanged()) {
            baseTaskVO = taskService.selectTaskDetailCommon(taskId);
            baseTaskVO.setPublishScopeChanged(true);
        }

        baseTaskVO.setUserType(userType.stream().distinct().collect(Collectors.toList()));

        baseTaskVO.setShowExportButton(
                (System.currentTimeMillis() - baseTaskVO.getUpdateTime()) <= TimeUnit.DAYS.toMillis(7));

        if (!baseTaskVO.getScopeKey().isEmpty()) {
            if (!baseTaskVO.getPublishScope().equals(PublishScopeEnum.DEPT_PUSH.getCode())) {
                baseTaskVO.setScopeKeyDepts(Collections.emptyList());
            }
            List<ScopeKeyDeptsDTO> scopeKeyDepts = getScopeKeyDepts(baseTaskVO.getScopeKey());
            baseTaskVO.setScopeKeyDepts(scopeKeyDepts);
        }

        TaskChannelEnum taskChannelEnum = TaskChannelEnum.getByCode(baseTaskVO.getChannel());
        switch (taskChannelEnum) {
            case CHANNEL_LARK:
                TaskLarkDetailVO taskLarkDetailVO = getLarkTaskDetail(baseTaskVO, userBO);
                result = BaseResult.of(taskLarkDetailVO);
                break;
            case CHANNEL_EMAIL:
                TaskEmailDetailVO emailTaskDetail = getEmailTaskDetail(baseTaskVO);
                result = BaseResult.of(emailTaskDetail);
                break;
            case CHANNEL_SMS:
                TaskSmsDetailVO taskSmsDetailVO = getSmsTaskDetail(baseTaskVO);
                result = BaseResult.of(taskSmsDetailVO);
                break;
            case CHANNEL_MI_PUSH:
            default:
                break;
        }

        return result;
    }

    private TaskLarkDetailVO getLarkTaskDetail(BaseTaskVO baseTaskVO, UserBO userBO) {
        //高管名单
        List<String> executiveUsernames = Splitter.on(",").splitToList(executives);
        TaskLarkDetailVO taskLarkDetailVO = new TaskLarkDetailVO();
        LarkTaskInfo larkTaskInfo = larkTaskInfoMapper.selectOne(
                Wrappers.<LarkTaskInfo>lambdaQuery().eq(LarkTaskInfo::getTaskId, baseTaskVO.getTaskId())
        );

        if (!baseTaskVO.getUserType().contains((Byte) (byte) 1) &&
                !baseTaskVO.getUserType().contains((Byte) (byte) 9)) {
            List<String> botManagerUsernameList = userBotInfoMapper.selectList(
                    Wrappers.<UserBotInfo>lambdaQuery()
                            .eq(UserBotInfo::getBotId, larkTaskInfo.getBotId())
                            .eq(UserBotInfo::getUserType, UserBotRoleEnum.USER_BOT_MANAGER.getCode())
            ).stream().map(UserBotInfo::getUsername).collect(Collectors.toList());

            if (!botManagerUsernameList.contains(userBO.getUsername())) {
                return taskLarkDetailVO;
            }
        }

        BeanUtils.copyProperties(baseTaskVO, taskLarkDetailVO);
        taskLarkDetailVO.setButtonName(larkTaskInfo.getButtonName());
        taskLarkDetailVO.setButtonNameEn(larkTaskInfo.getButtonNameEn());
        taskLarkDetailVO.setBotId(larkTaskInfo.getBotId());
        taskLarkDetailVO.setLarkMessageType(larkTaskInfo.getLarkMessageType());
        taskLarkDetailVO.setButton(larkTaskInfo.getButton());
        taskLarkDetailVO.setLandingPageUrl(larkTaskInfo.getLandingPageUrl());
        taskLarkDetailVO.setScheduleStartTime(larkTaskInfo.getScheduleStartTime());
        taskLarkDetailVO.setScheduleEndTime(larkTaskInfo.getScheduleEndTime());
        taskLarkDetailVO.setScheduleDescription(larkTaskInfo.getScheduleDescription());

        TaskInfo taskInfo = taskService.getById(larkTaskInfo.getTaskId());

        if (taskInfo.getTaskStatus() != TaskStatusEnum.RETRACTED.getCode()) {
            if (taskInfo.getTaskStatus() == TaskStatusEnum.PUBLISH_SUCCESS.getCode() &&
                    taskInfo.getTaskEngineStatus() == TaskStatusEnum.PUBLISHING.getCode()) {
                taskLarkDetailVO.setTaskStatus(TaskStatusEnum.PUBLISHING.getCode());
            } else if (taskInfo.getTaskEngineStatus() == TaskStatusEnum.PUBLISH_SUCCESS.getCode()) {
                taskLarkDetailVO.setTaskStatus(TaskStatusEnum.PUBLISH_SUCCESS.getCode());
            }
        }

        boolean isRush = taskService.isRush(taskInfo);
        List<String> subExtraIdList =
                taskService.list(Wrappers.<TaskInfo>lambdaQuery().eq(TaskInfo::getParentTaskId,
                                taskInfo.getId()))
                        .stream()
                        .map(TaskInfo::getExtraId)
                        .collect(Collectors.toList());
        MessageUnReadDetailResponse messageUnReadDetailResponse =
                messageGrpcClient.getPageUnRead(StringUtils.EMPTY,
                        1L,
                        10L,
                        taskInfo.getExtraId(),
                        subExtraIdList,
                        executiveUsernames);
        long unreadUsernameCount = messageUnReadDetailResponse.getMessageUnreadDetail().getTotalWithoutSearch();
        taskLarkDetailVO.setUnReadCount(unreadUsernameCount);

        PushResultDTO pushResult = new PushResultDTO();

        long theTimeMillis = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(7);
        if (taskInfo.getUpdateTime() < theTimeMillis) {
            pushResult.setAllCount(taskInfo.getTaskTotalCount());
            pushResult.setPushCount(taskInfo.getTaskPushCount());
            pushResult.setTodoCount(0L);
            pushResult.setReadCount(taskInfo.getTaskReadCount());
            pushResult.setRetractCount(taskInfo.getTaskRetractCount());
        } else {
            MessageNumberAndTimeResponse numberAndTimeResponse =
                    messageGrpcClient.getMessageNumberByExtraIdBatch(Collections.singletonList(taskInfo.getExtraId()));
            if (!numberAndTimeResponse.getMessageNumberAndTimeListList().isEmpty()) {
                MessageNumberAndTime messageNumberAndTime =
                        numberAndTimeResponse.getMessageNumberAndTimeListList().get(0);

                PublishScopeInfo publishScopeInfo =
                        publishScopeInfoMapper.selectOne(
                                Wrappers.<PublishScopeInfo>lambdaQuery().eq(PublishScopeInfo::getTaskId,
                                        taskInfo.getId()).last("LIMIT 1"));
                if (Objects.nonNull(publishScopeInfo)) {
                    pushResult.setAllCount(publishScopeInfo.getAllCount());
                } else {
                    pushResult.setAllCount(0L);
                }
                pushResult.setPushCount(messageNumberAndTime.getPushCount());
                pushResult.setTodoCount(messageNumberAndTime.getTodoCount());
                pushResult.setReadCount(messageNumberAndTime.getReadCount());
                pushResult.setRetractCount(messageNumberAndTime.getRetractCount());
            }
        }
        taskLarkDetailVO.setPushResultDTO(pushResult);

        taskLarkDetailVO.setRushable(!isRush &&
                TaskChannelEnum.CHANNEL_LARK.getCode() == taskInfo.getChannel() &&
                !PublishScopeEnum.GROUP_PUSH.getCode().equals(taskInfo.getPublishScope()) &&
                TaskStatusEnum.PUBLISH_SUCCESS.getCode() == taskInfo.getTaskStatus()
                && ("on".equals(rushTimeLimitSwitch) ?
                (System.currentTimeMillis() - taskInfo.getPublishSuccessTime() > TimeUnit.HOURS.toMillis(24) &&
                        System.currentTimeMillis() - taskInfo.getPublishTime() < TimeUnit.DAYS.toMillis(7)) : true)

        );

        BotInfo botInfo = botInfoMapper.selectById(larkTaskInfo.getBotId());
        taskLarkDetailVO.setBotName(botInfo.getBotName());
        taskLarkDetailVO.setBotBizId(botInfo.getBotBizId());
        taskLarkDetailVO.setAtId(larkTaskInfo.getAtId());
        taskLarkDetailVO.setBotId(larkTaskInfo.getBotId());

        return taskLarkDetailVO;
    }

    private TaskSmsDetailVO getSmsTaskDetail(BaseTaskVO baseTaskVO) {
        TaskSmsDetailVO taskSmsDetailVO = new TaskSmsDetailVO();
        if (!baseTaskVO.getUserType().contains((Byte) (byte) 1) &&
                !baseTaskVO.getUserType().contains((Byte) (byte) 9)) {
            return taskSmsDetailVO;
        }
        SmsTaskInfo smsTaskInfo = smsTaskInfoMapper.selectOne(
                Wrappers.<SmsTaskInfo>lambdaQuery().eq(SmsTaskInfo::getTaskId, baseTaskVO.getTaskId())
        );

        BeanUtils.copyProperties(baseTaskVO, taskSmsDetailVO);
        BotInfo botInfo = botInfoMapper.selectById(smsTaskInfo.getBotId());
        taskSmsDetailVO.setSignCode(botInfo.getBotKey());
        taskSmsDetailVO.setSignName(botInfo.getBotName());
        taskSmsDetailVO.setBotBizId(botInfo.getBotBizId());
        taskSmsDetailVO.setBotId(smsTaskInfo.getBotId());
        return taskSmsDetailVO;
    }

    private TaskEmailDetailVO getEmailTaskDetail(BaseTaskVO baseTaskVO) {
        TaskEmailDetailVO taskEmailDetailVO = new TaskEmailDetailVO();
        if (!baseTaskVO.getUserType().contains((Byte) (byte) 1) &&
                !baseTaskVO.getUserType().contains((Byte) (byte) 9)) {
            return taskEmailDetailVO;
        }
        EmailTaskInfo emailTaskInfo = emailTaskInfoMapper.selectOne(
                Wrappers.<EmailTaskInfo>lambdaQuery().eq(EmailTaskInfo::getTaskId, baseTaskVO.getTaskId())
        );

        BeanUtils.copyProperties(baseTaskVO, taskEmailDetailVO);
        taskEmailDetailVO.setButton(emailTaskInfo.getButton());
        if ("null".equals(emailTaskInfo.getButtonExtra())) {
            taskEmailDetailVO.setButtonExtra(null);
        } else {
            taskEmailDetailVO.setButtonExtra(JsonUtils.toMap(emailTaskInfo.getButtonExtra()));
        }
        taskEmailDetailVO.setEmailType(emailTaskInfo.getEmailType());
        taskEmailDetailVO.setHtmlBody(emailTaskInfo.getHtmlBody());
        BotInfo botInfo = botInfoMapper.selectById(emailTaskInfo.getBotId());
        taskEmailDetailVO.setSenderName(botInfo.getBotName());
        taskEmailDetailVO.setSender(botInfo.getBotKey());
        taskEmailDetailVO.setBotBizId(botInfo.getBotBizId());
        taskEmailDetailVO.setBotId(emailTaskInfo.getBotId());
        return taskEmailDetailVO;

    }

    private List<ScopeKeyDeptsDTO> getScopeKeyDepts(List<String> scopeKey) {
        return departmentInfoMapper.selectList(
                        Wrappers.<DepartmentInfo>lambdaQuery()
                                .in(DepartmentInfo::getDeptId, scopeKey)
                                .eq(DepartmentInfo::getStatus, HrApplyStatusEnum.VALID.getCode())
                )
                .stream()
                .map(p -> new ScopeKeyDeptsDTO(p.getDeptId(), p.getCnName()))
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public BaseResult<Map<String, Object>> download(Long taskId, UserBO userBO) {
        TaskInfo taskInfo = taskService.getOne(
                Wrappers.<TaskInfo>lambdaQuery()
                        .eq(TaskInfo::getId, taskId)
        );
        if (Objects.isNull(taskInfo)) {
            return new BaseResult<Map<String, Object>>().setCode(2000).setMessage("没有此任务信息");
        }
        boolean haveMessagePermission = permissionManager.isHaveMessagePermission(userBO, taskId);
        if (!haveMessagePermission) {
            return new BaseResult<Map<String, Object>>(ResultCode.FORBIDDEN).setMessage("您没有权限访问此接口");
        }
        if (PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode().equals(taskInfo.getPublishScope()) ||
                PublishScopeEnum.ALL_PUSH.getCode().equals(taskInfo.getPublishScope()) ||
                PublishScopeEnum.CHOOSE_PUSH.getCode().equals(taskInfo.getPublishScope()) ||
                PublishScopeEnum.CUSTOM_PUSH_MANAGER.getCode().equals(taskInfo.getPublishScope()) ||
                PublishScopeEnum.UNREAD_PUSH.getCode().equals(taskInfo.getPublishScope())) {
            return exportPushResultListByPerson(taskInfo);
        }
        if (PublishScopeEnum.GROUP_PUSH.getCode().equals(taskInfo.getPublishScope())) {
            return exportGroupPushResult(taskInfo);
        }
        if (PublishScopeEnum.DEPT_PUSH.getCode().equals(taskInfo.getPublishScope())) {
            return exportDeptPushResultList(taskInfo);
        }
        return BaseResult.of();
    }

    private BaseResult<Map<String, Object>> exportGroupPushResult(TaskInfo taskInfo) {
        MessageResultSingleForCustomResponse messagePageByExtraId =
                messageGrpcClient.getMessagePageByExtraId(taskInfo.getExtraId(), 1L, 4000);

        List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery().eq(PublishScopeInfo::getTaskId, taskInfo.getId())
        );

        PublishScopeInfo publishScopeInfo = publishScopeInfoList.get(0);

        List<TaskMessageLarkGroupListRecordDTO> taskMessageLarkGroupListRecordDTOList = new ArrayList<>();
        long totalPage = messagePageByExtraId.getMessageResultPage().getPages();
        List<LarkGroupPushInfo> larkGroupPushInfoList = larkGroupPushInfoMapper.selectList(
                Wrappers.<LarkGroupPushInfo>lambdaQuery()
                        .eq(LarkGroupPushInfo::getTagId, publishScopeInfo.getScopeKey())
        );
        Map<String, String> chatIdAndChatNameMap = larkGroupPushInfoList.stream()
                .collect(Collectors.toMap(LarkGroupPushInfo::getChatId, LarkGroupPushInfo::getChatName));
        Map<String, String> chatIdAndOwnerUserIdMap = larkGroupPushInfoList.stream()
                .collect(Collectors.toMap(LarkGroupPushInfo::getChatId, LarkGroupPushInfo::getOwnerUserId));
        Map<String, String> chatIdAndOwnerNameMap = larkGroupPushInfoList.stream()
                .collect(Collectors.toMap(LarkGroupPushInfo::getChatId, LarkGroupPushInfo::getOwnerName));
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ExcelWriter excelWriter = write(os, TaskMessageLarkGroupListRecordDTO.class).build();
        // 这里注意 如果同一个sheet只要创建一次
        WriteSheet writeSheet = writerSheet("发送结果").build();
        try {
            if (totalPage != 0) {
                for (long page = 1; page < totalPage + 1; page++) {
                    MessageResultSingleForCustomResponse messagePageByExtraIdTmp =
                            messageGrpcClient.getMessagePageByExtraId(taskInfo.getExtraId(), page, 4000);
                    List<MessageRecord> recordsList = messagePageByExtraIdTmp.getMessageResultPage().getRecordsList();
                    recordsList.forEach(
                            messageRecord -> {
                                TaskMessageLarkGroupListRecordDTO taskMessageLarkGroupListRecordDTO =
                                        new TaskMessageLarkGroupListRecordDTO();
                                taskMessageLarkGroupListRecordDTO.setChatId(messageRecord.getChatId());
                                taskMessageLarkGroupListRecordDTO.setChatName(
                                        chatIdAndChatNameMap.get(messageRecord.getChatId()));
                                taskMessageLarkGroupListRecordDTO.setOwnerUserId(
                                        chatIdAndOwnerUserIdMap.get(messageRecord.getChatId()));
                                taskMessageLarkGroupListRecordDTO.setOwnerName(
                                        chatIdAndOwnerNameMap.get(messageRecord.getChatId()));
                                // 1-发送中，2-发送成功，3-发送失败，4-消息中断
                                switch (messageRecord.getMessageStatus()) {
                                    case 1:
                                        taskMessageLarkGroupListRecordDTO.setIsSuccess(true);
                                        taskMessageLarkGroupListRecordDTO.setPushResDesc("发送中");
                                        break;
                                    case 2:
                                        taskMessageLarkGroupListRecordDTO.setIsSuccess(true);
                                        taskMessageLarkGroupListRecordDTO.setPushResDesc("成功");
                                        break;
                                    case 3:
                                        taskMessageLarkGroupListRecordDTO.setIsSuccess(false);
                                        taskMessageLarkGroupListRecordDTO.setPushResDesc("失败");
                                        break;
                                    case 4:
                                        taskMessageLarkGroupListRecordDTO.setIsSuccess(false);
                                        taskMessageLarkGroupListRecordDTO.setPushResDesc("中断");
                                        break;
                                    default:
                                        break;
                                }
                                boolean isSuccess = taskMessageLarkGroupListRecordDTO.getIsSuccess();
                                if (!isSuccess) {
                                    taskMessageLarkGroupListRecordDTO.setErrorLog(messageRecord.getErrorLog());
                                }
                                taskMessageLarkGroupListRecordDTOList.add(taskMessageLarkGroupListRecordDTO);
                            }
                    );
                    excelWriter.write(taskMessageLarkGroupListRecordDTOList, writeSheet);
                    taskMessageLarkGroupListRecordDTOList.clear();
                }

            }
        } catch (Exception e) {
            log.error("导出自定义名单时发生异常, taskId = [{}]", taskInfo.getId(), e);
        } finally {
            // 千万别忘记finish 会帮忙关闭流
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }

        String folderName = UMS_ADMIN_PATH_STR.concat(DateUtil.today()).concat("/")
                .concat(UUID.randomUUID().toString().replace("-", "").concat("/"));
        FDSObjectMetadata fdsObjectMetadata = new FDSObjectMetadata();
        fdsObjectMetadata.setContentType(MS_EXCEL_HEADER);
        byte[] content = os.toByteArray();
        InputStream is = new ByteArrayInputStream(content);
        String url = miCloudFdsManager.uploadFile(folderName + taskInfo.getBizId() + EXCEL_EXTENSION_STR, is,
                fdsObjectMetadata);
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("url", miCloudFdsManager.getHostUrl().concat(url));
        return BaseResult.of(returnMap);
    }

    private BaseResult<Map<String, Object>> exportDeptPushResultList(TaskInfo taskInfo) {

        // 组聚合条数
        List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery().eq(PublishScopeInfo::getTaskId, taskInfo.getId())
        );
        List<String> deptIdList =
                publishScopeInfoList.stream().map(PublishScopeInfo::getScopeKey).collect(Collectors.toList());

        List<DepartmentInfo> departmentInfoList = departmentInfoMapper.selectList(
                Wrappers.<DepartmentInfo>lambdaQuery().in(DepartmentInfo::getDeptId, deptIdList)
        );
        List<String> deptLevel2IdList = departmentInfoList.stream().filter(p -> p.getTreeLevelNum() == 1).distinct()
                .map(DepartmentInfo::getDeptId).collect(Collectors.toList());

        List<String> deptLevel3IdList = departmentInfoList.stream().filter(p -> p.getTreeLevelNum() == 2).distinct()
                .map(DepartmentInfo::getDeptId).collect(Collectors.toList());

        List<String> deptLevel4IdList = departmentInfoList.stream().filter(p -> p.getTreeLevelNum() == 3).distinct()
                .map(DepartmentInfo::getDeptId).collect(Collectors.toList());

        if (deptLevel2IdList.isEmpty() && deptLevel3IdList.isEmpty() && deptLevel4IdList.isEmpty()) {
            return new BaseResult<Map<String, Object>>().setCode(ResultCode.SERVER_INNER_ERROR.getCode())
                    .setMessage("此业务数据不存在");
        }

        MessageResultSingleForDeptResponse messageGroupDeptByExtraId =
                messageGrpcClient.getMessageGroupDeptByExtraId(taskInfo.getExtraId(), deptLevel2IdList,
                        deptLevel3IdList, deptLevel4IdList, 1L, 2000);
        List<TaskMessageDeptListRecordExportDTO> taskMessageDeptListRecordExportDTOList = new ArrayList<>();
        long totalPage = messageGroupDeptByExtraId.getMessageGroupDeptResultPage().getPages();
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ExcelWriter excelWriter = write(os, TaskMessageDeptListRecordExportDTO.class).build();
        // 这里注意 如果同一个sheet只要创建一次
        WriteSheet writeSheet = writerSheet("发送结果").build();
        try {
            if (totalPage != 0) {
                for (long page = 1; page < totalPage + 1; page++) {
                    MessageResultSingleForDeptResponse messageGroupDeptByExtraIdTmp =
                            messageGrpcClient.getMessageGroupDeptByExtraId(taskInfo.getExtraId(), deptLevel2IdList,
                                    deptLevel3IdList, deptLevel4IdList, page, 2000);
                    List<MessageGroupDeptResultRecord> recordsList =
                            messageGroupDeptByExtraIdTmp.getMessageGroupDeptResultPage().getRecordsList();
                    recordsList.forEach(
                            messageRecord -> {
                                TaskMessageDeptListRecordExportDTO taskMessageDeptListRecordExportDTO =
                                        new TaskMessageDeptListRecordExportDTO();
                                taskMessageDeptListRecordExportDTO.setMiDeptLevel2(messageRecord.getMiDeptLevel2Desc());
                                taskMessageDeptListRecordExportDTO.setMiDeptLevel3(messageRecord.getMiDeptLevel3Desc());
                                taskMessageDeptListRecordExportDTO.setMiDeptLevel4(messageRecord.getMiDeptLevel4Desc());
                                taskMessageDeptListRecordExportDTO.setAllCount(messageRecord.getAllCount());
                                taskMessageDeptListRecordExportDTO.setPushCount(messageRecord.getPushCount());
                                taskMessageDeptListRecordExportDTO.setTodoCount(messageRecord.getTodoCount());
                                taskMessageDeptListRecordExportDTO.setRetractCount(messageRecord.getRetractCount());
                                taskMessageDeptListRecordExportDTO.setIsSuccess(
                                        taskMessageDeptListRecordExportDTO.getAllCount()
                                                .equals(taskMessageDeptListRecordExportDTO.getPushCount()));
                                taskMessageDeptListRecordExportDTOList.add(taskMessageDeptListRecordExportDTO);
                            }
                    );
                    excelWriter.write(taskMessageDeptListRecordExportDTOList, writeSheet);
                    taskMessageDeptListRecordExportDTOList.clear();
                }
            }
        } catch (Exception e) {
            log.error("导出部门名单时发生异常， taskId = [{}]", taskInfo.getId(), e);
        } finally {
            // 千万别忘记finish 会帮忙关闭流
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }

        String folderName = UMS_ADMIN_PATH_STR.concat(DateUtil.today()).concat("/")
                .concat(UUID.randomUUID().toString().replace("-", "").concat("/"));
        FDSObjectMetadata fdsObjectMetadata = new FDSObjectMetadata();
        fdsObjectMetadata.setContentType(MS_EXCEL_HEADER);
        byte[] content = os.toByteArray();
        InputStream is = new ByteArrayInputStream(content);
        String url = miCloudFdsManager.uploadFile(folderName + taskInfo.getBizId() + EXCEL_EXTENSION_STR, is,
                fdsObjectMetadata);
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("url", miCloudFdsManager.getHostUrl().concat(url));
        return BaseResult.of(returnMap);
    }

    private BaseResult<Map<String, Object>> exportPushResultListByPerson(TaskInfo taskInfo) {
        MessageResultSingleForCustomResponse messagePageByExtraId =
                messageGrpcClient.getMessagePageByExtraId(taskInfo.getExtraId(), 1L, 4000);
        List<TaskMessageCustomListExportBO> taskMessageCustomListExportBOList = new ArrayList<>();
        long totalPage = messagePageByExtraId.getMessageResultPage().getPages();
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ExcelWriter excelWriter = write(os, TaskMessageCustomListExportBO.class).build();
        // 这里注意 如果同一个sheet只要创建一次
        WriteSheet writeSheet = writerSheet("发送结果").build();
        try {
            if (totalPage != 0) {
                for (long page = 1; page < totalPage + 1; page++) {
                    MessageResultSingleForCustomResponse messagePageByExtraIdTmp =
                            messageGrpcClient.getMessagePageByExtraId(taskInfo.getExtraId(), page, 4000);
                    List<MessageRecord> recordsList = messagePageByExtraIdTmp.getMessageResultPage().getRecordsList();
                    recordsList.forEach(
                            messageRecord -> {
                                TaskMessageCustomListRecordDTO taskMessageCustomListRecordDTO =
                                        new TaskMessageCustomListRecordDTO();
                                taskMessageCustomListRecordDTO.setUsername(messageRecord.getUsername());
                                taskMessageCustomListRecordDTO.setUserEmail(messageRecord.getEmail());
                                setNameAndDeptIdAndDeptName(messageRecord, taskMessageCustomListRecordDTO);
                                setIsSuccessAndPushResDesc(messageRecord, taskMessageCustomListRecordDTO);
                                boolean isSuccess = taskMessageCustomListRecordDTO.getIsSuccess();
                                if (!isSuccess) {
                                    taskMessageCustomListRecordDTO.setErrorLog(messageRecord.getErrorLog());
                                }
                                TaskMessageCustomListExportBO taskMessageCustomListExportBO =
                                        new TaskMessageCustomListExportBO();
                                BeanUtils.copyProperties(taskMessageCustomListRecordDTO, taskMessageCustomListExportBO);
                                taskMessageCustomListExportBOList.add(taskMessageCustomListExportBO);
                            }
                    );
                    excelWriter.write(taskMessageCustomListExportBOList, writeSheet);
                    taskMessageCustomListExportBOList.clear();
                }

            }
        } catch (Exception e) {
            log.error("导出自定义名单时发生异常, taskId = [{}]", taskInfo.getId(), e);
        } finally {
            // 千万别忘记finish 会帮忙关闭流
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }

        String folderName = UMS_ADMIN_PATH_STR.concat(DateUtil.today()).concat("/")
                .concat(UUID.randomUUID().toString().replace("-", "").concat("/"));
        FDSObjectMetadata fdsObjectMetadata = new FDSObjectMetadata();
        fdsObjectMetadata.setContentType(MS_EXCEL_HEADER);
        byte[] content = os.toByteArray();
        InputStream is = new ByteArrayInputStream(content);
        String url = miCloudFdsManager.uploadFile(folderName + taskInfo.getBizId() + EXCEL_EXTENSION_STR, is,
                fdsObjectMetadata);
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("url", miCloudFdsManager.getHostUrl().concat(url));
        return BaseResult.of(returnMap);
    }

    private void setNameAndDeptIdAndDeptName(MessageRecord messageRecord,
                                             TaskMessageCustomListRecordDTO taskMessageCustomListRecordDTO) {
        messageRecord.getExtraContent();
        if (JsonUtils.parse(messageRecord.getExtraContent(), ExtraContentBO.class).getName() != null) {
            taskMessageCustomListRecordDTO.setName(
                    JsonUtils.parse(messageRecord.getExtraContent(), ExtraContentBO.class).getName());
        }
    }

    private void setIsSuccessAndPushResDesc(MessageRecord messageRecord,
                                            TaskMessageCustomListRecordDTO taskMessageCustomListRecordDTO) {
        // 1-发送中，2-发送成功，3-发送失败，4-消息中断
        switch (messageRecord.getMessageStatus()) {
            case 1:
                taskMessageCustomListRecordDTO.setIsSuccess(true);
                taskMessageCustomListRecordDTO.setPushResDesc("发送中");
                break;
            case 2:
                taskMessageCustomListRecordDTO.setIsSuccess(true);
                taskMessageCustomListRecordDTO.setPushResDesc("成功");
                break;
            case 3:
                taskMessageCustomListRecordDTO.setIsSuccess(false);
                taskMessageCustomListRecordDTO.setPushResDesc("失败");
                break;
            case 4:
                taskMessageCustomListRecordDTO.setIsSuccess(false);
                taskMessageCustomListRecordDTO.setPushResDesc("中断");
                break;
            default:
                break;
        }
    }

}
