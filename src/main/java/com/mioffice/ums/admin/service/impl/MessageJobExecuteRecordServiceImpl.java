package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import static com.mioffice.ums.admin.constants.RuleConventionConstants.CLICK_KEY_PREFIX;
import static com.mioffice.ums.admin.constants.RuleConventionConstants.SEND_KEY_PREFIX;
import com.mioffice.ums.admin.entity.info.MessageJobExecuteRecord;
import com.mioffice.ums.admin.mapper.MessageJobExecuteRecordMapper;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.mioffice.ums.admin.service.MessageJobExecuteRecordService;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.UsernameResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 *
 */
@Service
@Slf4j
public class MessageJobExecuteRecordServiceImpl
        extends ServiceImpl<MessageJobExecuteRecordMapper, MessageJobExecuteRecord>
        implements MessageJobExecuteRecordService {

    @Resource
    RedisTemplate redisTemplate;

    @Resource
    private MessageGrpcClient messageGrpcClient;

    @Override
    public Set<String> getUnClickUserByExtraIdList(String parentExtraId, List<String> subExtraIdList) {
        UsernameResponse usernameResponse = messageGrpcClient.getFailed(parentExtraId);
        if (CollectionUtils.isNotEmpty(usernameResponse.getUsernamesList())) {
            redisTemplate.opsForSet().remove(SEND_KEY_PREFIX + parentExtraId,
                    usernameResponse.getUsernamesList().toArray(new String[0]));
        }
        subExtraIdList.add(parentExtraId);
        Set<String> result = redisTemplate.opsForSet().difference(SEND_KEY_PREFIX + parentExtraId,
                subExtraIdList
                        .stream()
                        .map(subExtraId -> CLICK_KEY_PREFIX + subExtraId)
                        .collect(Collectors.toList()));
        return result;
    }
}




