package com.mioffice.ums.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.entity.vo.OpenPushSummaryVo;
import com.mioffice.ums.admin.entity.vo.OpenTaskLogVo;

/**
 * <p>
 * 开放平台Service
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/23 9:04 下午
 */
public interface OpenApiPushSummaryService {

    /**
     * 获取使用概览
     *
     * @param systemIds 系统ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return PushSummaryBo
     */
    OpenPushSummaryVo getUseSummary(String systemIds, String startTime, String endTime);

    /**
     * 获取使用概览
     *
     * @param extraId 组ID
     * @param channels 渠道
     * @param systemId 系统ID
     * @param page 页码
     * @param size 页size
     * @return IPage AppTaskLogBo
     */
    IPage<OpenTaskLogVo> getAppTaskLog(String extraId, Long systemId, String channels, Integer page, Integer size);

}
