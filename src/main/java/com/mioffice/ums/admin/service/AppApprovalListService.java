package com.mioffice.ums.admin.service;

import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.vo.AppApprovalListVO;
import com.mioffice.ums.admin.result.BaseResult;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/21 3:23 上午
 * version: 1.0.0
 */
public interface AppApprovalListService {

    BaseResult<AppApprovalListVO> appApprovalPage(UserBO userBO, Long page, Long size, String appName, String managerUsernameList, String channel, String beginDate, String endDate);

    BaseResult<Object> appApprovalPass(UserBO userBO, Long id);

    BaseResult<Object> appApprovalReject(UserBO userBO, Long id, String reason);

    BaseResult<Object> appApplyCancel(UserBO userBO, Long id);
}
