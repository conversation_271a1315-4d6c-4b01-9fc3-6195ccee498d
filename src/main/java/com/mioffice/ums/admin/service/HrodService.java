package com.mioffice.ums.admin.service;

import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.mioffice.ums.admin.entity.vo.hrod.*;
import com.mioffice.ums.admin.utils.HttpUtil;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.stream.Collectors;

@Slf4j
@Component
public class HrodService {

    @Autowired
    HttpUtil httpUtil;

    @Value("${x5.hr.base-url}")
    private String URL;
    @Value("${x5.hr.app-id}")
    private String APPID;
    @Value("${x5.hr.app-key}")
    private String APPKEY;

    public static final String REQ_PAR_CONDITION = "hpcConditionEmployee";
    public static final String REQ_API_SEARCH_USER = "getEmployees";
    /**
     * 批量获取员工信息
     */
    public static final String REQ_PAR_CONDITIONS = "hpcConditionByOprids";
    public static final String REQ_API_SEARCH_USERS = "getEmployeeListByOprid";


    public static final String REQ_PAR_CONDITION_EMPLIDS = "hpcConditionByEmplids";
    public static final String REQ_API_SEARCH_LEADERS = "batchGetLeader";
    public static final String REQ_API_VIRTUAL_LEADERS = "getVirtualReportingLine";

    public static final String REQ_PAR_CONDITION_SUBORDINATE = "hpcConditionByOprid";
    public static final String REQ_API_SEARCH_SUBORDINATES = "getSubordinateByOprid";


    public static final String REQ_PAR_API_NAME = "api_name";
    public static final String REQ_METHOD = "/hapi/HR_C?version=2";
    public static final String OPR_ID = "oprid";

    private final DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private final DateFormat dfDate = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 获取用户基本信息
     *
     * @param userId
     */
    public HrodUserInfoVo.HrodUser getUserInfo(String userId) throws IOException {
        Map<String, String> conditions = new HashMap<>(16);
        conditions.put(OPR_ID, userId);
        Map<String, Object> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, "getEmployeeByOprid");
        params.put("hpcConditionByOprid", JacksonUtils.bean2Json(conditions));
        HrodUserInfoVo res = httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodUserInfoVo.class);
        return res.getBody();
    }


    /**
     * 批量获取用户信息
     */
    public List<HrodUserInfoVo.HrodUser> getUserInfos(List<String> userIds) throws IOException {
        Map<String, String> conditions = new HashMap<>(16);
        conditions.put("oprids", StringUtils.join(userIds, ','));
        Map<String, Object> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, REQ_API_SEARCH_USERS);
        params.put(REQ_PAR_CONDITIONS, JacksonUtils.bean2Json(conditions));
        HrodUserInfosVo res = httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodUserInfosVo.class);
        return res.getBody();
    }


    /**
     * 模糊搜索部门
     *
     * @return
     */
    public HrodDepartmentInfoVo getFuzzyDepartment(String departmentName, Integer pageNum, Integer pageSize) throws IOException {
        Map<String, Object> conditions = new HashMap<>(16);
        conditions.put("pageSize", pageSize);
        conditions.put("pageNum", pageNum);
        conditions.put("dept_name", departmentName);
        // 支持中英文
        conditions.put("hasEnSearch", true);
        Map<String, String> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, "getDeptPathByDeptName");
        params.put("hpcDepartmentFuzzyQueryCondition", JacksonUtils.bean2Json(conditions));
        return httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodDepartmentInfoVo.class);
    }

    /**
     * 模糊查询用户
     *
     * @param findKey
     * @param pageNum
     * @param pageSize
     * @return
     * @throws IOException
     */
    public HrodUserInfosVo getFuzzyUser(String findKey, Integer pageNum, Integer pageSize) throws IOException {

        Map<String, Object> conditions = new HashMap<>(16);
        conditions.put("pageSize", pageSize);
        conditions.put("pageNum", pageNum);
        conditions.put("findKey", findKey);
        conditions.put("isAllMatch", 0);
        conditions.put("hasEnSearch", true);

        Map<String, Object> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, REQ_API_SEARCH_USER);
        params.put(REQ_PAR_CONDITION, JacksonUtils.bean2Json(conditions));
        return httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodUserInfosVo.class);
    }

    /**
     * 获取直属上级
     *
     * @return
     */
    public HrodUserLeaderVo getUserLeaders(String uid) throws IOException {
        Map<String, Object> conditions = new HashMap<>(16);
        conditions.put("oprids", uid);
        Map<String, Object> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, REQ_API_SEARCH_LEADERS);
        params.put(REQ_PAR_CONDITION_EMPLIDS, JacksonUtils.bean2Json(conditions));
        return httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodUserLeaderVo.class);
    }

    public Map<String, HrodUserLeaderVo.UserLeader> batchGetUserLeaderIds(List<String> uid) throws IOException {
        Map<String, Object> conditions = new HashMap<>(16);
        conditions.put("oprids", String.join(",", uid));
        Map<String, Object> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, REQ_API_SEARCH_LEADERS);
        params.put(REQ_PAR_CONDITION_EMPLIDS, JacksonUtils.bean2Json(conditions));
        HrodUserLeaderVo res = httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodUserLeaderVo.class);
        return res.getBody().stream().collect(Collectors.toMap(HrodUserLeaderVo.UserLeader::getParamOprid, v -> v));
    }


    /**
     * 获取直属下级
     *
     * @return
     */
    public HrodUserInfosVo getUserSubordinates(String uid) throws IOException {
        Map<String, Object> conditions = new HashMap<>(16);
        conditions.put("oprid", uid);
        Map<String, Object> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, REQ_API_SEARCH_SUBORDINATES);
        params.put(REQ_PAR_CONDITION_SUBORDINATE, JacksonUtils.bean2Json(conditions));
        return httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodUserInfosVo.class);
    }

    /**
     * 获取部门列表
     *
     * @return
     */
    public HrodSubDepartmentInfoVo getDepartment(String deptId, Integer pageNum, Integer pageSize) throws IOException {
        Map<String, Object> conditions = new HashMap<>(16);
        conditions.put("parentNodeName", deptId);
        conditions.put("pageSize", pageSize);
        conditions.put("pageNum", pageNum);
        Map<String, Object> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, "getDepartments");
        params.put("hpcConditionDepartment", JacksonUtils.bean2Json(conditions));
        return httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodSubDepartmentInfoVo.class);
    }

    /**
     * 获取部门员工
     * 比如 IT拿到的就是 张涛跟宏华
     *
     * @return
     */
    public void getDepartmentEmployees() throws IOException {
        Map<String, Object> conditions = new HashMap<>(16);
        conditions.put("deptid", "IT");
        conditions.put("pageSize", "100");
        conditions.put("pageNum", "1");
        Map<String, Object> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, "getDepartmentEmployees");
        params.put("hpcConditionDepartment", JacksonUtils.bean2Json(conditions));
        Object res = httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, Object.class);
    }


    /**
     * 根据部门ID获取部门详情数据
     *
     * @param deptId
     * @throws IOException
     */
    public List<HrodDepartmentDetailVo.DeptItem> getDepartmentDetail(String deptId) throws IOException {
        Map<String, Object> conditions = new HashMap<>(16);
        conditions.put("deptid", deptId);
        conditions.put("pageSize", 10);
        conditions.put("pageNum", 1);
        Map<String, Object> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, "getDepartmentByDeptid");
        params.put("hpcConditionDepartment", JacksonUtils.bean2Json(conditions));
        HrodDepartmentDetailVo res = httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodDepartmentDetailVo.class);
        return res.getBody();
    }


    /**
     * 获取部门下面所有的人员信息
     *
     * @param deptId
     * @param deptLevel
     * @param pageNum
     * @param pageSize
     * @return
     * @throws IOException
     */
    public List<HrodUserInfoVo.HrodUser> getAllEmployeeByDeptId(String deptId, Integer deptLevel, Integer pageNum, Integer pageSize) throws IOException {
        Map<String, Object> conditions = new HashMap<>(16);
        conditions.put("deptid", deptId);
        conditions.put("pageSize", pageSize);
        conditions.put("pageNum", pageNum);
        conditions.put("dept_level", deptLevel);
        Map<String, Object> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, "getAllEmployeeByDeptid");
        params.put("hpcConditionAllEmployeeByDeptid", JacksonUtils.bean2Json(conditions));
        HrodUserInfosVo res = httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodUserInfosVo.class);
        return res.getBody();
    }

    public List<HrodUser> getEmployeeByUpdateTime(Date startTime, Date endTime, Integer pageNum, Integer pageSize) throws IOException {
        Map<String, Object> conditions = new HashMap<>(16);
        conditions.put("startTime", df.format(startTime));
        conditions.put("endTime", df.format(endTime));
        conditions.put("pageSize", pageSize);
        conditions.put("pageNum", pageNum);
        Map<String, Object> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, "getPersonalInfoByUpdateTime");
        params.put("hpcConditionByUpdateTime", JacksonUtils.bean2Json(conditions));
        HrodGetPersonalInfoResp
                res = httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodGetPersonalInfoResp.class);
        return res.getBody();
    }

    /**
     * 判定 A 是否是B的leader
     * 支持多层级 比如 fenghonghua jin.zhang raojian 都是zhouhaitian leader
     *
     * @return
     */
    public Boolean judgeAisBLeader(String uidA, String uidB) throws IOException {
        Map<String, Object> conditions = new HashMap<>(16);
        conditions.put("opridA", uidA);
        conditions.put("opridB", uidB);
        Map<String, Object> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, "checkIsOnReportingLine");
        params.put("hpcConditionOpridAB", JacksonUtils.bean2Json(conditions));
        HrodUserAisUserBLeader res = httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodUserAisUserBLeader.class);
        if (ObjectUtils.isEmpty(res) || res.getHeader().getCode() != 200) {
            log.error("judgeAisBLeader exception {}", res);
            return false;
        }
        return res.getBody().isFlag();
    }

    /**
     * 判定 某个人是否是一群人的leader
     *
     * @param uid
     * @param uids
     * @return
     */
    public Map<String, Boolean> judgeAisLeader(String uid, List<String> uids) {
        return uids.stream().parallel().map(item -> {
            UserLeader.UserLeaderBuilder userLeader = UserLeader.builder();
            userLeader.uid(item);
            try {
                boolean res = judgeAisBLeader(uid, item);
                userLeader.leader(res);
            } catch (IOException e) {
                userLeader.leader(false);
            }
            return userLeader.build();
        }).collect(Collectors.toMap(UserLeader::getUid, UserLeader::isLeader, (k1, k2) -> k2));
    }

    /**
     * 判定一个人是不是Leader（有没有下属）
     */
    public Boolean judgeIsLeader(String uid) throws IOException {
        Map<String, Object> conditions = new HashMap<>(16);
        conditions.put("oprid", uid);
        Map<String, Object> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, "isLeader");
        params.put("hpcConditionByOprid", JacksonUtils.bean2Json(conditions));
        HrodUserIsLeader res = httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodUserIsLeader.class);
        if (ObjectUtils.isEmpty(res) || res.getHeader().getCode() != 200) {
            log.error("调用isLeader接口出错{}", res);
            return false;
        }
        return res.getBody().getCount() > 0;
    }

    /**
     * 判定一群人是不是Leader（有没有下属）
     */
    public Map<String, Boolean> judgeIsLeader(List<String> uids) {
        return uids.stream().parallel().map(uid -> {
            UserLeader.UserLeaderBuilder userLeader = UserLeader.builder();
            userLeader.uid(uid);
            try {
                boolean res = judgeIsLeader(uid);
                userLeader.leader(res);
            } catch (IOException e) {
                userLeader.leader(false);
            }
            return userLeader.build();
        }).collect(Collectors.toMap(UserLeader::getUid, UserLeader::isLeader, (k1, k2) -> k2));
    }


    @Data
    @Builder
    public static class UserLeader {
        String uid;
        boolean leader;
    }

    /**
     * 查询员工休假时间
     *
     * @param datePoints    时间点列表
     * @param holidayType   MI_ANN_ABS(01-年休假)
     *                      MI_PAF_ABS(02-年休假)
     *                      MI_SCK_PAY(03-带薪病假)
     *                      MI_MAR(04-婚假)
     *                      MI_MEX(05-产前检查假)
     *                      MI_MMI_F(06-产假)
     *                      MI_MMI_M(07-陪产假)
     *                      MI_SCK(08-短期病假)
     *                      MI_LSK(08-长期病假)
     *                      MI_ABS(10-旷工)
     *                      MI_INJ(11-工伤假)
     *                      MI_ABR(12-流产假)
     *                      MI_FNR(13-丧假)
     * @param processStatus 流程状态（默认为已审批）
     *                      A：已批准
     *                      C：已取消
     *                      D：已拒绝
     *                      I：审批中
     *                      S：已提交
     */
    public List<HrodHolidayDetail> getHolidayInfo(
            List<Date> datePoints,
            String holidayType,
            String processStatus,
            String empId,
            Integer pageNum,
            Integer pageSize) throws IOException {
        Map<String, Object> conditions = new HashMap<>(16);
        conditions.put("timePointList", datePoints.stream().map(dfDate::format).collect(Collectors.toList()));
        conditions.put("emplid", empId);
        conditions.put("miAbsType", holidayType);
        conditions.put("wfStatus", processStatus);
        conditions.put("pageSize", pageSize);
        conditions.put("pageNum", pageNum);
        Map<String, Object> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, "findLeaveDetail");
        params.put("hpcConditionLeaveDetail", JacksonUtils.bean2Json(conditions));
        HrodGetHolidayInfoResp res = httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodGetHolidayInfoResp.class);
        return res.getBody();
    }

    public List<HrodUser> getReportLine(String userId) throws IOException {
        Map<String, String> conditions = new HashMap<>();
        conditions.put("oprid", userId);
        Map<String, Object> params = new HashMap<>();
        params.put(REQ_PAR_API_NAME, "getReportingLine");
        params.put("hpcConditionByOprid", JacksonUtils.bean2Json(conditions));
        HrodGetReportLineResp res = httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodGetReportLineResp.class);
        return res.getBody();
    }

    public int getSubLevel(String userId) throws IOException {
        Map<String, String> conditions = new HashMap<>();
        conditions.put("oprid", userId);
        Map<String, Object> params = new HashMap<>();
        params.put(REQ_PAR_API_NAME, "getSubordinateLevelByOprid");
        params.put("hpcConditionByOprid", JacksonUtils.bean2Json(conditions));
        HrodGetSubLevelResp res = httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodGetSubLevelResp.class);
        return res.getBody().getLevel();
    }


    public int getPosLevel(String userId) {
        try {
            return Integer.parseInt(StringUtils.defaultIfBlank(getUserInfo(userId).getSupvLvlId(), "0"));
        } catch (IOException e) {
            log.error("获取用户信息出错[{}]", userId, e);
            return 0;
        }
    }


    public List<HrodUserLeaderVo.UserLeader> getVirtualReportingLine(String uid) throws IOException {
        Map<String, Object> conditions = new HashMap<>(16);
        conditions.put(OPR_ID, uid);
        Map<String, Object> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, "getVirtualReportingLine");
        params.put(REQ_PAR_CONDITION_SUBORDINATE, JacksonUtils.bean2Json(conditions));
        HrodUserLeaderVo res = httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodUserLeaderVo.class);
        return res == null ? new ArrayList<>() : res.getBody();
    }

    /**
     * 获取hrbp，注意参数为员工号
     *
     * @param employeeIdA
     * @param employeeIdB
     * @return
     * @throws IOException
     */

    public List<HrodUserLeaderVo.UserLeader> getHrpbIdByStaffId(String employeeIdA, String employeeIdB) throws IOException {
        if (StringUtils.isEmpty(employeeIdA)) {
            return new ArrayList<>();
        }
        if (!StringUtils.isEmpty(employeeIdB)) {
            employeeIdA = employeeIdA + "," + employeeIdB;
        }
        Map<String, Object> conditions = new HashMap<>(16);
        conditions.put("emplids", employeeIdA);
        Map<String, Object> params = new HashMap<>(16);
        params.put(REQ_PAR_API_NAME, "getHrpbIdByStaffId");
        params.put("hpcConditionByEmplids", JacksonUtils.bean2Json(conditions));
        HrodUserLeaderVo res = httpUtil.postX5(URL, REQ_METHOD, APPID, APPKEY, "", params, HrodUserLeaderVo.class);
        return res == null ? new ArrayList<>() : res.getBody().stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(
                                () -> new TreeSet<>(Comparator.comparing(HrodUserLeaderVo.UserLeader::getOprid))), ArrayList::new));
    }
}
