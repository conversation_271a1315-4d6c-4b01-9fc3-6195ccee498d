package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.admin.entity.dto.DepartmentDTO;
import com.mioffice.ums.admin.entity.dto.DepartmentSummaryDTO;
import com.mioffice.ums.admin.entity.info.DepartmentInfo;
import com.mioffice.ums.admin.enums.BotTenantEnum;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.manager.DeptManager;
import com.mioffice.ums.admin.mapper.DepartmentInfoMapper;
import com.mioffice.ums.admin.service.DeptService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/13 17:54
 */
@Service
public class DeptServiceImpl implements DeptService {

    private static final int MAX_LEVEL = 3;

    private final DepartmentInfoMapper departmentInfoMapper;

    private final DeptManager deptManager;

    public DeptServiceImpl(DepartmentInfoMapper departmentInfoMapper,
                           DeptManager deptManager) {
        this.departmentInfoMapper = departmentInfoMapper;
        this.deptManager = deptManager;
    }

    @Override
    public List<DepartmentDTO> getDeptBySearchWord(String searchWord, BotTenantEnum botTenantEnum) {
        List<DepartmentInfo> departmentInfoList = departmentInfoMapper.selectList(
                Wrappers.<DepartmentInfo>lambdaQuery()
                        .eq(DepartmentInfo::getStatus, HrApplyStatusEnum.VALID.getCode())
                        .like(DepartmentInfo::getCnName, searchWord)
        );

        List<String> deptIdList = departmentInfoList.stream()
                .map(DepartmentInfo::getDeptId)
                .collect(Collectors.toList());

        List<DepartmentInfo> firstLevelDeptList = deptManager.getFirstLevelDeptInfo(deptIdList);

        List<DepartmentDTO> result = new ArrayList<>();

        firstLevelDeptList.forEach(
                item -> {
                    DepartmentDTO departmentDTO = new DepartmentDTO();
                    departmentDTO.setDeptId(item.getDeptId());
                    departmentDTO.setCnName(item.getCnName());
                    departmentDTO.setChildren(buildDeptTree(2, item.getDeptId(), botTenantEnum));
                    result.add(departmentDTO);
                }
        );
        return result;
    }

    @Override
    public List<DepartmentDTO> getDeptTree(BotTenantEnum botTenantEnum) {
        return buildDeptTree(1, "MI", botTenantEnum);
    }

    @Override
    public Map<String, List<DepartmentSummaryDTO>> getDeptCount(String deptIds, BotTenantEnum botTenantEnum) {

        List<DepartmentSummaryDTO> result = new ArrayList<>();

        if (StringUtils.isNotBlank(deptIds)) {
            String[] deptIdArr = deptIds.split(",");
            List<String> deptIdList = Arrays.asList(deptIdArr);
            result = deptManager.getDeptInfo(deptIdList, BotTenantEnum.BOT_TENANT_MI == botTenantEnum);
        }
        Map<String, List<DepartmentSummaryDTO>> map = new HashMap<>(16);
        map.put("list", result);
        return map;
    }

    private List<DepartmentDTO> buildDeptTree(int level, String parentDeptId, BotTenantEnum botTenantEnum) {
        // 暂时取三级部门以上
        if (level > MAX_LEVEL) {
            return null;
        }
        List<DepartmentDTO> departmentList = new ArrayList<>();

        List<DepartmentInfo> curDeptList = departmentInfoMapper.selectList(
                Wrappers.<DepartmentInfo>lambdaQuery()
                        .eq(DepartmentInfo::getParentNodeId, parentDeptId)
                        .eq(DepartmentInfo::getTreeLevelNum, level)
                        .eq(DepartmentInfo::getStatus, HrApplyStatusEnum.VALID.getCode())
        );

        curDeptList.forEach(
                item -> {
                    DepartmentDTO departmentDTO = new DepartmentDTO();
                    BeanUtils.copyProperties(item, departmentDTO);
                    departmentDTO.setChildren(buildDeptTree(level + 1, item.getDeptId(), botTenantEnum));
                    departmentList.add(departmentDTO);
                }
        );

        return departmentList;
    }
}
