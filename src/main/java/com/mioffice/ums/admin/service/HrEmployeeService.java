package com.mioffice.ums.admin.service;

import com.mioffice.ums.admin.result.BaseResult;

/**
 * <p>
 * HR同步用户接口
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/10 18:40
 */
public interface HrEmployeeService {

    /**
     * 全量同步员工
     *
     * @return String
     */
    BaseResult<String> syncEmployeeWithFullLoad();

    /**
     * 增量同步员工
     *
     * @param startTime 开始时间
     * @param endTime 截止时间
     * @return String
     */
    BaseResult<String> syncEmployeeWithIncrement(String startTime, String endTime);

    void syncIdmPartnerUser();

}
