package com.mioffice.ums.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.entity.bo.BtnNotifBO;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.MessageApprovalDTO;
import com.mioffice.ums.admin.entity.dto.MessageApprovalQueryDTO;
import com.mioffice.ums.admin.enums.LarkAuditCallBackEnum;
import com.mioffice.ums.admin.result.BaseResult;

/**
 * <p>
 * 消息审核
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/14 22:06
 */
public interface MessageApprovalService {

    /**
     * 查询审核列表
     *
     * @param messageApprovalQueryDTO dto
     * @param userBO user
     * @return IPage<TaskInfo>
     */
    IPage<MessageApprovalDTO> messageApprovalPage(MessageApprovalQueryDTO messageApprovalQueryDTO, UserBO userBO);

    /**
     * 审核通过
     *
     * @param messageApprovalQueryDTO dto
     * @param userBO user
     * @return int
     */
    BaseResult<Object> approved(MessageApprovalQueryDTO messageApprovalQueryDTO, UserBO userBO);

    /**
     * 审核拒绝
     *
     * @param messageApprovalQueryDTO dto
     * @param userBO user
     * @return int
     */
    BaseResult<Object> approveReject(MessageApprovalQueryDTO messageApprovalQueryDTO, UserBO userBO);

    /**
     * 判断用户有权限审核吗
     *
     * @param username username
     * @param taskId taskId
     * @return boolean = true 有权限
     */
    boolean checkHaveAccessAudit(String username, Long taskId);

    /**
     * 飞书审核通过回调
     *
     * @param btnNotifBO
     * @return {true} 回调成功
     */
    LarkAuditCallBackEnum larkApprovedCallBack(BtnNotifBO btnNotifBO);

    /**
     * 飞书审核拒绝回调
     *
     * @param btnNotifBO
     * @return {true} 回调成功
     */
    LarkAuditCallBackEnum larkRejectedCallBack(BtnNotifBO btnNotifBO);
}
