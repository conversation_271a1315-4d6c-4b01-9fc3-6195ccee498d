package com.mioffice.ums.admin.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.mioffice.ums.admin.constants.ApplyContentConstants;
import com.mioffice.ums.admin.constants.ProcessStatusConstant;
import com.mioffice.ums.admin.entity.bo.BtnNotifBO;
import com.mioffice.ums.admin.entity.bo.CardContentRefreshBO;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.MessageApprovalDTO;
import com.mioffice.ums.admin.entity.dto.MessageApprovalQueryDTO;
import com.mioffice.ums.admin.entity.info.BotInfo;
import com.mioffice.ums.admin.entity.info.DepartmentInfo;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.LarkTaskInfo;
import com.mioffice.ums.admin.entity.info.ProcessInstanceInfo;
import com.mioffice.ums.admin.entity.info.ProcessTaskInfo;
import com.mioffice.ums.admin.entity.info.PublishScopeInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.LarkAuditCallBackEnum;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.enums.TaskChannelEnum;
import com.mioffice.ums.admin.enums.TaskStatusEnum;
import com.mioffice.ums.admin.manager.ProcessManager;
import com.mioffice.ums.admin.mapper.BotInfoMapper;
import com.mioffice.ums.admin.mapper.DepartmentInfoMapper;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.LarkTaskInfoMapper;
import com.mioffice.ums.admin.mapper.ProcessInstanceInfoMapper;
import com.mioffice.ums.admin.mapper.ProcessTaskInfoMapper;
import com.mioffice.ums.admin.mapper.PublishScopeInfoMapper;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import com.mioffice.ums.admin.mapper.UserBotInfoMapper;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.MessageApprovalService;
import com.mioffice.ums.admin.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 消息审核
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/14 22:23
 */
@Slf4j
@Service
public class MessageApprovalServiceImpl implements MessageApprovalService {

    private final TaskInfoMapper taskInfoMapper;

    private final PublishScopeInfoMapper scopeInfoMapper;

    private final DepartmentInfoMapper departmentInfoMapper;

    private final UserBotInfoMapper userBotInfoMapper;

    private final LarkTaskInfoMapper larkTaskInfoMapper;

    private final EmployeeInfoMapper employeeInfoMapper;

    private final ProcessTaskInfoMapper processTaskInfoMapper;

    private final ProcessInstanceInfoMapper processInstanceInfoMapper;

    private final ProcessManager processManager;

    private final BotInfoMapper botInfoMapper;

    private final MessageGrpcClient messageGrpcClient;

    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public MessageApprovalServiceImpl(TaskInfoMapper taskInfoMapper, PublishScopeInfoMapper scopeInfoMapper, DepartmentInfoMapper departmentInfoMapper, UserBotInfoMapper userBotInfoMapper, LarkTaskInfoMapper larkTaskInfoMapper, EmployeeInfoMapper employeeInfoMapper, ProcessTaskInfoMapper processTaskInfoMapper, ProcessInstanceInfoMapper processInstanceInfoMapper, ProcessManager processManager, BotInfoMapper botInfoMapper, MessageGrpcClient messageGrpcClient) {
        this.taskInfoMapper = taskInfoMapper;
        this.scopeInfoMapper = scopeInfoMapper;
        this.departmentInfoMapper = departmentInfoMapper;
        this.userBotInfoMapper = userBotInfoMapper;
        this.larkTaskInfoMapper = larkTaskInfoMapper;
        this.employeeInfoMapper = employeeInfoMapper;
        this.processTaskInfoMapper = processTaskInfoMapper;
        this.processInstanceInfoMapper = processInstanceInfoMapper;
        this.processManager = processManager;
        this.botInfoMapper = botInfoMapper;
        this.messageGrpcClient = messageGrpcClient;
    }

    @Override
    public IPage<MessageApprovalDTO> messageApprovalPage(MessageApprovalQueryDTO messageApprovalQueryDTO, UserBO userBO) {

        // 获取审批流程实例里面管理员,运营和VP要审批的任务
        List<Long> taskIds = this.getProcessTaskIds(userBO.getUsername());

        List<Long> allTaskIds = new ArrayList<>(taskIds);

        if (allTaskIds.isEmpty()) {
            return new Page<>();
        }

        // page
        Page<TaskInfo> taskInfoPage = new Page<>(messageApprovalQueryDTO.getPage(), messageApprovalQueryDTO.getSize());

        LambdaQueryWrapper<TaskInfo> lambdaQuery = Wrappers.lambdaQuery();

        // 发布渠道
        if (StringUtils.isNotBlank(messageApprovalQueryDTO.getChannel())) {
            String[] channelArray = messageApprovalQueryDTO.getChannel().split(",");
            List<Byte> channelList = new ArrayList<>();
            for (String channel : channelArray) {
                channelList.add(Byte.valueOf(channel));
            }
            lambdaQuery.in(TaskInfo::getChannel, channelList);
        }

        // 推送范围
        if (StringUtils.isNotBlank(messageApprovalQueryDTO.getPublishScope())) {
            String[] publishScopeArray = messageApprovalQueryDTO.getPublishScope().split(",");
            List<Byte> publishScopeList = new ArrayList<>();
            for (String publishScope : publishScopeArray) {
                publishScopeList.add(Byte.valueOf(publishScope));
            }
            lambdaQuery.in(TaskInfo::getPublishScope, publishScopeList);
        }
        // 消息主题/或者消息ID
        if (StringUtils.isNotBlank(messageApprovalQueryDTO.getKey())) {
            lambdaQuery.and(
                    wrapper ->
                            wrapper.like(TaskInfo::getBizId, messageApprovalQueryDTO.getKey())
                                    .or()
                                    .like(TaskInfo::getTitleCn, messageApprovalQueryDTO.getKey())
            );
        }

        // 创建时间开始
        if (StringUtils.isNotBlank(messageApprovalQueryDTO.getBeginDate())) {
            DateTime dateTime = DateUtil.beginOfSecond(DateUtil.parseDateTime(messageApprovalQueryDTO.getBeginDate()));
            lambdaQuery.ge(TaskInfo::getCreateTime, dateTime.getTime());
        }

        // 创建时间结束
        if (StringUtils.isNotBlank(messageApprovalQueryDTO.getEndDate())) {
            DateTime dateTime = DateUtil.beginOfSecond(DateUtil.parseDateTime(messageApprovalQueryDTO.getEndDate()));
            lambdaQuery.le(TaskInfo::getCreateTime, dateTime.getTime());
        }

        // 条件为待审核
        lambdaQuery.eq(TaskInfo::getTaskStatus, TaskStatusEnum.APPROVING.getCode());
        lambdaQuery.in(TaskInfo::getId, allTaskIds);
        lambdaQuery.orderByDesc(TaskInfo::getCreateTime);

        IPage<TaskInfo> taskPage = taskInfoMapper.selectPage(taskInfoPage, lambdaQuery);
        // 组装成一个新Page
        IPage<MessageApprovalDTO> messageApprovalPage = new Page<>();

        List<MessageApprovalDTO> messageApproveRecords = new ArrayList<>();

        List<TaskInfo> records = taskPage.getRecords();

        if (records.isEmpty()) {
            messageApprovalPage.setRecords(messageApproveRecords);
            messageApprovalPage.setCurrent(messageApprovalQueryDTO.getPage());
            messageApprovalPage.setPages(0L);
            messageApprovalPage.setSize(messageApprovalQueryDTO.getSize());
            messageApprovalPage.setTotal(0L);
            return messageApprovalPage;
        }

        taskPage.getRecords().forEach(
                taskInfo -> {
                    MessageApprovalDTO messageApprovalDTO = new MessageApprovalDTO();
                    BeanUtils.copyProperties(taskInfo, messageApprovalDTO);
                    messageApprovalDTO.setTaskId(taskInfo.getId());

                    if (messageApprovalDTO.getPublishScope() != null) {
                        Byte publishScope = messageApprovalDTO.getPublishScope();
                        if (publishScope.intValue() == (PublishScopeEnum.DEPT_PUSH.getCode())) {
                            // 取出该任务推送范围为部门的所有范围信息
                            List<PublishScopeInfo> deptScopeList = scopeInfoMapper.selectList(
                                    Wrappers.<PublishScopeInfo>lambdaQuery()
                                            .eq(PublishScopeInfo::getScopeType, PublishScopeEnum.DEPT_PUSH.getCode())
                                            .eq(PublishScopeInfo::getTaskId, taskInfo.getId())
                            );
                            // 取出所有的DeptId
                            List<String> deptIds = deptScopeList.stream()
                                    .map(PublishScopeInfo::getScopeKey)
                                    .collect(Collectors.toList());
                            // 查出所有的部门Name
                            if (!deptIds.isEmpty()) {
                                List<DepartmentInfo> departmentInfoList = departmentInfoMapper.selectList(
                                        Wrappers.<DepartmentInfo>lambdaQuery()
                                                .in(DepartmentInfo::getDeptId, deptIds)
                                );
                                // 用逗号连接 部门名称
                                String deptScopeStr = departmentInfoList.stream()
                                        .distinct()
                                        .map(DepartmentInfo::getCnName)
                                        .collect(Collectors.joining(","));

                                messageApprovalDTO.setPublishScopeDesc(deptScopeStr);
                            }
                        } else if (publishScope.intValue() == (PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode())) {
                            messageApprovalDTO.setPublishScopeDesc(PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getMsg());
                        } else if (publishScope.intValue() == (PublishScopeEnum.ALL_PUSH.getCode())) {
                            messageApprovalDTO.setPublishScopeDesc(PublishScopeEnum.ALL_PUSH.getMsg());
                        } else if (publishScope.intValue() == (PublishScopeEnum.CHOOSE_PUSH.getCode())) {
                            messageApprovalDTO.setPublishScopeDesc(PublishScopeEnum.CHOOSE_PUSH.getMsg());
                        } else if (publishScope.intValue() == (PublishScopeEnum.GROUP_PUSH.getCode())) {
                            messageApprovalDTO.setPublishScopeDesc(PublishScopeEnum.GROUP_PUSH.getMsg());
                        }
                    }

                    List<PublishScopeInfo> publishScopeInfoList = scopeInfoMapper.selectList(
                            Wrappers.<PublishScopeInfo>lambdaQuery()
                                    .eq(PublishScopeInfo::getTaskId, taskInfo.getId())
                                    .select(PublishScopeInfo::getAllCount)
                    );

                    if (!publishScopeInfoList.isEmpty()) {
                        long allCount = publishScopeInfoList.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).orElse(0L);
                        messageApprovalDTO.setPushCount(allCount);
                    } else {
                        messageApprovalDTO.setPushCount(0L);
                    }

                    String username = taskInfo.getCreateUsername();
                    EmployeeInfo employeeInfo = employeeInfoMapper.selectOne(
                            Wrappers.<EmployeeInfo>lambdaQuery()
                                    .eq(EmployeeInfo::getUsername, username)
                                    .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                    );

                    if (Objects.nonNull(employeeInfo)) {
                        messageApprovalDTO.setCreateName(employeeInfo.getName());
                        messageApprovalDTO.setCreateUserEmail(employeeInfo.getEmail());
                    }

                    messageApproveRecords.add(messageApprovalDTO);
                }
        );
        messageApprovalPage.setRecords(messageApproveRecords);
        messageApprovalPage.setCurrent(taskPage.getCurrent());
        messageApprovalPage.setPages(taskPage.getPages());
        messageApprovalPage.setSize(taskPage.getSize());
        messageApprovalPage.setTotal(taskPage.getTotal());
        return messageApprovalPage;
    }

    @Override
    public BaseResult<Object> approved(MessageApprovalQueryDTO messageApprovalQueryDTO, UserBO userBO) {
        try {
            // 判断当前人是否能够审批
            List<Long> taskIds = this.getProcessTaskIds(userBO.getUsername());
            if (!taskIds.contains(messageApprovalQueryDTO.getTaskId())) {
                return new BaseResult<>(ResultCode.FORBIDDEN.getCode(), "当前登陆人越权操作");
            }
            TaskInfo taskInfo = taskInfoMapper.selectById(messageApprovalQueryDTO.getTaskId());
            // 1. 如果是飞书推送，先判断机器人是否被禁用
            if (taskInfo.getChannel() == TaskChannelEnum.CHANNEL_LARK.getCode() && taskInfo.getTaskStatus() == TaskStatusEnum.BOT_STOP.getCode()) {
                return new BaseResult<>(ResultCode.BOT_HAS_FORBIDDEN.getCode(), "机器人已经被禁用");
            }
            // 取出审批流程里面这个消息任务的信息
            ProcessInstanceInfo processInstanceInfo = processInstanceInfoMapper.selectOne(
                    Wrappers.<ProcessInstanceInfo>lambdaQuery()
                            .eq(ProcessInstanceInfo::getBizId, messageApprovalQueryDTO.getTaskId())
                            .eq(ProcessInstanceInfo::getStatus, ProcessStatusConstant.PROCESS_STATUS_APPROVAL)
            );

            if (Objects.isNull(processInstanceInfo)) {
                return new BaseResult<>(ResultCode.OK.getCode(), "任务已经被审批");
            }

            // 取出当前任务的审批节点
            List<ProcessTaskInfo> processTaskInfoList = processTaskInfoMapper.selectList(
                    Wrappers.<ProcessTaskInfo>lambdaQuery()
                            .eq(ProcessTaskInfo::getInstanceId, processInstanceInfo.getId())
            );

            Comparator<Integer> comparator = Comparator.comparingInt(Integer::intValue);

            Optional<Integer> maxProcessTaskId = processTaskInfoList.stream()
                    .map(e -> Integer.valueOf(e.getTaskId())).max(comparator);

            int processTaskId;

            if (maxProcessTaskId.isPresent()) {

                processTaskId = maxProcessTaskId.get();

                ProcessTaskInfo processTaskInfo = processTaskInfoMapper.selectOne(
                        Wrappers.<ProcessTaskInfo>lambdaQuery()
                                .eq(ProcessTaskInfo::getInstanceId, processInstanceInfo.getId())
                                .eq(ProcessTaskInfo::getApproverId, userBO.getUsername())
                                .eq(ProcessTaskInfo::getTaskId, String.valueOf(processTaskId))
                                .eq(ProcessTaskInfo::getApproveResultValue, ProcessStatusConstant.NO_ACTION)
                );

                if (Objects.nonNull(processTaskInfo)) {
                    processManager.approval(processInstanceInfo.getBizId(), processTaskInfo.getId(), userBO.getUsername(), taskInfo.getChannel());
                    // 循环审批下一个节点
                    loopExecuteNextApproval(processInstanceInfo.getId(), processInstanceInfo.getBizId(), (long) (processTaskId + 1), userBO.getUsername(), taskInfo.getChannel());
                } else {
                    return new BaseResult<>(ResultCode.OK.getCode(), "任务已经被审批");
                }

            }

        } catch (Exception e) {
            log.error("审批失败", e);
            return new BaseResult<>(ResultCode.SERVER_INNER_ERROR.getCode(), "审批异常");
        }
        return BaseResult.of();
    }

    private void loopExecuteNextApproval(Long processInstanceId, Long bizId, Long nextTaskId, String username, Byte channel) {
        do {
            nextTaskId = executeNextApprovalNode(processInstanceId, bizId, nextTaskId, username, channel);
        } while (!Objects.isNull(nextTaskId));
    }

    /**
     * 如果 下一个审批节点 是曾经已经审批过的节点，则直接执行审批，否则不作任何处理
     *
     * @param processInstanceId 流程 实例 id
     * @param bizId 业务 id
     * @param nextTaskId 下一级
     * @param username 当用用户
     * @param channel 消息渠道
     * @return 下一级的id
     */
    private Long executeNextApprovalNode(Long processInstanceId, Long bizId, Long nextTaskId, String username, Byte channel) {

        // 曾经审核过的用户id
        Set<String> hadApprovalIdList = processTaskInfoMapper.selectList(
                Wrappers.<ProcessTaskInfo>lambdaQuery()
                        .eq(ProcessTaskInfo::getInstanceId, processInstanceId)
                        .eq(ProcessTaskInfo::getApproveResultValue, ProcessStatusConstant.ACTION_AGREE)
        ).stream().map(ProcessTaskInfo::getUpdateUsername).collect(Collectors.toSet());

        // 加入当前操作用户
        hadApprovalIdList.add(username);

        // 下一个 待审核的节点
        List<ProcessTaskInfo> nextProcessTaskInfoList = processTaskInfoMapper.selectList(
                Wrappers.<ProcessTaskInfo>lambdaQuery()
                        .eq(ProcessTaskInfo::getInstanceId, processInstanceId)
                        .in(ProcessTaskInfo::getApproverId, hadApprovalIdList)
                        .eq(ProcessTaskInfo::getTaskId, nextTaskId)
                        .eq(ProcessTaskInfo::getApproveResultValue, ProcessStatusConstant.NO_ACTION)
        );

        log.info("executeNextApprovalNode begin nextProcessTaskInfoList = [{}]", JsonUtils.toJson(nextProcessTaskInfoList));

        if (!nextProcessTaskInfoList.isEmpty()) {
            Map<String, Long> approvalIdAndTaskIdMap = nextProcessTaskInfoList.stream().collect(Collectors.toMap(ProcessTaskInfo::getApproverId, ProcessTaskInfo::getId));
            if (approvalIdAndTaskIdMap.containsKey(username)) {
                log.info("executeNextApprovalNode bizId = [{}], nextTaskId = [{}], username = [{}]", bizId, nextTaskId, username);
                processManager.approval(bizId, approvalIdAndTaskIdMap.get(username), username, channel);
                return nextTaskId + 1;
            } else {
                hadApprovalIdList.retainAll(approvalIdAndTaskIdMap.keySet());
                Iterator<String> iterator = hadApprovalIdList.iterator();
                if (iterator.hasNext()) {
                    String oldUsername = iterator.next();
                    log.info("executeNextApprovalNode bizId = [{}], nextTaskId = [{}], username = [{}]", bizId, nextTaskId, oldUsername);
                    processManager.approval(bizId, approvalIdAndTaskIdMap.get(oldUsername), oldUsername, channel);
                    return nextTaskId + 1;
                }
            }
        }

        return null;
    }

    @Override
    public BaseResult<Object> approveReject(MessageApprovalQueryDTO messageApprovalQueryDTO, UserBO userBO) {
        try {
            // 判断当前人是否能够审批
            List<Long> taskIds = this.getProcessTaskIds(userBO.getUsername());
            if (!taskIds.contains(messageApprovalQueryDTO.getTaskId())) {
                return new BaseResult<>(ResultCode.FORBIDDEN.getCode(), "当前登陆人越权操作");
            }
            TaskInfo taskInfo = taskInfoMapper.selectById(messageApprovalQueryDTO.getTaskId());
            if (taskInfo.getChannel() == TaskChannelEnum.CHANNEL_LARK.getCode() && taskInfo.getTaskStatus() == TaskStatusEnum.BOT_STOP.getCode()) {
                return new BaseResult<>(ResultCode.BOT_HAS_FORBIDDEN.getCode(), "机器人已经被禁用");
            }
            // 取出审批流程里面这个消息任务的信息
            ProcessInstanceInfo processInstanceInfo = processInstanceInfoMapper.selectOne(
                    Wrappers.<ProcessInstanceInfo>lambdaQuery()
                            .eq(ProcessInstanceInfo::getBizId, messageApprovalQueryDTO.getTaskId())
                            .eq(ProcessInstanceInfo::getStatus, ProcessStatusConstant.PROCESS_STATUS_APPROVAL)
            );

            if (Objects.isNull(processInstanceInfo)) {
                return new BaseResult<>(ResultCode.OK.getCode(), "任务已经被审批");
            }

            // 取出当前任务的审批节点
            List<ProcessTaskInfo> processTaskInfoList = processTaskInfoMapper.selectList(
                    Wrappers.<ProcessTaskInfo>lambdaQuery()
                            .eq(ProcessTaskInfo::getInstanceId, processInstanceInfo.getId())
            );

            Comparator<Integer> comparator = Comparator.comparingInt(Integer::intValue);

            Optional<Integer> maxProcessTaskId = processTaskInfoList.stream()
                    .map(e -> Integer.valueOf(e.getTaskId())).max(comparator);

            int processTaskId;

            if (maxProcessTaskId.isPresent()) {

                processTaskId = maxProcessTaskId.get();

                ProcessTaskInfo processTaskInfo = processTaskInfoMapper.selectOne(
                        Wrappers.<ProcessTaskInfo>lambdaQuery()
                                .eq(ProcessTaskInfo::getInstanceId, processInstanceInfo.getId())
                                .eq(ProcessTaskInfo::getApproverId, userBO.getUsername())
                                .eq(ProcessTaskInfo::getTaskId, String.valueOf(processTaskId))
                                .eq(ProcessTaskInfo::getApproveResultValue, ProcessStatusConstant.NO_ACTION)
                );

                if (Objects.nonNull(processTaskInfo)) {
                    processManager.reject(processInstanceInfo.getBizId(), processTaskInfo.getId(), messageApprovalQueryDTO.getRemark(), userBO.getUsername(), taskInfo.getChannel());
                } else {
                    return new BaseResult<>(ResultCode.OK.getCode(), "任务已经被审批");
                }

            }
        } catch (Exception e) {
            log.error("审批失败,原因:[{}]", e);
            return new BaseResult<>(ResultCode.SERVER_INNER_ERROR.getCode(), "审批异常");
        }
        return BaseResult.of();
    }

    @Override
    public boolean checkHaveAccessAudit(String username, Long taskId) {
        ProcessInstanceInfo processInstanceInfo = processInstanceInfoMapper.selectOne(
                Wrappers.<ProcessInstanceInfo>lambdaQuery()
                        .eq(ProcessInstanceInfo::getBizId, taskId)
                        .eq(ProcessInstanceInfo::getStatus, ProcessStatusConstant.PROCESS_STATUS_APPROVAL)
        );

        if (Objects.isNull(processInstanceInfo)) {
            return false;
        }

        // 取出当前任务的审批节点
        List<ProcessTaskInfo> processTaskInfoList = processTaskInfoMapper.selectList(
                Wrappers.<ProcessTaskInfo>lambdaQuery()
                        .eq(ProcessTaskInfo::getInstanceId, processInstanceInfo.getId())
                        .eq(ProcessTaskInfo::getApproverId, username)
                        .eq(ProcessTaskInfo::getApproveResultValue, ProcessStatusConstant.NO_ACTION)
        );
        return processTaskInfoList != null && !processTaskInfoList.isEmpty();
    }

    /**
     * 飞书审核通过回调
     *
     * @param btnNotifBO 返回对象
     * @return int
     */
    @Override
    public LarkAuditCallBackEnum larkApprovedCallBack(BtnNotifBO btnNotifBO) {
        Map<String, Object> result = btnNotifBO.getBtnActionBo().getAction().getValue();
        try {
            String taskId = (String) result.get("taskId");
            String processTaskInfoId = (String) result.get("taskInfoId");

            // 0.如果是飞书任务, 检查是机器人是否被停用
            TaskInfo taskInfo = taskInfoMapper.selectById(Long.valueOf(taskId));

            if (Objects.isNull(taskInfo)) {
                return LarkAuditCallBackEnum.TASK_DELETE;
            }

            if (taskInfo.getChannel() == TaskChannelEnum.CHANNEL_LARK.getCode() && taskInfo.getTaskStatus() == TaskStatusEnum.BOT_STOP.getCode()) {
                return LarkAuditCallBackEnum.BOT_FORBIDDEN;
            }
            ProcessTaskInfo processTaskInfo = processTaskInfoMapper.selectById(Long.valueOf(processTaskInfoId));
            // 1. 判断是否被审核
            if (processTaskInfo.getApproveResultValue() != ProcessStatusConstant.NO_ACTION) {
                return LarkAuditCallBackEnum.HAVE_BEEN_AUDIT;
            }
            // 2.执行审核通过
            processManager.approval(Long.valueOf(taskId), processTaskInfo.getId(), processTaskInfo.getApproverId(), taskInfo.getChannel());
            // 循环审批下一个节点
            loopExecuteNextApproval(processTaskInfo.getInstanceId(), Long.valueOf(taskId), Long.parseLong(processTaskInfo.getTaskId()) + 1, processTaskInfo.getApproverId(), taskInfo.getChannel());
            // 3.返回执行状态
            return LarkAuditCallBackEnum.APPROVED;
        } catch (Exception e) {
            log.error("飞书审核app_id为[{}]的任务失败,原因:[{}],请求参数为[{}]", btnNotifBO.getAppId(), e.getMessage(), new Gson().toJson(btnNotifBO));
            throw e;
        }
    }

    /**
     * 飞书审核拒绝回调
     *
     * @param btnNotifBO 返回对象
     * @return int
     */
    @Override
    public LarkAuditCallBackEnum larkRejectedCallBack(BtnNotifBO btnNotifBO) {
        Map<String, Object> result = btnNotifBO.getBtnActionBo().getAction().getValue();
        try {
            String taskId = (String) result.get("taskId");
            String processTaskInfoId = (String) result.get("taskInfoId");

            // 0.如果是飞书任务，检查是机器人是否被停用
            TaskInfo taskInfo = taskInfoMapper.selectById(Long.valueOf(taskId));

            if (Objects.isNull(taskInfo)) {
                return LarkAuditCallBackEnum.TASK_DELETE;
            }

            if (taskInfo.getChannel() == TaskChannelEnum.CHANNEL_LARK.getCode() && taskInfo.getTaskStatus() == TaskStatusEnum.BOT_STOP.getCode()) {
                return LarkAuditCallBackEnum.BOT_FORBIDDEN;
            }
            ProcessTaskInfo processTaskInfo = processTaskInfoMapper.selectById(Long.valueOf(processTaskInfoId));
            // 1. 判断是否被审核
            if (processTaskInfo.getApproveResultValue() != ProcessStatusConstant.NO_ACTION) {
                return LarkAuditCallBackEnum.HAVE_BEEN_AUDIT;
            }
            // 2.执行审核拒绝
            processManager.reject(Long.valueOf(taskId), processTaskInfo.getId(), "", processTaskInfo.getApproverId(), taskInfo.getChannel());
            // 3.返回执行状态
            return LarkAuditCallBackEnum.APPROVE_REJECT;
        } catch (Exception e) {
            log.error("飞书审核app_id为[{}]的任务失败,原因:[{}],请求参数为[{}]", btnNotifBO.getAppId(), e.getMessage(), new Gson().toJson(btnNotifBO));
            throw e;
        }
    }

    private List<Long> getProcessTaskIds(String username) {
        // 流程任务里面状态为待办的
        List<ProcessTaskInfo> processTaskInfoList = processTaskInfoMapper.selectList(
                Wrappers.<ProcessTaskInfo>lambdaQuery()
                        .eq(ProcessTaskInfo::getApproverId, username)
                        .eq(ProcessTaskInfo::getApproveResultValue, ProcessStatusConstant.NO_ACTION)
                        .select(ProcessTaskInfo::getInstanceId)
        );

        if (processTaskInfoList.isEmpty()) {
            return new ArrayList<>();
        }

        List<Long> instanceIds = processTaskInfoList.stream()
                .map(ProcessTaskInfo::getInstanceId)
                .distinct()
                .collect(Collectors.toList());

        List<ProcessInstanceInfo> processInstanceInfoList = processInstanceInfoMapper.selectList(
                Wrappers.<ProcessInstanceInfo>lambdaQuery()
                        .in(ProcessInstanceInfo::getId, instanceIds)
                        .select(ProcessInstanceInfo::getBizId)
        );
        return processInstanceInfoList.stream()
                .map(ProcessInstanceInfo::getBizId)
                .distinct()
                .collect(Collectors.toList());
    }

    private Map<String, Object> generateRefreshCard(String msg, String taskId) {

        TaskInfo taskInfo = taskInfoMapper.selectById(Long.valueOf(taskId));

        LarkTaskInfo larkTaskInfo = larkTaskInfoMapper.selectOne(
                Wrappers.<LarkTaskInfo>lambdaQuery()
                        .eq(LarkTaskInfo::getTaskId, taskInfo.getId())
        );

        // 同一个任务，只能属于飞书，邮件，短信的一种
        if (larkTaskInfo != null) {
            BotInfo botInfo = botInfoMapper.selectOne(
                    Wrappers.<BotInfo>lambdaQuery()
                            .eq(BotInfo::getId, larkTaskInfo.getBotId())
            );
            EmployeeInfo employeeInfo = employeeInfoMapper.selectOne(
                    Wrappers.<EmployeeInfo>lambdaQuery()
                            .eq(EmployeeInfo::getUsername, taskInfo.getCreateUsername())
                            .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
            );

            List<PublishScopeInfo> publishScopeInfoList = scopeInfoMapper.selectList(
                    Wrappers.<PublishScopeInfo>lambdaQuery()
                            .eq(PublishScopeInfo::getTaskId, taskInfo.getId())
                            .select(PublishScopeInfo::getAllCount)
            );

            CardContentRefreshBO cardContentRefreshBO = new CardContentRefreshBO();
            cardContentRefreshBO.setTitle(ApplyContentConstants.TITLE);
            cardContentRefreshBO.setRobotName(botInfo.getBotName());
            cardContentRefreshBO.setCreateName(employeeInfo.getName());
            cardContentRefreshBO.setPublishDate(DateFormatUtils.format(taskInfo.getPublishTime(), DATE_TIME_PATTERN));
            cardContentRefreshBO.setCreateUsername(taskInfo.getCreateUsername());
            cardContentRefreshBO.setTaskId(taskInfo.getId());
            cardContentRefreshBO.setResultMsg(msg);
            if (!publishScopeInfoList.isEmpty()) {
                long allCount = publishScopeInfoList.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).orElse(0L);
                cardContentRefreshBO.setAllCount(allCount);
            } else {
                cardContentRefreshBO.setAllCount(0L);
            }

            cardContentRefreshBO.setPublishScopeEnum(PublishScopeEnum.getByCode(taskInfo.getPublishScope()));

            return cardContentRefreshBO.getFreshParam();
        }
        return null;
    }

}

