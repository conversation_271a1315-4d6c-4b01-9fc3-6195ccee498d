package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.admin.entity.bo.AppApprovalListBO;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.vo.AppApprovalListVO;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.manager.UserRoleManager;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.remote.grpc.AppListGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.AppApprovalListService;
import com.mioffice.ums.admin.utils.MapperUtil;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppApplyCancelQueryInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppApprovalListInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppApprovalListQueryInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppApprovalPassOrRejectQueryInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/21 3:24 上午
 * version: 1.0.0
 */
@Slf4j
@Service
public class AppApprovalListServiceImpl implements AppApprovalListService {

    private final EmployeeInfoMapper employeeInfoMapper;

    private final AppListGrpcClient appListGrpcClient;

    private final UserRoleManager userRoleManager;

    public AppApprovalListServiceImpl(EmployeeInfoMapper employeeInfoMapper, AppListGrpcClient appListGrpcClient, UserRoleManager userRoleManager) {
        this.employeeInfoMapper = employeeInfoMapper;
        this.appListGrpcClient = appListGrpcClient;
        this.userRoleManager = userRoleManager;
    }

    @Override
    public BaseResult<AppApprovalListVO> appApprovalPage(UserBO userBO, Long page, Long size, String appName, String managerUsernameList, String channel, String beginDate, String endDate) {

        if (validateApprovalPermission(userBO)) {
            return new BaseResult<AppApprovalListVO>(ResultCode.FORBIDDEN).setMessage("您没有权限访问此接口");
        }

        AppApprovalListQueryInfoResponse appApprovalListQueryInfoResponse;
        try {
            appApprovalListQueryInfoResponse = appListGrpcClient.getAppApprovalPage(userBO.getUsername(), page, size, appName, managerUsernameList, channel, beginDate, endDate);
        } catch (Exception e) {
            log.error("[{}]获取系统待审核列表时发生了异常, 异常信息为[{}]", userBO.getUsername(), e.getMessage());
            return new BaseResult<AppApprovalListVO>().setCode(500).setMessage("获取系统待审核列表时发生了异常");
        }
        int code = appApprovalListQueryInfoResponse.getCode();
        String desc = appApprovalListQueryInfoResponse.getDesc();
        if (code != 200) {
            AppApprovalListVO appApprovalListVO = AppApprovalListVO.newEmpty(page, size);
            return new BaseResult<AppApprovalListVO>().setCode(code).setMessage(desc).setData(appApprovalListVO);
        }
        AppApprovalListInfo appApprovalListInfo = appApprovalListQueryInfoResponse.getAppApprovalListInfo();
        AppApprovalListBO appApprovalListBO = MapperUtil.INSTANCE.mapToAppApprovalListBO(appApprovalListInfo);
        List<AppApprovalListBO.Record> boRecordsList = appApprovalListBO.getRecordsList();
        List<AppApprovalListVO.Record> voRecordList = new ArrayList<>();
        boRecordsList.forEach(
                record -> {
                    try {
                        AppApprovalListVO.Record voRecord = new AppApprovalListVO.Record();
                        List<Byte> isOutSendList = record.getIsOutSendList();
                        BeanUtils.copyProperties(record, voRecord);
                        isOutSendList.remove((Byte) (byte) 0);
                        if (isOutSendList.isEmpty()) {
                            voRecord.setIsOutSend((byte) 0);
                        } else {
                            voRecord.setIsOutSend(isOutSendList.get(0));
                        }
                        voRecordList.add(voRecord);
                    } catch (Exception e) {
                        log.warn("企业内企业外数值移除失败", e);
                    }
                }
        );
        AppApprovalListVO appApprovalListVO = new AppApprovalListVO();
        appApprovalListVO.setCurrent(appApprovalListBO.getCurrent());
        appApprovalListVO.setPages(appApprovalListBO.getPages());
        appApprovalListVO.setSearchCount(appApprovalListBO.getSearchCount());
        appApprovalListVO.setSize(appApprovalListBO.getSize());
        appApprovalListVO.setTotal(appApprovalListBO.getTotal());
        appApprovalListVO.setRecordsList(voRecordList);
        return BaseResult.of(appApprovalListVO);
    }

    @Override
    public BaseResult<Object> appApprovalPass(UserBO userBO, Long id) {
        if (validateApprovalPermission(userBO)) {
            return new BaseResult<>(ResultCode.FORBIDDEN).setMessage("您没有权限访问此接口");
        }

        EmployeeInfo employeeInfo = employeeInfoMapper.selectOne(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .eq(EmployeeInfo::getUsername, userBO.getUsername())
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        );
        AppApprovalPassOrRejectQueryInfoResponse response;
        try {
            response = appListGrpcClient.appApprovalPass(employeeInfo.getUsername(), employeeInfo.getName(), id);
        } catch (Exception e) {
            log.error("[{}]系统审核通过时发生了异常, 异常信息为[{}]", userBO.getUsername(), e.getMessage());
            return new BaseResult<>().setCode(500).setMessage("系统审核通过时发生了异常");
        }
        int code = response.getCode();
        String desc = response.getDesc();
        if (code != 200) {
            return new BaseResult<>().setCode(code).setMessage(desc);
        }
        return BaseResult.of();
    }

    @Override
    public BaseResult<Object> appApprovalReject(UserBO userBO, Long id, String reason) {
        if (validateApprovalPermission(userBO)) {
            return new BaseResult<>(ResultCode.FORBIDDEN).setMessage("您没有权限访问此接口");
        }

        EmployeeInfo employeeInfo = employeeInfoMapper.selectOne(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .eq(EmployeeInfo::getUsername, userBO.getUsername())
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        );
        AppApprovalPassOrRejectQueryInfoResponse response;
        try {
            response = appListGrpcClient.appApprovalReject(employeeInfo.getUsername(), employeeInfo.getName(), id, reason);
        } catch (Exception e) {
            log.error("[{}]系统审核驳回时发生了异常, 异常信息为[{}]", userBO.getUsername(), e.getMessage());
            return new BaseResult<>().setCode(500).setMessage("系统审核驳回时发生了异常");
        }
        int code = response.getCode();
        String desc = response.getDesc();
        if (code != 200) {
            return new BaseResult<>().setCode(code).setMessage(desc);
        }
        return BaseResult.of();
    }

    private boolean validateApprovalPermission(UserBO userBO) {
        List<String> roleList = userBO.getRoleList();
        return !(roleList.contains("ROLE_SYS_SUPER_ADMIN") || roleList.contains("ROLE_SYS_ADMIN"));
    }

    @Override
    public BaseResult<Object> appApplyCancel(UserBO userBO, Long id) {
        EmployeeInfo employeeInfo = employeeInfoMapper.selectOne(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .eq(EmployeeInfo::getUsername, userBO.getUsername())
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        );
        List<String> roleList = userBO.getRoleList();
        AppApplyCancelQueryInfoResponse response;
        try {
            response = appListGrpcClient.appApplyCancel(employeeInfo.getUsername(), employeeInfo.getName(), id, roleList);
        } catch (Exception e) {
            log.error("[{}]系统审核取消时发生了异常, 异常信息为[{}]", userBO.getUsername(), e.getMessage());
            return new BaseResult<>(ResultCode.SERVER_INNER_ERROR).setMessage("系统审核取消时发生了异常");
        }
        int code = response.getCode();
        String desc = response.getDesc();
        if (code == ResultCode.UNAUTHORIZED.getCode()) {
            return new BaseResult<>(ResultCode.FORBIDDEN).setMessage("用户无权操作此记录");
        }
        if (code != ResultCode.OK.getCode()) {
            return new BaseResult<>().setCode(code).setMessage(desc);
        }
        return BaseResult.of();
    }
}
