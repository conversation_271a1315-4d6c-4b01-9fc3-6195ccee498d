package com.mioffice.ums.admin.service;

/**
 * @desc 任务消息催办服务
 */
public interface MessageJobUrgeService {
    /**
     * 催办(自定义催办人)任务
     *
     * @param managerUrgeExtraId
     * @param appId
     * @param msgJobId
     * @param username
     * @return
     */
    void urgeCustom(String managerUrgeExtraId,String appId, Long msgJobId, String username);

    /**
     * 催办(上级(自动获取)催办)任务
     *
     * @param managerUrgeExtraId
     * @param appId
     * @param urgeData
     * @param username
     * @return
     */
    void urgeManager(String managerUrgeExtraId,String appId,Long msgJobId, Long taskId, String urgeData, String username);
}
