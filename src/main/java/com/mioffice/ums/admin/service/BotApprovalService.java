package com.mioffice.ums.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.entity.bo.BtnNotifBO;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.BotApplyApprovalPageDTO;
import com.mioffice.ums.admin.entity.dto.BotApplyDTO;
import com.mioffice.ums.admin.entity.dto.BotApplyDetailDTO;
import com.mioffice.ums.admin.entity.dto.BotApplyListDTO;
import com.mioffice.ums.admin.entity.dto.BotApprovalPassQueryDTO;
import com.mioffice.ums.admin.entity.dto.BotApprovalRejectQueryDTO;
import com.mioffice.ums.admin.enums.LarkAuditCallBackEnum;
import com.mioffice.ums.admin.result.BaseResult;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/31 2:15 下午
 * version: 1.0.0
 */
public interface BotApprovalService {

    /**
     * 机器人申请接口
     *
     * @param botApplyDTO 机器人申请参数结构体
     * @param userBO 用户信息
     * @return 申请机器人结果
     */
    BaseResult<Object> botApply(BotApplyDTO botApplyDTO, UserBO userBO);

    /**
     * 我申请的机器人列表接口
     *
     * @param userBO 当前登录用户信息
     * @param page 请求页码
     * @param size 每页数据数量
     * @return 我申请的机器人列表
     */
    IPage<BotApplyListDTO> botApplyListMy(UserBO userBO, Long page, Long size);

    /**
     * 我审核的机器人列表接口
     *
     * @param userBO 当前登录用户信息
     * @param page 请求页码
     * @param size 每页数据数量
     * @return 我审核的机器人列表
     */
    IPage<BotApplyApprovalPageDTO> botApprovalPage(UserBO userBO, Long page, Long size);

    /**
     * 申请详情接口
     *
     * @param id 申请记录id
     * @return 申请详情
     */
    BotApplyDetailDTO botApplyDetail(Long id);

    /**
     * 审批拒绝接口
     *
     * @param userBO 当前登录用户信息
     * @param botApprovalRejectQueryDTO 审批拒绝请求参数结构体
     * @return 审批拒绝结果
     */
    BaseResult<Object> botApprovalReject(UserBO userBO, BotApprovalRejectQueryDTO botApprovalRejectQueryDTO);

    /**
     * 审批通过接口
     *
     * @param userBO 当前登录用户信息
     * @param botApprovalPassQueryDTO 审批通过请求参数结构体
     * @return 审批通过结果
     */
    BaseResult<Object> botApprovalPass(UserBO userBO, BotApprovalPassQueryDTO botApprovalPassQueryDTO);

    /**
     * 机器人申请飞书审核通过回调
     *
     * @param btnNotifBO
     * @return {true} 回调成功
     */
    LarkAuditCallBackEnum larkApprovedCallBack(BtnNotifBO btnNotifBO);

    /**
     * 机器人申请飞书审核拒绝回调
     *
     * @param btnNotifBO
     * @return {true} 回调成功
     */
    LarkAuditCallBackEnum larkRejectedCallBack(BtnNotifBO btnNotifBO);

}
