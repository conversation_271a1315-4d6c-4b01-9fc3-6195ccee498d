package com.mioffice.ums.admin.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import com.google.common.base.Preconditions;
import com.google.gson.Gson;
import com.mioffice.ums.admin.config.SendWeeklyConfig;
import com.mioffice.ums.admin.entity.bo.AppAccessTokenBO;
import com.mioffice.ums.admin.entity.bo.LarkCallBackBO;
import com.mioffice.ums.admin.entity.bo.LarkSheetContentBO;
import com.mioffice.ums.admin.entity.bo.LarkSheetHeaderBO;
import com.mioffice.ums.admin.entity.bo.LarkSheetMetaInfoBO;
import com.mioffice.ums.admin.entity.bo.TitleAndContentBO;
import com.mioffice.ums.admin.entity.bo.UserAccessTokenBO;
import com.mioffice.ums.admin.enums.ContentFlagEnum;
import com.mioffice.ums.admin.manager.auth.NotifyDataDecrypter;
import com.mioffice.ums.admin.manager.file.DefaultLarkCloudFileManager;
import com.mioffice.ums.admin.manager.lark.LarkAccessTokenHelper;
import static com.mioffice.ums.admin.manager.lark.LarkAccessTokenHelper.GRANT_REFRESH;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.SendWeeklyMsgService;
import com.mioffice.ums.admin.utils.DateTimeUtil;
import com.mioffice.ums.admin.utils.JsonUtils;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageTemplateId;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUser;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUserResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 发送周报消息
 * </p>
 *
 * <AUTHOR>
 * @date 2021/3/23 3:38 下午
 */
@Slf4j
@Service
public class SendWeeklyMsgServiceImpl implements SendWeeklyMsgService {

    /**
     * 每次发送周报条数(因为飞书发送消息最大内容大小为30K,所以这里做了拆分)
     */
    private static final int SEND_SIZE = 12;
    /**
     * 飞书表格行列字母
     */
    private static final String[] LETTER = {"A", "B", "C", "D", "E", "F", "G", "H",
            "I", "J", "K", "L", "M", "N", "O", "P", "Q",
            "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};
    /**
     * Key刷新阈值
     */
    private static final long EXPIRE_THRESHOLD = 5 * 60;
    /**
     * Redis AccessToken Key
     */
    private static final String UMS_USER_ACCESS_TOKEN_KEY = "UMS_USER_ACCESS_TOKEN";
    /**
     * Redis Refresh_Token Key(用来刷新USER_ACCESS_TOKEN)
     */
    private static final String UMS_USER_REFRESH_TOKEN_KEY = "UMS_USER_REFRESH_TOKEN";
    /**
     * 飞书字段,用来判断是否内容进行了加密
     */
    private static final String ENCRYPT_KEY = "encrypt";
    /**
     * 暂时废弃(用来匹配机器人@时的文字)
     */
    private static final String BOT_CALL_WORD = "周日报";

    @Autowired
    private DefaultLarkCloudFileManager cloudFileManager;
    @Autowired
    private SendWeeklyConfig sendWeeklyConfig;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private LarkAccessTokenHelper larkAccessTokenHelper;
    @Autowired
    private MessageGrpcClient messageGrpcClient;
    @Autowired
    private Gson gson;

    /**
     * 发送周报动作
     *
     * @param larkCallBackBO @事件飞书传入参数(如果定时发送可以自行给对象赋值)
     */
    @Override
    public void sendWeeklyMsg(LarkCallBackBO larkCallBackBO) {

        // 飞书文档Token
        String sheetToken = sendWeeklyConfig.getSheetToken();

        //文档权限拥有者之一的user_access_token
        String userAccessToken = String.valueOf(redisTemplate.opsForValue().get(UMS_USER_ACCESS_TOKEN_KEY));

        log.info("发送周报开始,当前sheetToken=【{}】,userAccessToken =【{}】", sheetToken, userAccessToken);

        try {
            // 1. 获取sheet元数据
            LarkSheetMetaInfoBO sheetMetaInfo = this.getSheetMetaInfo(sheetToken, userAccessToken);

            // 2. 取第一个sheet表
            List<LarkSheetMetaInfoBO.SheetInfo> sheetInfoList = sheetMetaInfo.getSheets();
            LarkSheetMetaInfoBO.SheetInfo sheetInfo = sheetInfoList.get(0);

            // 3. 取出sheetId, rowCount行数, columnCount列行数
            String sheetId = sheetInfo.getSheetId();
            Integer rowCount = sheetInfo.getRowCount();
            Integer columnCount = sheetInfo.getColumnCount();

            // 4.获取sheet header数据
            String range = sheetId.concat("!").concat("A1:").concat(LETTER[columnCount - 1]).concat("1");
            LarkSheetHeaderBO larkSheetHeaderBO = this.getSheetHeader(sheetToken, userAccessToken, range);

            // 5.通过header组装要获取的行列数据请求体
            List<String> ranges2 = new ArrayList<>();
            for (int i = 2; i <= rowCount; i++) {
                ranges2.add(sheetId.concat("!").concat("A").concat(String.valueOf(i)).concat(":").concat(LETTER[columnCount - 1]).concat(String.valueOf(i)));
            }
            // 6.获取飞Sheet内容
            LarkSheetContentBO larkSheetContentBO = this.getSheetContent(sheetToken, userAccessToken, ranges2);

            if (larkSheetContentBO == null || larkSheetHeaderBO == null) {
                log.info("周报数据为空, 不发送");
                return;
            }
            // 7.组装要发送的数据
            TitleAndContentBO titleAndContentBO = this.packageContent(larkSheetHeaderBO, larkSheetContentBO);

            // 8.send msg
            boolean isSent = this.sendMsg(titleAndContentBO, larkCallBackBO);
            Preconditions.checkArgument(isSent, "发送周报报错");

        } catch (Exception e) {
            log.warn("发送周报报错,原因:【{}】", e.toString());
        }
    }

    /**
     * 定时任务自动发送周报
     */
    @Override
    public void autoSendWeeklyMsg() {
        LarkCallBackBO larkCallBackBO = new LarkCallBackBO();
        LarkCallBackBO.CallEvent callEvent = new LarkCallBackBO.CallEvent();
        // 自定义需要发送的群聊
        callEvent.setOpen_chat_id(sendWeeklyConfig.getChatId());
        larkCallBackBO.setEvent(callEvent);
        this.sendWeeklyMsg(larkCallBackBO);
    }

    /**
     * 用户登陆并将关键信息存入redis
     * 第一次需要拥有文档权限的用户进行登陆拿到登陆code，
     * 并将user_access_token和refresh_token存入redis,然后后续通过refresh_token定时进行刷新
     *
     * @param code 用户授权码
     * @param status 自己定义
     */
    @Override
    public void processAccessToken(String code, String status) {

        try {
            // 1.飞书应用的appId和appKey
            String appId = sendWeeklyConfig.getSenderAppId();
            String appKey = sendWeeklyConfig.getSenderAppKey();

            // 2.获取app_access_token
            AppAccessTokenBO appAccessTokenBO = larkAccessTokenHelper.acquireAppAccessToken(appId, appKey);
            Assert.notNull(appAccessTokenBO, "appAccessTokenBO为null");
            String appAccessToken = appAccessTokenBO.getApp_access_token();

            // 3.通过授权码获取user_access_token
            UserAccessTokenBO userAccessTokenBO = larkAccessTokenHelper.acquireUserAccessToken(appId, appKey, LarkAccessTokenHelper.GRANT_CODE, code, appAccessToken);
            Assert.notNull(userAccessTokenBO, "userAccessTokenBO为null");

            // 4.user_access_token过期时间
            String userAccessToken = userAccessTokenBO.getAccess_token();
            // 飞书返回的是过期时间戳
            Long userAccessTokenExpiresIn = userAccessTokenBO.getExpires_in();
            // 5.refresh_token过期时间
            String refreshUserAccessToken = userAccessTokenBO.getRefresh_token();
            // 飞书返回的是过期时间戳
            Long refreshExpiresIn = userAccessTokenBO.getRefresh_expires_in();

            Long currentTimeSecond = DateTimeUtil.getCurrentTimeSecond();
            // 6.存入redis
            redisTemplate.opsForValue().set(UMS_USER_ACCESS_TOKEN_KEY, userAccessToken, userAccessTokenExpiresIn - currentTimeSecond, TimeUnit.SECONDS);
            redisTemplate.opsForValue().set(UMS_USER_REFRESH_TOKEN_KEY, refreshUserAccessToken, refreshExpiresIn - currentTimeSecond, TimeUnit.SECONDS);

        } catch (Exception e) {
            log.warn("处理processAccessToken异常, 原因:[{}]", e.toString());
            throw new RuntimeException(e);
        }
    }

    /**
     * 飞书回调处理, @机器人
     *
     * @param appId
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> processLarkCallBack(String appId, Map<String, Object> params) {

        if (params.containsKey(ENCRYPT_KEY)) {
            //密文，需要解析
            return this.encrypt(appId, params);
        } else {
            // 明文
            ThreadUtil.execute(
                    () -> {
                        LarkCallBackBO larkCallBackBO = new Gson().fromJson(gson.toJson(params), LarkCallBackBO.class);
                        this.sendWeeklyMsg(larkCallBackBO);
                    }
            );
            return params;
        }
    }

    /**
     * 定时刷新user_access_token
     */
    @Override
    public void refreshToken() {

        // 1.redis取出refresh_token  和  user_access_token
        Long refreshExpire = redisTemplate.getExpire(UMS_USER_REFRESH_TOKEN_KEY, TimeUnit.SECONDS);
        // 2.取出 user_access_token过期时间
        Long userExpire = redisTemplate.getExpire(UMS_USER_ACCESS_TOKEN_KEY, TimeUnit.SECONDS);

        if (refreshExpire != null && userExpire != null) {
            //3.如果小于等于刷新阈值就刷新user_access_token
            Long minExpire = Math.min(refreshExpire, userExpire);
            if (minExpire <= EXPIRE_THRESHOLD) {
                String appId = sendWeeklyConfig.getSenderAppId();
                String appKey = sendWeeklyConfig.getSenderAppKey();

                AppAccessTokenBO appAccessTokenBO = larkAccessTokenHelper.acquireAppAccessToken(appId, appKey);
                Assert.notNull(appAccessTokenBO, "appAccessTokenBO为null");
                String appAccessToken = appAccessTokenBO.getApp_access_token();

                UserAccessTokenBO userAccessTokenBO = larkAccessTokenHelper.refreshUserAccessToken(appAccessToken, GRANT_REFRESH, String.valueOf(redisTemplate.opsForValue().get(UMS_USER_REFRESH_TOKEN_KEY)));

                String userAccessToken = userAccessTokenBO.getAccess_token();
                Long userAccessTokenExpires = userAccessTokenBO.getExpires_in();

                String refreshUserAccessToken = userAccessTokenBO.getRefresh_token();
                Long refreshExpires = userAccessTokenBO.getRefresh_expires_in();
                // 4.重新入库
                redisTemplate.opsForValue().set(UMS_USER_ACCESS_TOKEN_KEY, userAccessToken, userAccessTokenExpires, TimeUnit.SECONDS);
                redisTemplate.opsForValue().set(UMS_USER_REFRESH_TOKEN_KEY, refreshUserAccessToken, refreshExpires, TimeUnit.SECONDS);

            }
        }
    }

    /**
     * 解密飞书回调内容
     *
     * @param appId
     * @param params
     * @return
     */
    private Map<String, Object> encrypt(String appId, Map<String, Object> params) {

        String encrypt = params.get(ENCRYPT_KEY).toString();
        Map<String, Object> result = new HashMap<>(16);
        try {

            NotifyDataDecrypter notifyDataDecrypter = new NotifyDataDecrypter(sendWeeklyConfig.getEncryptKey());
            String jsonString = notifyDataDecrypter.decrypt(encrypt);
            Map<String, Object> jsonParams = JsonUtils.toMap(jsonString);
            log.info("飞书@消息 解密成功 appId = [{}], jsonParams = [{}]", appId, jsonString);
            result.put("challenge", jsonParams.get("challenge"));
            // 这里异步是因为飞书回调需要在3S内返回
            ThreadUtil.execute(
                    () -> {
                        LarkCallBackBO larkCallBackBO = new Gson().fromJson(jsonString, LarkCallBackBO.class);
                        this.sendWeeklyMsg(larkCallBackBO);
                    }
            );
        } catch (Exception e) {
            log.warn("飞书@消息 解密失败 appId = [{}] ", appId, e);
            result.put("400", e.getMessage());
        }
        return result;
    }

    /**
     * 发送消息核心逻辑
     *
     * @param titleAndContentBO 消息内容
     * @param larkCallBackBO 飞书回调信息
     * @return
     */
    private boolean sendMsg(TitleAndContentBO titleAndContentBO, LarkCallBackBO larkCallBackBO) {

        List<String> msgContent = titleAndContentBO.getMsgContent();
        String title = titleAndContentBO.getTitle();
        int start = 0;
        int end = SEND_SIZE;
        int size = msgContent.size();
        // 1. 每次发12条周报
        if (size <= SEND_SIZE) {
            return sendMessage(title, msgContent, larkCallBackBO);
        } else {
            while (end <= size) {
                List<String> newMsgContent = msgContent.subList(start, end);
                this.sendMessage(title, newMsgContent, larkCallBackBO);
                // 暂停3S因为UMS现在没有保证消息有序性
                try {
                    Thread.sleep(3000);
                } catch (InterruptedException e) {

                }
                if (end == size) {
                    break;
                }
                start = end;
                end = Math.min((end + SEND_SIZE), size);
            }
        }
        return true;
    }

    /**
     * 调用UMS进行发消息
     *
     * @param msgContent
     * @param larkCallBackBO
     * @return
     */
    private boolean sendMessage(String title, List<String> msgContent, LarkCallBackBO larkCallBackBO) {

        //1. 组装内容
        HashMap<String, Object> map = new HashMap<String, Object>();
        map.put("contentList", msgContent);
        map.put("header", title);
        String extraId = UUID.randomUUID().toString().replaceAll("-", "");
        //2. 获取模版
        MessageTemplateId messageTemplateId = MessageTemplateId.newBuilder().setMessageTemplateId(sendWeeklyConfig.getTemplateId()).build();
        List<MessageUser> messageUserList = new ArrayList<>();
        MessageUser.Builder messageUserBuilder = MessageUser.newBuilder();
        messageUserBuilder.setExtraId(extraId);
        //3. chatId要发送的群id
        messageUserBuilder.setChatId(larkCallBackBO.getEvent().getOpen_chat_id());
        messageUserBuilder.setPlaceholderContent(JsonUtils.toJson(map));
        messageUserBuilder.setAppId(sendWeeklyConfig.getSenderAppId());
        messageUserBuilder.setContentFlag(ContentFlagEnum.LARK_CONTENT.getFlag());
        messageUserList.add(messageUserBuilder.build());
        MessageUserResponse messageUserResponse = messageGrpcClient.sendMessageBatch(messageUserList, messageTemplateId);
        if (messageUserResponse.getCode() != ResultCode.OK.getCode()) {
            log.warn("【消息】发送审核卡片消息失败 code = [{}], msg = [{}]", messageUserResponse.getCode(), messageUserResponse.getMessage());
            return false;
        }
        return true;
    }

    /**
     * 包装数据
     *
     * @param larkSheetHeaderBO 飞书文档header
     * @param larkSheetContentBO 飞书文档内容
     * @return
     */
    private TitleAndContentBO packageContent(LarkSheetHeaderBO larkSheetHeaderBO, LarkSheetContentBO larkSheetContentBO) {
        TitleAndContentBO titleAndContentBO = new TitleAndContentBO();

        List<String> result = new ArrayList<>();

        List<Object> headers = larkSheetHeaderBO.getValueRange().getValues().get(0);
        List<LarkSheetHeaderBO.ValueRange> valueRanges = larkSheetContentBO.getValueRanges();

        int index = 1;
        int completeNum = 0;
        BigDecimal totalScore = BigDecimal.ZERO;
        for (LarkSheetHeaderBO.ValueRange valueRange : valueRanges) {
            List<Object> rowData = valueRange.getValues().get(0);
            String content = "";
            boolean isComplete = false;
            for (int i = 0; i < rowData.size(); i++) {
                Object column = rowData.get(i);
                Object header = headers.get(i);
                if (column != null && header != null) {
                    // 判断该列是不是link
                    LarkSheetContentBO.Link link = judgeIsLink(column);
                    String columnText = "";
                    if (link != null) {
                        if (i == 0) {
                            columnText = columnText.concat(String.valueOf(index)).concat(". ").concat(link.getText());
                        } else {
                            columnText = link.getText();
                        }
                    } else {
                        if (i == 0) {
                            columnText = columnText.concat(String.valueOf(index)).concat(". ").concat(String.valueOf(column));
                        } else if (i == 1) {
                            columnText = String.valueOf(column);
                            if ("已结项".equals(columnText)) {
                                completeNum = completeNum + 1;
                                isComplete = true;
                            }
                        } else if (i == 2) {
                            columnText = String.valueOf(column);
                            if (isComplete) {
                                if (StringUtils.isNotBlank(columnText)) {
                                    try {
                                        totalScore = new BigDecimal(columnText).add(totalScore);
                                    } catch (Exception e) {
                                        log.info("计算总分失败, columnText = [{}]", columnText, e);
                                    }
                                }
                            }
                        } else {
                            columnText = String.valueOf(column);
                        }
                        // 判断是否小数，这里暂时先写死 小于1的浮点数 先处理为%比
                        if (NumberUtils.isParsable(columnText)) {
                            columnText = getPercentNumber(columnText);
                        }
                    }
                    content = content.concat("**").concat(String.valueOf(header)).concat("**")
                            .concat("\n")
                            .concat(columnText)
                            .concat("\n");
                }
            }
            result.add(content.concat("\n"));
            index++;
        }

        List<String> msgContent = result.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        titleAndContentBO.setMsgContent(msgContent);
        if (completeNum != 0) {
            try {
                titleAndContentBO.setTitle(String.format("A计划项目周报(已结项 %s 个，平均分 %s 分)", completeNum, totalScore.divide(new BigDecimal(String.valueOf(completeNum)), 2, BigDecimal.ROUND_HALF_UP)));
            } catch (Exception e) {
                log.info("计算title报错", e);
                titleAndContentBO.setTitle("A计划项目周报");
            }
        }
        log.info("completeNum = [{}], totalNum = [{}], totalScore = [{}]", completeNum, msgContent.size(), totalScore);
        if (StringUtils.isBlank(titleAndContentBO.getTitle())) {
            titleAndContentBO.setTitle("A计划项目周报");
        }

        return titleAndContentBO;
    }

    private LarkSheetMetaInfoBO getSheetMetaInfo(String sheetToken, String userAccessToken) {

        if (StringUtils.isBlank(sheetToken) || StringUtils.isBlank(userAccessToken)) {
            return null;
        }

        return cloudFileManager.getLarkSheetMetaInfo(sendWeeklyConfig.getSheetToken(), userAccessToken);
    }

    private LarkSheetHeaderBO getSheetHeader(String sheetToken, String userAccessToken, String range) {

        if (StringUtils.isBlank(sheetToken) || StringUtils.isBlank(userAccessToken)) {
            return null;
        }

        return cloudFileManager.getLarkSheetHeader(sendWeeklyConfig.getSheetToken(), userAccessToken, range);
    }

    private LarkSheetContentBO getSheetContent(String sheetToken, String userAccessToken, List<String> ranges) {

        if (StringUtils.isBlank(sheetToken) || StringUtils.isBlank(userAccessToken)) {
            return null;
        }

        return cloudFileManager.getLarkSheetData(sendWeeklyConfig.getSheetToken(), userAccessToken, ranges);
    }

    private LarkSheetContentBO.Link judgeIsLink(Object object) {
        try {
            if (object instanceof ArrayList) {
                Object object2 = ((ArrayList) object).get(0);
                return gson.fromJson(gson.toJson(object2), LarkSheetContentBO.Link.class);
            }
        } catch (Exception e) {
            log.info("判断是否是Link, 原因[{}]", e.toString());
            return null;
        }
        return null;
    }

    private String getPercentNumber(String text) {
        if (NumberUtils.isParsable(text)) {

            double d = NumberUtils.toDouble(text);

            if (d <= 1.0) {
                //获取格式化对象
                NumberFormat nt = NumberFormat.getPercentInstance();
                //设置百分数精确度即保留0小数
                nt.setMinimumFractionDigits(0);

                return nt.format(d);

            } else {
                return text;
            }
        } else {
            return text;
        }
    }

}
