package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cronutils.model.Cron;
import static com.cronutils.model.CronType.QUARTZ;
import com.cronutils.model.definition.CronDefinition;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.parser.CronParser;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.mioffice.ums.admin.entity.dto.IdDTO;
import com.mioffice.ums.admin.entity.dto.MessageRuleJobDTO;
import com.mioffice.ums.admin.entity.dto.MessageRuleV2JobDTO;
import com.mioffice.ums.admin.entity.info.MessageButtonActions;
import com.mioffice.ums.admin.entity.info.MessageJob;
import com.mioffice.ums.admin.enums.CommonJudgeEnum;
import com.mioffice.ums.admin.enums.MessageChannelsEnum;
import com.mioffice.ums.admin.enums.MessageJobTypeEnum;
import com.mioffice.ums.admin.manager.UmsJobManager;
import com.mioffice.ums.admin.mapper.MessageButtonActionsMapper;
import com.mioffice.ums.admin.mapper.MessageJobMapper;
import com.mioffice.ums.admin.remote.grpc.AppListGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.MessageRuleJobService;
import com.mioffice.ums.admin.utils.JsonUtils;
import com.xiaomi.info.grpc.client.annotation.RpcClientAutowired;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AddMessageRuleResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.GetMessageRuleDetailResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.MessageRuleDetail;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.OpenServerBlockingClient;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.PrecheckBotAndTemplateRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.PrecheckBotAndTemplateResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.QueryAppInfoByAppIdRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.QueryAppInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.UpdateMessageRuleResponse;
import com.xiaomi.mit.common.collect.Collects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 11 30,2022
 */
@Service
@Slf4j
@AllArgsConstructor
public class MessageRuleJobServiceImpl extends ServiceImpl<MessageJobMapper,
        MessageJob> implements MessageRuleJobService {

    private final MessageJobMapper messageJobMapper;
    private final MessageButtonActionsMapper messageButtonActionsMapper;
    private final AppListGrpcClient appListGrpcClient;
    private final UmsJobManager jobManager;

    @RpcClientAutowired("open-server")
    private OpenServerBlockingClient openServerBlockingClient;

    @Override
    public BaseResult<Long> ruleJobAdd(MessageRuleJobDTO messageRuleJob) {
        checkCommonArgument(messageRuleJob);
        //grpc去完成规则的创建
        String appId = messageRuleJob.getAppId();
        String ruleName = messageRuleJob.getRuleName();
        String callback = messageRuleJob.getCallback();
        Map<String, String> placeholder = messageRuleJob.getPlaceholder();
        String opUsername = messageRuleJob.getOpUsername();
        long appSysId = 0L;
        QueryAppInfoResponse response = openServerBlockingClient.queryAppInfoByAppId(
                QueryAppInfoByAppIdRequest.newBuilder().setAppId(appId).build());
        if (response.getCode() != ResultCode.OK.getCode()) {
            Preconditions.checkArgument(false, "appId错误");
        } else {
            appSysId = response.getAppInfo().getId();
        }
        String placeholderStr = Objects.nonNull(placeholder) ? JsonUtils.toJson(placeholder) : StringUtils.EMPTY;
        AddMessageRuleResponse addMessageRuleResponse =
                appListGrpcClient.addMessageRule(appSysId,
                        appId,
                        ruleName,
                        callback,
                        placeholderStr,
                        opUsername);
        int code = addMessageRuleResponse.getCode();
        String message = addMessageRuleResponse.getMessage();
        Long ruleId = addMessageRuleResponse.getRuleId();
        if (code == ResultCode.UNAUTHORIZED.getCode()) {
            return new BaseResult<Long>(ResultCode.FORBIDDEN).setMessage(message);
        }
        if (code != ResultCode.OK.getCode()) {
            return new BaseResult<Long>().setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setMessage(message);
        }
        //基础信息
        MessageJob messageJob = commonFill(messageRuleJob);
        messageJob.setRuleId(ruleId);
        //消息任务插入
        messageJobMapper.insert(messageJob);
        Long id = messageJob.getId();
        return startXxlJob(id, messageRuleJob.getRuleName(), messageRuleJob.getCronExpr());
    }

    @Override
    public BaseResult<Long> ruleJobUpdate(MessageRuleJobDTO messageRuleJob) {
        checkUpdateArgument(messageRuleJob);
        Long messageJobId = messageRuleJob.getId();
        MessageJob messageJob = messageJobMapper.selectById(messageJobId);
        Preconditions.checkArgument(Objects.nonNull(messageJob), "规则未找到");
        MessageJob updateMessageJob = commonFill(messageRuleJob);
        updateMessageJob.setId(messageJobId);
        messageJobMapper.updateById(updateMessageJob);
        String ruleName = messageRuleJob.getRuleName();
        String callback = messageRuleJob.getCallback();
        Map<String, String> placeholder = messageRuleJob.getPlaceholder();
        String opUsername = messageRuleJob.getOpUsername();
        String placeholderStr = Objects.nonNull(placeholder) ? JsonUtils.toJson(placeholder) : StringUtils.EMPTY;
        UpdateMessageRuleResponse updateMessageRuleResponse =
                appListGrpcClient.updateMessageRule(messageJob.getRuleId(), ruleName, callback, placeholderStr,
                        opUsername);
        int code = updateMessageRuleResponse.getCode();
        String message = updateMessageRuleResponse.getMessage();
        Long ruleId = updateMessageRuleResponse.getRuleId();
        if (code == ResultCode.UNAUTHORIZED.getCode()) {
            return new BaseResult<Long>(ResultCode.FORBIDDEN).setMessage(message);
        }
        if (code != ResultCode.OK.getCode()) {
            return new BaseResult<Long>().setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setMessage(message);
        }
        jobManager.updateMsgJob(messageJob.getJobId(), messageRuleJob.getRuleName(), messageRuleJob.getCronExpr(),
                String.valueOf(messageJob.getId()));
        return BaseResult.of(messageJobId);
    }

    @Override
    public BaseResult<MessageRuleJobDTO> ruleJobDetail(IdDTO idDTO) {
        Long id = idDTO.getId();
        Preconditions.checkArgument(Objects.nonNull(id), "ID为空");
        MessageJob messageJob = messageJobMapper.selectById(id);
        Preconditions.checkArgument(Objects.nonNull(messageJob), "任务不存在");
        GetMessageRuleDetailResponse getMessageRuleDetailResponse = appListGrpcClient.getMessageRuleDetail(null,
                messageJob.getRuleId());
        int code = getMessageRuleDetailResponse.getCode();
        String message = getMessageRuleDetailResponse.getMessage();
        if (code == ResultCode.UNAUTHORIZED.getCode()) {
            return new BaseResult<MessageRuleJobDTO>(ResultCode.FORBIDDEN).setMessage(message);
        }
        if (code != ResultCode.OK.getCode()) {
            log.error("id:{} get rule error!", id);
            return new BaseResult<MessageRuleJobDTO>().setCode(ResultCode.SERVER_INNER_ERROR.getCode())
                    .setMessage(ResultCode.SERVER_INNER_ERROR.getMessage());
        }
        MessageRuleDetail messageRuleDetail = getMessageRuleDetailResponse.getMessageRuleDetail();
        MessageRuleJobDTO dto = new MessageRuleJobDTO();
        dto.setId(messageJob.getId());
        dto.setAppSysId(messageRuleDetail.getAppSysId());
        dto.setAppId(messageRuleDetail.getAppId());
        dto.setRuleName(messageRuleDetail.getRuleName());
        dto.setCallback(messageRuleDetail.getCallback());
        String placeholderStr = messageRuleDetail.getPlaceholder();
        dto.setPlaceholder(
                StringUtils.isNotBlank(placeholderStr) ? JsonUtils.parse(placeholderStr, Map.class) :
                        Maps.newHashMap());
        dto.setCronExpr(messageJob.getCronExpr());
        dto.setJobType(messageJob.getJobType());
        dto.setLarkChannel(messageJob.getLarkChannel());
        if (CommonJudgeEnum.YES.getCode().equals(messageJob.getLarkChannel())) {
            dto.setLarkBotBizId(messageJob.getLarkBotBizId());
            dto.setLarkTemplateBizId(messageJob.getLarkTemplateBizId());
        } else {
            dto.setLarkBotBizId(StringUtils.EMPTY);
            dto.setLarkTemplateBizId(StringUtils.EMPTY);
        }
        dto.setSmsChannel(messageJob.getSmsChannel());
        if (CommonJudgeEnum.YES.getCode().equals(messageJob.getSmsChannel())) {
            dto.setSmsBotBizId(messageJob.getSmsBotBizId());
            dto.setSmsTemplateBizId(messageJob.getSmsTemplateBizId());
        } else {
            dto.setSmsBotBizId(StringUtils.EMPTY);
            dto.setSmsTemplateBizId(StringUtils.EMPTY);
        }
        dto.setMailChannel(messageJob.getMailChannel());
        if (CommonJudgeEnum.YES.getCode().equals(messageJob.getMailChannel())) {
            dto.setMailBotBizId(messageJob.getMailBotBizId());
            dto.setMailTemplateBizId(messageJob.getMailTemplateBizId());
        } else {
            dto.setMailBotBizId(StringUtils.EMPTY);
            dto.setMailTemplateBizId(StringUtils.EMPTY);
        }
        LambdaQueryWrapper<MessageButtonActions> wrappers =
                Wrappers.<MessageButtonActions>lambdaQuery().eq(MessageButtonActions::getMsgJobId, id);
        List<MessageButtonActions> actions = messageButtonActionsMapper.selectList(wrappers);
        List<MessageRuleJobDTO.ButtonAction> actionList = actions.stream().map(p -> {
            MessageRuleJobDTO.ButtonAction action = new MessageRuleJobDTO.ButtonAction();
            action.setActionName(p.getActionName().trim());
            action.setReceiveType(p.getReceivesType());
            action.setReceiveParam(p.getReceivesParam());
            action.setMsgContent(p.getMessageContent());
            action.setMsgTitle(p.getMessageTitle());
            action.setBtnText(p.getBtnText());
            action.setBtnUrl(p.getBtnUrl());
            return action;
        }).collect(Collectors.toList());
        dto.setBtnActions(actionList);
        return BaseResult.of(dto);
    }

    @Override
    public BaseResult<Object> delRuleJob(IdDTO idDTO) {
        Long id = idDTO.getId();
        Preconditions.checkArgument(Objects.nonNull(id), "ID为空");
        MessageJob messageJob = messageJobMapper.selectById(id);
        Preconditions.checkArgument(Objects.nonNull(messageJob), "任务不存在");
        jobManager.removeJob(messageJob.getJobId());
        messageJobMapper.deleteById(id);
        LambdaQueryWrapper<MessageButtonActions> wrappers =
                Wrappers.<MessageButtonActions>lambdaQuery().eq(MessageButtonActions::getMsgJobId, id);
        messageButtonActionsMapper.delete(wrappers);
        return BaseResult.of();
    }

    @Override
    public BaseResult<Object> stopRuleJob(IdDTO idDTO) {
        Long id = idDTO.getId();
        Preconditions.checkArgument(Objects.nonNull(id), "ID为空");
        MessageJob messageJob = messageJobMapper.selectById(id);
        Preconditions.checkArgument(Objects.nonNull(messageJob), "任务不存在");
        jobManager.jobStop(messageJob.getJobId());
        return BaseResult.of();
    }

    @Override
    public BaseResult<Object> startRuleJob(IdDTO idDTO) {
        Long id = idDTO.getId();
        Preconditions.checkArgument(Objects.nonNull(id), "ID为空");
        MessageJob messageJob = messageJobMapper.selectById(id);
        Preconditions.checkArgument(Objects.nonNull(messageJob), "任务不存在");
        jobManager.jobStart(messageJob.getJobId());
        return BaseResult.of();
    }

    @Override
    public BaseResult<Long> ruleJobAddV2(MessageRuleV2JobDTO messageRuleJob) {
        if (!CollectionUtils.isEmpty(messageRuleJob.getBtnActions())) {
            messageRuleJob.setBtnText("一键提醒");
        }
        checkCommonArgumentV2(messageRuleJob);
        //grpc去完成规则的创建
        String appId = messageRuleJob.getAppId();
        String ruleName = messageRuleJob.getRuleName();
        String callback = messageRuleJob.getCallback();
        QueryAppInfoResponse response = openServerBlockingClient.queryAppInfoByAppId(
                QueryAppInfoByAppIdRequest.newBuilder().setAppId(appId).build());
        long appSysId = 0L;
        if (response.getCode() != ResultCode.OK.getCode()) {
            Preconditions.checkArgument(false, "appId错误");
        } else {
            appSysId = response.getAppInfo().getId();
        }
        //插入规则信息
        AddMessageRuleResponse addMessageRuleResponse =
                appListGrpcClient.addMessageRule(appSysId,
                        appId,
                        ruleName,
                        callback,
                        Objects.nonNull(messageRuleJob.getPlaceholder()) &&
                                MapUtils.isNotEmpty(messageRuleJob.getPlaceholder()) ?
                                JsonUtils.toJson(messageRuleJob.getPlaceholder()) : JsonUtils.toJson(Maps.newHashMap()),
                        StringUtils.EMPTY);
        int code = addMessageRuleResponse.getCode();
        String message = addMessageRuleResponse.getMessage();
        Long ruleId = addMessageRuleResponse.getRuleId();
        if (code == ResultCode.UNAUTHORIZED.getCode()) {
            return new BaseResult<Long>(ResultCode.FORBIDDEN).setMessage(message);
        }
        if (code != ResultCode.OK.getCode()) {
            return new BaseResult<Long>().setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setMessage(message);
        }
        //基础信息
        MessageJob messageJob = commonFillV2(messageRuleJob);
        messageJob.setRuleId(ruleId);
        //消息任务插入
        messageJobMapper.insert(messageJob);
        Long id = messageJob.getId();
        if (!CollectionUtils.isEmpty(messageRuleJob.getBtnActions())) {
            List<MessageButtonActions> btnActions = commonFillActions(messageRuleJob, id);
            messageButtonActionsMapper.batchInsert(btnActions);
        }
        return startXxlJob(id, messageRuleJob.getRuleName(), messageRuleJob.getCronExpr());
    }

    @Override
    public BaseResult<Long> ruleJobUpdateV2(MessageRuleV2JobDTO messageRuleJob) {
        checkUpdateArgumentV2(messageRuleJob);
        Long messageJobId = messageRuleJob.getId();
        MessageJob messageJob = messageJobMapper.selectById(messageJobId);
        Preconditions.checkArgument(messageJob.getSubsequentJobType() == messageRuleJob.getSubsequentJobType(),
                "后续推送人员类型指定后不可更改");
        if(messageJob.getSubsequentJobType()>0){
            Preconditions.checkArgument(messageJob.getJobType() == messageRuleJob.getJobType(),
                    "指定了后续推送人员类型的规则的任务类型不可更改");
        }
        Preconditions.checkArgument(Objects.nonNull(messageJob), "规则未找到");
        MessageJob updateMessageJob = commonFillV2(messageRuleJob);
        updateMessageJob.setId(messageJobId);
        messageJobMapper.updateById(updateMessageJob);
        String ruleName = messageRuleJob.getRuleName();
        String callback = messageRuleJob.getCallback();

        UpdateMessageRuleResponse updateMessageRuleResponse =
                appListGrpcClient.updateMessageRule(messageJob.getRuleId(),
                        ruleName,
                        callback,
                        StringUtils.EMPTY,
                        StringUtils.EMPTY);
        int code = updateMessageRuleResponse.getCode();
        String message = updateMessageRuleResponse.getMessage();
        Long ruleId = updateMessageRuleResponse.getRuleId();
        if (code == ResultCode.UNAUTHORIZED.getCode()) {
            return new BaseResult<Long>(ResultCode.FORBIDDEN).setMessage(message);
        }
        if (code != ResultCode.OK.getCode()) {
            return new BaseResult<Long>().setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setMessage(message);
        }
        LambdaQueryWrapper<MessageButtonActions> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MessageButtonActions::getMsgJobId, messageJobId);
        messageButtonActionsMapper.delete(wrapper);
        if (!CollectionUtils.isEmpty(messageRuleJob.getBtnActions())) {
            List<MessageButtonActions> btnActions = commonFillActions(messageRuleJob, messageJobId);
            messageButtonActionsMapper.batchInsert(btnActions);
        }
        jobManager.updateMsgJob(messageJob.getJobId(),
                messageRuleJob.getRuleName(),
                messageRuleJob.getCronExpr(),
                String.valueOf(messageJob.getId()));
        return BaseResult.of(messageJobId);
    }

    private BaseResult<Long> startXxlJob(Long id,
                                         String ruleName2,
                                         String cronExpr) {
        ImmutablePair<Boolean, String> pair =
                jobManager.addMsgJobAndStart(ruleName2,
                        cronExpr,
                        String.valueOf(id));
        if (pair.getLeft()) {
            MessageJob job = new MessageJob();
            job.setJobId(Long.valueOf(pair.getRight()));
            messageJobMapper.update(job,
                    Wrappers.<MessageJob>lambdaUpdate()
                            .eq(MessageJob::getId, id));
            return BaseResult.of(id);
        }
        throw new RuntimeException(pair.getRight());
    }

    private void checkUpdateArgument(MessageRuleJobDTO messageRuleJob) {
        Preconditions.checkNotNull(messageRuleJob.getId(), "更新ID为空");
        checkCommonArgument(messageRuleJob);
    }

    private void checkUpdateArgumentV2(MessageRuleV2JobDTO jobDTO) {
        Preconditions.checkNotNull(jobDTO.getId(), "更新ID为空");
        checkCommonArgumentV2(jobDTO);
    }

    private void checkCommonArgument(MessageRuleJobDTO messageRuleJob) {
        //校验定时规则
        Preconditions.checkArgument(StringUtils.isNotBlank(messageRuleJob.getCronExpr()), "cron表达式不能为空");
        //校验回调地址
        Preconditions.checkArgument(StringUtils.isNotBlank(messageRuleJob.getCallback()), "回调地址不能为空");
        //校验规则名称
        Preconditions.checkArgument(StringUtils.isNotBlank(messageRuleJob.getRuleName()), "规则名称不能为空");
        //appId校验
        Preconditions.checkArgument(StringUtils.isNotBlank(messageRuleJob.getAppId()), "appId不能为空");
        Integer larkChannel = messageRuleJob.getLarkChannel();
        Integer mailChannel = messageRuleJob.getMailChannel();
        Integer smsChannel = messageRuleJob.getSmsChannel();
        Preconditions.checkArgument(
                validateJudge(larkChannel) && validateJudge(mailChannel) && validateJudge(smsChannel), "非法渠道值");
        Preconditions.checkArgument(messageRuleJob.getRuleName().length() <= 250, "规则名称超长");
        Preconditions.checkArgument(CommonJudgeEnum.YES.getCode().equals(larkChannel) ||
                        CommonJudgeEnum.YES.getCode().equals(mailChannel) || CommonJudgeEnum.YES.getCode().equals(smsChannel),
                "至少开启一个渠道");

        try {
            java.net.URL url = new java.net.URL(messageRuleJob.getCallback());
        } catch (Exception e) {
            Preconditions.checkArgument(false, "非法url");
        }
        try {
            CronDefinition cronDefinition = CronDefinitionBuilder.instanceDefinitionFor(QUARTZ);
            CronParser parser = new CronParser(cronDefinition);
            Cron quartzCron = parser.parse(messageRuleJob.getCronExpr());
            quartzCron.validate();
        } catch (Exception e) {
            Preconditions.checkArgument(false, e.getMessage());
        }
        //校验飞书渠道
        if (CommonJudgeEnum.YES.getCode().equals(larkChannel)) {
            Preconditions.checkArgument(StringUtils.isNotBlank(messageRuleJob.getLarkBotBizId()) &&
                    StringUtils.isNotBlank(messageRuleJob.getLarkTemplateBizId()), "飞书渠道缺少必要信息");
            checkBotAndTemplate(messageRuleJob.getAppId(), MessageChannelsEnum.LARK.getChannel(),
                    messageRuleJob.getLarkBotBizId(), messageRuleJob.getLarkTemplateBizId());

        }
        //校验短信渠道
        if (CommonJudgeEnum.YES.getCode().equals(mailChannel)) {
            Preconditions.checkArgument(StringUtils.isNotBlank(messageRuleJob.getMailBotBizId()) &&
                    StringUtils.isNotBlank(messageRuleJob.getMailTemplateBizId()), "邮件渠道缺少必要信息");
            checkBotAndTemplate(messageRuleJob.getAppId(), MessageChannelsEnum.MAIL.getChannel(),
                    messageRuleJob.getMailBotBizId(), messageRuleJob.getMailTemplateBizId());
        }
        //校验短信渠道
        if (CommonJudgeEnum.YES.getCode().equals(smsChannel)) {
            Preconditions.checkArgument(StringUtils.isNotBlank(messageRuleJob.getSmsBotBizId()) &&
                    StringUtils.isNotBlank(messageRuleJob.getSmsTemplateBizId()), "短信渠道缺少必要信息");
            checkBotAndTemplate(messageRuleJob.getAppId(), MessageChannelsEnum.SMS.getChannel(),
                    messageRuleJob.getSmsBotBizId(), messageRuleJob.getSmsTemplateBizId());
        }

    }

    private void checkCommonArgumentV2(MessageRuleV2JobDTO messageRuleV2Job) {
        //校验定时规则
        Preconditions.checkArgument(StringUtils.isNotBlank(messageRuleV2Job.getCronExpr()), "cron表达式不能为空");
        //校验回调地址
        Preconditions.checkArgument(StringUtils.isNotBlank(messageRuleV2Job.getCallback()), "回调地址不能为空");
        //校验规则名称
        Preconditions.checkArgument(StringUtils.isNotBlank(messageRuleV2Job.getRuleName()), "规则名称不能为空");
        //appId校验
        Preconditions.checkArgument(StringUtils.isNotBlank(messageRuleV2Job.getAppId()), "appId不能为空");
        //规则名长度校验
        Preconditions.checkArgument(messageRuleV2Job.getRuleName().length() <= 250, "规则名称超长");
        Preconditions.checkArgument(StringUtils.isBlank(messageRuleV2Job.getBtnText()) ||
                messageRuleV2Job.getBtnText().trim().length() < 64, "按钮文本过长");
        Integer jobType = messageRuleV2Job.getJobType();

        Preconditions.checkArgument(validate(MessageJobTypeEnum.class, jobType, MessageJobTypeEnum::getCode),
                "无效的任务类型");

        if (MessageJobTypeEnum.URGE_CUSTOM.getCode().equals(jobType) &&
                messageRuleV2Job.getSubsequentJobType() != 0) {
            Preconditions.checkArgument(false, "催办(自定义催办人)任务不支持指定后续推送人员类型");
        }

        try {
            java.net.URL url = new java.net.URL(messageRuleV2Job.getCallback());
        } catch (Exception e) {
            Preconditions.checkArgument(false, "回调接口地址格式不正确");
        }

        try {
            CronDefinition cronDefinition = CronDefinitionBuilder.instanceDefinitionFor(QUARTZ);
            CronParser parser = new CronParser(cronDefinition);
            Cron quartzCron = parser.parse(messageRuleV2Job.getCronExpr());
            quartzCron.validate();
        } catch (Exception e) {
            Preconditions.checkArgument(false, "cron表达式格式不正确:" + e.getMessage());
        }

        if (MessageJobTypeEnum.URGE_NORMAL.getCode().equals(jobType)) {
            Preconditions.checkArgument(StringUtils.isNotBlank(messageRuleV2Job.getLarkTemplateBizId()),
                    "普通定时规则需要提供模板");
        } else {
            List<MessageRuleJobDTO.ButtonAction> btnActions = messageRuleV2Job.getBtnActions();
            Preconditions.checkArgument(CollectionUtils.isNotEmpty(btnActions), "催办规则需提供【被催办人消息配置】");

            btnActions.forEach(p -> {
                Preconditions.checkArgument(StringUtils.isNotBlank(p.getActionName()), "被催办人消息配置名称不能为空");
                Preconditions.checkArgument(p.getActionName().trim().length() < 64, "被催办人消息配置名称过长");
                Preconditions.checkArgument(
                        StringUtils.isBlank(p.getBtnText()) || p.getBtnText().trim().length() < 64,
                        "按钮文本过长");
                if (MessageJobTypeEnum.URGE_CUSTOM.getCode().equals(jobType)) {
                    Preconditions.checkArgument(StringUtils.isNotBlank(p.getReceiveParam()),
                            "被催办人名单回调地址不能为空");
                }
                Preconditions.checkArgument(StringUtils.isNotBlank(p.getMsgTitle()), "被催办人消息标题不能为空");
                Preconditions.checkArgument(StringUtils.isNotBlank(p.getMsgContent()), "被催办人消息内容不能为空");
            });
        }
    }

    private void checkBotAndTemplate(String appId, Integer channel, String botBizId, String templateBizId) {
        PrecheckBotAndTemplateRequest.Builder builder = PrecheckBotAndTemplateRequest.newBuilder();
        builder.setAppId(appId).setBotBizId(botBizId).setTemplateBizId(templateBizId).setChannel(channel);
        PrecheckBotAndTemplateResponse precheckBotAndTemplateResponse =
                openServerBlockingClient.precheckBotAndTemplate(builder.build());
        int code = precheckBotAndTemplateResponse.getCode();
        String message = precheckBotAndTemplateResponse.getDesc();
        Preconditions.checkArgument(ResultCode.OK.getCode() == code, message);
    }

    private MessageJob commonFill(MessageRuleJobDTO messageRuleJob) {
        Long nowTimestamp = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        MessageJob job = new MessageJob();
        //规则ID
        job.setCronExpr(messageRuleJob.getCronExpr());

        //飞书信息
        job.setLarkChannel(messageRuleJob.getLarkChannel());
        job.setLarkBotBizId(messageRuleJob.getLarkBotBizId());
        job.setLarkTemplateBizId(messageRuleJob.getLarkTemplateBizId());

        //邮件
        job.setMailChannel(messageRuleJob.getMailChannel());
        job.setMailBotBizId(messageRuleJob.getMailBotBizId());
        job.setMailTemplateBizId(messageRuleJob.getMailTemplateBizId());
        //短信
        job.setSmsChannel(messageRuleJob.getSmsChannel());
        job.setSmsBotBizId(messageRuleJob.getSmsBotBizId());
        job.setSmsTemplateBizId(messageRuleJob.getSmsTemplateBizId());

        //创建信息
        job.setCreateUsername(messageRuleJob.getOpUsername());
        job.setJobParam(messageRuleJob.getJobParam());
        job.setCreateTime(nowTimestamp);
        //更新信息
        job.setUpdateUsername(messageRuleJob.getOpUsername());
        job.setUpdateTime(nowTimestamp);
        return job;
    }

    private MessageJob commonFillV2(MessageRuleV2JobDTO dto) {
        Long nowTimestamp = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        MessageJob job = new MessageJob();
        //cron表达式
        job.setCronExpr(dto.getCronExpr());

        //飞书信息
        job.setLarkChannel(CommonJudgeEnum.YES.getCode());
        job.setLarkBotBizId(dto.getLarkBotBizId());
        job.setLarkTemplateBizId(dto.getLarkTemplateBizId());
        job.setLarkTitleTmp(dto.getMsgTitle());
        job.setLarkTitleTmpEn(Optional.ofNullable(dto.getMsgTitleEn()).filter(StringUtils::isNotEmpty)
                .orElse(dto.getMsgTitle()));
        job.setLarkContentTmp(dto.getMsgContent());
        job.setLarkContentTmpEn(Optional.ofNullable(dto.getMsgContentEn()).filter(StringUtils::isNotEmpty)
                .orElse(dto.getMsgContent()));
        job.setLarkBtnText(dto.getBtnText());
        job.setLarkBtnTextEn(Optional.ofNullable(dto.getBtnTextEn()).filter(StringUtils::isNotEmpty)
                .orElse(dto.getBtnText()));
        job.setLarkBtnUrl(MessageJobTypeEnum.URGE_NORMAL.getCode().equals(dto.getJobType()) ?
                dto.getBtnUrl() : StringUtils.EMPTY
        );

        //邮件
        job.setMailChannel(CommonJudgeEnum.NO.getCode());
        job.setMailBotBizId(StringUtils.EMPTY);
        job.setMailTemplateBizId(StringUtils.EMPTY);
        //短信
        job.setSmsChannel(CommonJudgeEnum.NO.getCode());
        job.setSmsBotBizId(StringUtils.EMPTY);
        job.setSmsTemplateBizId(StringUtils.EMPTY);

        //创建信息
        job.setCreateUsername(StringUtils.EMPTY);
        job.setJobParam(StringUtils.EMPTY);
        job.setCreateTime(nowTimestamp);
        //更新信息
        job.setUpdateUsername(StringUtils.EMPTY);
        job.setUpdateTime(nowTimestamp);

        job.setJobType(dto.getJobType());

        job.setSubsequentJobType(dto.getSubsequentJobType());
        return job;
    }

    private List<MessageButtonActions> commonFillActions(MessageRuleV2JobDTO messageRuleJob, Long msgJobId) {
        if (Objects.isNull(messageRuleJob) || CollectionUtils.isEmpty(messageRuleJob.getBtnActions())) {
            return new ArrayList<>();
        }
        return messageRuleJob.getBtnActions()
                .stream()
                .map(p -> {
                    MessageButtonActions actions = new MessageButtonActions();
                    actions.setMsgJobId(msgJobId);
                    actions.setActionName(p.getActionName());
                    actions.setReceivesType(p.getReceiveType());
                    actions.setReceivesParam(p.getReceiveParam());
                    actions.setMessageContent(p.getMsgContent());
                    actions.setMessageContentEn(Optional.ofNullable(p.getMsgContentEn()).filter(StringUtils::isNotEmpty)
                            .orElse(p.getMsgContent()));
                    actions.setMessageTitle(p.getMsgTitle());
                    actions.setMessageTitleEn(Optional.ofNullable(p.getMsgTitleEn()).filter(StringUtils::isNotEmpty)
                            .orElse(p.getMsgTitle()));
                    actions.setBtnText(p.getBtnText());
                    actions.setBtnTextEn(Optional.ofNullable(p.getBtnTextEn()).filter(StringUtils::isNotEmpty)
                            .orElse(p.getBtnText()));
                    actions.setBtnUrl(StringUtils.isBlank(p.getBtnUrl()) ? StringUtils.EMPTY : p.getBtnUrl().trim());
                    actions.setExpandInfo(p.getExpandInfo());
                    return actions;
                }).collect(Collectors.toList());
    }

    private static boolean validateJudge(Integer code) {
        return Arrays.stream(CommonJudgeEnum.values()).map(CommonJudgeEnum::getCode).anyMatch(p -> p.equals(code));
    }

    private static <T, E extends Enum<E>> boolean validate(Class<E> range, T value, Function<E, T> getValFunc) {
        return Collects.find(range.getEnumConstants(), e -> getValFunc.apply(e).equals(value)).isPresent();
    }

}
