package com.mioffice.ums.admin.service.impl;

import cn.hutool.core.date.DateUtil;
import static com.alibaba.excel.EasyExcelFactory.write;
import com.mioffice.ums.admin.entity.bo.AppUseLogBO;
import com.mioffice.ums.admin.manager.MiCloudFdsManager;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.AppLogService;
import com.mioffice.ums.admin.utils.AesUtils;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageRecord;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageResultSingleForCustomResponse;
import com.xiaomi.infra.galaxy.fds.model.FDSObjectMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/23 8:52 上午
 * version: 1.0.0
 */
@Slf4j
@Service
public class AppLogServiceImpl implements AppLogService {

    private final MiCloudFdsManager miCloudFdsManager;

    private final MessageGrpcClient messageGrpcClient;

    public AppLogServiceImpl(MiCloudFdsManager miCloudFdsManager, MessageGrpcClient messageGrpcClient) {
        this.miCloudFdsManager = miCloudFdsManager;
        this.messageGrpcClient = messageGrpcClient;
    }

    @Override
    public BaseResult<Map<String, Object>> download(String extraId) {
        MessageResultSingleForCustomResponse messagePageByExtraId = messageGrpcClient.getMessagePageByExtraId(extraId, 1L, 500);
        List<AppUseLogBO> appUseLogBOList = new ArrayList<>();
        long totalPage = messagePageByExtraId.getMessageResultPage().getPages();
        if (totalPage != 0) {
            for (long page = 1; page < totalPage + 1; page++) {
                MessageResultSingleForCustomResponse messagePageByExtraIdTmp = messageGrpcClient.getMessagePageByExtraId(extraId, page, 500);
                List<MessageRecord> recordsList = messagePageByExtraIdTmp.getMessageResultPage().getRecordsList();
                recordsList.forEach(
                        messageRecord -> {
                            AppUseLogBO appUseLogBO = getAppUseLogBO(messageRecord);
                            appUseLogBOList.add(appUseLogBO);
                        }
                );
            }

        }
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        List<AppUseLogBO> appUseLogExportBOList = new ArrayList<>();
        appUseLogBOList.forEach(
                item -> {
                    AppUseLogBO appUseLogBO = new AppUseLogBO();
                    BeanUtils.copyProperties(item, appUseLogBO);
                    appUseLogExportBOList.add(appUseLogBO);
                }
        );
        write(os, AppUseLogBO.class).sheet(extraId).doWrite(appUseLogExportBOList);
        String folderName = "umsAdmin/".concat(DateUtil.today()).concat("/").concat(UUID.randomUUID().toString().replace("-", "").concat("/"));
        FDSObjectMetadata fdsObjectMetadata = new FDSObjectMetadata();
        fdsObjectMetadata.setContentType("application/vnd.ms-excel");
        byte[] content = os.toByteArray();
        InputStream is = new ByteArrayInputStream(content);
        String url = miCloudFdsManager.uploadFile(folderName + extraId + ".xlsx", is, fdsObjectMetadata);
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("url", miCloudFdsManager.getHostUrl().concat(url));
        return BaseResult.of(returnMap);
    }

    private AppUseLogBO getAppUseLogBO(MessageRecord messageRecord) {
        AppUseLogBO appUseLogBO = new AppUseLogBO();
        appUseLogBO.setUsername(messageRecord.getUsername());
        appUseLogBO.setUserEmail(messageRecord.getEmail());
        appUseLogBO.setChatId(messageRecord.getChatId());
        try {
            String phone = AesUtils.decrypt(messageRecord.getPhone());
            if (StringUtils.isNotBlank(phone)) {
                appUseLogBO.setPhone(phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));
            } else {
                appUseLogBO.setPhone("");
            }
        } catch (Exception e) {
            log.warn("导出extraId = [{}] 的记录时手机号解密错误", messageRecord.getExtraId(), e);
        }
        setPushResult(messageRecord, appUseLogBO);
        setChannel(messageRecord, appUseLogBO);
        boolean isSuccess = appUseLogBO.getIsSuccess();
        if (!isSuccess) {
            appUseLogBO.setErrorLog(messageRecord.getErrorLog());
        }
        return appUseLogBO;
    }

    private void setPushResult(MessageRecord messageRecord, AppUseLogBO appUseLogBO) {
        // 1-发送中，2-发送成功，3-发送失败，4-消息中断
        switch (messageRecord.getMessageStatus()) {
            case 1:
                appUseLogBO.setIsSuccess(true);
                appUseLogBO.setPushResDesc("发送中");
                break;
            case 2:
                appUseLogBO.setIsSuccess(true);
                appUseLogBO.setPushResDesc("成功");
                break;
            case 3:
                appUseLogBO.setIsSuccess(false);
                appUseLogBO.setPushResDesc("失败");
                break;
            case 4:
                appUseLogBO.setIsSuccess(false);
                appUseLogBO.setPushResDesc("中断");
                break;
            default:
                break;
        }
    }

    private void setChannel(MessageRecord messageRecord, AppUseLogBO appUseLogBO) {
        // 1-飞书，2-邮件，3-短信
        switch (messageRecord.getChannel()) {
            case 1:
                appUseLogBO.setChannel("飞书");
                break;
            case 2:
                appUseLogBO.setChannel("邮件");
                break;
            case 3:
                appUseLogBO.setChannel("短信");
                break;
            default:
                break;
        }
    }
}
