package com.mioffice.ums.admin.service.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import static com.mioffice.ums.admin.constants.RuleConventionConstants.SUB_LIST;
import static com.mioffice.ums.admin.constants.RuleConventionConstants.SUB_LIST_JSON;
import com.mioffice.ums.admin.entity.dto.SendUsersDTO;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.manager.ReportLineManager;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.service.EmployeeService;
import com.mioffice.ums.admin.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * @ClassName EmployeeServiceImpl
 * @Description EmployeeServiceImpl
 * <AUTHOR>
 * @Date 2023/6/8 17:52
 **/
@Slf4j
@Service
public class EmployeeServiceImpl extends ServiceImpl<EmployeeInfoMapper, EmployeeInfo> implements EmployeeService {

    @NacosValue(value = "${ums.admin.executives:}", autoRefreshed = true)
    private String executives;

    @Resource
    private EmployeeInfoMapper employeeInfoMapper;

    @Resource
    private ReportLineManager reportLineManager;

    @Override
    public Map<String, EmployeeInfo> getMapByOprIdList(List<String> oprIdList) {
        return oprIdList.isEmpty()? Maps.newHashMap():employeeInfoMapper.selectList(
                        Wrappers.<EmployeeInfo>lambdaQuery()
                                .in(EmployeeInfo::getUsername, oprIdList)
                                .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
                )
                .stream()
                .collect(Collectors.toMap(EmployeeInfo::getUsername, Function.identity(), (x, y) -> y));
    }

    @Override
    public List<SendUsersDTO> convert2LeaderList(List<SendUsersDTO> list) {
        // 高管名单
        List<String> executiveUsernames = Splitter.on(",").splitToList(executives);
        // 过滤掉高管或者oprid为空的数据
        list = list.stream()
                .filter(p -> StringUtils.isNotBlank(p.getOprid()) &&
                        !executiveUsernames.contains(p.getOprid()))
                .collect(Collectors.toList());
        list.forEach(p -> {
            String directLeaderOprId =
                    reportLineManager.getDirectLeader(p.getOprid(),
                            StringUtils.EMPTY).getSupUserName();
            p.setLeader(StringUtils.isNotBlank(directLeaderOprId) && !executiveUsernames.contains(directLeaderOprId) ?
                    directLeaderOprId :
                    StringUtils.EMPTY);
        });
        Map<String, List<SendUsersDTO>> leaderMap = list.stream()
                .filter(p -> StringUtils.isNotBlank(p.getLeader()))
                .collect(Collectors.groupingBy(SendUsersDTO::getLeader));
        return leaderMap
                .entrySet()
                .stream()
                .map(leaderEntry -> {
                    EmployeeInfo leaderEmployeeInfo =
                            employeeInfoMapper.selectOne(
                                    Wrappers.<EmployeeInfo>lambdaQuery().eq(EmployeeInfo::getUsername,
                                                    leaderEntry.getKey())
                                            .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode()));
                    if (Objects.isNull(leaderEmployeeInfo)) {
                        return null;
                    }
                    SendUsersDTO leader = new SendUsersDTO();
                    leader.setOprid(leaderEntry.getKey());
                    leader.setEmail(leaderEmployeeInfo.getEmail());
                    leader.setPhone(leaderEmployeeInfo.getPhone());
                    leader.setName(leaderEmployeeInfo.getName());

                    // 下属列表
                    List<SendUsersDTO> subList = leaderEntry.getValue();
                    // 下属内网账号列表
                    List<String> subOprIdList =
                            subList.stream().map(SendUsersDTO::getOprid)
                                    .collect(Collectors.toList());
                    // 下属人员信息map(内网账号->人员信息)
                    Map<String, EmployeeInfo> subEmployeeInfoMap = employeeInfoMapper.selectList(
                                    Wrappers.<EmployeeInfo>lambdaQuery().in(EmployeeInfo::getUsername, subOprIdList)
                                            .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode()))
                            .stream()
                            .collect(Collectors.toMap(EmployeeInfo::getUsername, Function.identity()));

                    // 剔除已离职的
                    subList = subList
                            .stream()
                            .filter(sub -> subEmployeeInfoMap.containsKey(sub.getOprid()) &&
                                    Objects.nonNull(subEmployeeInfoMap.get(sub.getOprid())))
                            .collect(Collectors.toList());

                    subList.forEach(sub -> {
                        EmployeeInfo subEmployeeInfo = subEmployeeInfoMap.get(sub.getOprid());
                        sub.setEmail(subEmployeeInfo.getEmail());
                        sub.setPhone(subEmployeeInfo.getPhone());
                        sub.setName(subEmployeeInfo.getName());
                    });

                    // 处理直属上级的param
                    Map<String, Object> leaderParam = new HashMap<>();
                    // param:【subList(下属人员列表)、subListJson(下属人员列表json)】是系统保留的参数名称，不允许用户定义
                    leaderParam.put(SUB_LIST, subList);

                    leaderParam.put(SUB_LIST_JSON, StringEscapeUtils.escapeJson(JsonUtils.toJson(subList)));

                    // 聚合下属人员所有类型的param，相同的key会被处理成不同下属的value拼接后的值
                    subList.forEach(sub -> {
                        String subOprId = sub.getOprid();
                        String subName = subEmployeeInfoMap.get(subOprId).getName();

                        if (Objects.nonNull(sub.getParam())) {
                            Map<String, Object> subParam = sub.getParam();
                            if (subParam.size() > 0) {
                                subParam.forEach((key, value) -> {
                                    String withEmployeeInfoValue =
                                            subName + "(" + subOprId + "):" + value.toString();
                                    if (leaderParam.containsKey(key)) {
                                        String subParamSummaryValue =
                                                (String) leaderParam.get(key);
                                        subParamSummaryValue = subParamSummaryValue.concat("," + withEmployeeInfoValue);
                                        leaderParam.put(key, subParamSummaryValue);
                                    } else {
                                        leaderParam.put(key, withEmployeeInfoValue);
                                    }
                                });
                            }
                        }
                    });

                    leader.setParam(leaderParam);

                    return leader;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
