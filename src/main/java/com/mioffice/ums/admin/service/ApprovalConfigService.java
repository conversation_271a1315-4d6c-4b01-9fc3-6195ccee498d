package com.mioffice.ums.admin.service;

import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.ApprovalConfigDTO;
import com.mioffice.ums.admin.entity.dto.TaskApprovalNodeQueryDTO;
import com.mioffice.ums.admin.entity.vo.ApprovalTimeLine;
import com.mioffice.ums.admin.entity.vo.TaskApprovalNodeVO;
import com.mioffice.ums.admin.result.BaseResult;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 审核配置
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/1 4:24 下午
 */
public interface ApprovalConfigService {

    /**
     * 获取审核配置
     *
     * @return BaseResult<ApprovalConfigDTO>
     */
    BaseResult<ApprovalConfigDTO> getApprovalConfig();

    /**
     * 更新审核配置
     *
     * @param approvalConfigDTO dto
     * @param username 当前登录人
     * @return BaseResult<ApprovalConfigDTO>
     */
    BaseResult<Object> updateApprovalConfig(ApprovalConfigDTO approvalConfigDTO, String username);

    /**
     * 获取特殊审批阈值
     *
     * @param channel 发布渠道
     * @return BaseResult<Map < String, Long>
     */
    BaseResult<Map<String, Object>> getApprovalInfo(Byte channel);

    /**
     * 获取时间轴
     *
     * @param taskId 任务ID
     * @return BaseResult<ApprovalTimeLine>
     */
    BaseResult<List<ApprovalTimeLine>> getApprovalTimeLine(Long taskId, UserBO userBO);

    BaseResult<List<ApprovalTimeLine>> getBpmApprovalTimeLine(Long taskId, UserBO userBO);

    /**
     * 获取时间轴
     * @param approvalNodeQueryDTO 查询DTO
     * @param createUserName 账号
     * @return BaseResult<ApprovalTimeLine>
     */
    BaseResult<TaskApprovalNodeVO> getApproval(TaskApprovalNodeQueryDTO approvalNodeQueryDTO, String createUserName);

    /**
     * 获取BPM流程的时间轴
     * @param approvalNodeQueryDTO
     * @param createUserName
     * @return
     */
    BaseResult<TaskApprovalNodeVO> getBpmApproval(TaskApprovalNodeQueryDTO approvalNodeQueryDTO, String createUserName);

    /**
     * 根据流程定义获取审批人列表
     * @param metaProcess 流程定义
     * @param createUserName 创建人
     * @return
     */
    List<String> getCustomApprover(String metaProcess, String createUserName);

}
