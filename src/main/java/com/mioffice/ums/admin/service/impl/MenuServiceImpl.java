package com.mioffice.ums.admin.service.impl;

import com.mioffice.ums.admin.entity.vo.MenuVO;
import com.mioffice.ums.admin.enums.MenuEnum;
import com.mioffice.ums.admin.manager.UserRoleManager;
import com.mioffice.ums.admin.service.MenuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 菜单
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/8 6:09 下午
 */
@Slf4j
@Service
public class MenuServiceImpl implements MenuService {

    private final UserRoleManager userRoleManager;

    public MenuServiceImpl(UserRoleManager userRoleManager) {
        this.userRoleManager = userRoleManager;
    }

    @Override
    public List<MenuVO> getMenuListByLoginUser(String username) {
        if (userRoleManager.isSuperAdmin(username)) {
            return getMenuVOList(MenuEnum.getSuperAdminMenu());
        } else if (userRoleManager.isSystemAdmin(username)) {
            return getMenuVOList(MenuEnum.getSystemAdminMenu());
        } else if (userRoleManager.isBotManager(username)) {
            return getMenuVOList(MenuEnum.getBotManagerMenu());
        } else if (userRoleManager.isOperator(username)) {
            return getMenuVOList(MenuEnum.getOperatorMenu());
        } else {
            return getMenuVOList(MenuEnum.getCommonMenu());
        }
    }

    private List<MenuVO> getMenuVOList(List<MenuEnum> menuEnums) {
        List<MenuVO> menuVOList = new ArrayList<>();
        menuEnums.forEach(
                menuEnum -> {
                    MenuVO menuVO = new MenuVO();
                    menuVO.setName(menuEnum.getName());
                    menuVO.setPath(menuEnum.getPath());
                    menuVOList.add(menuVO);
                }
        );
        return menuVOList;
    }
}
