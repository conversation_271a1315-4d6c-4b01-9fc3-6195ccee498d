package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.bo.UsernameAndNameBO;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.vo.AppSystemListVO;
import com.mioffice.ums.admin.enums.AppApprovalRoleEnum;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.manager.UserRoleManager;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.remote.grpc.AppListGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.AppSystemListService;
import com.mioffice.ums.admin.utils.MapperUtil;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppApplyUserSearchQueryInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppListInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppListQueryInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppManagerSearchQueryInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppStartOrStopQueryInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppUseApplyQueryInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/18 9:48 上午
 * version: 1.0.0
 */
@Slf4j
@Service
public class AppSystemListServiceImpl implements AppSystemListService {

    private final AppListGrpcClient appListGrpcClient;

    private final UserRoleManager userRoleManager;

    private final EmployeeInfoMapper employeeInfoMapper;

    public AppSystemListServiceImpl(AppListGrpcClient appListGrpcClient, UserRoleManager userRoleManager, EmployeeInfoMapper employeeInfoMapper) {
        this.appListGrpcClient = appListGrpcClient;
        this.userRoleManager = userRoleManager;
        this.employeeInfoMapper = employeeInfoMapper;
    }

    @Override
    public BaseResult<AppSystemListVO> getAppPage(UserBO userBO, Long page, Long size, String appName, String managerUsernameList, String applyUsername, String channel, String appSysStatus, String beginDate, String endDate) {
        String username = userBO.getUsername();
        AppListQueryInfoResponse appListQueryInfoResponse;
        try {
            if (userRoleManager.isSuperAdmin(username)) {
                appListQueryInfoResponse = appListGrpcClient.getAppPage("", "", page, size, appName, managerUsernameList, applyUsername, channel, appSysStatus, beginDate, endDate);
            } else {
                appListQueryInfoResponse = appListGrpcClient.getAppPage("", username, page, size, appName, managerUsernameList, applyUsername, channel, appSysStatus, beginDate, endDate);
            }
        } catch (Exception e) {
            log.error("[{}]获取系统列表时发生了异常, 异常信息为[{}]", username, e.getMessage());
            return new BaseResult<AppSystemListVO>().setCode(500).setMessage("获取系统列表时发生了异常");
        }
        int code = appListQueryInfoResponse.getCode();
        String desc = appListQueryInfoResponse.getDesc();
        if (code != 200) {
            AppSystemListVO appSystemListVO = AppSystemListVO.newEmpty(page, size);
            return new BaseResult<AppSystemListVO>().setCode(code).setMessage(desc).setData(appSystemListVO);
        }
        AppListInfo appListInfo = appListQueryInfoResponse.getAppListInfo();
        AppSystemListVO appSystemListVO = MapperUtil.INSTANCE.mapToAppSystemListVO(appListInfo);
        List<AppSystemListVO.Record> recordsList = appSystemListVO.getRecordsList();
        recordsList.forEach(
                record -> {
                    List<Byte> roleTypeList = new ArrayList<>();
                    if (record.getCreateUser().getUsername().equals(username)) {
                        roleTypeList.add(AppApprovalRoleEnum.CREATE_USER.getCode());
                    }
                    if (userRoleManager.isSystemAdmin(username) || userRoleManager.isSuperAdmin(username)) {
                        roleTypeList.add(AppApprovalRoleEnum.MANAGER.getCode());
                    }
                    record.setRoleTypeList(roleTypeList);
                }
        );
        return BaseResult.of(appSystemListVO);
    }

    @Override
    public BaseResult<Object> appStart(UserBO userBO, Long id) {
        AppStartOrStopQueryInfoResponse response;
        String username = userBO.getUsername();
        String name = userBO.getName();
        List<String> roleList = userBO.getRoleList();
        try {
            response = appListGrpcClient.appStart(id, username, name, roleList);
        } catch (Exception e) {
            log.error("[{}]启用系统时发生了异常, 异常信息为[{}]", userBO.getUsername(), e.getMessage());
            return new BaseResult<>().setCode(500).setMessage("启用系统时发生了异常");
        }
        int code = response.getCode();
        String desc = response.getDesc();
        if (code == ResultCode.UNAUTHORIZED.getCode()) {
            return new BaseResult<>(ResultCode.FORBIDDEN).setMessage("用户无权操作此记录");
        }
        if (code != 200) {
            return new BaseResult<>().setCode(code).setMessage(desc);
        }
        return BaseResult.of();
    }

    @Override
    public BaseResult<Object> appStop(UserBO userBO, Long id) {
        AppStartOrStopQueryInfoResponse response;
        String username = userBO.getUsername();
        String name = userBO.getName();
        List<String> roleList = userBO.getRoleList();
        try {
            response = appListGrpcClient.appStop(id, username, name, roleList);
        } catch (Exception e) {
            log.error("[{}]停用系统时发生了异常, 异常信息为[{}]", username, e.getMessage());
            return new BaseResult<>(ResultCode.SERVER_INNER_ERROR).setMessage("停用系统时发生了异常");
        }
        int code = response.getCode();
        String desc = response.getDesc();
        if (code == ResultCode.UNAUTHORIZED.getCode()) {
            return new BaseResult<>(ResultCode.FORBIDDEN).setMessage("用户无权操作此记录");
        }
        if (code != 200) {
            return new BaseResult<>().setCode(code).setMessage(desc);
        }
        return BaseResult.of();
    }

    @Override
    public BaseResult<Object> appUseApply(UserBO userBO, Long id) {
        String username = userBO.getUsername();
        EmployeeInfo employeeInfo = employeeInfoMapper.selectOne(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .eq(EmployeeInfo::getUsername, username)
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        );
        AppUseApplyQueryInfoResponse response;
        try {
            response = appListGrpcClient.appUseApply(id, employeeInfo.getName(), username);
        } catch (Exception e) {
            log.error("[{}]申请使用时发生了异常, 异常信息为[{}]", username, e.getMessage());
            return new BaseResult<>().setCode(500).setMessage("申请使用时发生了异常");
        }
        int code = response.getCode();
        String desc = response.getDesc();
        if (code != 200) {
            return new BaseResult<>().setCode(code).setMessage(desc);
        }
        return BaseResult.of();
    }

    @Override
    public BaseResult<List<UsernameAndNameBO>> getManagers(UserBO userBO, String searchWord) {
        AppManagerSearchQueryInfoResponse response;
        try {
            response = appListGrpcClient.appManagerSearch(searchWord);
        } catch (Exception e) {
            log.error("[{}]获取管理员时发生了异常, 异常信息为[{}]", userBO.getUsername(), e.getMessage());
            return new BaseResult<List<UsernameAndNameBO>>().setCode(500).setMessage("获取管理员时发生了异常").setData(Collections.emptyList());
        }
        int code = response.getCode();
        String desc = response.getDesc();
        if (code != 200) {
            return new BaseResult<List<UsernameAndNameBO>>().setCode(500).setMessage(desc).setData(Collections.emptyList());
        }
        List<UsernameAndNameBO> managers = MapperUtil.INSTANCE.mapToUsernameAndNameBOList(response.getManagersList());
        return BaseResult.of(managers);
    }

    @Override
    public BaseResult<List<UsernameAndNameBO>> getAppApplyUsers(UserBO userBO, String searchWord) {
        AppApplyUserSearchQueryInfoResponse response;
        try {
            response = appListGrpcClient.appApplyUserSearch(searchWord);
        } catch (Exception e) {
            log.error("[{}]获取申请人列表时发生了异常, 异常信息为[{}]", userBO.getUsername(), e.getMessage());
            return new BaseResult<List<UsernameAndNameBO>>().setCode(500).setMessage("获取申请人列表时发生了异常").setData(Collections.emptyList());
        }
        int code = response.getCode();
        String desc = response.getDesc();
        if (code != 200) {
            return new BaseResult<List<UsernameAndNameBO>>().setCode(500).setMessage(desc).setData(Collections.emptyList());
        }
        List<UsernameAndNameBO> appApplyUsers = MapperUtil.INSTANCE.mapToUsernameAndNameBOList(response.getAppApplyUsersList());
        return BaseResult.of(appApplyUsers);
    }
}
