package com.mioffice.ums.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.EmailTaskDTO;
import com.mioffice.ums.admin.entity.dto.LarkTaskDTO;
import com.mioffice.ums.admin.entity.dto.LarkTaskDetailDTO;
import com.mioffice.ums.admin.entity.dto.MessagePrePushDTO;
import com.mioffice.ums.admin.entity.dto.MyTaskRecordDTO;
import com.mioffice.ums.admin.entity.dto.SmsTaskDTO;
import com.mioffice.ums.admin.entity.dto.TaskHistoryPageDTO;
import com.mioffice.ums.admin.entity.dto.TaskResultDTO;
import com.mioffice.ums.admin.result.BaseResult;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/13 7:57 下午
 * version: 1.0.0
 */
public interface MessageService {

    /**
     * 创建飞书消息任务
     *
     * @param userBO
     * @param larkTaskDTO
     * @return
     */
    BaseResult<TaskResultDTO> createLarkTask(UserBO userBO, LarkTaskDTO larkTaskDTO);

    /**
     * 修改飞书消息任务
     *
     * @param userBO
     * @param larkTaskDTO
     * @return
     */
    BaseResult<TaskResultDTO> updateLarkTask(UserBO userBO, LarkTaskDTO larkTaskDTO);

    /**
     * 触发发送消息的任务
     */
    void triggerSendTask();

    /**
     * 重试发送异常的消息
     */
    void triggerRetrySendExceptionTask();

    /**
     * 重试发送中超时的消息
     */
    void triggerRetrySendingTimeoutTask();

    /**
     * 分页查询我的消息
     *
     * @param page
     * @param size
     * @param key
     * @param channel
     * @param taskStatus
     * @param beginDate
     * @param endDate
     * @param userBO
     * @param publishScope
     * @return
     */
    BaseResult<IPage<MyTaskRecordDTO>> queryMyTaskByPage(Long page, Long size, String key, List<Byte> channel,
                                                         List<Byte> taskStatus, String beginDate, String endDate,
                                                         UserBO userBO, List<Byte> publishScope);

    /**
     * 分页查询推送详情
     *
     * @param page
     * @param size
     * @param taskId
     * @param userBO
     * @return
     */
    BaseResult queryTaskMessageByPage(Long page, int size, Long taskId, UserBO userBO);

    /**
     * @param page
     * @param size
     * @param key
     * @param channel
     * @param taskStatus
     * @param beginDate
     * @param endDate
     * @param publishScope
     * @param userBO
     * @return
     */
    BaseResult<TaskHistoryPageDTO> queryTaskHistoryByPage(Long page, Long size, String key, String channel,
                                                          List<Byte> taskStatus, String beginDate, String endDate,
                                                          String publishScope, List<String> createUsernameList,
                                                          UserBO userBO);

    /**
     * 获取消息任务列表(下拉菜单使用)
     *
     * @param userBO
     * @return
     */
    BaseResult<Object> getSummaryList(UserBO userBO);

    /**
     * 预推送 lark
     *
     * @param userBO
     * @param messagePrePushDTO
     * @return
     */
    BaseResult<Object> prePushMsg(UserBO userBO, MessagePrePushDTO messagePrePushDTO);

    /**
     * 消息任务取消取消或者终止
     *
     * @param userBO
     * @param taskResultDTO
     * @return
     */
    BaseResult<Object> stopMessageTask(UserBO userBO, TaskResultDTO taskResultDTO);

    /**
     *
     */
    BaseResult<LarkTaskDetailDTO> taskLarkDetail(Long taskId, UserBO userBO);

    BaseResult<TaskResultDTO> taskLarkRetract(Long taskId, UserBO userBO);

    /**
     *
     */
    BaseResult<Object> taskDetail(Long taskId, UserBO userBO);

    /**
     * @param taskId
     * @return
     * @throws IOException
     */
    BaseResult<Map<String, Object>> download(Long taskId, UserBO userBO) throws IOException;

    /**
     * 创建邮件消息
     *
     * @param userBO
     * @param emailTaskDTO
     * @return
     */
    BaseResult<TaskResultDTO> createEmailTask(UserBO userBO, EmailTaskDTO emailTaskDTO);

    /**
     * 修改邮件消息
     *
     * @param userBO
     * @param emailTaskDTO
     * @return
     */
    BaseResult<TaskResultDTO> updateEmailTask(UserBO userBO, EmailTaskDTO emailTaskDTO);

    /**
     * 创建短信消息
     *
     * @param userBO
     * @param smsTaskDTO
     * @return
     */
    BaseResult<TaskResultDTO> createSmsTask(UserBO userBO, SmsTaskDTO smsTaskDTO);

    /**
     * 更新短信消息
     *
     * @param userBO
     * @param smsTaskDTO
     * @return
     */
    BaseResult<TaskResultDTO> updateSmsTask(UserBO userBO, SmsTaskDTO smsTaskDTO);

    /**
     * 加推飞书消息
     *
     * @param userBO
     * @param larkTaskDTO
     * @return
     */
    BaseResult<TaskResultDTO> rushLarkTask(UserBO userBO, LarkTaskDTO larkTaskDTO);
}
