package com.mioffice.ums.admin.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import static com.alibaba.excel.EasyExcelFactory.read;
import static com.alibaba.excel.EasyExcelFactory.write;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.ImportPageDTO;
import com.mioffice.ums.admin.entity.dto.NameListAddDTO;
import com.mioffice.ums.admin.entity.dto.NameListDeleteDTO;
import com.mioffice.ums.admin.entity.dto.ParseTmpExportDTO;
import com.mioffice.ums.admin.entity.dto.RateDTO;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.ImportCountInfo;
import com.mioffice.ums.admin.entity.info.ParseTmpInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.ParseDataStatusEnum;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.excel.listener.UploadUserDataListener;
import com.mioffice.ums.admin.excel.parse.DoParseAllOnfinish;
import com.mioffice.ums.admin.excel.parse.DoParseBatchData;
import com.mioffice.ums.admin.excel.parse.DoParseHeadProcess;
import com.mioffice.ums.admin.manager.MiCloudFdsManager;
import com.mioffice.ums.admin.manager.ReportLineManager;
import com.mioffice.ums.admin.mapper.ImportCountInfoMapper;
import com.mioffice.ums.admin.mapper.ParseTmpInfoMapper;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.EmployeeService;
import com.mioffice.ums.admin.service.ImportNameListService;
import com.mioffice.ums.admin.service.TaskService;
import com.mioffice.ums.admin.utils.AesEcbUtils;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUnReadDetailResponse;
import com.xiaomi.infra.galaxy.fds.model.FDSObjectMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/11 1:10 上午
 * version: 1.0.0
 */
@Slf4j
@Service
public class ImportNameListServiceImpl extends ServiceImpl<ParseTmpInfoMapper, ParseTmpInfo>
        implements ImportNameListService {

    private final ImportCountInfoMapper importCountInfoMapper;

    private final ParseTmpInfoMapper parseTmpInfoMapper;

    private final DoParseAllOnfinish doParseAllOnfinish;

    private final DoParseBatchData doParseBatchData;

    private final DoParseHeadProcess doParseHeadProcess;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private MessageGrpcClient messageGrpcClient;

    @Autowired
    private TaskService taskService;

    private final MiCloudFdsManager miCloudFdsManager;

    private final ReportLineManager reportLineManager;

    @NacosValue(value = "${ums.admin.executives:}", autoRefreshed = true)
    private String executives;

    public ImportNameListServiceImpl(ImportCountInfoMapper importCountInfoMapper,
                                     ParseTmpInfoMapper parseTmpInfoMapper,
                                     DoParseAllOnfinish doParseAllOnfinish,
                                     DoParseBatchData doParseBatchData,
                                     DoParseHeadProcess doParseHeadProcess,
                                     MiCloudFdsManager miCloudFdsManager,
                                     ReportLineManager reportLineManager) {
        this.importCountInfoMapper = importCountInfoMapper;
        this.parseTmpInfoMapper = parseTmpInfoMapper;
        this.doParseAllOnfinish = doParseAllOnfinish;
        this.doParseBatchData = doParseBatchData;
        this.doParseHeadProcess = doParseHeadProcess;
        this.miCloudFdsManager = miCloudFdsManager;
        this.reportLineManager = reportLineManager;
    }

    @Override
    public BaseResult<Object> upload(UserBO userBO, MultipartFile file, String excelId) {
        try {

            Integer indexNo = parseTmpInfoMapper.selectCount(
                    Wrappers.<ParseTmpInfo>lambdaQuery().eq(ParseTmpInfo::getExcelId, excelId)
                            .eq(ParseTmpInfo::getPublishScope, PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode()));

            List<ImportCountInfo> importCountInfoList = importCountInfoMapper.selectList(
                    Wrappers.<ImportCountInfo>lambdaQuery().eq(ImportCountInfo::getExcelId, excelId));

            if (importCountInfoList.isEmpty()) {
                ImportCountInfo importCountInfo = ImportCountInfo.newCreateAndUpdateTimeInstant();
                importCountInfo.setTotalCount(-1L);
                importCountInfo.setExcelId(excelId);
                importCountInfo.setErrorCount(0L);
                importCountInfo.setSaveCount(0L);
                importCountInfoMapper.insert(importCountInfo);
            } else {
                ImportCountInfo importCountInfo = ImportCountInfo.newUpdateTimeInstant();
                importCountInfo.setTotalCount(-1L);
                importCountInfo.setErrorCount(0L);
                importCountInfo.setSaveCount(0L);
                importCountInfoMapper.update(importCountInfo,
                        Wrappers.<ImportCountInfo>lambdaQuery().eq(ImportCountInfo::getExcelId, excelId));
            }
            InputStream inputStream = file.getInputStream();
            ThreadUtil.execute(() -> {
                UploadUserDataListener listener =
                        new UploadUserDataListener(ImportNameListServiceImpl.this, doParseBatchData, doParseAllOnfinish,
                                doParseHeadProcess, userBO, excelId, indexNo, StringUtils.isNotBlank(executives) ?
                                Splitter.on(",").splitToList(executives) : Lists.newArrayList());
                try {
                    read(inputStream, ParseTmpInfo.class, listener).sheet().doRead();
                } catch (Exception e) {
                    ImportCountInfo importCountInfo = ImportCountInfo.newUpdateTimeInstant();
                    importCountInfo.setTotalCount(-11L);
                    importCountInfo.setErrorCount(0L);
                    importCountInfo.setSaveCount(0L);
                    importCountInfoMapper.update(importCountInfo,
                            Wrappers.<ImportCountInfo>lambdaQuery().eq(ImportCountInfo::getExcelId, excelId));
                    log.warn("excelId = [{}]的任务导入发生异常", excelId, e);
                }
            });
        } catch (IOException e) {
            log.info(e.getMessage());
            return new BaseResult<>(ResultCode.SERVER_INNER_ERROR.getCode(), "服务器处理Excel出错");
        }
        return BaseResult.of(excelId);
    }

    @Override
    public BaseResult<Object> rate(String excelId) {
        ImportCountInfo importCountInfo = importCountInfoMapper.selectOne(
                Wrappers.<ImportCountInfo>lambdaQuery().eq(ImportCountInfo::getExcelId, excelId)
        );
        if (importCountInfo == null) {
            return new BaseResult<>(ResultCode.RESOURCE_NOT_FOUND).setMessage("任务不存在");
        }
        if (importCountInfo.getTotalCount() == -10) {
            return new BaseResult<>(ResultCode.SERVER_INNER_ERROR).setMessage("模板错误");
        }
        if (importCountInfo.getTotalCount() == -11) {
            return new BaseResult<>(ResultCode.SERVER_INNER_ERROR).setMessage("导入excel错误，请检查excel类型及数据");
        }
        if (importCountInfo.getErrorCount() != 0) {
            return new BaseResult<>(ResultCode.SERVER_INNER_ERROR).setMessage("服务器发生了错误，请联系系统管理员处理");
        }
        RateDTO rateDTO = new RateDTO();
        rateDTO.setAllCount(importCountInfo.getTotalCount());
        rateDTO.setDealCount(importCountInfo.getErrorCount() + importCountInfo.getSaveCount());
        rateDTO.setTodoCount(importCountInfo.getTotalCount() - rateDTO.getDealCount());
        if ((rateDTO.getAllCount() != -1) && (rateDTO.getTodoCount() == 0)) {
            rateDTO.setFinish(true);
            Integer errorCount = parseTmpInfoMapper.selectCount(
                    Wrappers.<ParseTmpInfo>lambdaQuery()
                            .eq(ParseTmpInfo::getExcelId, excelId)
                            .and(wrapper -> wrapper.eq(ParseTmpInfo::getDataStatus,
                                    ParseDataStatusEnum.EMPLOYEE_NOT_EXIST.getCode())));
            rateDTO.setErrorCount(errorCount);
        } else {
            rateDTO.setFinish(false);
        }
        return BaseResult.of(rateDTO);
    }

    @Override
    public BaseResult<ImportPageDTO<ParseTmpInfo>> page(Long page, Long size, String excelId, String key,
                                                        PublishScopeEnum publishScopeEnum) {
        if (PublishScopeEnum.UNREAD_PUSH == publishScopeEnum) {
            List<String> excludeUsernameList = Lists.newArrayList();
            if (excelId.contains("_")) {
                String[] excelId_scopeKey = excelId.split("_");
                Assert.isTrue(excelId_scopeKey.length == 2, "excelId参数错误");
                excelId = excelId_scopeKey[0];
                String scopeKey = excelId_scopeKey[1];
                excludeUsernameList = parseTmpInfoMapper.selectList(Wrappers.<ParseTmpInfo>lambdaQuery()
                                .eq(ParseTmpInfo::getExcelId, scopeKey)
                                .eq(ParseTmpInfo::getPublishScope, PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode()))
                        .stream().map(ParseTmpInfo::getUsername).collect(Collectors.toList());
            }
            Assert.isTrue(NumberUtils.isParsable(excelId), "excelId参数错误");
            Long parentTaskId = NumberUtils.createLong(excelId);
            TaskInfo parentTaskInfo = taskService.getById(parentTaskId);
            Assert.notNull(parentTaskInfo, "excelId参数错误");
            List<String> subExtraIdList =
                    taskService.list(Wrappers.<TaskInfo>lambdaQuery().eq(TaskInfo::getParentTaskId,
                                    parentTaskInfo.getId()))
                            .stream()
                            .map(TaskInfo::getExtraId)
                            .collect(Collectors.toList());
            MessageUnReadDetailResponse messageUnReadDetailResponse = messageGrpcClient.getPageUnRead(
                    key,
                    page,
                    size,
                    parentTaskInfo.getExtraId(),
                    subExtraIdList,
                    excludeUsernameList);
            String finalExcelId = excelId;
            ImportPageDTO<ParseTmpInfo> importPageDTO = new ImportPageDTO<>();

            IPage<ParseTmpInfo> pageData = new IPage<ParseTmpInfo>() {
                @Override
                public List<OrderItem> orders() {
                    return null;
                }

                @Override
                public List<ParseTmpInfo> getRecords() {
                    return messageUnReadDetailResponse.getMessageUnreadDetail().getRecordsList()
                            .stream()
                            .map(oprId -> {
                                EmployeeInfo employeeInfo =
                                        employeeService.getOne(
                                                Wrappers.<EmployeeInfo>lambdaQuery().eq(EmployeeInfo::getUsername,
                                                                oprId)
                                                        .eq(EmployeeInfo::getHrStatus,
                                                                HrApplyStatusEnum.VALID.getCode()));
                                if (Objects.isNull(employeeInfo)) {
                                    return null;
                                }
                                ParseTmpInfo parseTmpInfo = ParseTmpInfo.newCreateAndUpdateTimeInstant();
                                parseTmpInfo.setExcelId(finalExcelId);
                                setProperty(parseTmpInfo, employeeInfo, PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE);
                                return parseTmpInfo;
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                }

                @Override
                public IPage<ParseTmpInfo> setRecords(List<ParseTmpInfo> records) {
                    return null;
                }

                @Override
                public long getTotal() {
                    return messageUnReadDetailResponse.getMessageUnreadDetail().getTotal();
                }

                @Override
                public IPage<ParseTmpInfo> setTotal(long total) {
                    return null;
                }

                @Override
                public long getSize() {
                    return messageUnReadDetailResponse.getMessageUnreadDetail().getPageSize();
                }

                @Override
                public IPage<ParseTmpInfo> setSize(long size) {
                    return null;
                }

                @Override
                public long getCurrent() {
                    return messageUnReadDetailResponse.getMessageUnreadDetail().getPageNo();
                }

                @Override
                public IPage<ParseTmpInfo> setCurrent(long current) {
                    return null;
                }
            };

            importPageDTO.setPageData(pageData);
            importPageDTO.setTotal(
                    messageUnReadDetailResponse.getMessageUnreadDetail().getTotalWithoutSearch());
            return BaseResult.of(importPageDTO);
        } else {
            ImportPageDTO<ParseTmpInfo> importPageDTO = new ImportPageDTO<>();
            // page
            Page<ParseTmpInfo> dataPage = new Page<>(page, size);

            importPageDTO.setPageData(dataPage);
            importPageDTO.setTotal(dataPage.getTotal());

            LambdaQueryWrapper<ParseTmpInfo> lambdaQuery = Wrappers.lambdaQuery();

            if (!StringUtils.isNotBlank(excelId)) {
                return BaseResult.of(importPageDTO);
            }

            lambdaQuery.eq(ParseTmpInfo::getExcelId, excelId).eq(ParseTmpInfo::getPublishScope, publishScopeEnum.getCode());

            lambdaQuery.and(
                    wrapper -> wrapper
                            .eq(ParseTmpInfo::getDataStatus, ParseDataStatusEnum.WAIT_PROCESS.getCode())
                            .or()
                            .eq(ParseTmpInfo::getDataStatus, ParseDataStatusEnum.PROCESSED.getCode()));

            IPage<ParseTmpInfo> parseTmpInfoIPageWithoutSearch = parseTmpInfoMapper.selectPage(new Page<ParseTmpInfo>(page,
                    size),lambdaQuery);

            if (StringUtils.isNotBlank(key)) {
                lambdaQuery.and(wrapper -> wrapper.like(ParseTmpInfo::getName, key)
                        .or()
                        .like(ParseTmpInfo::getUsername, key));
            }

            lambdaQuery.orderByDesc(ParseTmpInfo::getId);

            IPage<ParseTmpInfo> parseTmpInfoPage = parseTmpInfoMapper.selectPage(dataPage, lambdaQuery);

            List<ParseTmpInfo> parseTmpInfoList = parseTmpInfoPage.getRecords();

            long total = parseTmpInfoPage.getTotal();

            for (int i = 0; i < parseTmpInfoList.size(); i++) {
                ParseTmpInfo parseTmpInfo = parseTmpInfoList.get(i);
                try {
                    parseTmpInfo.setPhone(
                            AesEcbUtils.decrypt(parseTmpInfo.getPhone())
                                    .replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));
                } catch (Exception e) {
                    log.error("邮箱为[{}]的人员手机号解密失败", parseTmpInfo.getEmail());
                }
                parseTmpInfo.setIndexNo((int) (total - size * (page - 1) - i));
            }

            importPageDTO.setPageData(parseTmpInfoPage);
            importPageDTO.setTotal(parseTmpInfoIPageWithoutSearch.getTotal());

            return BaseResult.of(importPageDTO);
        }
    }

    @Override
    public BaseResult<Object> delete(NameListDeleteDTO nameListDeleteDTO) {
        parseTmpInfoMapper.delete(
                Wrappers.<ParseTmpInfo>lambdaQuery()
                        .eq(ParseTmpInfo::getExcelId, nameListDeleteDTO.getExcelId())
                        .in(ParseTmpInfo::getId, nameListDeleteDTO.getIdList())
        );
        return BaseResult.of();
    }

    @Override
    public BaseResult<Object> add(NameListAddDTO nameListAddDTO) {
        if (StringUtils.isNotBlank(nameListAddDTO.getExcelId())) {
            return saveImportData(nameListAddDTO);
        } else {
            String excelIdNew = UUID.randomUUID().toString();
            nameListAddDTO.setExcelId(excelIdNew);
            return saveImportData(nameListAddDTO);
        }
    }

    private BaseResult<Object> saveImportData(NameListAddDTO nameListAddDTO) {
        List<String> executiveUsernames = Splitter.on(",").splitToList(executives);
        List<ParseTmpInfo> parseTmpInfoList = new ArrayList<>();
        if (nameListAddDTO.getUserNameList().isEmpty()) {
            return new BaseResult<>().setCode(200).setMessage("传过来的userName为空");
        }
        List<EmployeeInfo> employeeInfoList = employeeService.list(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .in(EmployeeInfo::getUsername, nameListAddDTO.getUserNameList())
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        );

        if (employeeInfoList.isEmpty()) {
            return new BaseResult<>().setCode(704).setMessage("此用户不存在");
        }

        AtomicInteger indexNo = new AtomicInteger(
                parseTmpInfoMapper.selectCount(
                        Wrappers.<ParseTmpInfo>lambdaQuery()
                                .eq(ParseTmpInfo::getExcelId, nameListAddDTO.getExcelId())
                                .eq(ParseTmpInfo::getPublishScope, PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE.getCode()))
        );

        employeeInfoList.forEach(
                data -> {
                    ParseTmpInfo parseTmpInfo = ParseTmpInfo.newCreateAndUpdateTimeInstant();
                    parseTmpInfo.setExcelId(nameListAddDTO.getExcelId());
                    parseTmpInfo.setIndexNo(indexNo.incrementAndGet());
                    setProperty(parseTmpInfo, data, PublishScopeEnum.CUSTOM_PUSH_EMPLOYEE);
                    parseTmpInfoList.add(parseTmpInfo);
                }
        );

        if (!parseTmpInfoList.isEmpty()) {
            parseTmpInfoMapper.insertAutoDuplicate(parseTmpInfoList, System.currentTimeMillis());
        }

        List<ParseTmpInfo> leaderParseTmpInfoList = new ArrayList<>();

        Set<String> leaderOprIdSet =
                employeeInfoList
                        .stream()
                        .map(employeeInfo -> reportLineManager.getDirectLeader(employeeInfo.getUsername(),
                                StringUtils.EMPTY).getSupUserName())
                        .filter(leaderOprId -> StringUtils.isNotBlank(leaderOprId)
                                && !executiveUsernames.contains(leaderOprId))
                        .collect(Collectors.toSet());
        Map<String, EmployeeInfo> leaderOprIdAndEmployeeInfoMap =
                employeeService.getMapByOprIdList(new ArrayList<>(leaderOprIdSet));
        if (!leaderOprIdAndEmployeeInfoMap.isEmpty()) {
            for (EmployeeInfo leader : leaderOprIdAndEmployeeInfoMap.values()) {
                ParseTmpInfo parseTmpInfo = ParseTmpInfo.newCreateAndUpdateTimeInstant();
                parseTmpInfo.setExcelId(nameListAddDTO.getExcelId());
                setProperty(parseTmpInfo, leader, PublishScopeEnum.CUSTOM_PUSH_MANAGER);
                leaderParseTmpInfoList.add(parseTmpInfo);
            }
        }
        if (!leaderParseTmpInfoList.isEmpty()) {
            parseTmpInfoMapper.insertAutoDuplicate(leaderParseTmpInfoList, System.currentTimeMillis());
        }

        return BaseResult.of(nameListAddDTO);
    }

    private void setProperty(ParseTmpInfo parseTmpInfo, EmployeeInfo employeeInfo, PublishScopeEnum publishScopeEnum) {
        parseTmpInfo.setUsername(employeeInfo.getUsername());
        parseTmpInfo.setPublishScope(publishScopeEnum.getCode());
        if (StringUtils.isBlank(employeeInfo.getEmail())) {
            parseTmpInfo.setEmail("-");
        } else {
            parseTmpInfo.setEmail(employeeInfo.getEmail());
        }
        if (StringUtils.isBlank(employeeInfo.getEmpId())) {
            parseTmpInfo.setEmpNo("-");
        } else {
            parseTmpInfo.setEmpNo(employeeInfo.getEmpId());
        }
        if (StringUtils.isBlank(employeeInfo.getName())) {
            parseTmpInfo.setName("-");
        } else {
            parseTmpInfo.setName(employeeInfo.getName());
        }
        if (StringUtils.isBlank(employeeInfo.getDeptId())) {
            parseTmpInfo.setDeptId("-");
        } else {
            parseTmpInfo.setDeptId(employeeInfo.getDeptId());
        }

        if (StringUtils.isBlank(employeeInfo.getMiDeptLevel2Desc())) {
            parseTmpInfo.setMiDeptLevel2Desc("-");
        } else {
            parseTmpInfo.setMiDeptLevel2Desc(employeeInfo.getMiDeptLevel2Desc());
        }

        if (StringUtils.isBlank(employeeInfo.getMiDeptLevel2())) {
            parseTmpInfo.setMiDeptLevel2("-");
        } else {
            parseTmpInfo.setMiDeptLevel2(employeeInfo.getMiDeptLevel2());
        }

        if (StringUtils.isBlank(employeeInfo.getMiDeptLevel3Desc())) {
            parseTmpInfo.setMiDeptLevel3Desc("-");
        } else {
            parseTmpInfo.setMiDeptLevel3Desc(employeeInfo.getMiDeptLevel3Desc());
        }

        if (StringUtils.isBlank(employeeInfo.getMiDeptLevel3())) {
            parseTmpInfo.setMiDeptLevel3("-");
        } else {
            parseTmpInfo.setMiDeptLevel3(employeeInfo.getMiDeptLevel3());
        }

        if (StringUtils.isBlank(employeeInfo.getMiDeptLevel4Desc())) {
            parseTmpInfo.setMiDeptLevel4Desc("-");
        } else {
            parseTmpInfo.setMiDeptLevel4Desc(employeeInfo.getMiDeptLevel4Desc());
        }

        if (StringUtils.isBlank(employeeInfo.getMiDeptLevel4())) {
            parseTmpInfo.setMiDeptLevel4("-");
        } else {
            parseTmpInfo.setMiDeptLevel4(employeeInfo.getMiDeptLevel4());
        }

        if (StringUtils.isBlank(employeeInfo.getPhone())) {
            parseTmpInfo.setPhone("-");
        } else {
            parseTmpInfo.setPhone(employeeInfo.getPhone());
        }

        StringBuilder sb = new StringBuilder();

        if (!"-".equals(parseTmpInfo.getMiDeptLevel2Desc())) {
            sb.append(parseTmpInfo.getMiDeptLevel2Desc());
        }
        if (!"-".equals(parseTmpInfo.getMiDeptLevel3Desc())) {
            sb.append("·");
            sb.append(parseTmpInfo.getMiDeptLevel3Desc());
        }
        if (!"-".equals(parseTmpInfo.getMiDeptLevel4Desc())) {
            sb.append("·");
            sb.append(parseTmpInfo.getMiDeptLevel4Desc());
        }

        parseTmpInfo.setDeptDesc(sb.toString());

        parseTmpInfo.setDataStatus(ParseDataStatusEnum.WAIT_PROCESS.getCode());
    }

    @Override
    public BaseResult<Map<String, Object>> download(String excelId)
            throws IOException {
        String fileName = URLEncoder.encode("errorData-" + excelId, "UTF-8");
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        write(os, ParseTmpExportDTO.class).sheet("errorData").doWrite(exportData(excelId));
        String folderName = "notice/".concat(DateUtil.today()).concat("/");
        FDSObjectMetadata fdsObjectMetadata = new FDSObjectMetadata();
        fdsObjectMetadata.setContentType("application/vnd.ms-excel");
        byte[] content = os.toByteArray();
        InputStream is = new ByteArrayInputStream(content);
        String url = miCloudFdsManager.uploadFile(folderName + fileName + ".xlsx", is, fdsObjectMetadata);
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("url", miCloudFdsManager.getHostUrl().concat(url));
        return BaseResult.of(returnMap);
    }

    private List<ParseTmpExportDTO> exportData(String excelId) {
        List<ParseTmpInfo> parseTmpInfoList = parseTmpInfoMapper.selectList(
                Wrappers.<ParseTmpInfo>lambdaQuery()
                        .ge(ParseTmpInfo::getDataStatus, ParseDataStatusEnum.EMPLOYEE_NOT_EXIST.getCode())
                        .eq(ParseTmpInfo::getExcelId, excelId)
        );

        if (parseTmpInfoList.isEmpty()) {
            return Collections.emptyList();
        } else {
            List<ParseTmpExportDTO> parseTmpExportDTOList = new ArrayList<>();
            parseTmpInfoList.forEach(
                    parseTmpInfo -> {
                        ParseTmpExportDTO parseTmpExportDTO = new ParseTmpExportDTO();
                        parseTmpExportDTO.setUsername(parseTmpInfo.getUsername());
                        parseTmpExportDTO.setDataStatusDesc(
                                ParseDataStatusEnum.getMsgByCode(parseTmpInfo.getDataStatus()));
                        parseTmpExportDTOList.add(parseTmpExportDTO);
                    }
            );
            return parseTmpExportDTOList;
        }
    }
}
