package com.mioffice.ums.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mioffice.ums.admin.entity.info.MessageJobExecuteRecord;

import java.util.List;
import java.util.Set;

/**
 *
 */
public interface MessageJobExecuteRecordService extends IService<MessageJobExecuteRecord> {

    /**
     * 获取未点击的人员oprId
     *
     * @param extraIdList
     * @return
     */
    Set<String> getUnClickUserByExtraIdList(String parentExtraId, List<String> extraIdList);
}
