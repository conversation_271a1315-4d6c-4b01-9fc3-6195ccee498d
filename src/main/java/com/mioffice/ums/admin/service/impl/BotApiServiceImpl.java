package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mioffice.ums.admin.constants.ProcessStatusConstant;
import com.mioffice.ums.admin.entity.bo.EmailRobotBO;
import com.mioffice.ums.admin.entity.bo.PostGuardBo;
import com.mioffice.ums.admin.entity.bo.ProcessTaskCreateEvent;
import com.mioffice.ums.admin.entity.bo.PushCountBo;
import com.mioffice.ums.admin.entity.bo.SmsRobotBO;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.bo.UserInfoBO;
import com.mioffice.ums.admin.entity.bo.UsernameAndNameBO;
import com.mioffice.ums.admin.entity.dto.*;
import com.mioffice.ums.admin.entity.info.BotInfo;
import com.mioffice.ums.admin.entity.info.BotStopTaskInfo;
import com.mioffice.ums.admin.entity.info.DepartmentInfo;
import com.mioffice.ums.admin.entity.info.DeptBotInfo;
import com.mioffice.ums.admin.entity.info.EmailTaskInfo;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.LarkTaskInfo;
import com.mioffice.ums.admin.entity.info.ProcessInstanceInfo;
import com.mioffice.ums.admin.entity.info.ProcessTaskInfo;
import com.mioffice.ums.admin.entity.info.PublishScopeInfo;
import com.mioffice.ums.admin.entity.info.SmsTaskInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.entity.info.UserBotInfo;
import com.mioffice.ums.admin.enums.BotRangeScopeEnum;
import com.mioffice.ums.admin.enums.BotTypeEnum;
import com.mioffice.ums.admin.enums.CalendarEnableEnum;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.LarkRobotStatusEnum;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.enums.TaskChannelEnum;
import com.mioffice.ums.admin.enums.TaskStatusEnum;
import com.mioffice.ums.admin.enums.UmsRobotStatusEnum;
import com.mioffice.ums.admin.enums.UserBotRoleEnum;
import com.mioffice.ums.admin.exception.RobotVerifyException;
import com.mioffice.ums.admin.manager.DeptManager;
import com.mioffice.ums.admin.manager.PostGuardValidate;
import com.mioffice.ums.admin.manager.RobotVerifyManager;
import com.mioffice.ums.admin.manager.UserRoleManager;
import com.mioffice.ums.admin.mapper.BotInfoMapper;
import com.mioffice.ums.admin.mapper.BotStopTaskInfoMapper;
import com.mioffice.ums.admin.mapper.DepartmentInfoMapper;
import com.mioffice.ums.admin.mapper.DeptBotInfoMapper;
import com.mioffice.ums.admin.mapper.EmailTaskInfoMapper;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.LarkGroupPushInfoMapper;
import com.mioffice.ums.admin.mapper.LarkTaskInfoMapper;
import com.mioffice.ums.admin.mapper.ProcessInstanceInfoMapper;
import com.mioffice.ums.admin.mapper.ProcessTaskInfoMapper;
import com.mioffice.ums.admin.mapper.PublishScopeInfoMapper;
import com.mioffice.ums.admin.mapper.SmsTaskInfoMapper;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import com.mioffice.ums.admin.mapper.UserBotInfoMapper;
import com.mioffice.ums.admin.remote.grpc.MessageBotGrpcClient;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.LarkAppCode;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.BotApiService;
import com.mioffice.ums.admin.utils.DateTimeUtil;
import com.mioffice.ums.admin.utils.JsonUtils;
import com.mioffice.ums.admin.utils.KeyCenterUtil;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.BotStatus;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageNumberAndTime;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageNumberAndTimeResponse;
import com.xiaomi.keycenter.common.iface.DataProtectionException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <p>
 * 机器人相关API
 * <p>
 *
 * <AUTHOR> tie tou
 * @since 2020/8/10 上午10:31
 */
@Slf4j
@Service
public class BotApiServiceImpl extends ServiceImpl<BotInfoMapper, BotInfo> implements BotApiService {

    private static final String APP_ID_STATUS_PREFIX = "APP_ID_STATUS:";
    private static final String LARK_APP_ID_OR_SECRET_INVALID_STR = "输入的机器人ID&秘钥有误";

    @Value("${lark.callback-url}")
    private String larkCallBackUrl;

    @Resource
    private LarkGroupPushInfoMapper larkGroupPushInfoMapper;

    private final MessageBotGrpcClient messageBotGrpcClient;

    private final BotInfoMapper botInfoMapper;

    private final DeptBotInfoMapper deptBotInfoMapper;

    private final UserBotInfoMapper userBotInfoMapper;

    private final RobotVerifyManager robotVerifyManager;

    private final TaskInfoMapper taskInfoMapper;

    private final LarkTaskInfoMapper larkTaskInfoMapper;

    private final DepartmentInfoMapper departmentInfoMapper;

    private final EmployeeInfoMapper employeeInfoMapper;

    private final MessageGrpcClient messageGrpcClient;

    private final RedisTemplate<String, String> redisTemplate;

    private final BotStopTaskInfoMapper botStopTaskInfoMapper;

    private final ProcessTaskInfoMapper processTaskInfoMapper;

    private final ProcessInstanceInfoMapper processInstanceInfoMapper;

    private final ApplicationEventPublisher applicationEventPublisher;

    private final UserRoleManager userRoleManager;

    private final PublishScopeInfoMapper publishScopeInfoMapper;

    private final DeptManager deptManager;

    private final EmailTaskInfoMapper emailTaskInfoMapper;

    private final SmsTaskInfoMapper smsTaskInfoMapper;

    private final PostGuardValidate postGuardValidate;

    public BotApiServiceImpl(MessageBotGrpcClient messageBotGrpcClient, BotInfoMapper botInfoMapper,
                             DeptBotInfoMapper deptBotInfoMapper, UserBotInfoMapper userBotInfoMapper,
                             RobotVerifyManager robotVerifyManager, TaskInfoMapper taskInfoMapper,
                             LarkTaskInfoMapper larkTaskInfoMapper, DepartmentInfoMapper departmentInfoMapper,
                             EmployeeInfoMapper employeeInfoMapper, MessageGrpcClient messageGrpcClient,
                             RedisTemplate redisTemplate, BotStopTaskInfoMapper botStopTaskInfoMapper,
                             ProcessTaskInfoMapper processTaskInfoMapper,
                             ProcessInstanceInfoMapper processInstanceInfoMapper,
                             ApplicationEventPublisher applicationEventPublisher,
                             PublishScopeInfoMapper publishScopeInfoMapper, DeptManager deptManager,
                             UserRoleManager userRoleManager, EmailTaskInfoMapper emailTaskInfoMapper,
                             SmsTaskInfoMapper smsTaskInfoMapper, PostGuardValidate postGuardValidate) {
        this.messageBotGrpcClient = messageBotGrpcClient;
        this.botInfoMapper = botInfoMapper;
        this.deptBotInfoMapper = deptBotInfoMapper;
        this.userBotInfoMapper = userBotInfoMapper;
        this.robotVerifyManager = robotVerifyManager;
        this.taskInfoMapper = taskInfoMapper;
        this.larkTaskInfoMapper = larkTaskInfoMapper;
        this.departmentInfoMapper = departmentInfoMapper;
        this.employeeInfoMapper = employeeInfoMapper;
        this.messageGrpcClient = messageGrpcClient;
        this.redisTemplate = redisTemplate;
        this.botStopTaskInfoMapper = botStopTaskInfoMapper;
        this.processTaskInfoMapper = processTaskInfoMapper;
        this.processInstanceInfoMapper = processInstanceInfoMapper;
        this.applicationEventPublisher = applicationEventPublisher;
        this.publishScopeInfoMapper = publishScopeInfoMapper;
        this.deptManager = deptManager;
        this.userRoleManager = userRoleManager;
        this.emailTaskInfoMapper = emailTaskInfoMapper;
        this.smsTaskInfoMapper = smsTaskInfoMapper;
        this.postGuardValidate = postGuardValidate;
    }

    @Override
    public BaseResult<AllBotDTO> queryAllBotsByPage(Long page, Long size, String botName, String deptId, String owner
            , String botStatus, String botType, Byte botTenant) {
        // page
        Page<BotInfo> dataPage = new Page<>(page, size);
        AllBotDTO allBotDTO = AllBotDTO.newEmpty(page, size);

        LambdaQueryWrapper<BotInfo> lambdaQuery = Wrappers.lambdaQuery();
        // botName
        if (StringUtils.isNotBlank(botName)) {
            lambdaQuery
                    .and(wrapper -> wrapper
                            .like(BotInfo::getBotName, botName)
                    );
        }
        // department
        if (StringUtils.isNotBlank(deptId)) {
            String[] deptIdSplit = deptId.split(",");
            List<String> deptIdList = new ArrayList<>(Arrays.asList(deptIdSplit));
            List<DeptBotInfo> deptBotInfoList = deptBotInfoMapper.selectList(
                    Wrappers.<DeptBotInfo>lambdaQuery().in(DeptBotInfo::getDeptId, deptIdList)
            );
            if (deptBotInfoList.isEmpty()) {
                return BaseResult.of(allBotDTO);
            }
            List<Long> botIdList =
                    deptBotInfoList.stream().map(DeptBotInfo::getBotId).distinct().collect(Collectors.toList());
            lambdaQuery.in(BotInfo::getId, botIdList);
        }
        // owner
        if (StringUtils.isNotBlank(owner)) {
            String[] ownerSplit = owner.split(",");
            List<String> ownerList = new ArrayList<>(Arrays.asList(ownerSplit));
            List<UserBotInfo> userBotInfoList = userBotInfoMapper.selectList(
                    Wrappers.<UserBotInfo>lambdaQuery().in(UserBotInfo::getUsername, ownerList)
                            .eq(UserBotInfo::getUserType, UserBotRoleEnum.USER_BOT_MANAGER.getCode())
            );
            if (userBotInfoList.isEmpty()) {
                return BaseResult.of(allBotDTO);
            }
            List<Long> botIdList =
                    userBotInfoList.stream().map(UserBotInfo::getBotId).distinct().collect(Collectors.toList());
            lambdaQuery.in(BotInfo::getId, botIdList);
        }
        // botStatus
        if (StringUtils.isNotBlank(botStatus)) {
            String[] botStatusSplit = botStatus.split(",");
            List<Byte> botStatusList = new ArrayList<>();
            for (String s : botStatusSplit) {
                botStatusList.add((byte) Integer.parseInt(s));
                if ((byte) Integer.parseInt(s) == 1) {
                    botStatusList.add((byte) 3);
                    botStatusList.add((byte) 99);
                    botStatusList.add((byte) 100);
                }
            }

            lambdaQuery.in(BotInfo::getBotLarkStatus, botStatusList);
        }
        // botType
        if (StringUtils.isNotBlank(botType)) {
            String[] botTypeSplit = botType.split(",");
            List<Byte> botTypeList = new ArrayList<>();
            for (String s : botTypeSplit) {
                botTypeList.add((byte) Integer.parseInt(s));
            }

            lambdaQuery.in(BotInfo::getBotType, botTypeList);
        }
        // botTenant
        if (Objects.nonNull(botTenant)) {
            lambdaQuery.eq(BotInfo::getBotTenant, botTenant);
        }
        lambdaQuery.orderByDesc(BotInfo::getCreateTime);
        IPage<BotInfo> botInfoIPage = botInfoMapper.selectPage(dataPage, lambdaQuery);
        List<BotInfo> botInfoList = botInfoIPage.getRecords();
        List<AllBotRecordDTO> allBotRecordDTOList = new ArrayList<>();

        if (botInfoList.isEmpty()) {
            return BaseResult.of(allBotDTO);
        }

        List<Long> botIdList = botInfoList.stream().map(BotInfo::getId).collect(Collectors.toList());
        List<DeptBotInfo> deptBotInfos = deptBotInfoMapper.selectList(
                Wrappers.<DeptBotInfo>lambdaQuery()
                        .in(DeptBotInfo::getBotId, botIdList)
        );
        Map<Long, List<DeptBotInfo>> botIdAndDeptBotInfoListMap =
                deptBotInfos.stream().collect(Collectors.groupingBy(DeptBotInfo::getBotId));
        List<String> deptIdList = deptBotInfos.stream().map(DeptBotInfo::getDeptId).collect(Collectors.toList());
        List<DepartmentInfo> departmentInfos = departmentInfoMapper.selectList(
                Wrappers.<DepartmentInfo>lambdaQuery().in(DepartmentInfo::getDeptId, deptIdList)
        ).stream().distinct().collect(Collectors.toList());

        List<UserBotInfo> userBotInfoList = userBotInfoMapper.selectList(
                Wrappers.<UserBotInfo>lambdaQuery()
                        .in(UserBotInfo::getBotId, botIdList)
                        .eq(UserBotInfo::getUserType, UserBotRoleEnum.USER_BOT_MANAGER.getCode())
        );
        Map<Long, List<UserBotInfo>> botIdAndUserBotInfoListMap =
                userBotInfoList.stream().distinct().collect(Collectors.groupingBy(UserBotInfo::getBotId));
        List<String> usernameList = userBotInfoList.stream()
                .map(UserBotInfo::getUsername)
                .distinct()
                .collect(Collectors.toList());
        List<EmployeeInfo> employeeInfos = getEmployeeInfos(usernameList);
        botInfoList.forEach(
                botInfo -> {
                    AllBotRecordDTO allBotRecordDTO = new AllBotRecordDTO();

                    BeanUtils.copyProperties(botInfo, allBotRecordDTO);

                    allBotRecordDTO.setBotTypeDesc(BotTypeEnum.getMsgByCode(botInfo.getBotType()));

                    setManageNameList(allBotRecordDTO, botInfo, botIdAndUserBotInfoListMap, employeeInfos);

                    setDeptDesc(botIdAndDeptBotInfoListMap, departmentInfos, botInfo, allBotRecordDTO);

                    setBotStatusDesc(botInfo, allBotRecordDTO);

                    setBotLarkStatusAndBotAvailableStatus(botInfo, allBotRecordDTO);
                    allBotRecordDTOList.add(allBotRecordDTO);
                }
        );

        allBotDTO.setCurrent(botInfoIPage.getCurrent());
        allBotDTO.setPages(botInfoIPage.getPages());
        allBotDTO.setSize(botInfoIPage.getSize());
        allBotDTO.setTotal(botInfoIPage.getTotal());
        allBotDTO.setRecords(allBotRecordDTOList);

        return BaseResult.of(allBotDTO);
    }

    private void setDeptDesc(Map<Long, List<DeptBotInfo>> botIdAndDeptBotInfoListMap,
                             List<DepartmentInfo> departmentInfos, BotInfo botInfo, AllBotRecordDTO allBotRecordDTO) {
        List<DeptBotInfo> deptBotInfoList = botIdAndDeptBotInfoListMap.get(botInfo.getId());
        if (deptBotInfoList == null || deptBotInfoList.isEmpty()) {
            allBotRecordDTO.setDeptDesc("-");
        } else {
            List<String> deptIdListTmp =
                    deptBotInfoList.stream().map(DeptBotInfo::getDeptId).collect(Collectors.toList());
            String deptDesc = departmentInfos.stream().filter(item -> deptIdListTmp.contains(item.getDeptId()))
                    .map(DepartmentInfo::getCnName).collect(Collectors.joining(","));
            allBotRecordDTO.setDeptDesc(deptDesc);
        }
    }

    private void setBotLarkStatusAndBotAvailableStatus(BotInfo botInfo, AllBotRecordDTO allBotRecordDTO) {
        if (BotTypeEnum.BOT_TYPE_LARK.getCode() == botInfo.getBotType()) {
            LarkRobotStatusEnum larkRobotStatusEnum = LarkRobotStatusEnum.getByCode(botInfo.getBotLarkStatus());
            // 飞书端机器人状态
            switch (larkRobotStatusEnum) {
                case LARK_ROBOT_INIT:
                    allBotRecordDTO.setBotLarkStatusDesc("待上线");
                    allBotRecordDTO.setBotAvailableStatus(LarkRobotStatusEnum.LARK_ROBOT_INIT.getCode());
                    break;
                case LARK_ROBOT_OFF:
                case LARK_ROBOT_NOT_EXIST:
                case LARK_ROBOT_APP_ID_ERROR:
                case LARK_ROBOT_APP_SECRET_ERROR:
                    allBotRecordDTO.setBotLarkStatusDesc("已停用");
                    allBotRecordDTO.setBotAvailableStatus(LarkRobotStatusEnum.LARK_ROBOT_OFF.getCode());
                    break;
                case LARK_ROBOT_ON:
                    allBotRecordDTO.setBotLarkStatusDesc("已启用");
                    allBotRecordDTO.setBotAvailableStatus(LarkRobotStatusEnum.LARK_ROBOT_ON.getCode());
                    break;
                default:
                    break;
            }
            allBotRecordDTO.setBotAvailableStatusDesc(allBotRecordDTO.getBotLarkStatusDesc());
        } else {
            allBotRecordDTO.setBotAvailableStatus(botInfo.getBotStatus());
            allBotRecordDTO.setBotAvailableStatusDesc(UmsRobotStatusEnum.getMsgByCode(botInfo.getBotStatus()));
        }

    }

    private void setBotStatusDesc(BotInfo botInfo, AllBotRecordDTO allBotRecordDTO) {
        UmsRobotStatusEnum umsRobotStatusEnum = UmsRobotStatusEnum.getByCode(botInfo.getBotStatus());
        // 消息平台机器人状态
        switch (umsRobotStatusEnum) {
            case UMS_ROBOT_INIT:
                allBotRecordDTO.setBotStatusDesc("待上线");
                break;
            case UMS_ROBOT_OFF:
                allBotRecordDTO.setBotStatusDesc("已停用");
                break;
            case UMS_ROBOT_ON:
                allBotRecordDTO.setBotStatusDesc("已启用");
                break;
            default:
                break;
        }
    }

    private void setManageNameList(AllBotRecordDTO allBotRecordDTO, BotInfo botInfo,
                                   Map<Long, List<UserBotInfo>> botIdAndUserBotInfoListMap,
                                   List<EmployeeInfo> employeeInfos) {
        List<UserBotInfo> userBotInfoList = botIdAndUserBotInfoListMap.get(botInfo.getId());
        if (userBotInfoList == null || userBotInfoList.isEmpty()) {
            allBotRecordDTO.setManageNameList(Collections.emptyList());
            allBotRecordDTO.setManageUsernameList(Collections.emptyList());
            allBotRecordDTO.setManageUserEmailList(Collections.emptyList());
        } else {
            List<String> manageUsernameList =
                    userBotInfoList.stream().map(UserBotInfo::getUsername).distinct().collect(Collectors.toList());
            allBotRecordDTO.setManageUsernameList(manageUsernameList);
            List<EmployeeInfo> managerInfos =
                    employeeInfos.stream().filter(item -> manageUsernameList.contains(item.getUsername())).distinct()
                            .collect(Collectors.toList());
            List<String> manageNameList = managerInfos.stream().map(EmployeeInfo::getName).collect(Collectors.toList());
            allBotRecordDTO.setManageNameList(manageNameList);
            List<String> manageUserEmailList =
                    managerInfos.stream().map(EmployeeInfo::getEmail).collect(Collectors.toList());
            allBotRecordDTO.setManageUserEmailList(manageUserEmailList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResult<BotInfo> addRobotInfo(BotDTO botDTO, String username) {
        // 原始botSecret 未加密的
        String botSecret = botDTO.getBotSecret();

        if (botDTO.getBotType() == BotTypeEnum.BOT_TYPE_EMAIL.getCode()) {
            List<BotInfo> existsEmailBotList =
                    botInfoMapper.selectList(Wrappers.<BotInfo>lambdaQuery().eq(BotInfo::getBotKey,
                            botDTO.getBotKey()).eq(BotInfo::getBotType, BotTypeEnum.BOT_TYPE_EMAIL.getCode()));
            if (CollectionUtils.isNotEmpty(existsEmailBotList)) {
                Set<String> emailTokenSet = existsEmailBotList.stream().map(x -> {
                            try {
                                return KeyCenterUtil.decrypt(x.getBotSecret());
                            } catch (DataProtectionException e) {
                                return StringUtils.EMPTY;
                            }
                        }).filter(StringUtils::isNotBlank)
                        .collect(Collectors.toSet());
                if (emailTokenSet.contains(botSecret)) {
                    return new BaseResult<>(ResultCode.BOT_EXIST_ERROR.getCode(), "系统中已存在当前机器人", null);
                }
            }
        } else {
            // 验证是否已经存在机器人
            BotInfo exist =
                    botInfoMapper.selectOne(Wrappers.<BotInfo>lambdaQuery().eq(BotInfo::getBotKey, botDTO.getBotKey()));

            if (Objects.nonNull(exist)) {
                return new BaseResult<>(ResultCode.BOT_EXIST_ERROR.getCode(), "系统中已存在当前机器人", exist);
            }
        }

        // 如果是飞书机器人
        if (botDTO.getBotType() == BotTypeEnum.BOT_TYPE_LARK.getCode()) {
            // 验证机器人消息
            RobotVerifyDTO robotVerifyDTO = this.verifyRobot(botDTO);

            if (Objects.isNull(robotVerifyDTO)) {
                return new BaseResult<>(LarkAppCode.LARK_APP_NOT_EXIST.getCode(), "二次验证失败", null);
            } else if (robotVerifyDTO.getBotLarkStatus() ==
                    LarkRobotStatusEnum.LARK_ROBOT_NOT_EXIST.getCode().intValue()) {
                return new BaseResult<>(LarkAppCode.LARK_APP_NOT_BOT.getCode(), "机器人不存在", null);
            } else if (robotVerifyDTO.getBotLarkStatus() ==
                    LarkRobotStatusEnum.LARK_ROBOT_APP_ID_ERROR.getCode().intValue()) {
                return new BaseResult<>(LarkAppCode.LARK_APP_ID_INVALID.getCode(), LARK_APP_ID_OR_SECRET_INVALID_STR,
                        null);
            } else if (robotVerifyDTO.getBotLarkStatus() ==
                    LarkRobotStatusEnum.LARK_ROBOT_APP_SECRET_ERROR.getCode().intValue()) {
                return new BaseResult<>(LarkAppCode.LARK_APP_SECRET_INVALID.getCode(),
                        LARK_APP_ID_OR_SECRET_INVALID_STR, null);
            }

            // 验证完之后,将机器人信息更新
            botDTO.setBotIconUrl(robotVerifyDTO.getAvatarUrl());
            botDTO.setBotName(robotVerifyDTO.getBotName());
            botDTO.setBotLarkStatus(robotVerifyDTO.getBotLarkStatus());
        }

        // 生成基本的机器人信息
        BotInfo baseBotInfo = generateBaseBotInfo(botDTO, username);

        // 通知消息引擎
        boolean notifySuccess = this.notifyEngineAddRobot(baseBotInfo, botSecret);
        // 通知消息引擎失败
        Assert.isTrue(notifySuccess, "创建机器人失败");

        if (Objects.nonNull(botDTO.getManageUsernameList())) {
            this.saveUserBotInfo(UserBotRoleEnum.USER_BOT_MANAGER.getCode(), botDTO.getManageUsernameList(),
                    baseBotInfo);
        }
        if (Objects.nonNull(botDTO.getDeptIds())) {
            this.saveDeptBotInfo(botDTO.getDeptIds(), baseBotInfo);
        }
        if (Objects.nonNull(botDTO.getUseUsernameList())) {
            this.saveUserBotInfo(UserBotRoleEnum.USER_BOT_USER.getCode(), botDTO.getUseUsernameList(), baseBotInfo);
        }
        if (Objects.nonNull(botDTO.getDeveloperList())) {
            this.saveUserBotInfo(UserBotRoleEnum.USER_BOT_USER.getCode(), botDTO.getUseUsernameList(), baseBotInfo);
        }

        return BaseResult.of(baseBotInfo);
    }

    @Override
    public BaseResult<RobotVerifyDTO> verify(BotDTO botDTO) {
        try {
            if (botDTO.getBotType() == BotTypeEnum.BOT_TYPE_LARK.getCode()) {
                String token = robotVerifyManager.getTenantAccessToken(botDTO.getBotKey(), botDTO.getBotSecret());
                RobotVerifyDTO robotVerifyDTO = robotVerifyManager.verifyRobot(token);

                if (robotVerifyDTO.getBotLarkStatus() ==
                        LarkRobotStatusEnum.LARK_ROBOT_NOT_EXIST.getCode().intValue()) {
                    return new BaseResult<>(LarkAppCode.LARK_APP_NOT_BOT.getCode(), "机器人不存在", robotVerifyDTO);
                } else if (robotVerifyDTO.getBotLarkStatus() ==
                        LarkRobotStatusEnum.LARK_ROBOT_APP_ID_ERROR.getCode().intValue()) {
                    return new BaseResult<>(LarkAppCode.LARK_APP_ID_INVALID.getCode(),
                            LARK_APP_ID_OR_SECRET_INVALID_STR, robotVerifyDTO);
                } else if (robotVerifyDTO.getBotLarkStatus() ==
                        LarkRobotStatusEnum.LARK_ROBOT_APP_SECRET_ERROR.getCode().intValue()) {
                    return new BaseResult<>(LarkAppCode.LARK_APP_SECRET_INVALID.getCode(),
                            LARK_APP_ID_OR_SECRET_INVALID_STR, robotVerifyDTO);
                }

                return BaseResult.of(robotVerifyDTO);
            } else {
                PostGuardBo postGuardBo = postGuardValidate.getPostGuardBoWithToken(botDTO.getBotSecret());
                if (Objects.isNull(postGuardBo)) {
                    return new BaseResult<>(ResultCode.PARAM_ERROR.getCode(), "输入的token无效");
                }
                if (!postGuardBo.getFromMailAccount().equals(botDTO.getBotKey())) {
                    return new BaseResult<>(ResultCode.PARAM_ERROR.getCode(), "发送邮件和token不匹配");
                }

                RobotVerifyDTO robotVerifyDTO = new RobotVerifyDTO();
                String emailName = postGuardBo.getFromMailAccountDisplayName();
                if (StringUtils.isBlank(emailName)) {
                    emailName = postGuardBo.getSysName();
                }
                robotVerifyDTO.setBotName(emailName);
                return BaseResult.of(robotVerifyDTO);
            }
        } catch (Exception e) {
            log.error("机器人验证失败,原因:[{}]", e.getMessage());
            return new BaseResult<>(ResultCode.PARAM_ERROR.getCode(), e.getMessage(), new RobotVerifyDTO());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResult<BotInfo> stopBot(BotDTO botDTO, String username) {
        // notify engine
        BotInfo query = botInfoMapper.selectById(botDTO.getId());

        String originBotSecret;
        try {
            originBotSecret = KeyCenterUtil.decrypt(query.getBotSecret());
        } catch (DataProtectionException e) {
            throw new RuntimeException("停止机器人时keycenter解密失败", e);
        }

        boolean notifySuccess = this.notifyEngineAlertRobot(query, BotStatus.BOT_STATUS_OFF_VALUE, originBotSecret);
        Assert.isTrue(notifySuccess, "通知消息引擎失败");

        BotInfo botInfo = new BotInfo();
        botInfo.setId(botDTO.getId());
        botInfo.setBotStatus(UmsRobotStatusEnum.UMS_ROBOT_OFF.getCode());
        if (query.getBotType() == BotTypeEnum.BOT_TYPE_LARK.getCode()) {
            botInfo.setBotLarkStatus(LarkRobotStatusEnum.LARK_ROBOT_OFF.getCode());
        }
        botInfoMapper.update(botInfo,
                Wrappers.<BotInfo>lambdaUpdate()
                        .eq(BotInfo::getId, botInfo.getId())
        );

        this.stopTaskByBot(botDTO.getId(), username);

        return BaseResult.of();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResult<BotInfo> startBot(BotDTO botDTO, String username) {
        // notify engine
        BotInfo query = botInfoMapper.selectById(botDTO.getId());

        BotInfo botInfo = new BotInfo();
        botInfo.setId(botDTO.getId());
        botInfo.setBotStatus(UmsRobotStatusEnum.UMS_ROBOT_ON.getCode());

        // 重新拉取飞书端机器人状态
        if (query.getBotType() == BotTypeEnum.BOT_TYPE_LARK.getCode()) {
            Integer needUpdateStatus = syncBotAfterStartBot(query.getBotKey(), query.getBotSecret());
            if (Objects.nonNull(needUpdateStatus)) {
                botInfo.setBotLarkStatus(needUpdateStatus.byteValue());
            } else {
                botInfo.setBotLarkStatus(LarkRobotStatusEnum.LARK_ROBOT_OFF.getCode());
            }
            // 如果飞书端是待启用或者已下线就提示
            if (Objects.isNull(needUpdateStatus) ||
                    needUpdateStatus == LarkRobotStatusEnum.LARK_ROBOT_INIT.getCode().intValue()
                    || needUpdateStatus == LarkRobotStatusEnum.LARK_ROBOT_OFF.getCode().intValue()
                    || needUpdateStatus == LarkRobotStatusEnum.LARK_ROBOT_NOT_EXIST.getCode().intValue()
            ) {
                return new BaseResult<>(ResultCode.MEDIA_UPDATE_ERROR.getCode(),
                        "飞书端机器人状态为待上线状态，请先在飞书端上线机器人");
            }
        }

        String originBotSecret;
        try {
            originBotSecret = KeyCenterUtil.decrypt(query.getBotSecret());
        } catch (DataProtectionException e) {
            throw new RuntimeException("开启机器人时keycenter解密失败", e);
        }

        boolean notifySuccess = this.notifyEngineAlertRobot(query, BotStatus.BOT_STATUS_ON_VALUE, originBotSecret);
        Assert.isTrue(notifySuccess, "通知消息引擎失败");

        botInfoMapper.update(botInfo,
                Wrappers.<BotInfo>lambdaUpdate()
                        .eq(BotInfo::getId, botInfo.getId())
        );

        List<Long> taskIds = this.startTaskByBot(botDTO.getId(), username);

        if (!taskIds.isEmpty()) {
            taskIds.forEach(
                    taskId -> {
                        ProcessInstanceInfo processInstanceInfo = processInstanceInfoMapper.selectOne(
                                Wrappers.<ProcessInstanceInfo>lambdaQuery()
                                        .eq(ProcessInstanceInfo::getStatus,
                                                ProcessStatusConstant.PROCESS_STATUS_APPROVAL)
                                        .eq(ProcessInstanceInfo::getBizId, taskId)
                        );
                        if (Objects.nonNull(processInstanceInfo)) {
                            List<ProcessTaskInfo> list = processTaskInfoMapper.selectList(
                                    Wrappers.<ProcessTaskInfo>lambdaQuery()
                                            .eq(ProcessTaskInfo::getApproveResultValue, ProcessStatusConstant.NO_ACTION)
                                            .eq(ProcessTaskInfo::getInstanceId, processInstanceInfo.getId())
                            );
                            for (ProcessTaskInfo processTaskInfo : list) {
                                ProcessTaskCreateEvent processTaskCreateEvent = new ProcessTaskCreateEvent(this);
                                processTaskCreateEvent.setProcessId("messageRelease");
                                processTaskCreateEvent.setProcessInstanceId(processInstanceInfo.getId());
                                processTaskCreateEvent.setTaskId(processTaskInfo.getTaskId());
                                processTaskCreateEvent.setTaskInfoId(processTaskInfo.getId());
                                processTaskCreateEvent.setApproverId(processTaskInfo.getApproverId());
                                processTaskCreateEvent.setBizId(taskId);
                                processTaskCreateEvent.setProcessInstanceStatus(processInstanceInfo.getStatus());
                                applicationEventPublisher.publishEvent(processTaskCreateEvent);
                            }
                        }
                    }
            );
        }

        return BaseResult.of();
    }

    @Override
    public BaseResult<BotInfo> updateBot(BotDTO botDTO, String username) {

        // 原始botSecret 未加密的
        String originBotSecret = botDTO.getBotSecret();
        BotInfo engineAlterBot = new BotInfo();
        BeanUtils.copyProperties(botDTO, engineAlterBot);

        boolean notifySuccess =
                this.notifyEngineAlertRobot(engineAlterBot, BotStatus.BOT_STATUS_ON_VALUE, originBotSecret);
        Assert.isTrue(notifySuccess, "通知消息引擎失败");

        BotInfo baseBotInfo = this.updateBaseBotInfo(botDTO, username);

        if (Objects.nonNull(botDTO.getManageUsernameList())) {
            this.deleteUserBotInfo(UserBotRoleEnum.USER_BOT_MANAGER.getCode(), botDTO.getManageUsernameList(),
                    baseBotInfo);
            this.saveUserBotInfo(UserBotRoleEnum.USER_BOT_MANAGER.getCode(), botDTO.getManageUsernameList(),
                    baseBotInfo);
        }
        if (Objects.nonNull(botDTO.getDeptIds())) {
            this.deleteDeptBotInfo(botDTO.getDeptIds(), baseBotInfo);
            this.saveDeptBotInfo(botDTO.getDeptIds(), baseBotInfo);
        }
        if (Objects.nonNull(botDTO.getUseUsernameList())) {
            this.deleteUserBotInfo(UserBotRoleEnum.USER_BOT_USER.getCode(), botDTO.getManageUsernameList(),
                    baseBotInfo);
            this.saveUserBotInfo(UserBotRoleEnum.USER_BOT_USER.getCode(), botDTO.getUseUsernameList(), baseBotInfo);
        }
        if (Objects.nonNull(botDTO.getDeveloperList())) {
            this.deleteUserBotInfo(UserBotRoleEnum.USER_BOT_DEVELOPER.getCode(), botDTO.getManageUsernameList(),
                    baseBotInfo);
            this.saveUserBotInfo(UserBotRoleEnum.USER_BOT_DEVELOPER.getCode(), botDTO.getUseUsernameList(),
                    baseBotInfo);
        }
        return BaseResult.of(baseBotInfo);
    }

    @Override
    public BaseResult<MyBotDTO> myBot(String username) {

        // 终极优化：提取所有公共查询到上层，避免重复的数据库查询
        TaskDataContext taskDataContext = getTaskDataContext(username);

        // 获取机器人总览
        BotOverviewDTO botOverviewDTO = this.getBotOverview(taskDataContext);

        // 获取每个机器人的推送情况
        List<BotPushDetailDTO> botPushDetailDTO = this.getBotPushDetail(taskDataContext);

        MyBotDTO myBotDTO = new MyBotDTO();
        myBotDTO.setOverview(botOverviewDTO);

        List<BotPushDetailDTO> larkBotPushDetail = botPushDetailDTO.stream()
                .filter(botPushDetail -> botPushDetail.getBotType() == BotTypeEnum.BOT_TYPE_LARK.getCode())
                .collect(Collectors.toList());

        List<BotPushDetailDTO> smsBotPushDetail = botPushDetailDTO.stream()
                .filter(botPushDetail -> botPushDetail.getBotType() == BotTypeEnum.BOT_TYPE_SMS.getCode())
                .collect(Collectors.toList());

        List<BotPushDetailDTO> emailBotPushDetail = botPushDetailDTO.stream()
                .filter(botPushDetail -> botPushDetail.getBotType() == BotTypeEnum.BOT_TYPE_EMAIL.getCode())
                .collect(Collectors.toList());

        myBotDTO.setBotList(botPushDetailDTO);
        myBotDTO.setLarkBotList(larkBotPushDetail);
        myBotDTO.setEmailBotList(emailBotPushDetail);
        myBotDTO.setSmsBotList(smsBotPushDetail);

        return BaseResult.of(myBotDTO);
    }

    @Override
    public BaseResult<BotUseDetailDTO> queryBotUseByPage(Long page, Long size, Long botId, UserBO userBO) {

        // page
        Page<TaskInfo> dataPage = new Page<>(page, size);

        BotUseDetailDTO botUseDetailDTO = new BotUseDetailDTO();

        LambdaQueryWrapper<TaskInfo> lambdaQuery = Wrappers.lambdaQuery();

        BotInfo botInfo = botInfoMapper.selectById(botId);
        if (!userRoleManager.isSuperAdmin(userBO.getUsername())) {
            if (BotRangeScopeEnum.PUBLIC.getCode() == botInfo.getBotRangeScope()) {
                lambdaQuery.and(
                        wrapper -> wrapper.eq(TaskInfo::getCreateUsername, userBO.getUsername())
                );
            } else {
                List<UserBotInfo> userBotInfoList = userBotInfoMapper.selectList(
                        Wrappers.<UserBotInfo>lambdaQuery().eq(UserBotInfo::getBotId, botId)
                                .eq(UserBotInfo::getUsername, userBO.getUsername())
                );

                List<UserBotInfo> manageUserBotInfoList = userBotInfoList.stream()
                        .filter(record -> record.getUserType() == UserBotRoleEnum.USER_BOT_MANAGER.getCode())
                        .collect(Collectors.toList());

                if (userBotInfoList.isEmpty()) {
                    setBotUseDetailToNull(page, size, botUseDetailDTO);
                    return BaseResult.of(botUseDetailDTO);
                }

                if (manageUserBotInfoList.isEmpty()) {
                    lambdaQuery.and(
                            wrapper -> wrapper.eq(TaskInfo::getCreateUsername, userBO.getUsername())
                    );
                }
            }

        }

        List<Long> taskIdList = getTaskIdList(botId, botInfo);
        if (taskIdList.isEmpty()) {
            setBotUseDetailToNull(page, size, botUseDetailDTO);
            return BaseResult.of(botUseDetailDTO);
        }

        lambdaQuery.and(
                wrapper -> wrapper.in(TaskInfo::getId, taskIdList)
        );

        lambdaQuery.and(
                wrapper -> wrapper.ne(TaskInfo::getTaskStatus, TaskStatusEnum.DRAFT.getCode())
                        .ne(TaskInfo::getTaskStatus, TaskStatusEnum.CANCEL.getCode())
                        .ne(TaskInfo::getTaskStatus, TaskStatusEnum.APPROVE_BACK.getCode())
        );

        lambdaQuery.orderByDesc(TaskInfo::getPublishTime);

        IPage<TaskInfo> taskInfoIPage = taskInfoMapper.selectPage(dataPage, lambdaQuery);

        List<TaskInfo> taskInfoList = taskInfoIPage.getRecords();

        if (taskInfoList.isEmpty()) {
            setBotUseDetailToNull(page, size, botUseDetailDTO);
            return BaseResult.of(botUseDetailDTO);
        }

        List<String> extraIdList = taskInfoList.stream().map(TaskInfo::getExtraId).collect(Collectors.toList());

        List<Long> taskIdListTmp = taskInfoList.stream().map(TaskInfo::getId).collect(Collectors.toList());
        List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery().in(PublishScopeInfo::getTaskId, taskIdListTmp)
        );

        List<BotUseDetailRecordDTO> botUseDetailRecordDTOList = new ArrayList<>();

        MessageNumberAndTimeResponse messageNumberByExtraIdBatch =
                messageGrpcClient.getMessageNumberByExtraIdBatch(extraIdList);
        List<String> createUsernameList =
                taskInfoList.stream().map(TaskInfo::getCreateUsername).distinct().collect(Collectors.toList());
        List<EmployeeInfo> employeeInfos = getEmployeeInfos(createUsernameList);
        Map<String, EmployeeInfo> usernameAndEmployeeInfoMap = employeeInfos.stream()
                .collect(Collectors.toMap(EmployeeInfo::getUsername, Function.identity(), (v1, v2) -> v2));
        for (TaskInfo taskInfo : taskInfoList) {
            BotUseDetailRecordDTO botUseDetailRecordDTO = new BotUseDetailRecordDTO();
            botUseDetailRecordDTO.setTaskId(taskInfo.getId());
            botUseDetailRecordDTO.setTitleCn(taskInfo.getTitleCn());
            botUseDetailRecordDTO.setTitleEn(taskInfo.getTitleEn());
            String createUsername = taskInfo.getCreateUsername();
            botUseDetailRecordDTO.setCreateUsername(createUsername);
            setCreateNameAndEmail(usernameAndEmployeeInfoMap, botUseDetailRecordDTO, createUsername);
            botUseDetailRecordDTO.setPublishScope(taskInfo.getPublishScope());
            setPublishScopeDesc(publishScopeInfoList, taskInfo, botUseDetailRecordDTO);
            botUseDetailRecordDTO.setType("-");
            botUseDetailRecordDTO.setPublishTime(taskInfo.getPublishTime());
            botUseDetailRecordDTO.setTaskStatus(taskInfo.getTaskStatus());
            botUseDetailRecordDTO.setTaskStatusDesc(TaskStatusEnum.getMsgByCode(taskInfo.getTaskStatus()));
            List<MessageNumberAndTime> messageNumberAndTimeListList =
                    messageNumberByExtraIdBatch.getMessageNumberAndTimeListList();
            messageNumberAndTimeListList.forEach(
                    messageNumberAndTime -> {
                        if (taskInfo.getExtraId().equals(messageNumberAndTime.getExtraId())) {
                            PushResultDTO pushResultDTO = new PushResultDTO();
                            List<PublishScopeInfo> publishScopeInfos = publishScopeInfoList.stream()
                                    .filter(scopeInfo -> scopeInfo.getTaskId().equals(taskInfo.getId()))
                                    .collect(Collectors.toList());
                            setAllCount(messageNumberAndTime, pushResultDTO, publishScopeInfos);
                            pushResultDTO.setPushCount(messageNumberAndTime.getPushCount());
                            pushResultDTO.setTodoCount(messageNumberAndTime.getTodoCount());
                            botUseDetailRecordDTO.setPushResult(pushResultDTO);
                            botUseDetailRecordDTO.setCostTime(messageNumberAndTime.getCostTime());
                            botUseDetailRecordDTO.setIsSuccess(
                                    pushResultDTO.getAllCount().equals(pushResultDTO.getPushCount()));
                            judgeRealTaskStatus(taskInfo, botUseDetailRecordDTO);
                        }
                    }
            );
            botUseDetailRecordDTOList.add(botUseDetailRecordDTO);
        }

        botUseDetailDTO.setCurrent(taskInfoIPage.getCurrent());
        botUseDetailDTO.setPages(taskInfoIPage.getPages());
        botUseDetailDTO.setSearchCount(taskInfoIPage.isSearchCount());
        botUseDetailDTO.setSize(taskInfoIPage.getSize());
        botUseDetailDTO.setTotal(taskInfoIPage.getTotal());
        botUseDetailDTO.setRecords(botUseDetailRecordDTOList);

        return BaseResult.of(botUseDetailDTO);
    }

    private List<Long> getTaskIdList(Long botId, BotInfo botInfo) {
        if (botInfo.getBotType() == BotTypeEnum.BOT_TYPE_LARK.getCode()) {
            List<LarkTaskInfo> larkTaskInfoList = larkTaskInfoMapper.selectList(
                    Wrappers.<LarkTaskInfo>lambdaQuery().eq(LarkTaskInfo::getBotId, botId)
            );

            if (larkTaskInfoList.isEmpty()) {
                return Collections.emptyList();
            }

            return larkTaskInfoList.stream().map(LarkTaskInfo::getTaskId).collect(Collectors.toList());
        } else if (botInfo.getBotType() == BotTypeEnum.BOT_TYPE_EMAIL.getCode()) {
            List<EmailTaskInfo> emailTaskInfoList = emailTaskInfoMapper.selectList(
                    Wrappers.<EmailTaskInfo>lambdaQuery().eq(EmailTaskInfo::getBotId, botId)
            );

            if (emailTaskInfoList.isEmpty()) {
                return Collections.emptyList();
            }

            return emailTaskInfoList.stream().map(EmailTaskInfo::getTaskId).collect(Collectors.toList());
        } else if (botInfo.getBotType() == BotTypeEnum.BOT_TYPE_SMS.getCode()) {
            List<SmsTaskInfo> smsTaskInfoList = smsTaskInfoMapper.selectList(
                    Wrappers.<SmsTaskInfo>lambdaQuery().eq(SmsTaskInfo::getBotId, botId)
            );

            if (smsTaskInfoList.isEmpty()) {
                return Collections.emptyList();
            }

            return smsTaskInfoList.stream().map(SmsTaskInfo::getTaskId).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private void judgeRealTaskStatus(TaskInfo taskInfo, BotUseDetailRecordDTO botUseDetailRecordDTO) {
        if (taskInfo.getTaskStatus() == TaskStatusEnum.PUBLISH_SUCCESS.getCode() &&
                taskInfo.getTaskEngineStatus() == TaskStatusEnum.PUBLISHING.getCode()) {
            botUseDetailRecordDTO.setTaskStatus(TaskStatusEnum.PUBLISHING.getCode());
            botUseDetailRecordDTO.setTaskStatusDesc(TaskStatusEnum.getMsgByCode(botUseDetailRecordDTO.getTaskStatus()));
        } else if (taskInfo.getTaskEngineStatus() == TaskStatusEnum.PUBLISH_SUCCESS.getCode()) {
            botUseDetailRecordDTO.setTaskStatus(TaskStatusEnum.PUBLISH_SUCCESS.getCode());
            botUseDetailRecordDTO.setTaskStatusDesc(TaskStatusEnum.getMsgByCode(botUseDetailRecordDTO.getTaskStatus()));
        }
    }

    private void setAllCount(MessageNumberAndTime messageNumberAndTime, PushResultDTO pushResultDTO,
                             List<PublishScopeInfo> publishScopeInfos) {
        if (!publishScopeInfos.isEmpty()) {
            long allCount = publishScopeInfos.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).orElse(0L);
            pushResultDTO.setAllCount(allCount);
        } else {
            pushResultDTO.setAllCount(messageNumberAndTime.getAllCount());
        }
    }

    private void setPublishScopeDesc(List<PublishScopeInfo> publishScopeInfoList, TaskInfo taskInfo,
                                     BotUseDetailRecordDTO botUseDetailRecordDTO) {
        if (taskInfo.getPublishScope().equals(PublishScopeEnum.DEPT_PUSH.getCode())) {
            List<String> deptIdList = publishScopeInfoList.stream()
                    .filter(record -> record.getTaskId().equals(taskInfo.getId()))
                    .map(PublishScopeInfo::getScopeKey).distinct().collect(Collectors.toList());
            String deptName = deptManager.getFirstLevelDeptString(deptIdList);
            botUseDetailRecordDTO.setPublishScopeDesc(deptName);
        } else {
            botUseDetailRecordDTO.setPublishScopeDesc(PublishScopeEnum.getMsgByCode(taskInfo.getPublishScope()));
        }
    }

    private void setCreateNameAndEmail(Map<String, EmployeeInfo> usernameAndEmployeeInfoMap,
                                       BotUseDetailRecordDTO botUseDetailRecordDTO, String createUsername) {
        if (StringUtils.isNotBlank(createUsername)) {
            EmployeeInfo employeeInfo = usernameAndEmployeeInfoMap.get(createUsername);
            if (Objects.isNull(employeeInfo)) {
                botUseDetailRecordDTO.setCreateName(null);
                botUseDetailRecordDTO.setCreateUserEmail(null);
            } else {
                botUseDetailRecordDTO.setCreateName(employeeInfo.getName());
                botUseDetailRecordDTO.setCreateUserEmail(employeeInfo.getEmail());
            }
        }
    }

    private void setBotUseDetailToNull(Long page, Long size, BotUseDetailDTO botUseDetailDTO) {
        botUseDetailDTO.setSearchCount(true);
        botUseDetailDTO.setTotal(0L);
        botUseDetailDTO.setPages(0L);
        botUseDetailDTO.setCurrent(page);
        botUseDetailDTO.setSize(size);
        botUseDetailDTO.setRecords(Collections.emptyList());
    }

    @Override
    public BaseResult<QueryBotDTO> queryBotById(Long id, String username) {
        QueryBotDTO queryBotDTO = new QueryBotDTO();
        BotInfo botInfo = botInfoMapper.selectById(id);
        queryBotDTO.setId(botInfo.getId());
        queryBotDTO.setBotKey(botInfo.getBotKey());
        queryBotDTO.setBotName(botInfo.getBotName());
        queryBotDTO.setBotLarkStatus(botInfo.getBotLarkStatus());
        queryBotDTO.setBotStatus(botInfo.getBotStatus());
        queryBotDTO.setCalendarStatus(botInfo.getCalendarStatus());
        queryBotDTO.setCreateTime(botInfo.getCreateTime());
        queryBotDTO.setBotBizId(botInfo.getBotBizId());
        queryBotDTO.setNotifyUrl(botInfo.getNotifyUrl());
        queryBotDTO.setBotEncryptKey(botInfo.getBotEncryptKey());
        queryBotDTO.setBotVerificationToken(botInfo.getBotVerificationToken());
        queryBotDTO.setBotType(botInfo.getBotType());
        queryBotDTO.setBotTypeDesc(BotTypeEnum.getMsgByCode(botInfo.getBotType()));
        queryBotDTO.setBotRangeScope(botInfo.getBotRangeScope());
        queryBotDTO.setBotTenant(botInfo.getBotTenant());

        List<UserBotInfo> userBotInfoList = userBotInfoMapper.selectList(
                Wrappers.<UserBotInfo>lambdaQuery()
                        .eq(UserBotInfo::getBotId, id)
                        .eq(UserBotInfo::getUsername, username)
        );

        if (userBotInfoList.isEmpty()) {
            queryBotDTO.setUserType(UserBotRoleEnum.USER_BOT_NO.getCode());
            if (userRoleManager.isSuperAdmin(username)) {
                queryBotDTO.setUserType(UserBotRoleEnum.USER_BOT_MANAGER.getCode());
                try {
                    queryBotDTO.setBotSecret(KeyCenterUtil.decrypt(botInfo.getBotSecret()));
                } catch (DataProtectionException e) {
                    log.info("botName为[{}]的机器人密钥解密失败", botInfo.getBotName(), e);
                }
            }
        } else {
            queryBotDTO.setUserType(UserBotRoleEnum.USER_BOT_USER.getCode());
            userBotInfoList.forEach(
                    userBotInfo -> {
                        if (userBotInfo.getUserType() == UserBotRoleEnum.USER_BOT_MANAGER.getCode() ||
                                userRoleManager.isSuperAdmin(username)) {
                            queryBotDTO.setUserType(UserBotRoleEnum.USER_BOT_MANAGER.getCode());
                            try {
                                queryBotDTO.setBotSecret(KeyCenterUtil.decrypt(botInfo.getBotSecret()));
                            } catch (DataProtectionException e) {
                                log.info("botName为[{}]的机器人密钥解密失败", botInfo.getBotName(), e);
                            }
                        }
                    }
            );
        }

        setUseUsernameAndManageUsernameList(id, queryBotDTO);

        List<DeptBotInfo> deptBotInfoList = deptBotInfoMapper.selectList(
                Wrappers.<DeptBotInfo>lambdaQuery().eq(DeptBotInfo::getBotId, id)
        );

        List<String> deptIds = deptBotInfoList.stream().map(DeptBotInfo::getDeptId).distinct()
                .collect(Collectors.toList());

        if (deptIds.isEmpty()) {
            queryBotDTO.setDeptIds(Collections.singletonList("-"));
            queryBotDTO.setDeptCnNameList(Collections.singletonList("-"));
        } else {
            queryBotDTO.setDeptIds(deptIds);
            List<String> deptCnNameList = departmentInfoMapper.selectList(
                    Wrappers.<DepartmentInfo>lambdaQuery().in(DepartmentInfo::getDeptId, deptIds)
            ).stream().distinct().map(DepartmentInfo::getCnName).collect(Collectors.toList());
            queryBotDTO.setDeptCnNameList(deptCnNameList);
        }

        return BaseResult.of(queryBotDTO);
    }

    @Override
    public BaseResult<List<BotDTO>> getMyLarkBotList(String username) {
        List<BotDTO> botDTOList = new ArrayList<>();

        List<BotInfo> botInfoList = selectMyBotList(username, BotTypeEnum.BOT_TYPE_LARK.getCode());
        if (botInfoList.isEmpty()) {
            return BaseResult.of(botDTOList);
        }

        botInfoList.forEach(
                item -> {
                    BotDTO botDTO = new BotDTO();
                    botDTO.setBotId(item.getId());
                    botDTO.setBotKey(item.getBotKey());
                    botDTO.setBotName(item.getBotName());
                    botDTO.setCalendarStatus(item.getCalendarStatus());
                    botDTO.setBotIconUrl(item.getBotIconUrl());
                    botDTO.setBotBizId(item.getBotBizId());
                    botDTO.setBotTenant(item.getBotTenant());
                    botDTOList.add(botDTO);
                }
        );
        return BaseResult.of(botDTOList);
    }

    @Override
    public BaseResult<Object> getMyEmailBotList(String username) {
        List<EmailRobotBO> list = new ArrayList<>();

        List<BotInfo> botInfoList = selectMyBotList(username, BotTypeEnum.BOT_TYPE_EMAIL.getCode());
        if (botInfoList.isEmpty()) {
            return BaseResult.of(list);
        }

        botInfoList.forEach(
                item -> {
                    EmailRobotBO emailRobot = new EmailRobotBO();
                    emailRobot.setBotBizId(item.getBotBizId());
                    emailRobot.setSenderName(item.getBotName());
                    emailRobot.setSender(item.getBotKey());
                    emailRobot.setBotId(item.getId());
                    list.add(emailRobot);
                }
        );

        Map<String, Object> data = new HashMap<>(1);
        data.put("list", list);

        return BaseResult.of(data);
    }

    @Override
    public BaseResult<Object> getMyMessageBotList(String username) {
        List<SmsRobotBO> list = new ArrayList<>();
        List<BotInfo> botInfoList = selectMyBotList(username, BotTypeEnum.BOT_TYPE_SMS.getCode());
        if (botInfoList.isEmpty()) {
            return BaseResult.of(list);
        }

        botInfoList.forEach(
                item -> {
                    SmsRobotBO smsRobot = new SmsRobotBO();
                    smsRobot.setBotBizId(item.getBotBizId());
                    smsRobot.setSignName(item.getBotName());
                    smsRobot.setSignCode(item.getBotKey());
                    smsRobot.setBotId(item.getId());
                    list.add(smsRobot);
                }
        );

        Map<String, Object> data = new HashMap<>(1);
        data.put("list", list);

        return BaseResult.of(data);
    }

    private List<BotInfo> selectMyBotList(String username, byte code) {
        List<UserBotInfo> userBotInfoList = userBotInfoMapper.selectList(
                Wrappers.<UserBotInfo>lambdaQuery()
                        .eq(UserBotInfo::getUsername, username)
        );

        List<BotInfo> botInfoList = new ArrayList<>();
        if (!userBotInfoList.isEmpty()) {
            List<Long> botIds = userBotInfoList.stream()
                    .map(UserBotInfo::getBotId)
                    .distinct()
                    .collect(Collectors.toList());

            botInfoList = botInfoMapper.selectList(
                    Wrappers.<BotInfo>lambdaQuery()
                            .in(BotInfo::getId, botIds)
                            .eq(BotInfo::getBotStatus, UmsRobotStatusEnum.UMS_ROBOT_ON.getCode())
                            .eq(BotInfo::getBotType, code)
            );
        }

        List<BotInfo> publicBotInfoList = botInfoMapper.selectList(
                Wrappers.<BotInfo>lambdaQuery()
                        .eq(BotInfo::getBotStatus, UmsRobotStatusEnum.UMS_ROBOT_ON.getCode())
                        .eq(BotInfo::getBotType, code)
                        .eq(BotInfo::getBotRangeScope, BotRangeScopeEnum.PUBLIC.getCode())
        );

        botInfoList.addAll(publicBotInfoList);
        return botInfoList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public void syncLarkBotStatus() {
        List<BotInfo> botInfos = botInfoMapper.selectList(
                Wrappers.<BotInfo>lambdaQuery()
                        .eq(BotInfo::getBotType, BotTypeEnum.BOT_TYPE_LARK.getCode())
                        .eq(BotInfo::getBotStatus, UmsRobotStatusEnum.UMS_ROBOT_ON.getCode())
        );
        botInfos.forEach(
                botInfo -> {
                    try {
                        log.info("当前开始同步机器人[{}]，加密secret为[{}]", botInfo.getBotKey(),
                                botInfo.getBotSecret());
                        // 先解密Boot Secret
                        String botSecret = KeyCenterUtil.decrypt(botInfo.getBotSecret());
                        // 进行验证，获取最新的机器人信息
                        String token = robotVerifyManager.getTenantAccessToken(botInfo.getBotKey(), botSecret);
                        RobotVerifyDTO robotVerifyDTO = robotVerifyManager.verifyRobot(token);

                        log.info("机器人的基本信息[{}],获取的验证信息[{}]", JsonUtils.toJson(botInfo),
                                JsonUtils.toJson(robotVerifyDTO));

                        if (robotVerifyDTO.getBotLarkStatus() ==
                                LarkRobotStatusEnum.LARK_ROBOT_APP_ID_ERROR.getCode().intValue()) {
                            throw new RobotVerifyException(
                                    "同步飞书机器人状态失败，机器人验证失败, 原因: 应用的APP_ID无效");
                        } else if (robotVerifyDTO.getBotLarkStatus() ==
                                LarkRobotStatusEnum.LARK_ROBOT_APP_SECRET_ERROR.getCode().intValue()) {
                            throw new RobotVerifyException(
                                    "同步飞书机器人状态失败，机器人验证失败, 原因: 应用的APP_SECRET无效");
                        }

                        BotInfo needUpdateBot = new BotInfo();
                        boolean needUpdate = false;

                        // 1.如果飞书端关掉机器人,就直接关闭机器人
                        if (robotVerifyDTO.getBotLarkStatus() ==
                                LarkRobotStatusEnum.LARK_ROBOT_NOT_EXIST.getCode().intValue()) {
                            needUpdate = true;

                            needUpdateBot.setId(botInfo.getId());
                            needUpdateBot.setBotStatus(UmsRobotStatusEnum.UMS_ROBOT_OFF.getCode());
                            needUpdateBot.setBotLarkStatus(robotVerifyDTO.getBotLarkStatus());
                        } else {
                            // 2. 如果飞书没有关闭机器人, 判断是否需要更新, 1.首先判断状态，2.判断机器人名称, 3.判断头像是否改变
                            if ((botInfo.getBotLarkStatus().intValue() != robotVerifyDTO.getBotLarkStatus().intValue())
                                    || !botInfo.getBotName().equals(robotVerifyDTO.getBotName())
                                    || !botInfo.getBotIconUrl().equals(robotVerifyDTO.getAvatarUrl())
                            ) {
                                needUpdate = true;

                                needUpdateBot.setId(botInfo.getId());
                                needUpdateBot.setBotName(robotVerifyDTO.getBotName());
                                needUpdateBot.setBotIconUrl(robotVerifyDTO.getAvatarUrl());
                                needUpdateBot.setBotLarkStatus(robotVerifyDTO.getBotLarkStatus());
                                if (robotVerifyDTO.getBotLarkStatus() ==
                                        LarkRobotStatusEnum.LARK_ROBOT_OFF.getCode().intValue()) {
                                    needUpdateBot.setBotStatus(UmsRobotStatusEnum.UMS_ROBOT_OFF.getCode());
                                }
                            }
                        }

                        if (needUpdate) {
                            log.info("当前正在更新机器人[{}], 更新信息为[{}]", botInfo.getBotKey(),
                                    JsonUtils.toJson(needUpdateBot));
                            botInfoMapper.update(needUpdateBot,
                                    Wrappers.<BotInfo>lambdaUpdate()
                                            .eq(BotInfo::getId, needUpdateBot.getId())
                            );
                        }

                    } catch (DataProtectionException e) {
                        log.warn("同步飞书机器人状态失败，key center 解密失败", e);
                    } catch (RobotVerifyException e) {
                        log.warn("同步飞书机器人状态失败，机器人验证失败", e);
                    } catch (Exception e) {
                        log.error("同步飞ID为[{}]书机器人状态失败", botInfo.getId(), e);
                    }

                    log.info("同步机器人[{}]结束", botInfo.getBotKey());

                    try {
                        Thread.sleep(800);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
        );
    }

    @Override
    public void updateBotCalendarStatus(String appId) {

        BotInfo botInfo = new BotInfo();
        botInfo.setUpdateTime(System.currentTimeMillis());
        botInfo.setCalendarStatus(CalendarEnableEnum.ON.getCode());

        botInfoMapper.update(botInfo, Wrappers.<BotInfo>lambdaUpdate().eq(BotInfo::getBotKey, appId));
    }

    private void setUseUsernameAndManageUsernameList(Long id, QueryBotDTO queryBotDTO) {
        List<UserBotInfo> userBotInfoByIdList = userBotInfoMapper.selectList(
                Wrappers.<UserBotInfo>lambdaQuery().eq(UserBotInfo::getBotId, id)
        );
        List<String> useUsernameList = userBotInfoByIdList.stream()
                .filter(item -> item.getUserType() == UserBotRoleEnum.USER_BOT_USER.getCode())
                .map(UserBotInfo::getUsername).distinct().collect(Collectors.toList());

        if (!useUsernameList.isEmpty()) {
            List<EmployeeInfo> employeeInfoList = getEmployeeInfos(useUsernameList);
            List<UsernameAndNameBO> usernameAndNameBOS = employeeInfoList.stream()
                    .map(employeeInfo -> new UsernameAndNameBO(employeeInfo.getUsername(), employeeInfo.getName()))
                    .collect(Collectors.toList());
            queryBotDTO.setUseUsernameList(usernameAndNameBOS);
        } else {
            queryBotDTO.setUseUsernameList(Collections.singletonList(new UsernameAndNameBO("-", "-")));
        }

        List<String> manageUsernameList = userBotInfoByIdList.stream()
                .filter(item -> item.getUserType() == UserBotRoleEnum.USER_BOT_MANAGER.getCode())
                .map(UserBotInfo::getUsername).distinct().collect(Collectors.toList());
        if (!manageUsernameList.isEmpty()) {
            List<EmployeeInfo> employeeInfoList = getEmployeeInfos(manageUsernameList);
            List<UsernameAndNameBO> usernameAndNameBOS = employeeInfoList.stream()
                    .map(employeeInfo -> new UsernameAndNameBO(employeeInfo.getUsername(), employeeInfo.getName()))
                    .collect(Collectors.toList());
            queryBotDTO.setManageUsernameList(usernameAndNameBOS);
        } else {
            queryBotDTO.setManageUsernameList(Collections.singletonList(new UsernameAndNameBO("-", "-")));
        }
    }

    private List<EmployeeInfo> getEmployeeInfos(List<String> usernameList) {
        return employeeInfoMapper.selectList(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .in(EmployeeInfo::getUsername, usernameList)
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        );
    }

    @Override
    public void putBotCalendarCacheStatus(String appId) {
        redisTemplate.opsForValue()
                .set(APP_ID_STATUS_PREFIX.concat(appId), String.valueOf(System.currentTimeMillis()), 24,
                        TimeUnit.HOURS);
    }

    @Override
    public BaseResult<Map<String, Object>> verifyCalendarStatus(NotifyUrlVerifyDTO notifyUrlVerifyDTO) {
        Map<String, Object> res = new HashMap<>(1);
        res.put("isReady", isOnBotCalendarCacheStatus(notifyUrlVerifyDTO.getBotKey()));
        return BaseResult.of(res);
    }

    @Override
    public BaseResult<Map<String, Object>> getNotifyUrl(NotifyUrlVerifyDTO notifyUrlVerifyDTO) {
        Map<String, Object> res = new HashMap<>(1);
        res.put("notifyUrl", larkCallBackUrl.concat(notifyUrlVerifyDTO.getBotKey()));
        return BaseResult.of(res);
    }

    /**
     * 查询机器人是配置 notify 回调地址
     *
     * @param appId
     * @return true 开启 / false  关闭
     */
    private boolean isOnBotCalendarCacheStatus(String appId) {
        String temp = redisTemplate.opsForValue().get(APP_ID_STATUS_PREFIX.concat(appId));
        if (StringUtils.isNotBlank(temp)) {
            return true;
        }

        return false;
    }

    /**
     * 计算这个人拥有的机器人的本月发送和历史发送的消息次数（使用任务数据上下文）
     * @param taskDataContext 任务数据上下文
     * @return
     */
    private BotOverviewDTO getBotOverview(TaskDataContext taskDataContext) {
        List<Long> myBotIds = taskDataContext.getMyBotIds();
        if (CollectionUtils.isEmpty(myBotIds)) {
            return null;
        }

        long botTotal = taskDataContext.getBotTotal();
        long curMonthPushCount = 0;
        long pushCountUpToNow = 0;

        List<Long> taskIdsUpToNow = taskDataContext.getTaskIdsUpToNow();
        Set<Long> curMonthTaskIdSet = taskDataContext.getCurMonthTaskIdSet();

        // 如果没有有效任务，返回默认值
        if (CollectionUtils.isEmpty(taskIdsUpToNow)) {
            return BotOverviewDTO.createDefault(botTotal);
        }

        // 一次性查询所有符合条件的子表记录，获取taskId用于内存过滤
        List<LarkTaskInfo> allLarkRecords = larkTaskInfoMapper.selectList(
                Wrappers.<LarkTaskInfo>lambdaQuery()
                        .in(LarkTaskInfo::getTaskId, taskIdsUpToNow)
                        .in(LarkTaskInfo::getBotId, myBotIds)
                        .select(LarkTaskInfo::getTaskId)
        );
        List<EmailTaskInfo> allEmailRecords = emailTaskInfoMapper.selectList(
                Wrappers.<EmailTaskInfo>lambdaQuery()
                        .in(EmailTaskInfo::getTaskId, taskIdsUpToNow)
                        .in(EmailTaskInfo::getBotId, myBotIds)
                        .select(EmailTaskInfo::getTaskId)
        );
        List<SmsTaskInfo> allSmsRecords = smsTaskInfoMapper.selectList(
                Wrappers.<SmsTaskInfo>lambdaQuery()
                        .in(SmsTaskInfo::getTaskId, taskIdsUpToNow)
                        .in(SmsTaskInfo::getBotId, myBotIds)
                        .select(SmsTaskInfo::getTaskId)
        );

        // 历史总数
        pushCountUpToNow = allLarkRecords.size() + allEmailRecords.size() + allSmsRecords.size();

        // 在内存中计算本月数量
        long curMonthLarkCount = allLarkRecords.stream()
                .filter(record -> curMonthTaskIdSet.contains(record.getTaskId()))
                .count();
        long curMonthEmailCount = allEmailRecords.stream()
                .filter(record -> curMonthTaskIdSet.contains(record.getTaskId()))
                .count();
        long curMonthSmsCount = allSmsRecords.stream()
                .filter(record -> curMonthTaskIdSet.contains(record.getTaskId()))
                .count();

        curMonthPushCount = curMonthLarkCount + curMonthEmailCount + curMonthSmsCount;

        BotOverviewDTO botOverviewDTO = new BotOverviewDTO();
        botOverviewDTO.setBotTotalNum(botTotal);
        botOverviewDTO.setCurMonthPushCount(curMonthPushCount);
        botOverviewDTO.setPushCountUpToNow(pushCountUpToNow);
        return botOverviewDTO;
    }

    /**
     * 获取用户可访问的机器人ID列表（包括用户管理的机器人和公共机器人）
     * @param username 用户名
     * @return 机器人ID列表
     */
    private List<Long> getUserAccessibleBotIds(String username) {
        // 查询我的机器人
        List<UserBotInfo> myBotList = userBotInfoMapper.selectList(
                Wrappers.<UserBotInfo>lambdaQuery()
                        .eq(UserBotInfo::getUsername, username)
                        .select(UserBotInfo::getBotId)
        );
        // 取出BOT ID
        List<Long> myBotIds = myBotList.stream().map(UserBotInfo::getBotId).distinct().collect(Collectors.toList());

        // 查询公共的机器人
        List<Long> publicBots = botInfoMapper.selectList(
                Wrappers.<BotInfo>lambdaQuery()
                        .eq(BotInfo::getBotRangeScope, BotRangeScopeEnum.PUBLIC.getCode())
                        .select(BotInfo::getId)
        ).stream().map(BotInfo::getId).collect(Collectors.toList());

        myBotIds.addAll(publicBots);
        return myBotIds;
    }

    /**
     * 任务数据传输对象，用于在方法间传递公共查询结果
     */
    private static class TaskDataContext {
        private final List<Long> myBotIds;
        private final List<Long> taskIdsCurMonth;
        private final List<Long> taskIdsUpToNow;
        private final Set<Long> curMonthTaskIdSet;
        private final int botTotal;
        private final List<LarkTaskInfo> allLarkRecords;
        private final List<EmailTaskInfo> allEmailRecords;
        private final List<SmsTaskInfo> allSmsRecords;

        public TaskDataContext(List<Long> myBotIds, List<Long> taskIdsCurMonth,
                              List<Long> taskIdsUpToNow, Set<Long> curMonthTaskIdSet, int botTotal,
                              List<LarkTaskInfo> allLarkRecords, List<EmailTaskInfo> allEmailRecords,
                              List<SmsTaskInfo> allSmsRecords) {
            this.myBotIds = myBotIds;
            this.taskIdsCurMonth = taskIdsCurMonth;
            this.taskIdsUpToNow = taskIdsUpToNow;
            this.curMonthTaskIdSet = curMonthTaskIdSet;
            this.botTotal = botTotal;
            this.allLarkRecords = allLarkRecords;
            this.allEmailRecords = allEmailRecords;
            this.allSmsRecords = allSmsRecords;
        }

        // 简化构造函数，用于没有子表数据的情况
        public TaskDataContext(List<Long> myBotIds, List<Long> taskIdsCurMonth,
                              List<Long> taskIdsUpToNow, Set<Long> curMonthTaskIdSet, int botTotal) {
            this(myBotIds, taskIdsCurMonth, taskIdsUpToNow, curMonthTaskIdSet, botTotal,
                 new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
        }

        public List<Long> getMyBotIds() { return myBotIds; }
        public List<Long> getTaskIdsCurMonth() { return taskIdsCurMonth; }
        public List<Long> getTaskIdsUpToNow() { return taskIdsUpToNow; }
        public Set<Long> getCurMonthTaskIdSet() { return curMonthTaskIdSet; }
        public int getBotTotal() { return botTotal; }
        public List<LarkTaskInfo> getAllLarkRecords() { return allLarkRecords; }
        public List<EmailTaskInfo> getAllEmailRecords() { return allEmailRecords; }
        public List<SmsTaskInfo> getAllSmsRecords() { return allSmsRecords; }
    }

    /**
     * 获取任务数据上下文，包含所有公共的查询结果
     * @param username 用户名
     * @return 任务数据上下文
     */
    private TaskDataContext getTaskDataContext(String username) {
        // 获取用户可访问的机器人ID列表
        List<Long> myBotIds = getUserAccessibleBotIds(username);
        int botTotal = myBotIds.size();

        // 获取有效推送状态
        List<Byte> effectiveStatus = TaskStatusEnum.getEffectivePushStatus();

        // 一次性查询所有有效状态的任务，然后在内存中按时间分组
        List<TaskInfo> allEffectiveTaskInfoList = taskInfoMapper.selectList(
                Wrappers.<TaskInfo>lambdaQuery()
                        .in(TaskInfo::getTaskStatus, effectiveStatus)
                        .select(TaskInfo::getId, TaskInfo::getCreateTime)
        );

        // 如果没有有效任务，返回空的上下文
        if (CollectionUtils.isEmpty(allEffectiveTaskInfoList)) {
            return new TaskDataContext(myBotIds, new ArrayList<>(), new ArrayList<>(), new HashSet<>(), botTotal);
        }

        // 计算本月时间范围
        Long currentMonthStart = DateTimeUtil.getLongTime(DateTimeUtil.getCurrentMonthStart(),
                DateTimeUtil.DATE_TIME_PATTERN);
        Long currentMonthEnd = DateTimeUtil.getLongTime(DateTimeUtil.getCurrentMonthEnd(),
                DateTimeUtil.DATE_TIME_PATTERN);

        // 在内存中分组：本月任务和所有任务
        List<Long> taskIdsCurMonth = allEffectiveTaskInfoList.stream()
                .filter(task -> task.getCreateTime() >= currentMonthStart && task.getCreateTime() <= currentMonthEnd)
                .map(TaskInfo::getId)
                .distinct()
                .collect(Collectors.toList());

        List<Long> taskIdsUpToNow = allEffectiveTaskInfoList.stream()
                .map(TaskInfo::getId)
                .distinct()
                .collect(Collectors.toList());

        Set<Long> curMonthTaskIdSet = new HashSet<>(taskIdsCurMonth);

        // 一次性查询所有符合条件的子表记录
        List<LarkTaskInfo> allLarkRecords = new ArrayList<>();
        List<EmailTaskInfo> allEmailRecords = new ArrayList<>();
        List<SmsTaskInfo> allSmsRecords = new ArrayList<>();

        if (!taskIdsUpToNow.isEmpty() && !myBotIds.isEmpty()) {
            allLarkRecords = larkTaskInfoMapper.selectList(
                    Wrappers.<LarkTaskInfo>lambdaQuery()
                            .in(LarkTaskInfo::getTaskId, taskIdsUpToNow)
                            .in(LarkTaskInfo::getBotId, myBotIds)
                            .select(LarkTaskInfo::getTaskId, LarkTaskInfo::getBotId)
            );
            allEmailRecords = emailTaskInfoMapper.selectList(
                    Wrappers.<EmailTaskInfo>lambdaQuery()
                            .in(EmailTaskInfo::getTaskId, taskIdsUpToNow)
                            .in(EmailTaskInfo::getBotId, myBotIds)
                            .select(EmailTaskInfo::getTaskId, EmailTaskInfo::getBotId)
            );
            allSmsRecords = smsTaskInfoMapper.selectList(
                    Wrappers.<SmsTaskInfo>lambdaQuery()
                            .in(SmsTaskInfo::getTaskId, taskIdsUpToNow)
                            .in(SmsTaskInfo::getBotId, myBotIds)
                            .select(SmsTaskInfo::getTaskId, SmsTaskInfo::getBotId)
            );
        }

        return new TaskDataContext(myBotIds, taskIdsCurMonth, taskIdsUpToNow, curMonthTaskIdSet, botTotal,
                                  allLarkRecords, allEmailRecords, allSmsRecords);
    }

    private Integer getPushCount(List<Long> taskIds, List<Long> myBotIds) {
        int pushCount = 0;
        if (!taskIds.isEmpty()) {
            // 根据TaskId提取推送成功飞书机器人任务
            Integer larkPushCurMonth = larkTaskInfoMapper.selectCount(
                    Wrappers.<LarkTaskInfo>lambdaQuery()
                            .in(LarkTaskInfo::getTaskId, taskIds)
                            .in(LarkTaskInfo::getBotId, myBotIds)
            );

            // 根据TaskId提取推送成功邮件机器人任务
            Integer emailPushCurMonth = emailTaskInfoMapper.selectCount(
                    Wrappers.<EmailTaskInfo>lambdaQuery()
                            .in(EmailTaskInfo::getTaskId, taskIds)
                            .in(EmailTaskInfo::getBotId, myBotIds)
            );

            // 根据TaskId提取推送成功短信机器人任务
            Integer smsPushCurMonth = smsTaskInfoMapper.selectCount(
                    Wrappers.<SmsTaskInfo>lambdaQuery()
                            .in(SmsTaskInfo::getTaskId, taskIds)
                            .in(SmsTaskInfo::getBotId, myBotIds)
            );

            // 取出所有飞书机器人任务的BotId属于我的机器人ID
            pushCount = larkPushCurMonth + emailPushCurMonth + smsPushCurMonth;
        }
        return pushCount;
    }

    /**
     * 获取机器人推送详情
     * @param taskDataContext 任务执行上下文
     * @return
     */
    private List<BotPushDetailDTO> getBotPushDetail(TaskDataContext taskDataContext) {
        List<BotPushDetailDTO> result = new ArrayList<>();

        // 在内存中分组：本月任务和所有任务
        List<Long> taskInfoIdListCurMonth = taskDataContext.getTaskIdsCurMonth();
        List<Long> taskIdListUpToNow = taskDataContext.getTaskIdsUpToNow();
        List<Long> myBotIds = taskDataContext.getMyBotIds();

        // 优化：一次性查询所有子表数据，然后在内存中按时间和botId分组
        Map<Long, Long> larkCurMonthPushMap = new HashMap<>();
        Map<Long, Long> emailCurMonthPushMap = new HashMap<>();
        Map<Long, Long> smsCurMonthPushMap = new HashMap<>();
        Map<Long, Long> larkUpToNowPushMap = new HashMap<>();
        Map<Long, Long> emailUpToNowPushMap = new HashMap<>();
        Map<Long, Long> smsUpToNowPushMap = new HashMap<>();

        if (!taskIdListUpToNow.isEmpty()) {
            // 创建本月任务ID的Set，用于快速查找
            Set<Long> curMonthTaskIdSet = new HashSet<>(taskInfoIdListCurMonth);

            // 一次性查询所有符合条件的子表记录
            List<LarkTaskInfo> allLarkRecords = larkTaskInfoMapper.selectList(
                    Wrappers.<LarkTaskInfo>lambdaQuery()
                            .in(LarkTaskInfo::getTaskId, taskIdListUpToNow)
                            .in(LarkTaskInfo::getBotId, myBotIds)
                            .select(LarkTaskInfo::getTaskId, LarkTaskInfo::getBotId)
            );
            List<EmailTaskInfo> allEmailRecords = emailTaskInfoMapper.selectList(
                    Wrappers.<EmailTaskInfo>lambdaQuery()
                            .in(EmailTaskInfo::getTaskId, taskIdListUpToNow)
                            .in(EmailTaskInfo::getBotId, myBotIds)
                            .select(EmailTaskInfo::getTaskId, EmailTaskInfo::getBotId)
            );
            List<SmsTaskInfo> allSmsRecords = smsTaskInfoMapper.selectList(
                    Wrappers.<SmsTaskInfo>lambdaQuery()
                            .in(SmsTaskInfo::getTaskId, taskIdListUpToNow)
                            .in(SmsTaskInfo::getBotId, myBotIds)
                            .select(SmsTaskInfo::getTaskId, SmsTaskInfo::getBotId)
            );

            // 在内存中按botId和时间分组统计
            larkUpToNowPushMap = allLarkRecords.stream()
                    .collect(Collectors.groupingBy(LarkTaskInfo::getBotId, Collectors.counting()));

            larkCurMonthPushMap = allLarkRecords.stream()
                    .filter(record -> curMonthTaskIdSet.contains(record.getTaskId()))
                    .collect(Collectors.groupingBy(LarkTaskInfo::getBotId, Collectors.counting()));

            emailUpToNowPushMap = allEmailRecords.stream()
                    .collect(Collectors.groupingBy(EmailTaskInfo::getBotId, Collectors.counting()));

            emailCurMonthPushMap = allEmailRecords.stream()
                    .filter(record -> curMonthTaskIdSet.contains(record.getTaskId()))
                    .collect(Collectors.groupingBy(EmailTaskInfo::getBotId, Collectors.counting()));

            smsUpToNowPushMap = allSmsRecords.stream()
                    .collect(Collectors.groupingBy(SmsTaskInfo::getBotId, Collectors.counting()));

            smsCurMonthPushMap = allSmsRecords.stream()
                    .filter(record -> curMonthTaskIdSet.contains(record.getTaskId()))
                    .collect(Collectors.groupingBy(SmsTaskInfo::getBotId, Collectors.counting()));
        }

        List<UserInfoBO> userBotManagerInfos = userBotInfoMapper.selectManageUserList(myBotIds);

        if (!myBotIds.isEmpty()) {
            List<BotInfo> botInfoList = botInfoMapper.selectList(
                    Wrappers.<BotInfo>lambdaQuery()
                            .in(BotInfo::getId, myBotIds)
                            .orderByAsc(BotInfo::getCreateTime)
            );
            botInfoList.forEach(
                    botInfo -> {
                        BotPushDetailDTO botPushDetailDTO = new BotPushDetailDTO();
                        botPushDetailDTO.setBotId(botInfo.getId());
                        botPushDetailDTO.setBotType(botInfo.getBotType());
                        botPushDetailDTO.setBotName(botInfo.getBotName());
                        botPushDetailDTO.setBotIconUrl(botInfo.getBotIconUrl());

                        long curMonthPush = 0L;
                        long upToNowPush = 0L;
                        if (botInfo.getBotType() == BotTypeEnum.BOT_TYPE_LARK.getCode()) {
                            if (!larkCurMonthPushMap.isEmpty()) {
                                curMonthPush += larkCurMonthPushMap.get(botInfo.getId()) == null ? 0 :
                                        larkCurMonthPushMap.get(botInfo.getId());
                            }
                            if (!larkUpToNowPushMap.isEmpty()) {
                                upToNowPush += larkUpToNowPushMap.get(botInfo.getId()) == null ? 0 :
                                        larkUpToNowPushMap.get(botInfo.getId());
                            }
                        } else if (botInfo.getBotType() == BotTypeEnum.BOT_TYPE_EMAIL.getCode()) {
                            if (!emailCurMonthPushMap.isEmpty()) {
                                curMonthPush += emailCurMonthPushMap.get(botInfo.getId()) == null ? 0 :
                                        emailCurMonthPushMap.get(botInfo.getId());
                            }
                            if (!emailUpToNowPushMap.isEmpty()) {
                                upToNowPush += emailUpToNowPushMap.get(botInfo.getId()) == null ? 0 :
                                        emailUpToNowPushMap.get(botInfo.getId());
                            }
                        } else if (botInfo.getBotType() == BotTypeEnum.BOT_TYPE_SMS.getCode()) {
                            if (!smsCurMonthPushMap.isEmpty()) {
                                curMonthPush += smsCurMonthPushMap.get(botInfo.getId()) == null ? 0 :
                                        smsCurMonthPushMap.get(botInfo.getId());
                            }
                            if (!smsUpToNowPushMap.isEmpty()) {
                                upToNowPush += smsUpToNowPushMap.get(botInfo.getId()) == null ? 0 :
                                        smsUpToNowPushMap.get(botInfo.getId());
                            }
                        }

                        botPushDetailDTO.setCurMonthPushCount(curMonthPush);
                        botPushDetailDTO.setPushCountUpToNow(upToNowPush);
                        botPushDetailDTO.setBotManager(userBotManagerInfos.stream()
                                .filter(userInfoBO -> userInfoBO.getBotId() == botInfo.getId().longValue())
                                .collect(Collectors.toList()));
                        // 返回机器人当前状态，由飞书端和消息平台一起决定
                        int available = 0;
                        if (botInfo.getBotStatus() == UmsRobotStatusEnum.UMS_ROBOT_OFF.getCode().intValue()) {
                            // 1. 如果消息平台机器人是禁用状态，那当前状态就是禁用
                            available = UmsRobotStatusEnum.UMS_ROBOT_OFF.getCode().intValue();
                        } else if (botInfo.getBotStatus() == UmsRobotStatusEnum.UMS_ROBOT_ON.getCode().intValue()) {
                            // 2. 如果消息平台机器人是开启状态,飞书机器人就以飞书为主,其他机器人是开启
                            if (botInfo.getBotType() == BotTypeEnum.BOT_TYPE_LARK.getCode()) {
                                available = botInfo.getBotLarkStatus();
                            } else {
                                available = UmsRobotStatusEnum.UMS_ROBOT_ON.getCode().intValue();
                            }
                        }
                        botPushDetailDTO.setAvailable(available);

                        result.add(botPushDetailDTO);
                    }
            );
        }

        return result;
    }

    private RobotVerifyDTO verifyRobot(BotDTO botDTO) {
        try {
            String token = robotVerifyManager.getTenantAccessToken(botDTO.getBotKey(), botDTO.getBotSecret());
            return robotVerifyManager.verifyRobot(token);
        } catch (Exception e) {
            log.error("机器人验证失败,原因:[{}]", e.getMessage());
            return null;
        }
    }

    private Integer syncBotAfterStartBot(String botKey, String encryptBotSecret) {

        try {
            String botSecret = KeyCenterUtil.decrypt(encryptBotSecret);
            String token = robotVerifyManager.getTenantAccessToken(botKey, botSecret);
            // 验证机器人消息
            RobotVerifyDTO robotVerifyDTO = robotVerifyManager.verifyRobot(token);

            if (Objects.isNull(robotVerifyDTO)) {
                return null;
            } else if (robotVerifyDTO.getBotLarkStatus() ==
                    LarkRobotStatusEnum.LARK_ROBOT_NOT_EXIST.getCode().intValue()) {
                return LarkRobotStatusEnum.LARK_ROBOT_NOT_EXIST.getCode().intValue();
            } else if (robotVerifyDTO.getBotLarkStatus() ==
                    LarkRobotStatusEnum.LARK_ROBOT_APP_ID_ERROR.getCode().intValue()) {
                return null;
            } else if (robotVerifyDTO.getBotLarkStatus() ==
                    LarkRobotStatusEnum.LARK_ROBOT_APP_SECRET_ERROR.getCode().intValue()) {
                return null;
            } else {
                return Integer.valueOf(robotVerifyDTO.getBotLarkStatus());
            }
        } catch (Exception e) {
            log.error("重启之后机器人验证失败,原因:[{}]", e.getMessage());
            return null;
        }
    }

    private BotInfo generateBaseBotInfo(BotDTO botDTO, String username) {
        try {
            if (botDTO.getBotType() != BotTypeEnum.BOT_TYPE_SMS.getCode()) {
                botDTO.setBotSecret(KeyCenterUtil.encrypt(botDTO.getBotSecret()));
            }
        } catch (DataProtectionException e) {
            log.error("加密失败, botkey = [{}]", botDTO.getBotKey(), e);
            throw new RuntimeException("机器人secret加密失败");
        }
        BotInfo botInfo = new BotInfo();
        BeanUtils.copyProperties(botDTO, botInfo);

        int botStatus = 0;

        if (botDTO.getBotType() == BotTypeEnum.BOT_TYPE_LARK.getCode()) {
            if (LarkRobotStatusEnum.LARK_ROBOT_INIT.getCode() == (int) botDTO.getBotLarkStatus()) {
                botStatus = UmsRobotStatusEnum.UMS_ROBOT_OFF.getCode();
            } else if (LarkRobotStatusEnum.LARK_ROBOT_OFF.getCode() == (int) botDTO.getBotLarkStatus()) {
                botStatus = UmsRobotStatusEnum.UMS_ROBOT_OFF.getCode();
            } else if (LarkRobotStatusEnum.LARK_ROBOT_ON.getCode() == (int) botDTO.getBotLarkStatus()) {
                botStatus = UmsRobotStatusEnum.UMS_ROBOT_ON.getCode();
            }
        } else {
            botStatus = UmsRobotStatusEnum.UMS_ROBOT_ON.getCode();
            botInfo.setBotLarkStatus(LarkRobotStatusEnum.LARK_ROBOT_ON.getCode());
        }

        long curTime = System.currentTimeMillis();

        botInfo.setCreateTime(curTime);
        botInfo.setUpdateTime(curTime);
        botInfo.setBotStatus((byte) botStatus);
        botInfo.setCreateUsername(username);
        botInfo.setUpdateUsername(username);

        BotInfo maxIdBot = botInfoMapper.selectOne(
                Wrappers.<BotInfo>lambdaQuery()
                        .orderByDesc(BotInfo::getId)
                        .last("limit 1")
        );
        if (maxIdBot != null) {
            String botBizId = String.format("B%04d", 100 + maxIdBot.getId() + 1);
            botInfo.setBotBizId(botBizId);
        }

        botInfoMapper.insert(botInfo);

        return botInfo;
    }

    private BotInfo updateBaseBotInfo(BotDTO botDTO, String username) {

        try {
            if (botDTO.getBotType() != BotTypeEnum.BOT_TYPE_SMS.getCode()) {
                botDTO.setBotSecret(KeyCenterUtil.encrypt(botDTO.getBotSecret()));
            }
        } catch (DataProtectionException e) {
            log.error("加密失败, botkey = [{}]", botDTO.getBotKey(), e);
            botDTO.setBotSecret(null);
        }

        // 机器人维护
        BotInfo botInfo = new BotInfo();
        BeanUtils.copyProperties(botDTO, botInfo);

        long curTime = System.currentTimeMillis();

        botInfo.setUpdateUsername(username);
        botInfo.setUpdateTime(curTime);

        botInfoMapper.update(botInfo,
                Wrappers.<BotInfo>lambdaUpdate()
                        .eq(BotInfo::getId, botInfo.getId()));

        return botInfo;
    }

    private boolean notifyEngineAddRobot(BotInfo botInfo, String originSecret) {

        RobotRpcDTO robotRpcDTO = new RobotRpcDTO();
        robotRpcDTO.setAppId(botInfo.getBotKey());
        robotRpcDTO.setAppSecret(originSecret);
        robotRpcDTO.setAppShortName(botInfo.getBotName());
        robotRpcDTO.setEncryptKey(!StringUtils.isEmpty(botInfo.getBotEncryptKey()) ? botInfo.getBotEncryptKey() : "");
        robotRpcDTO.setVerificationToken(
                !StringUtils.isEmpty(botInfo.getBotVerificationToken()) ? botInfo.getBotVerificationToken() : "");
        robotRpcDTO.setBotType(botInfo.getBotType());

        return messageBotGrpcClient.addRobot(robotRpcDTO);

    }

    private boolean notifyEngineAlertRobot(BotInfo botInfo, int status, String originAppSecret) {

        // alertRobot
        RobotRpcDTO robotRpcDTO = new RobotRpcDTO();
        robotRpcDTO.setAppId(botInfo.getBotKey());
        robotRpcDTO.setStopFlag(status);
        robotRpcDTO.setAppSecret(originAppSecret);
        robotRpcDTO.setBotType(botInfo.getBotType());

        return messageBotGrpcClient.alterRobot(robotRpcDTO);
    }

    private void saveDeptBotInfo(List<String> deptIds, BotInfo botInfo) {
        if (deptIds.isEmpty()) {
            return;
        }
        List<DeptBotInfo> list = new ArrayList<>();
        long curTime = System.currentTimeMillis();
        deptIds.forEach(
                item -> {
                    DeptBotInfo deptBotInfo = new DeptBotInfo();
                    deptBotInfo.setBotId(botInfo.getId());
                    deptBotInfo.setDeptId(item);
                    deptBotInfo.setCreateTime(curTime);
                    deptBotInfo.setCreateUsername(botInfo.getUpdateUsername());
                    deptBotInfo.setUpdateTime(curTime);
                    deptBotInfo.setUpdateUsername(botInfo.getUpdateUsername());
                    list.add(deptBotInfo);
                }
        );
        deptBotInfoMapper.batchInsert(list);
    }

    private void saveUserBotInfo(int userRole, List<String> userList, BotInfo botInfo) {
        if (userList.isEmpty()) {
            return;
        }
        List<UserBotInfo> list = new ArrayList<>();
        long curTime = System.currentTimeMillis();
        userList.forEach(
                item -> {
                    UserBotInfo userBotInfo = new UserBotInfo();
                    userBotInfo.setBotId(botInfo.getId());
                    userBotInfo.setUsername(item);
                    userBotInfo.setCreateTime(curTime);
                    userBotInfo.setCreateUsername(botInfo.getUpdateUsername());
                    userBotInfo.setUpdateTime(curTime);
                    userBotInfo.setUpdateUsername(botInfo.getUpdateUsername());
                    userBotInfo.setUserType((byte) userRole);
                    list.add(userBotInfo);
                }
        );
        userBotInfoMapper.batchInsert(list);
    }

    private void deleteDeptBotInfo(List<String> deptIds, BotInfo botInfo) {
        if (deptIds.isEmpty()) {
            return;
        }
        DeptBotInfo deptBotInfo = new DeptBotInfo();
        deptBotInfo.setBotId(botInfo.getId());
        deptBotInfoMapper.delete(
                Wrappers.<DeptBotInfo>lambdaQuery()
                        .eq(DeptBotInfo::getBotId, deptBotInfo.getBotId())

        );
    }

    private void deleteUserBotInfo(int userRole, List<String> userList, BotInfo botInfo) {
        if (userList.isEmpty()) {
            return;
        }
        UserBotInfo userBotInfo = new UserBotInfo();
        userBotInfo.setBotId(botInfo.getId());
        userBotInfoMapper.delete(
                Wrappers.<UserBotInfo>lambdaQuery()
                        .eq(UserBotInfo::getUserType, userRole)
                        .eq(UserBotInfo::getBotId, userBotInfo.getBotId())

        );
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void stopTaskByBot(Long botId, String updateUsername) {

        List<Byte> taskStatusList = Arrays.asList(
                TaskStatusEnum.APPROVED.getCode(),
                TaskStatusEnum.APPROVING.getCode(),
                TaskStatusEnum.PUBLISHING.getCode(),
                TaskStatusEnum.APPROVE_BACK.getCode(),
                TaskStatusEnum.CANCEL.getCode(),
                TaskStatusEnum.PUBLISH_FAILED.getCode(),
                TaskStatusEnum.PUBLISH_EXCEPTION.getCode()
        );
        List<TaskInfo> taskInfoList = taskInfoMapper.selectByBotIdAndStatus(botId, taskStatusList);
        if (taskInfoList.isEmpty()) {
            return;
        }

        List<BotStopTaskInfo> list = new ArrayList<>();
        List<TaskInfo> publishingTaskList = new ArrayList<>();
        for (TaskInfo taskInfo : taskInfoList) {
            BotStopTaskInfo botStopTaskInfo = BotStopTaskInfo.newCreateAndUpdateTimeInstant();
            botStopTaskInfo.setTaskId(taskInfo.getId());
            if (taskInfo.getTaskStatus() == TaskStatusEnum.PUBLISHING.getCode()
                    || taskInfo.getTaskStatus() == TaskStatusEnum.PUBLISH_EXCEPTION.getCode()) {
                publishingTaskList.add(taskInfo);
            }
            botStopTaskInfo.setNewTaskStatus(TaskStatusEnum.BOT_STOP.getCode());
            botStopTaskInfo.setOldTaskStatus(taskInfo.getTaskStatus());
            botStopTaskInfo.setBotId(botId);
            list.add(botStopTaskInfo);
        }

        botStopTaskInfoMapper.batchInsert(list);

        /**
         * 消息任务变成  机器人暂停
         */
        List<Long> idList = taskInfoList.stream().map(TaskInfo::getId).collect(Collectors.toList());
        TaskInfo updateTaskInfo = TaskInfo.newUpdateTimeInstant();
        updateTaskInfo.setTaskStatus(TaskStatusEnum.BOT_STOP.getCode());
        updateTaskInfo.setTaskEngineStatus(TaskStatusEnum.NO.getCode());
        updateTaskInfo.setUpdateUsername(updateUsername);
        taskInfoMapper.update(updateTaskInfo, Wrappers.<TaskInfo>lambdaUpdate().in(TaskInfo::getId, idList));

        /**
         * 发送中的消息，引擎层中断
         */
        for (TaskInfo taskInfo : publishingTaskList) {
            messageGrpcClient.interruptMessageByExtraId(taskInfo.getExtraId());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<Long> startTaskByBot(Long botId, String updateUsername) {

        List<BotStopTaskInfo> botStopTaskInfoList = botStopTaskInfoMapper.selectList(
                Wrappers.<BotStopTaskInfo>lambdaQuery().eq(BotStopTaskInfo::getBotId, botId));
        if (botStopTaskInfoList.isEmpty()) {
            return new ArrayList<>();
        }

        Map<Long, BotStopTaskInfo> taskIdMap = botStopTaskInfoList.stream()
                .collect(Collectors.toMap(BotStopTaskInfo::getTaskId, Function.identity(), (v1, v2) -> v2));

        /**
         * 发送中  状态变成中断
         * 其他的状态都恢复
         */

        List<Long> taskIdList =
                botStopTaskInfoList.stream().map(BotStopTaskInfo::getTaskId).collect(Collectors.toList());
        List<TaskInfo> taskInfoList =
                taskInfoMapper.selectList(Wrappers.<TaskInfo>lambdaQuery().in(TaskInfo::getId, taskIdList));
        if (taskInfoList.isEmpty()) {
            return new ArrayList<>();
        }

        // 审核中 taskId list
        List<Long> approvingIdList = new ArrayList<>();

        for (TaskInfo taskInfo : taskInfoList) {
            taskInfo.setUpdateTime(System.currentTimeMillis());
            taskInfo.setUpdateUsername(updateUsername);
            if (!taskIdMap.containsKey(taskInfo.getId())) {
                continue;
            }
            BotStopTaskInfo botStopTaskInfo = taskIdMap.get(taskInfo.getId());
            if (botStopTaskInfo.getOldTaskStatus() == TaskStatusEnum.PUBLISHING.getCode()
                    || botStopTaskInfo.getOldTaskStatus() == TaskStatusEnum.PUBLISH_EXCEPTION.getCode()) {
                // 发送中 和 发布异常 恢复成中断
                taskInfo.setTaskStatus(TaskStatusEnum.INTERRUPT.getCode());
            } else {
                taskInfo.setTaskStatus(botStopTaskInfo.getOldTaskStatus());
            }

            // 记录审核中的 taskId
            if (botStopTaskInfo.getOldTaskStatus() == TaskStatusEnum.APPROVING.getCode()) {
                approvingIdList.add(botStopTaskInfo.getTaskId());
            }
        }
        taskInfoMapper.updateBatch(taskInfoList);

        // 删除
        List<Long> idList = botStopTaskInfoList.stream().map(BotStopTaskInfo::getId).collect(Collectors.toList());
        botStopTaskInfoMapper.deleteBatchIds(idList);

        // 发布中的taskId
        return approvingIdList;
    }

    @Override
    public List<BotInfo> getBotList(Long botId, String login) {
        List<BotInfo> botInfos = new ArrayList<>();
        if (Objects.nonNull(botId)) {
            botInfos.add(botInfoMapper.selectOne(Wrappers.<BotInfo>lambdaQuery().eq(BotInfo::getId, botId)));
        } else {
            return botInfoMapper.selectList(Wrappers.lambdaQuery());
        }
        return botInfos;
    }

    @Override
    public BaseResult<Object> fillBotBizId(Long botId) {
        boolean success = startFillBotBizId(botId);
        if (success) {
            return new BaseResult<>(ResultCode.OK);
        } else {
            return new BaseResult<>(ResultCode.SERVER_INNER_ERROR);
        }
    }

    private boolean startFillBotBizId(Long botId) {

        List<BotInfo> toUpdateBotList = new ArrayList<>();

        if (Objects.nonNull(botId)) {
            BotInfo botInfo1 = new BotInfo();
            botInfo1.setId(botId);
            botInfo1.setBotBizId(getBizId(botId));
            toUpdateBotList.add(botInfo1);
        } else {
            List<BotInfo> botInfos = botInfoMapper.selectList(
                    Wrappers.<BotInfo>lambdaQuery()
            );
            botInfos.forEach(
                    botInfo -> {
                        BotInfo botInfo1 = new BotInfo();
                        botInfo1.setId(botInfo.getId());
                        botInfo1.setBotBizId(getBizId(botInfo.getId()));
                        toUpdateBotList.add(botInfo1);
                    }
            );
        }
        return this.updateBatchById(toUpdateBotList);
    }

    private String getBizId(Long id) {
        return String.format("B%04d", 100 + id);
    }
}
