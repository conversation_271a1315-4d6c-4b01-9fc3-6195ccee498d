package com.mioffice.ums.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mioffice.ums.admin.entity.dto.SendUsersDTO;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;

import java.util.List;
import java.util.Map;

public interface EmployeeService extends IService<EmployeeInfo> {
    Map<String, EmployeeInfo> getMapByOprIdList(List<String> oprIdList);

    List<SendUsersDTO> convert2LeaderList(List<SendUsersDTO> list);
}
