package com.mioffice.ums.admin.service;

import com.mioffice.ums.admin.entity.dto.DepartmentDTO;
import com.mioffice.ums.admin.entity.dto.DepartmentSummaryDTO;
import com.mioffice.ums.admin.enums.BotTenantEnum;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 部门Service
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/13 17:53
 */
public interface DeptService {

    /**
     * 查询部门根据查询词
     *
     * @param searchWord 搜索词
     * @return List<DepartmentDTO>
     */
    List<DepartmentDTO> getDeptBySearchWord(String searchWord,BotTenantEnum botTenantEnum);

    /**
     * 查询部门根据查询词
     *
     * @return List<DepartmentDTO>
     */
    List<DepartmentDTO> getDeptTree(BotTenantEnum botTenantEnum);

    /**
     * 查询部门数量
     *
     * @param deptIds 部门ID字符串。例如IT,MIUI
     * @return Map<String, Long>
     */
    Map<String, List<DepartmentSummaryDTO>> getDeptCount(String deptIds, BotTenantEnum botTenantEnum);

}
