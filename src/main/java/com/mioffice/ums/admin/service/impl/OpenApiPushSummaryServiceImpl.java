package com.mioffice.ums.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mioffice.ums.admin.entity.vo.OpenPushSummaryVo;
import com.mioffice.ums.admin.entity.vo.OpenTaskLogVo;
import com.mioffice.ums.admin.remote.grpc.PushSummaryGrpcClient;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.OpenApiPushSummaryService;
import com.mioffice.ums.admin.utils.MapperUtil;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppPushLogResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppPushSummaryInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppPushSummaryResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.PushDetail;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.PushLogPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 开放平台推送
 * </p>
 *
 * <AUTHOR>
 * @since 2020/9/23 9:05 下午
 */
@Slf4j
@Service
public class OpenApiPushSummaryServiceImpl implements OpenApiPushSummaryService {

    @Autowired
    PushSummaryGrpcClient pushSummaryGrpcClient;

    @Override
    public OpenPushSummaryVo getUseSummary(String systemIds, String startTime, String endTime) {

        AppPushSummaryResponse appPushSummaryResponse = pushSummaryGrpcClient.getOpenPushSummary(getSystemIdList(systemIds), startTime, endTime);
        if (appPushSummaryResponse.getCode() != ResultCode.OK.getCode()) {
            log.warn("system = [{}] 获取使用概览异常， 原因: [{}]", systemIds, appPushSummaryResponse.getDesc());
            return new OpenPushSummaryVo();
        }
        AppPushSummaryInfo appPushSummaryInfo = appPushSummaryResponse.getAppPushSummaryInfo();
        // get xAxis
        List<String> xAxisList = appPushSummaryInfo.getXAxisList();
        // get yAxis
        PushDetail pushDetail = appPushSummaryInfo.getYAxis();
        List<Long> miWork = pushDetail.getMiWorkList();
        List<Long> email = pushDetail.getEmailList();
        List<Long> sms = pushDetail.getSmsList();
        // return
        OpenPushSummaryVo openPushSummaryVo = new OpenPushSummaryVo();
        OpenPushSummaryVo.PushDetail yAxis = new OpenPushSummaryVo.PushDetail();
        openPushSummaryVo.setXAxis(xAxisList);
        yAxis.setMiWork(miWork);
        yAxis.setEmail(email);
        yAxis.setSms(sms);
        openPushSummaryVo.setYAxis(yAxis);
        return openPushSummaryVo;
    }

    @Override
    public IPage<OpenTaskLogVo> getAppTaskLog(String extraId, Long systemId, String channels, Integer page, Integer size) {
        AppPushLogResponse appPushLogResponse = pushSummaryGrpcClient.getOpenPushLog(extraId, systemId, channels, page, size);

        IPage<OpenTaskLogVo> result = new Page<>(page, size);
        if (appPushLogResponse.getCode() != ResultCode.OK.getCode()) {
            log.warn("system = [{}] 获取推送日志异常， 原因: [{}]", systemId, appPushLogResponse.getDesc());
            return result;
        }

        PushLogPage pushLogPage = appPushLogResponse.getPushLogPage();
        result.setPages(pushLogPage.getPages());
        result.setTotal(pushLogPage.getTotal());
        result.setRecords(pushLogPage.getRecordsList().stream().map(MapperUtil.INSTANCE::mapToOpenTaskLogVo).collect(Collectors.toList()));
        return result;
    }

    private List<Long> getSystemIdList(String systemIds) {
        List<Long> systemIdList = new ArrayList<>();
        if (StringUtils.isBlank(systemIds)) {
            return systemIdList;
        }
        String[] systemIdArray = systemIds.split(",");
        for (String systemId : systemIdArray) {
            systemIdList.add(Long.valueOf(systemId));
        }
        return systemIdList;
    }
}
