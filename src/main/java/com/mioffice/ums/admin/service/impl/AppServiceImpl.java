package com.mioffice.ums.admin.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.admin.constants.AppConstants;
import static com.mioffice.ums.admin.constants.AppConstants.TOPIC_NAME_PATTERN;
import com.mioffice.ums.admin.entity.bo.AppBo;
import com.mioffice.ums.admin.entity.bo.AppChannelBo;
import com.mioffice.ums.admin.entity.bo.AppChannelDetailBo;
import com.mioffice.ums.admin.entity.bo.AppStatusBo;
import com.mioffice.ums.admin.entity.bo.AppTopicBO;
import com.mioffice.ums.admin.entity.bo.AppTopicDetailBO;
import com.mioffice.ums.admin.entity.bo.BtnNotifBO;
import com.mioffice.ums.admin.entity.bo.EmailRobotBO;
import com.mioffice.ums.admin.entity.bo.SmsRobotBO;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.AppWhiteStatusDto;
import com.mioffice.ums.admin.entity.info.OperationInfo;
import com.mioffice.ums.admin.entity.vo.AppDetailVO;
import com.mioffice.ums.admin.entity.vo.BotIdNameVO;
import com.mioffice.ums.admin.entity.vo.BotInfoBizVO;
import com.mioffice.ums.admin.entity.vo.DeptIdNameVO;
import com.mioffice.ums.admin.entity.vo.UserVo;
import com.mioffice.ums.admin.entity.vo.hrod.HrodUser;
import com.mioffice.ums.admin.enums.TaskChannelEnum;
import com.mioffice.ums.admin.manager.ReportLineManager;
import com.mioffice.ums.admin.manager.UserRoleManager;
import com.mioffice.ums.admin.mapper.BotInfoMapper;
import com.mioffice.ums.admin.mapper.DepartmentInfoMapper;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.OperationInfoMapper;
import com.mioffice.ums.admin.remote.grpc.AppListGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.AppService;
import com.mioffice.ums.admin.utils.MapperUtil;
import com.xiaomi.info.grpc.client.annotation.RpcClientAutowired;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppManager;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppStartOrStopQueryInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.BotInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.CancelAppTopicResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.CreateAppTopicResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.Dept;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.MessageChannel;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.OpenServerBlockingClient;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.QueryAppInfoRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.QueryAppInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.QueryAppTopicResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.UpdateWhiteStatusRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.UpdateWhiteStatusResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2020/9/21
 */
@Slf4j
@Service
public class AppServiceImpl implements AppService {
    @RpcClientAutowired("open-server")
    private OpenServerBlockingClient openServerBlockingClient;
    @Autowired
    private DepartmentInfoMapper departmentInfoMapper;
    @Autowired
    private EmployeeInfoMapper employeeInfoMapper;
    @Autowired
    private BotInfoMapper botInfoMapper;
    @Autowired
    private UserRoleManager userRoleManager;
    @Autowired
    private OperationInfoMapper operationInfoMapper;
    @Autowired
    private AppListGrpcClient appListGrpcClient;

    @Resource
    private ReportLineManager reportLineManager;

    private static final String CHECK_PARAM_FORMAT = "%s 包含无效值 %s";
    private static final String BOT_APP_ID = "botAppId";

    @Override
    public BaseResult<AppStatusBo> apply(AppBo appBo, UserBO userBO) {
        AppInfo.Builder builder = AppInfo.newBuilder();
        if (ObjectUtil.isNotNull(appBo.getId())) {
            builder.setId(appBo.getId());
        }

        List<UserVo> operators = employeeInfoMapper.selectByUserName(Collections.singletonList(userBO.getUsername()));
        if (CollectionUtils.isEmpty(operators)) {
            return new BaseResult<>(ResultCode.PARAM_ERROR);
        }

        builder.setOperatorName(operators.get(0).getName());
        builder.setOperatorUsername(userBO.getUsername());
        List<DeptIdNameVO> byDeptIds = departmentInfoMapper.getByDeptIds(appBo.getDeptIdList());
        List<UserVo> userVos = employeeInfoMapper.selectByUserName(appBo.getAppManager());

        // 校验数据合法性
        BaseResult<AppStatusBo> checkParam = checkParam(appBo, byDeptIds, userVos, builder);
        if (checkParam != null) {
            return checkParam;
        }

        for (DeptIdNameVO vo : byDeptIds) {
            builder.addDepts(Dept.newBuilder().setDeptId(vo.getDeptId())
                    .setDeptName(vo.getDeptName()).build());
        }

        for (UserVo userVo : userVos) {
            builder.addAppManagers(
                    AppManager.newBuilder().setUsername(userVo.getUsername()).setName(userVo.getName()).build());
        }

        builder.setQps(appBo.getQps()).setAppName(appBo.getAppName());
        builder.addAllRole(userBO.getRoleList());
        builder.setAlarmStatus(appBo.getAlarmStatus());
        builder.setAlarmThreshold(appBo.getAlarmThreshold());
        builder.setDeduplicateStatus(appBo.getDeduplicateStatus());

        AppInfoResponse response = openServerBlockingClient.saveOrUpdate(builder.build());
        if (response.getCode() != ResultCode.OK.getCode()) {
            return new BaseResult<>(response.getCode(), response.getDesc());
        }
        AppStatusBo appStatusBo = new AppStatusBo();
        appStatusBo.setId(response.getId());
        appStatusBo.setAppSysStatus(response.getStatus());
        return BaseResult.of(appStatusBo);
    }

    private BaseResult<AppStatusBo> checkParam(AppBo appBo, List<DeptIdNameVO> deptIds, List<UserVo> userVos,
                                               AppInfo.Builder builder) {
        // deptIdList
        if (deptIds.size() < appBo.getDeptIdList().size()) {
            Set<String> dbList = new HashSet<>();
            Set<String> inputList = new HashSet<>(appBo.getDeptIdList());
            deptIds.forEach(deptIdNameVO -> dbList.add(deptIdNameVO.getDeptId()));
            inputList.removeAll(dbList);
            return new BaseResult<AppStatusBo>(ResultCode.PARAM_ERROR).setMessage(
                    String.format(CHECK_PARAM_FORMAT, "deptIdList", inputList.isEmpty() ? "" : inputList.toString()));
        }
        // appManager
        if (userVos.size() < appBo.getAppManager().stream().distinct().count()) {
            Set<String> dbList = new HashSet<>();
            Set<String> inputList = new HashSet<>(appBo.getAppManager());
            userVos.forEach(userVo -> dbList.add(userVo.getUsername()));
            inputList.removeAll(dbList);
            return new BaseResult<AppStatusBo>(ResultCode.PARAM_ERROR).setMessage(
                    String.format(CHECK_PARAM_FORMAT, "appManager", inputList.isEmpty() ? "" : inputList.toString()));
        }

        // channel
        for (AppChannelBo appChannelBo : appBo.getAppBotList()) {
            TaskChannelEnum channelEnum = TaskChannelEnum.getByCode(appChannelBo.getChannel());
            MessageChannel.Builder channelBuilder = MessageChannel.newBuilder().setChannel(appChannelBo.getChannel());
            switch (channelEnum) {
                case CHANNEL_LARK:
                    Set<String> inputBotBizIds = new HashSet<>(appChannelBo.getBotBizId());
                    Set<BotInfoBizVO> dbList = new HashSet<>(botInfoMapper.getBotInfoBiz(inputBotBizIds));

                    if (dbList.size() < inputBotBizIds.size()) {
                        dbList.forEach(botIdNameVO -> inputBotBizIds.remove(botIdNameVO.getBotAppId()));
                        return new BaseResult<AppStatusBo>(ResultCode.PARAM_ERROR).setMessage(
                                String.format(CHECK_PARAM_FORMAT, BOT_APP_ID,
                                        inputBotBizIds.isEmpty() ? "" : inputBotBizIds.toString()));
                    }
                    dbList.forEach(botInfoBizVO -> channelBuilder.addBotInfos(BotInfo.newBuilder()
                            .setBotAppId(botInfoBizVO.getBotAppId())
                            .setBotBizId(botInfoBizVO.getBotBizId())
                            .build()));
                    break;
                case CHANNEL_EMAIL:
                    if (appChannelBo.getIsOutSend() == null) {
                        return new BaseResult<AppStatusBo>(ResultCode.PARAM_ERROR).setMessage("邮件渠道请选择用户群体");
                    }
                    if (appChannelBo.getIsOutSend() < AppConstants.INNER ||
                            appChannelBo.getIsOutSend() > AppConstants.BOTH) {
                        return new BaseResult<AppStatusBo>(ResultCode.PARAM_ERROR).setMessage(
                                "邮件渠道用户群体输入非法，允许值[1,2,3]");
                    }
                    List<BotInfoBizVO> botInfoBiz = botInfoMapper.getBotInfoBiz(appChannelBo.getBotBizId());
                    Map<String, BotInfoBizVO> bizIdAndBotInfoBizVOMap = botInfoBiz.stream()
                            .collect(Collectors.toMap(BotInfoBizVO::getBotBizId, Function.identity(), (v1, v2) -> v2));
                    for (String key : appChannelBo.getBotBizId()) {
                        EmailRobotBO emailRobot = new EmailRobotBO();
                        BotInfoBizVO botInfoBizVO = bizIdAndBotInfoBizVOMap.get(key);
                        if (botInfoBizVO == null) {
                            return new BaseResult<AppStatusBo>(ResultCode.PARAM_ERROR).setMessage(
                                    String.format(CHECK_PARAM_FORMAT, BOT_APP_ID, key));
                        }
                        emailRobot.setBotBizId(key);
                        emailRobot.setSenderName(botInfoBizVO.getBotName());
                        emailRobot.setSender(botInfoBizVO.getBotAppId());
                        channelBuilder.addBotInfos(BotInfo.newBuilder()
                                        .setBotAppId(emailRobot.getSender())
                                        .setBotBizId(emailRobot.getBotBizId()).build())
                                .setIsOutSend(appChannelBo.getIsOutSend());
                    }
                    break;
                case CHANNEL_SMS:
                    List<BotInfoBizVO> botInfo = botInfoMapper.getBotInfoBiz(appChannelBo.getBotBizId());
                    Map<String, BotInfoBizVO> bizIdAndBotInfoMap = botInfo.stream()
                            .collect(Collectors.toMap(BotInfoBizVO::getBotBizId, Function.identity(), (v1, v2) -> v2));
                    for (String key : appChannelBo.getBotBizId()) {
                        SmsRobotBO smsRobot = new SmsRobotBO();
                        BotInfoBizVO botInfoBizVO = bizIdAndBotInfoMap.get(key);
                        if (botInfoBizVO == null) {
                            return new BaseResult<AppStatusBo>(ResultCode.PARAM_ERROR).setMessage(
                                    String.format(CHECK_PARAM_FORMAT, BOT_APP_ID, key));
                        }
                        smsRobot.setBotBizId(key);
                        smsRobot.setSignCode(botInfoBizVO.getBotAppId());
                        smsRobot.setSignName(botInfoBizVO.getBotName());
                        channelBuilder.addBotInfos(BotInfo.newBuilder()
                                .setBotAppId(smsRobot.getSignCode())
                                .setBotBizId(smsRobot.getBotBizId()).build());
                    }
                    break;
                default:
                    return new BaseResult<AppStatusBo>(ResultCode.PARAM_ERROR).setMessage(
                            String.format("channel 错误传入[%d] 允许值[1,2,3]", appChannelBo.getChannel()));
            }

            builder.addChannels(channelBuilder.build());
        }
        return null;
    }

    @Override
    public BaseResult<AppDetailVO> detail(Long id, UserBO userBO) {
        QueryAppInfoResponse response = openServerBlockingClient.queryAppInfo(
                QueryAppInfoRequest.newBuilder().addAllRole(userBO.getRoleList()).setUsername(userBO.getUsername())
                        .setId(id).build());
        if (response.getCode() != ResultCode.OK.getCode()) {
            return new BaseResult<>(response.getCode(), response.getDesc());
        }
        AppDetailVO detailVO = MapperUtil.INSTANCE.mapToAppDetail(response.getAppInfo());

        Map<Byte, AppChannelDetailBo> channelDetailBoMap = new HashMap<>(detailVO.getAppBotList().size());
        detailVO.getAppBotList().forEach(
                appChannelDetailBo -> channelDetailBoMap.put(appChannelDetailBo.getChannel(), appChannelDetailBo));

        List<String> deptIds = new LinkedList<>();
        for (DeptIdNameVO deptIdNameVO : detailVO.getDeptList()) {
            deptIds.add(deptIdNameVO.getDeptId());
        }
        List<DeptIdNameVO> completeNames = departmentInfoMapper.getCompleteNameByDeptIds(deptIds);
        Map<String, DeptIdNameVO> completeDeptMap = new HashMap<>(deptIds.size());
        completeNames.forEach(deptIdNameVO -> completeDeptMap.put(deptIdNameVO.getDeptId(), deptIdNameVO));
        for (DeptIdNameVO deptIdNameVO : detailVO.getDeptList()) {
            DeptIdNameVO dept = completeDeptMap.get(deptIdNameVO.getDeptId());
            deptIdNameVO.setDeptName(getFullName(dept.getDeptName()));
        }

        for (MessageChannel messageChannel : response.getAppInfo().getChannelsList()) {
            List<BotInfo> botInfoKey = messageChannel.getBotInfosList();
            TaskChannelEnum channelEnum = TaskChannelEnum.getByCode((byte) messageChannel.getChannel());
            List<BotIdNameVO> botBizList;
            switch (channelEnum) {
                case CHANNEL_LARK:
                    List<String> botKey = new ArrayList<>(botInfoKey.size());
                    botInfoKey.forEach(botInfo -> botKey.add(botInfo.getBotAppId()));
                    botBizList = botInfoMapper.getBotKeyName(botKey);
                    break;
                case CHANNEL_EMAIL:
                case CHANNEL_SMS:
                    List<String> botBizIdList = new ArrayList<>();
                    botInfoKey.forEach(botInfo -> botBizIdList.add(botInfo.getBotBizId()));
                    List<BotInfoBizVO> botInfoBizList = botInfoMapper.getBotInfoBiz(botBizIdList);
                    Map<String, BotInfoBizVO> bizIdAndBotInfoBizVOMap = botInfoBizList.stream()
                            .collect(Collectors.toMap(BotInfoBizVO::getBotBizId, Function.identity(), (v1, v2) -> v2));
                    botBizList = new LinkedList<>();
                    for (BotInfo botInfo : botInfoKey) {
                        BotInfoBizVO botInfoBizVO = bizIdAndBotInfoBizVOMap.get(botInfo.getBotBizId());
                        if (botInfoBizVO != null) {
                            botBizList.add(new BotIdNameVO(botInfoBizVO.getBotBizId(), botInfoBizVO.getBotName()));
                        }
                    }
                    break;
                default:
                    return new BaseResult<AppDetailVO>(ResultCode.PARAM_ERROR).setMessage(
                            String.format("channel 错误传入[%d] 允许值[1,2,3]", messageChannel.getChannel()));
            }
            channelDetailBoMap.get((byte) messageChannel.getChannel()).setBot(botBizList);
        }
        detailVO.setAppSysStatus(response.getStatus());
        detailVO.setUserType(userRoleManager.getUserRole(userBO.getUsername(), id));
        return BaseResult.of(detailVO);
    }

    private String getFullName(String completeName) {
        return completeName.replace("\"", "")
                .replace("[", "")
                .replace("]", "").replace(",", "-");
    }

    @Override
    public boolean stopApp(BtnNotifBO btnNotifBO) {

        Map<String, Object> values = btnNotifBO.getBtnActionBo().getAction().getValue();
        long appSysId = Long.parseLong(values.get("appSysId").toString());
        AppStartOrStopQueryInfoResponse response =
                appListGrpcClient.appStop(appSysId, "", "", Arrays.asList("ROLE_SYS_SUPER_ADMIN"));

        return response.getCode() == 200;
    }

    @Override
    public BaseResult<Boolean> updateWhiteStatus(AppWhiteStatusDto appWhiteStatusDto) {
        UpdateWhiteStatusRequest updateWhiteStatusRequest =
                MapperUtil.INSTANCE.mapToUpdateWhiteStatusRequest(appWhiteStatusDto);
        UpdateWhiteStatusResponse response = openServerBlockingClient.updateWhiteStatus(updateWhiteStatusRequest);
        if (response.getCode() != ResultCode.OK.getCode()) {
            return new BaseResult<>(response.getCode(), response.getDesc());
        }
        return BaseResult.of(response.getSuccess());
    }

    @Override
    public BaseResult<Boolean> applyTopic(AppTopicBO appTopicBO, UserBO userBO) {
        if (StringUtils.isBlank(appTopicBO.getAppId())) {
            throw new IllegalArgumentException("Topic名称不能为空");
        }
        if (StringUtils.isBlank(appTopicBO.getDesc())) {
            throw new IllegalArgumentException("申请理由不能为空");
        }
        if (appTopicBO.getName().trim().length() > 64) {
            throw new IllegalArgumentException("Topic名称太长(不能超过64个字符)");
        }
        if (appTopicBO.getDesc().length() > 500) {
            throw new IllegalArgumentException("申请理由太长(不能超过500个字符)");
        }
        Matcher topicNameMatcher = TOPIC_NAME_PATTERN.matcher(appTopicBO.getName().trim());
        if (!topicNameMatcher.matches()) {
            throw new IllegalArgumentException("队列名称不合规(请填写由数字、26个英文字母或者下划线组成的字符串)");
        }
        String untilLevel2Leader =
                reportLineManager.getUntilLevelNLeader(userBO.getUsername(), 2).stream().map(HrodUser::getOprId)
                        .collect(Collectors.joining(","));

        String itOperators = operationInfoMapper
                .selectList(Wrappers.lambdaQuery())
                .stream()
                .map(OperationInfo::getOperationUsername)
                .collect(Collectors.joining(","));

        CreateAppTopicResponse response = appListGrpcClient.createAppTopic(appTopicBO.getName().trim(),
                appTopicBO.getAppId(),
                userBO.getUsername(),
                untilLevel2Leader,
                itOperators,
                appTopicBO.getDesc());
        if (response.getCode() != ResultCode.OK.getCode()) {
            return new BaseResult<>(response.getCode(), response.getDesc());
        }
        return BaseResult.of(true);
    }

    @Override
    public BaseResult<Boolean> cancelTopic(String appId, UserBO userBO) {
        CancelAppTopicResponse response = appListGrpcClient.cancelAppTopic(appId, userBO.getUsername());
        if (response.getCode() != ResultCode.OK.getCode()) {
            return new BaseResult<>(response.getCode(), response.getDesc());
        }
        return BaseResult.of(true);
    }

    @Override
    public BaseResult<AppTopicDetailBO> getAppTopic(String appId) {
        QueryAppTopicResponse response = appListGrpcClient.getAppTopic(appId);
        if (response.getCode() != ResultCode.OK.getCode()) {
            return new BaseResult<>(response.getCode(), response.getDesc());
        }
        return BaseResult.of(MapperUtil.INSTANCE.map2AppTopicDetailBO(response.getAppTopicInfo()));
    }
}
