package com.mioffice.ums.admin.manager;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.gson.Gson;
import com.mioffice.ums.admin.entity.bo.ProcessDefinition;
import com.mioffice.ums.admin.entity.info.ApprovalInfo;
import com.mioffice.ums.admin.mapper.ApprovalInfoMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 流程定义资源管理器
 *
 * <AUTHOR>
 * @date 2020/8/14 18:10
 */
@Component
public class ProcessDefinitionResource {

    /**
     * SPECIAL_RESOURCE 针对特殊审批专用的Map
     */
    private static final ConcurrentHashMap<String, ProcessDefinition> SPECIAL_RESOURCE = new ConcurrentHashMap<>();
    public static final String PROCESS_MESSAGE_RELEASE = "messageRelease";
    public static final String PROCESS_MESSAGE_TOPIC = "消息发布审核";
    /**
     * 针对特殊审批的Channel
     */
    public static final byte SPECIAL_RESOURCE_CHANNEL = 99;
    private final ApprovalInfoMapper approvalInfoMapper;

    private ProcessDefinitionResource(ApprovalInfoMapper approvalInfoMapper) {
        this.approvalInfoMapper = approvalInfoMapper;
    }

    public ProcessDefinition get(byte channel) {
        final Gson gson = new Gson();
        List<ApprovalInfo> approvalInfoList = approvalInfoMapper.selectList(Wrappers.<ApprovalInfo>lambdaQuery().ne(ApprovalInfo::getChannel, SPECIAL_RESOURCE_CHANNEL));
        Map<Byte, ApprovalInfo> approvalInfoMap = approvalInfoList
                .stream()
                .collect(Collectors.toMap(ApprovalInfo::getChannel, Function.identity(), (v1, v2) -> v2));
        return gson.fromJson(approvalInfoMap.get(channel).getApprovalProcessNode(), ProcessDefinition.class);
    }

    public void deploySpecial(long botId, ProcessDefinition processDefinition) {
        SPECIAL_RESOURCE.put(String.valueOf(botId), processDefinition);
    }

    public ProcessDefinition getSpecial(long botId) {
        final Gson gson = new Gson();
        List<ApprovalInfo> approvalInfoList = approvalInfoMapper.selectList(Wrappers.<ApprovalInfo>lambdaQuery().eq(ApprovalInfo::getChannel, SPECIAL_RESOURCE_CHANNEL));
        approvalInfoList.forEach(
                approvalInfo -> {
                    if (Objects.nonNull(approvalInfo.getBotId())) {
                        deploySpecial(approvalInfo.getBotId(), gson.fromJson(approvalInfo.getApprovalProcessNode(), ProcessDefinition.class));
                    }
                }
        );
        return SPECIAL_RESOURCE.get(String.valueOf(botId));
    }
}
