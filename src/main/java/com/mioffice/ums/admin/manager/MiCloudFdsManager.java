package com.mioffice.ums.admin.manager;

import com.xiaomi.infra.galaxy.fds.client.FDSClientConfiguration;
import com.xiaomi.infra.galaxy.fds.client.GalaxyFDSClient;
import com.xiaomi.infra.galaxy.fds.client.credential.BasicFDSCredential;
import com.xiaomi.infra.galaxy.fds.client.credential.GalaxyFDSCredential;
import com.xiaomi.infra.galaxy.fds.client.exception.GalaxyFDSClientException;
import com.xiaomi.infra.galaxy.fds.model.FDSObjectMetadata;
import com.xiaomi.infra.galaxy.fds.result.PutObjectResult;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/5 6:51 下午
 * version: 1.0.0
 */
@Slf4j
public class MiCloudFdsManager {

    private String accessId;
    private String accessSecret;
    private String endpoint;
    private String bucketPrefix;
    private boolean enableCdnForDownload;

    private String hostUrl;

    private GalaxyFDSCredential credential;
    private GalaxyFDSClient fdsClient;

    public MiCloudFdsManager(String accessId, String accessSecret, String endpoint, String bucketPrefix, boolean enableCdnForDownload, String hostUrl) {
        this.accessId = accessId;
        this.accessSecret = accessSecret;
        this.endpoint = endpoint;
        this.bucketPrefix = bucketPrefix;
        this.enableCdnForDownload = enableCdnForDownload;
        this.hostUrl = hostUrl;
    }

    public void init() {
        credential = new BasicFDSCredential(accessId, accessSecret);
        FDSClientConfiguration fdsConfig = new FDSClientConfiguration(endpoint, false);
        // 要不要启用https
        fdsConfig.enableHttps(true);
        // 上传走不走CDN
        fdsConfig.enableCdnForUpload(false);
        // 下载走不走CDN
        fdsConfig.enableCdnForDownload(enableCdnForDownload);
        fdsClient = new GalaxyFDSClient(credential, fdsConfig);
    }

    /**
     * 上传文件
     *
     * @param objectName
     * @param input
     * @param metadata
     * @return
     */
    public String uploadFile(String objectName, InputStream input, FDSObjectMetadata metadata) {
        try {
            PutObjectResult putObjectResult = fdsClient.putObject(bucketPrefix, objectName, input, metadata);
            fdsClient.setPublic(bucketPrefix, objectName);
            return "/" + bucketPrefix + "/" + objectName;
        } catch (GalaxyFDSClientException e) {
            log.error("上传文件到云服务异常,objectName=" + objectName, e);
        }
        return "/" + bucketPrefix + "/" + objectName;
    }

    public String getHostUrl() {
        return hostUrl;
    }
}
