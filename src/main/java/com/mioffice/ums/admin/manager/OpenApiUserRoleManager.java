package com.mioffice.ums.admin.manager;

import com.mioffice.ums.admin.entity.bo.OpenApiUserRoleBo;

/**
 * <p>
 * 开放平台用户权限
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/24 10:11 上午
 */
public interface OpenApiUserRoleManager {

    /**
     * 获取用户角色信息
     *
     * @param username 账号
     * @param systemId 系统ID
     * @return OpenApiUserRoleBo
     */
    OpenApiUserRoleBo getOpenApiUserRoleInfo(String username, Long systemId);

    /**
     * 是申请人
     *
     * @param username 账号
     * @param systemId 系统ID
     * @return bool
     */
    boolean isApplicant(String username, Long systemId);

    /**
     * 是负责人
     *
     * @param username 账号
     * @param systemId 系统ID
     * @return bool
     */
    boolean isInChargeUser(String username, Long systemId);
}
