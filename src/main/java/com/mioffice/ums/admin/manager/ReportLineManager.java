package com.mioffice.ums.admin.manager;

import com.mioffice.ums.admin.entity.dto.ReportLineDTO;
import com.mioffice.ums.admin.entity.vo.hrod.HrodUser;

import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 获取汇报线接口
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/3 2:19 下午
 */
public interface ReportLineManager {

    /**
     * 获取部门VP
     *
     * @param username 账号
     * @param emailAddr 邮箱
     * @return ReportLineDTO
     */
    ReportLineDTO getDeptVp(String username, String emailAddr);

    /**
     * 获取直属主管
     *
     * @param username 账号
     * @param emailAddr 邮箱
     * @return ReportLineDTO
     */
    ReportLineDTO getDirectLeader(String username, String emailAddr);

    /**
     * 获取二级主管
     *
     * @param username 账号
     * @param emailAddr 邮箱
     * @return ReportLineDTO
     */
    ReportLineDTO get2thDeptLeader(String username, String emailAddr);

    /**
     * 获取信息部运营
     *
     * @param username 账号
     * @param emailAddr 邮箱
     * @return ReportLineDTO
     */
    ReportLineDTO getOperators(String username, String emailAddr);

    /**
     * 获取直到N级的汇报线
     * @param userName 账号
     * @param level 级
     * @return List<HrodUser>
     */
    List<HrodUser> getUntilLevelNLeader(String userName, int level);
}
