package com.mioffice.ums.admin.manager;

import com.mioffice.ums.admin.entity.info.ProcessTaskInfo;
import com.mioffice.ums.admin.mapper.ProcessTaskInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 流程审批任务逻辑处理类
 *
 * <AUTHOR>
 * @date 2020/8/14 19:47
 */
@Service
public class ProcessTaskManager {

    @Autowired
    private ProcessTaskInfoMapper processTaskInfoMapper;

    public void createTask(ProcessTaskInfo processTaskInfo) {
        processTaskInfoMapper.insert(processTaskInfo);
    }
}
