package com.mioffice.ums.admin.manager;

import com.github.kevinsawicki.http.HttpRequest;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.mioffice.ums.admin.entity.dto.RobotVerifyDTO;
import com.mioffice.ums.admin.enums.LarkRobotStatusEnum;
import com.mioffice.ums.admin.exception.RobotVerifyException;
import com.mioffice.ums.admin.result.LarkAppCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 机器人验证管理
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/10 3:12
 */
@Slf4j
@Component
public class RobotVerifyManager {

    /**
     * 域名
     */
    private static final String BASE_URL = "https://open.f.mioffice.cn";
    /**
     * 获取 access_token
     */
    private static final String GET_ACCESS_TOKEN = "/open-apis/auth/v3/tenant_access_token/internal";
    /**
     * 验证ROBOT, 会返回ROBOT信息
     */
    private static final String VERIFY_ROBOT = "/open-apis/bot/v3/info/";

    /**
     * 飞书返会内容KEY
     */
    private static final String LARK_CODE_KEY = "code";

    /**
     * 获取 自建应用access_token
     *
     * @param appId app_id
     * @param appSecret app_secret
     */
    public String getTenantAccessToken(String appId, String appSecret) throws RobotVerifyException {

        Map<String, String> params = new HashMap<>(2);
        params.put("app_id", appId);
        params.put("app_secret", appSecret);

        log.info("当前appKey为[{}], 当前appSecret为[{}]", appId, appSecret);

        try {
            HttpRequest request = HttpRequest
                    .post(BASE_URL.concat(GET_ACCESS_TOKEN))
                    .contentType("application/json")
                    .send(new Gson().toJson(params));

            String str = request.body();
            JsonObject data = new JsonParser().parse(str).getAsJsonObject();

            if (request.ok()) {
                // 飞书返回码--非0表示失败
                if (data.get(LARK_CODE_KEY).getAsInt() == LarkAppCode.LARK_APP_OK.getCode()) {
                    return data.get("tenant_access_token").getAsString();
                } else {
                    log.error("获取机器人access_token失败,原因: [{}]", data.get("msg").getAsString());
                    throw new RobotVerifyException("获取机器人access_token失败,原因:" + data.get("msg").getAsString());
                }
            }

        } catch (HttpRequest.HttpRequestException e) {
            log.error("获取机器人access_token失败, 无法建立连接");
            throw new RobotVerifyException("获取机器人access_token失败,无法建立连接");
        }
        return null;
    }

    /**
     * 验证机器人，会返回机器人的信息
     *
     * @param token 自建应用的access_token
     */
    public RobotVerifyDTO verifyRobot(String token) throws RobotVerifyException {

        try {
            HttpRequest request = HttpRequest
                    .get(BASE_URL.concat(VERIFY_ROBOT))
                    .authorization("Bearer ".concat(token));

            String str = request.body();
            JsonObject data = new JsonParser().parse(str).getAsJsonObject();

            if (request.ok()) {
                // 飞书返回码--非0表示失败
                if (data.get(LARK_CODE_KEY).getAsInt() == LarkAppCode.LARK_APP_OK.getCode()) {
                    return new Gson().fromJson(data.get("bot"), RobotVerifyDTO.class);
                } else {
                    log.error("验证机器人失败,原因: [{}]", data.get("msg").getAsString());

                    int status = getLarkBotStatusByResultCode(data.get(LARK_CODE_KEY).getAsInt());
                    if (status == -1) {
                        throw new RobotVerifyException("验证机器人失败,原因:" + data.get("msg").getAsString());
                    }
                    // 返回机器人失败状态
                    RobotVerifyDTO robotVerifyDTO = new RobotVerifyDTO();
                    robotVerifyDTO.setBotLarkStatus((byte) status);
                    return robotVerifyDTO;
                }
            }
        } catch (HttpRequest.HttpRequestException e) {
            log.error("验证机器人失败, 无法建立连接");
            throw new RobotVerifyException("验证机器人失败,无法建立连接", e);
        }
        return null;
    }

    private int getLarkBotStatusByResultCode(int larkAppCode) {
        int status;
        switch (larkAppCode) {
            case 10003:
                status = LarkRobotStatusEnum.LARK_ROBOT_APP_ID_ERROR.getCode();
                break;
            case 10014:
                status = LarkRobotStatusEnum.LARK_ROBOT_APP_SECRET_ERROR.getCode();
                break;
            case 11205:
                status = LarkRobotStatusEnum.LARK_ROBOT_NOT_EXIST.getCode();
                break;
            default:
                status = -1;
        }
        return status;
    }

    // todo 获取 robot 的开发者, 目前飞书还没实现这个接口
}
