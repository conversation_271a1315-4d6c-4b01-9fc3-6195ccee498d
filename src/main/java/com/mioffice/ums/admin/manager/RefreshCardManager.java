package com.mioffice.ums.admin.manager;

import com.mioffice.ums.admin.entity.bo.BtnNotifBO;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.mioffice.ums.admin.utils.JsonUtils;
import com.mioffice.ums.admin.utils.MessageUtils;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.FinalContentResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

/**
 * <p>
 * 刷新卡片样式
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.23
 */
@Slf4j
@Component
public class RefreshCardManager {

    private final MessageGrpcClient messageGrpcClient;

    public RefreshCardManager(MessageGrpcClient messageGrpcClient) {
        this.messageGrpcClient = messageGrpcClient;
    }

    public Map<String, Object> refresh(BtnNotifBO btnNotifBO, String newCnMsg, String newEnMsg) {
        String extraId = btnNotifBO.getBtnActionBo().getAction().getValue().get("extraId").toString();
        String username = btnNotifBO.getBtnActionBo().getUserId();

        // 获取原始的消息内容
        FinalContentResponse finalContentResponse = messageGrpcClient.getFinalMessageContent(extraId, username);
        if (finalContentResponse.getFinalContentList().isEmpty()) {
            return new HashMap<>();
        }

        // 最开始的消息体
        Map<String, Object> initialContent = JsonUtils.toMap(finalContentResponse.getFinalContentList().get(0).getFinalContent());
        return MessageUtils.resetBtnContent(initialContent, newCnMsg, newEnMsg);
    }

    public Map<String, Object> refresh(BtnNotifBO btnNotifBO, String newMsg) {
        return this.refresh(btnNotifBO, newMsg, null);
    }

    public Map<String, Object> process(BtnNotifBO btnNotifBO, Consumer<Map<String, Object>> consumer) {
        String extraId = btnNotifBO.getBtnActionBo().getAction().getValue().get("extraId").toString();
        String username = btnNotifBO.getBtnActionBo().getUserId();

        // 获取原始的消息内容
        FinalContentResponse finalContentResponse = messageGrpcClient.getFinalMessageContent(extraId, username);
        if (finalContentResponse.getFinalContentList().isEmpty()) {
            return new HashMap<>();
        }

        // 最开始的消息体
        Map<String, Object> initialContent = JsonUtils.toMap(finalContentResponse.getFinalContentList().get(0).getFinalContent());
        return MessageUtils.resetBtnContent(initialContent, consumer);
    }
}
