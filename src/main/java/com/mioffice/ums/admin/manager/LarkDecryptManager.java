package com.mioffice.ums.admin.manager;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.admin.entity.info.BotInfo;
import com.mioffice.ums.admin.manager.auth.NotifyDataDecrypter;
import com.mioffice.ums.admin.mapper.BotInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;

/**
 * <p>
 * lark回调解密
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.18
 */
@Slf4j
@Component
public class LarkDecryptManager {

    private final ConcurrentHashMap<String, NotifyDataDecrypter> appIdMap = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();

    private final BotInfoMapper botInfoMapper;

    public LarkDecryptManager(BotInfoMapper botInfoMapper) {
        this.botInfoMapper = botInfoMapper;
    }

    @PostConstruct
    public void init() {
        this.load();
        scheduledExecutorService.scheduleAtFixedRate(this::load, 10, 10, TimeUnit.MINUTES);
    }

    private void load() {

        try {
            List<BotInfo> botInfoList = botInfoMapper.selectList(Wrappers.<BotInfo>lambdaQuery());
            for (BotInfo botInfo : botInfoList) {
                String botEncryptKey = botInfo.getBotEncryptKey();
                if (Strings.isBlank(botEncryptKey)) {
                    continue;
                }

                appIdMap.put(botInfo.getBotKey(), new NotifyDataDecrypter(botEncryptKey));
            }
        } catch (Exception e) {
            log.warn("加载机器人解密器失败", e);
        }

    }

    public String decrypt(String appId, String encrypt)
            throws BadPaddingException, InvalidKeyException, NoSuchAlgorithmException, IllegalBlockSizeException,
                   NoSuchPaddingException, InvalidAlgorithmParameterException {
        NotifyDataDecrypter notifyDataDecrypter = appIdMap.get(appId);
        if (Objects.isNull(notifyDataDecrypter)) {
            return null;
        }

        return notifyDataDecrypter.decrypt(encrypt);
    }

}
