package com.mioffice.ums.admin.manager;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.infra.oaucf.bpm.rep.ApprovalTaskResp;
import com.mi.oa.infra.oaucf.bpm.service.ApprovalService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mioffice.ums.admin.constants.ProcessStatusConstant;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.info.LarkTaskInfo;
import com.mioffice.ums.admin.entity.info.ProcessInstanceInfo;
import com.mioffice.ums.admin.entity.info.ProcessTaskInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.entity.info.UserBotInfo;
import com.mioffice.ums.admin.enums.TaskChannelEnum;
import com.mioffice.ums.admin.enums.UserBotRoleEnum;
import com.mioffice.ums.admin.mapper.LarkTaskInfoMapper;
import com.mioffice.ums.admin.mapper.ProcessInstanceInfoMapper;
import com.mioffice.ums.admin.mapper.ProcessTaskInfoMapper;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import com.mioffice.ums.admin.mapper.UserBotInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * @since 2020/11/11 2:04 下午
 * version: 1.0.0
 */
@Slf4j
@Component
public class PermissionManager {

    @NacosValue(value = "${ums.admin.bpm-start-taskid:}", autoRefreshed = true)
    private String bpmStartTaskId;

    @Resource
    private TaskInfoMapper taskInfoMapper;

    @Resource
    private LarkTaskInfoMapper larkTaskInfoMapper;

    @Resource
    private UserBotInfoMapper userBotInfoMapper;

    @Resource
    private ProcessInstanceInfoMapper processInstanceInfoMapper;

    @Resource
    private ProcessTaskInfoMapper processTaskInfoMapper;

    @Resource
    private UserRoleManager userRoleManager;

    @Resource
    private ApprovalService approvalService;

    /**
     * 消息详情页里相关接口，鉴权
     *
     * @param userBO
     * @param taskId
     * @return
     */
    public boolean isHaveMessagePermission(UserBO userBO, Long taskId) {
        TaskInfo taskInfo = taskInfoMapper.selectById(taskId);
        List<Byte> userType = new ArrayList<>();
        if (userBO.getUsername().equals(taskInfo.getCreateUsername()) ||
                userRoleManager.isSuperAdmin(userBO.getUsername())) {
            return true;
        }
        try {
            if (checkHaveAccessAudit(userBO.getUsername(), taskId)) {
                return true;
            }
        } catch (Exception e) {
            log.info("查询任务id = [{}]的任务审核权限时发生异常", taskId, e);
        }
        if (TaskChannelEnum.CHANNEL_LARK.getCode() == taskInfo.getChannel()) {
            LarkTaskInfo larkTaskInfo = larkTaskInfoMapper.selectOne(
                    Wrappers.<LarkTaskInfo>lambdaQuery().eq(LarkTaskInfo::getTaskId, taskId)
            );

            List<String> botManagerUsernameList = userBotInfoMapper.selectList(
                    Wrappers.<UserBotInfo>lambdaQuery()
                            .eq(UserBotInfo::getBotId, larkTaskInfo.getBotId())
                            .eq(UserBotInfo::getUserType, UserBotRoleEnum.USER_BOT_MANAGER.getCode())
            ).stream().map(UserBotInfo::getUsername).collect(Collectors.toList());

            return botManagerUsernameList.contains(userBO.getUsername());
        }
        return false;
    }

    /**
     * 整个审批线上的人都有权限
     *
     * @param username
     * @param taskId
     * @return
     */
    public boolean checkHaveAccessAudit(String username, Long taskId) {
        if (taskId < Long.parseLong(bpmStartTaskId)) {
            ProcessInstanceInfo processInstanceInfo = processInstanceInfoMapper.selectOne(
                    Wrappers.<ProcessInstanceInfo>lambdaQuery()
                            .eq(ProcessInstanceInfo::getBizId, taskId)
                            .orderByDesc(ProcessInstanceInfo::getCreateTime)
                            .last("limit 1")
            );

            if (Objects.isNull(processInstanceInfo)) {
                return false;
            }

            // 取出当前任务的审批节点
            List<ProcessTaskInfo> processTaskInfoList = processTaskInfoMapper.selectList(
                    Wrappers.<ProcessTaskInfo>lambdaQuery()
                            .eq(ProcessTaskInfo::getApproverId, username)
                            .eq(ProcessTaskInfo::getInstanceId, processInstanceInfo.getId())
            );
            return processTaskInfoList != null && !processTaskInfoList.isEmpty();
        } else {
            return isBpmApprover(username, taskId);
        }
    }

    /**
     * 当前正在审批人的权限
     *
     * @param username
     * @param taskId
     * @return
     */
    public boolean checkNowHaveAccessAudit(String username, Long taskId) {
        if (taskId < Long.parseLong(bpmStartTaskId)) {
            ProcessInstanceInfo processInstanceInfo = processInstanceInfoMapper.selectOne(
                    Wrappers.<ProcessInstanceInfo>lambdaQuery()
                            .eq(ProcessInstanceInfo::getStatus, ProcessStatusConstant.PROCESS_STATUS_APPROVAL)
                            .eq(ProcessInstanceInfo::getBizId, taskId)
            );

            if (Objects.isNull(processInstanceInfo)) {
                return false;
            }

            // 取出当前任务的审批节点
            List<ProcessTaskInfo> processTaskInfoList = processTaskInfoMapper.selectList(
                    Wrappers.<ProcessTaskInfo>lambdaQuery()
                            .eq(ProcessTaskInfo::getApproverId, username)
                            .eq(ProcessTaskInfo::getInstanceId, processInstanceInfo.getId())
                            .eq(ProcessTaskInfo::getApproveResultValue, ProcessStatusConstant.NO_ACTION)
            );
            return processTaskInfoList != null && !processTaskInfoList.isEmpty();
        } else {
            return isBpmApprover(username, taskId);
        }
    }

    private boolean isBpmApprover(String username, Long taskId) {
        TaskInfo taskInfo = taskInfoMapper.selectById(taskId);
        if (Objects.nonNull(taskInfo)) {
            try {
                BaseResp<List<ApprovalTaskResp>> resp = approvalService.list(taskInfo.getBpmInstanceId(), true);
                if (0 == resp.getCode() && CollectionUtils.isNotEmpty(resp.getData())) {
                    return resp.getData().stream().anyMatch(x -> username.equals(x.getAssignee().getUserName()));
                }
            } catch (Exception ex) {
                log.error("isBpmApprover_error:", ex);
            }
        }
        return false;
    }

}
