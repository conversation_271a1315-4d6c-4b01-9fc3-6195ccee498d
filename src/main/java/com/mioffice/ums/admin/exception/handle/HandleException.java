package com.mioffice.ums.admin.exception.handle;

import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.io.FileNotFoundException;
import javax.validation.ConstraintViolationException;

/**
 * <p>
 * 异常处理
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/5 10:20
 * @Created by Shi tie tou
 */
@Slf4j
@RestControllerAdvice
public class HandleException {

    private static final String PARAM_CHECK_ERROR_STR = "参数校验异常";

    /**
     * 顶级未知异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(Throwable.class)
    public BaseResult<Object> globalExceptionHandler(Throwable e) {
        log.warn("未知异常", e);
        return new BaseResult<>(ResultCode.SERVER_INNER_ERROR).setMessage(e.getMessage());
    }

    /**
     * 参数异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(BindException.class)
    public BaseResult<Object> bindException(BindException e) {
        log.warn(PARAM_CHECK_ERROR_STR, e);
        return new BaseResult<>(ResultCode.PARAM_ERROR).setMessage(e.getMessage());
    }

    /**
     * 参数异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public BaseResult<Object> constraintViolationException(ConstraintViolationException e) {
        log.warn(PARAM_CHECK_ERROR_STR, e);
        return new BaseResult<>(ResultCode.PARAM_ERROR).setMessage(e.getMessage());
    }

    /**
     * 参数异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public BaseResult<Object> illegalArgumentException(IllegalArgumentException e) {
        log.warn(PARAM_CHECK_ERROR_STR, e);
        return new BaseResult<>(ResultCode.PARAM_ERROR).setMessage(e.getMessage());
    }

    /**
     * 参数异常
     *
     * @param validException
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public BaseResult<Object> methodArgumentNotValidException(MethodArgumentNotValidException validException) {
        log.warn(PARAM_CHECK_ERROR_STR, validException);
        StringBuilder errorMessage = new StringBuilder();
        validException.getBindingResult().getAllErrors().forEach(
                item -> errorMessage.append(item.getDefaultMessage()).append(",")
        );
        if (errorMessage.length() > 1) {
            errorMessage.setLength(errorMessage.length() - 1);
        }
        return new BaseResult<>(ResultCode.PARAM_ERROR).setMessage(errorMessage.toString());
    }

    @ExceptionHandler(FileNotFoundException.class)
    public BaseResult<Object> fileNotFoundException(FileNotFoundException e) {
        log.warn("文件不存在", e);
        return new BaseResult<>(ResultCode.RESOURCE_NOT_FOUND).setMessage(e.getMessage());
    }

    @ExceptionHandler(AccessDeniedException.class)
    public BaseResult<Object> accessDeniedException(AccessDeniedException e) {
        log.warn("不允许访问", e);
        return new BaseResult<>(ResultCode.FORBIDDEN).setMessage(e.getMessage());
    }

}
