package com.mioffice.ums.admin.exception;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/7 1:44 上午
 * version: 1.0.0
 */
public class FileOperateException extends Exception {

    public FileOperateException() {
    }

    public FileOperateException(String message) {
        super(message);
    }

    public FileOperateException(String message, Throwable cause) {
        super(message, cause);
    }

    public FileOperateException(Throwable cause) {
        super(cause);
    }

    public FileOperateException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
