package com.mioffice.ums.admin.listener;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Preconditions;
import com.mioffice.ums.admin.entity.bo.InvisibleWarningEvent;
import com.mioffice.ums.admin.entity.bo.SuperAdminWarningCardContentBo;
import com.mioffice.ums.admin.entity.bo.TaskSummaryBo;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.bo.WarningCardContentBo;
import com.mioffice.ums.admin.entity.dto.UserInfoDTO;
import com.mioffice.ums.admin.entity.info.PublishScopeInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.entity.info.UserRoleInfo;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.enums.RoleTypeEnum;
import com.mioffice.ums.admin.enums.WarningTypeEnum;
import com.mioffice.ums.admin.manager.impl.SendMessageManager;
import com.mioffice.ums.admin.mapper.PublishScopeInfoMapper;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import com.mioffice.ums.admin.mapper.UserRoleInfoMapper;
import com.mioffice.ums.admin.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 机器人未配置可见性预警监听器
 *
 * <AUTHOR>
 * @since 2020/9/10 00:52
 */
@Slf4j
@Service
public class InvisibleWarningListener implements ApplicationListener<InvisibleWarningEvent> {

    @Autowired
    private UserService userService;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private SendMessageManager sendMessageManager;
    @Autowired
    private UserRoleInfoMapper userRoleInfoMapper;
    @Autowired
    private PublishScopeInfoMapper scopeInfoMapper;

    @Value("${frontendUrl}")
    private String frontendUrl;

    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    private static final String LANDING_URL = "message/detail/";

    @Override
    public void onApplicationEvent(InvisibleWarningEvent invisibleWarningEvent) {
        if (invisibleWarningEvent.getWarningNumber() > 0) {
            return;
        }

        TaskSummaryBo taskSummaryBo = invisibleWarningEvent.getTaskSummaryBo();
        TaskSummaryBo.ErrorSummary errorSummary = invisibleWarningEvent.getErrorSummary();
        TaskInfo taskInfo = getTaskInfo(taskSummaryBo);
        if (null == taskInfo) {
            log.warn("taskInfo not be found, extraId = [{}]", taskSummaryBo.getExtraId());
            return;
        }

        UserInfoDTO createUser = userService.getLoginUser(taskInfo.getCreateUsername());
        if (null == createUser) {
            log.warn("createUser not be found, username = [{}]", taskInfo.getCreateUsername());
            return;
        }

        WarningCardContentBo warningCardContentBo = new WarningCardContentBo();
        warningCardContentBo.setName(createUser.getName());
        warningCardContentBo.setTitle(taskInfo.getTitleCn());
        PublishScopeEnum scope = PublishScopeEnum.getByCode(taskInfo.getPublishScope());
        warningCardContentBo.setScope(scope.getMsg());
        warningCardContentBo.setNumber(String.valueOf(errorSummary.getCount()));
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("landingUrl", getLandingUrl(taskInfo));
        warningCardContentBo.setAction(paramMap);
        warningCardContentBo.setPushUserList(Collections.singletonList(new UserBO(createUser.getUsername(), createUser.getEmail())));
        boolean isSend = sendMessageManager.sendVisibleWarningCardMessage(warningCardContentBo);
        Preconditions.checkArgument(isSend, "发送失败");
        sendWarnToAdmin(errorSummary, taskInfo, createUser, scope, paramMap);
        log.info("发送机器人未配置可见性预警通知，extraId = [{}]", taskSummaryBo.getExtraId());
    }

    private void sendWarnToAdmin(TaskSummaryBo.ErrorSummary errorSummary, TaskInfo taskInfo, UserInfoDTO createUser, PublishScopeEnum scope, Map<String, Object> paramMap) {
        SuperAdminWarningCardContentBo superAdminWarningCardContentBo = new SuperAdminWarningCardContentBo();
        superAdminWarningCardContentBo.setName(createUser.getName());
        superAdminWarningCardContentBo.setTitle(taskInfo.getTitleCn());
        superAdminWarningCardContentBo.setScope(scope.getMsg());
        superAdminWarningCardContentBo.setPublishDate(DateFormatUtils.format(taskInfo.getPublishTime(), DATE_TIME_PATTERN));
        paramMap.put("landingUrl", getLandingUrl(taskInfo));
        superAdminWarningCardContentBo.setAction(paramMap);
        superAdminWarningCardContentBo.setNumber(String.valueOf(errorSummary.getCount()));
        setPushUserList(superAdminWarningCardContentBo);
        setChannel(taskInfo, superAdminWarningCardContentBo);
        setAllCount(taskInfo, superAdminWarningCardContentBo);
        boolean isAdminSend = sendMessageManager.sendWarningCardMessageToAdmin(superAdminWarningCardContentBo, WarningTypeEnum.INVISIBLE);
        Preconditions.checkArgument(isAdminSend, "超管提醒消息发送失败");
    }

    private void setAllCount(TaskInfo taskInfo, SuperAdminWarningCardContentBo superAdminWarningCardContentBo) {
        List<PublishScopeInfo> publishScopeInfoList = scopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery()
                        .eq(PublishScopeInfo::getTaskId, taskInfo.getId())
                        .select(PublishScopeInfo::getAllCount)
        );
        if (!publishScopeInfoList.isEmpty()) {
            long allCount = publishScopeInfoList.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).orElse(0L);
            superAdminWarningCardContentBo.setAllCount(allCount);
        } else {
            superAdminWarningCardContentBo.setAllCount(0L);
        }
    }

    private void setPushUserList(SuperAdminWarningCardContentBo superAdminWarningCardContentBo) {
        List<UserRoleInfo> userRoleInfoList = userRoleInfoMapper.selectList(
                Wrappers.<UserRoleInfo>lambdaQuery().eq(UserRoleInfo::getRoleType, RoleTypeEnum.ROLE_SUPER_ADMIN.getCode())
        );
        List<UserBO> userBOList = new ArrayList<>();
        if (!userRoleInfoList.isEmpty()) {
            userRoleInfoList.forEach(
                    userRoleInfo -> userBOList.add(new UserBO(userRoleInfo.getUsername(), userRoleInfo.getUsername().concat("@xiaomi.com")))

            );
        }
        superAdminWarningCardContentBo.setPushUserList(userBOList);
    }

    private void setChannel(TaskInfo taskInfo, SuperAdminWarningCardContentBo superAdminWarningCardContentBo) {
        Byte channel = taskInfo.getChannel();
        switch (channel) {
            case 1:
                superAdminWarningCardContentBo.setChannel("小米办公");
                break;
            case 2:
                superAdminWarningCardContentBo.setChannel("邮箱");
                break;
            case 3:
                superAdminWarningCardContentBo.setChannel("短信");
                break;
            default:
                break;
        }
    }

    private TaskInfo getTaskInfo(TaskSummaryBo taskSummaryBo) {
        return taskInfoMapper.selectOne(Wrappers.<TaskInfo>lambdaQuery()
                .eq(TaskInfo::getExtraId, taskSummaryBo.getExtraId()));

    }

    private String getLandingUrl(TaskInfo taskInfo) {
        return frontendUrl.concat(LANDING_URL).concat(String.valueOf(taskInfo.getId()));
    }
}
