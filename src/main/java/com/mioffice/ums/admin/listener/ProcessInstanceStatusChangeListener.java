package com.mioffice.ums.admin.listener;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Preconditions;
import com.mioffice.ums.admin.constants.ProcessStatusConstant;
import com.mioffice.ums.admin.entity.bo.MsgApproveCardContentBo;
import com.mioffice.ums.admin.entity.bo.ProcessInstanceStatusChangeEvent;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.ProcessInstanceInfo;
import com.mioffice.ums.admin.entity.info.ProcessTaskInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.TaskStatusEnum;
import com.mioffice.ums.admin.manager.SendMessage;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.ProcessInstanceInfoMapper;
import com.mioffice.ums.admin.mapper.ProcessTaskInfoMapper;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 流程实例状态变化事件监听器
 *
 * <AUTHOR>
 * @date 2020/8/16 18:13
 */
@Slf4j
@Service
public class ProcessInstanceStatusChangeListener implements ApplicationListener<ProcessInstanceStatusChangeEvent> {

    @Value("${frontendUrl}")
    private String frontendUrl;

    @Value("${lark.appId}")
    private String umsBotAppId;

    private final TaskInfoMapper taskInfoMapper;

    private final SendMessage sendMessage;

    private final EmployeeInfoMapper employeeInfoMapper;

    private final ProcessTaskInfoMapper processTaskInfoMapper;

    private final ProcessInstanceInfoMapper processInstanceInfoMapper;

    private static final String LANDING_URL = "message/detail/";

    public ProcessInstanceStatusChangeListener(TaskInfoMapper taskInfoMapper, SendMessage sendMessage, EmployeeInfoMapper employeeInfoMapper, ProcessTaskInfoMapper processTaskInfoMapper, ProcessInstanceInfoMapper processInstanceInfoMapper) {
        this.taskInfoMapper = taskInfoMapper;
        this.sendMessage = sendMessage;
        this.employeeInfoMapper = employeeInfoMapper;
        this.processTaskInfoMapper = processTaskInfoMapper;
        this.processInstanceInfoMapper = processInstanceInfoMapper;
    }

    @Override
    public void onApplicationEvent(ProcessInstanceStatusChangeEvent processInstanceStatusChangeEvent) {

        log.info("任务ID为[{}]的流程审批完成, 当前参数[{}]", processInstanceStatusChangeEvent.getBizId(), processInstanceStatusChangeEvent.toString());

        TaskInfo taskInfo = taskInfoMapper.selectById(processInstanceStatusChangeEvent.getBizId());

        if (taskInfo.getTaskStatus().intValue() != (int) TaskStatusEnum.APPROVING.getCode()) {
            log.warn("任务ID为[{}]的任务已经被审批", taskInfo.getId());
            return;
        }

        Byte taskUpdateStatus = this.taskUpdateStatus(Integer.parseInt(processInstanceStatusChangeEvent.getStatus()));

        TaskInfo updateTaskInfo = new TaskInfo();
        updateTaskInfo.setId(taskInfo.getId());
        updateTaskInfo.setTaskStatus(taskUpdateStatus);
        taskInfoMapper.update(updateTaskInfo,
                Wrappers.<TaskInfo>lambdaUpdate()
                        .eq(TaskInfo::getId, taskInfo.getId())
        );

        this.pushResultLarkCard(processInstanceStatusChangeEvent, taskInfo);
    }

    /**
     * 获取流程状态
     *
     * @param processStatus 流程状态（1：审批中，2：驳回，3：已完成，4：终止)
     * @return 需要更新的状态
     */
    public Byte taskUpdateStatus(int processStatus) {
        switch (processStatus) {
            case 2:
                log.info("审批驳回，获取流程状态 processStatus = [{}]", processStatus);
                return TaskStatusEnum.APPROVE_BACK.getCode();
            case 3:
                log.info("审批通过，获取流程状态 processStatus = [{}]", processStatus);
                return TaskStatusEnum.APPROVED.getCode();
            default:
                return null;
        }
    }

    private void pushResultLarkCard(ProcessInstanceStatusChangeEvent processInstanceStatusChangeEvent, TaskInfo taskInfo) {

        String status = processInstanceStatusChangeEvent.getStatus();

        if (StringUtils.isNotBlank(status)) {

            EmployeeInfo employeeInfo = employeeInfoMapper.selectOne(
                    Wrappers.<EmployeeInfo>lambdaQuery()
                            .eq(EmployeeInfo::getUsername, taskInfo.getCreateUsername())
                            .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
            );

            ProcessInstanceInfo processInstanceInfo = processInstanceInfoMapper.selectOne(
                    Wrappers.<ProcessInstanceInfo>lambdaQuery()
                            .eq(ProcessInstanceInfo::getId, processInstanceStatusChangeEvent.getProcessInstanceId())
            );

            EmployeeInfo employeeInfo2 = employeeInfoMapper.selectOne(
                    Wrappers.<EmployeeInfo>lambdaQuery()
                            .eq(EmployeeInfo::getUsername, processInstanceInfo.getUpdateUsername())
                            .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
            );

            MsgApproveCardContentBo msgApproveCardContentBo = new MsgApproveCardContentBo();
            msgApproveCardContentBo.setTitle(taskInfo.getTitleCn());
            msgApproveCardContentBo.setApproveName(employeeInfo2.getName());
            msgApproveCardContentBo.setApproveUsername(employeeInfo2.getUsername());
            msgApproveCardContentBo.setCreateEmail(employeeInfo.getEmail());
            msgApproveCardContentBo.setCreateName(employeeInfo.getName());
            msgApproveCardContentBo.setCreateUsername(taskInfo.getCreateUsername());
            msgApproveCardContentBo.setLandingUrl(frontendUrl.concat(LANDING_URL).concat(String.valueOf(processInstanceStatusChangeEvent.getBizId())));
            msgApproveCardContentBo.setProcessStatus(Integer.parseInt(status));

            // 如果是拒绝 需要记录拒绝原因:
            if (msgApproveCardContentBo.getProcessStatus() == Integer.parseInt(ProcessStatusConstant.PROCESS_STATUS_REJECT)) {
                List<ProcessTaskInfo> processTaskInfoList = processTaskInfoMapper.selectList(
                        Wrappers.<ProcessTaskInfo>lambdaQuery()
                                .eq(ProcessTaskInfo::getInstanceId, processInstanceStatusChangeEvent.getProcessInstanceId())
                                .eq(ProcessTaskInfo::getApproveResultValue, ProcessStatusConstant.ACTION_REJECT)
                );
                if (!processTaskInfoList.isEmpty()) {
                    String remark = processTaskInfoList.get(0).getRemark();
                    if (StringUtils.isNotBlank(remark)) {
                        msgApproveCardContentBo.setRefuseReason(remark);
                    } else {
                        msgApproveCardContentBo.setRefuseReason("-");
                    }
                }
            }

            boolean isSend = sendMessage.sendApplyResultCardMessage(umsBotAppId, msgApproveCardContentBo);
            Preconditions.checkArgument(isSend, "发送审核结果卡失败");
        }
    }
}