package com.mioffice.ums.admin.listener;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.admin.entity.bo.MsgApplyCardContentBo;
import com.mioffice.ums.admin.entity.bo.ProcessTaskCreateEvent;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.info.BotInfo;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.LarkTaskInfo;
import com.mioffice.ums.admin.entity.info.PublishScopeInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.enums.TaskChannelEnum;
import com.mioffice.ums.admin.manager.SendMessage;
import com.mioffice.ums.admin.mapper.BotInfoMapper;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.LarkTaskInfoMapper;
import com.mioffice.ums.admin.mapper.PublishScopeInfoMapper;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 审批任务创建事件监听器
 *
 * <AUTHOR>
 * @date 2020/8/16 18:10
 */
@Slf4j
@Service
public class ProcessTaskCreateListener implements ApplicationListener<ProcessTaskCreateEvent> {

    @Value("${frontendUrl}")
    private String frontendUrl;

    @Value("${lark.appId}")
    private String umsBotAppId;

    private final BotInfoMapper botInfoMapper;

    private final TaskInfoMapper taskInfoMapper;

    private final LarkTaskInfoMapper larkTaskInfoMapper;

    private final SendMessage sendMessage;

    private final EmployeeInfoMapper employeeInfoMapper;

    private final PublishScopeInfoMapper scopeInfoMapper;

    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    private static final String LANDING_URL = "message/detail/";

    public ProcessTaskCreateListener(BotInfoMapper botInfoMapper, TaskInfoMapper taskInfoMapper, LarkTaskInfoMapper larkTaskInfoMapper, SendMessage sendMessage, EmployeeInfoMapper employeeInfoMapper, PublishScopeInfoMapper scopeInfoMapper) {
        this.botInfoMapper = botInfoMapper;
        this.taskInfoMapper = taskInfoMapper;
        this.larkTaskInfoMapper = larkTaskInfoMapper;
        this.sendMessage = sendMessage;
        this.employeeInfoMapper = employeeInfoMapper;
        this.scopeInfoMapper = scopeInfoMapper;
    }

    @Override
    public void onApplicationEvent(ProcessTaskCreateEvent processTaskCreateEvent) {
        log.info("当前为消息ID为[{}]的任务向审批人发送卡片消息,当前请求参数为[{}]", processTaskCreateEvent.getBizId(), processTaskCreateEvent.toString());

        TaskInfo taskInfo = taskInfoMapper.selectById(processTaskCreateEvent.getBizId());

        MsgApplyCardContentBo applyCardContentBo = convert(processTaskCreateEvent, taskInfo, taskInfo.getChannel());

        sendMessage.sendApplyCardMessage(umsBotAppId, applyCardContentBo);
    }

    private MsgApplyCardContentBo convert(ProcessTaskCreateEvent processTaskCreateEvent, TaskInfo taskInfo, byte channel) {

        EmployeeInfo employeeInfo = employeeInfoMapper.selectOne(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .eq(EmployeeInfo::getUsername, taskInfo.getCreateUsername())
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        );

        List<PublishScopeInfo> publishScopeInfoList = scopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery()
                        .eq(PublishScopeInfo::getTaskId, taskInfo.getId())
                        .select(PublishScopeInfo::getAllCount)
        );

        long allCount = 0;
        if (!publishScopeInfoList.isEmpty()) {
            allCount = publishScopeInfoList.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).orElse(0L);
        }

        MsgApplyCardContentBo applyCardContentBo = new MsgApplyCardContentBo();
        applyCardContentBo.setTitle(taskInfo.getTitleCn());
        applyCardContentBo.setPushMode(getPushMode(channel, taskInfo.getId()));
        applyCardContentBo.setCreateName(employeeInfo.getName());
        applyCardContentBo.setPublishDate(DateFormatUtils.format(taskInfo.getPublishTime(), DATE_TIME_PATTERN));
        applyCardContentBo.setCreateUsername(taskInfo.getCreateUsername());
        applyCardContentBo.setTaskId(taskInfo.getId());
        applyCardContentBo.setLandingUrl(frontendUrl.concat(LANDING_URL).concat(String.valueOf(processTaskCreateEvent.getBizId())));
        applyCardContentBo.setTaskInfoId(processTaskCreateEvent.getTaskInfoId());
        applyCardContentBo.setAllCount(allCount);
        applyCardContentBo.setPublishScopeEnum(PublishScopeEnum.getByCode(taskInfo.getPublishScope()));
        applyCardContentBo.setUnit(taskInfo.getPublishScope() == PublishScopeEnum.GROUP_PUSH.getCode().intValue() ? "群" : "人");

        EmployeeInfo employeeInfo2 = employeeInfoMapper.selectOne(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .eq(EmployeeInfo::getUsername, processTaskCreateEvent.getApproverId())
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        );

        applyCardContentBo.setPushUserList(Collections.singletonList(new UserBO(processTaskCreateEvent.getApproverId(), employeeInfo2.getEmail())));

        return applyCardContentBo;
    }

    private String getPushMode(byte channel, long taskId) {
        if (TaskChannelEnum.CHANNEL_LARK.getCode() == channel) {
            return getBotInfo(taskId).getBotName().concat("机器人");
        } else if (TaskChannelEnum.CHANNEL_EMAIL.getCode() == channel) {
            return TaskChannelEnum.CHANNEL_EMAIL.getMsg();
        } else if (TaskChannelEnum.CHANNEL_SMS.getCode() == channel) {
            return TaskChannelEnum.CHANNEL_SMS.getMsg();
        }
        return "";
    }

    private BotInfo getBotInfo(long taskId) {
        LarkTaskInfo larkTaskInfo = larkTaskInfoMapper.selectOne(
                Wrappers.<LarkTaskInfo>lambdaQuery()
                        .eq(LarkTaskInfo::getTaskId, taskId)
        );
        return botInfoMapper.selectById(larkTaskInfo.getBotId());
    }
}
