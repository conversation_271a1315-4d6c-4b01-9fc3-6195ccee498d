package com.mioffice.ums.admin.controller.api;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.AlarmBo;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.vo.AlarmTrendVO;
import com.mioffice.ums.admin.entity.vo.TaskMonitorListVO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.WarningService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.09
 */
@RestController
@RequestMapping("/ums-api/api")
@PreAuthorize("hasRole('ROLE_COMMON') || hasRole('ROLE_OPERATOR') || hasRole('ROLE_SYS_ADMIN') || hasRole('ROLE_SYS_SUPER_ADMIN') || hasRole('ROLE_BOT_ADMIN') || hasRole('ROLE_BOT_USER')")
public class WarningController {

    private final WarningService warningService;

    public WarningController(WarningService warningService) {
        this.warningService = warningService;
    }

    @GetMapping("/data/analysis/alarm")
    public BaseResult<AlarmTrendVO> getAlarmTrendApi(
            String start, String end,
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO
    ) {
        Assert.hasText(start, "起始时间为空");
        Assert.hasText(end, "结束时间为空");

        DateTime beginTime = DateUtil.beginOfDay(DateUtil.parseDate(start));
        DateTime endTime = DateUtil.endOfDay(DateUtil.parseDate(end));
        return warningService.getAlarmTrend(userBO, beginTime, endTime);
    }

    @GetMapping("/data/analysis/alarm/page")
    public BaseResult<IPage<AlarmBo>> getAlarmPage(
            Long page,
            Long size,
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO
    ) {
        Assert.notNull(page, "页码不能为空");
        Assert.notNull(size, "页大小不能为空");

        return warningService.getAlarmPage(userBO, page, size);
    }

    @GetMapping("/data/analysis/alarm/now")
    public BaseResult<TaskMonitorListVO> getNowTaskMonitor(@RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO) {

        return warningService.getNowMonitorList(userBO);
    }
}
