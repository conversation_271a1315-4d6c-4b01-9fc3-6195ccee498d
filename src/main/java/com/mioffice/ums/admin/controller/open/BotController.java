package com.mioffice.ums.admin.controller.open;

import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.BotApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020/10/19 1:42 下午
 */
@Slf4j
@RestController
@RequestMapping("/ums-api/open")
public class BotController {

    @Autowired
    private BotApiService botApiService;

    @GetMapping("/fillBotBizId")
    public BaseResult<Object> clearReceiveMember(Long botId) {
        return botApiService.fillBotBizId(botId);
    }
}
