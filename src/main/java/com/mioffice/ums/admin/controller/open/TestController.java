package com.mioffice.ums.admin.controller.open;

import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.admin.entity.info.ReceiveMemberInfo;
import com.mioffice.ums.admin.manager.ProcessManager;
import com.mioffice.ums.admin.mapper.ReceiveMemberInfoMapper;
import com.mioffice.ums.admin.remote.grpc.PushSummaryGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 勿删
 * </p>
 *
 * <AUTHOR>
 * @date 2020/8/31 16:03
 */
@Slf4j
@RestController
@RequestMapping("/ums-api/open")
public class TestController {

    @Autowired
    private PushSummaryGrpcClient pushSummaryGrpcClient;

    private final ReceiveMemberInfoMapper receiveMemberInfoMapper;

    private final ProcessManager processManager;

    private static final String UNIQUE_ID = "clearDirtyDataAA";

    public TestController(ReceiveMemberInfoMapper receiveMemberInfoMapper, ProcessManager processManager) {
        this.receiveMemberInfoMapper = receiveMemberInfoMapper;
        this.processManager = processManager;
    }

    @GetMapping("/clearReceiveMember")
    public BaseResult<Object> clearReceiveMember(String taskId, String uniqueId) {
        Assert.isTrue(uniqueId.equals(UNIQUE_ID), "uniqueId为空");
        receiveMemberInfoMapper.delete(
                Wrappers.<ReceiveMemberInfo>lambdaUpdate()
                        .eq(ReceiveMemberInfo::getTaskId, taskId));
        return BaseResult.of();
    }

    @GetMapping("/straightPass")
    public BaseResult<Object> straightPass(Long taskId, String uniqueId, String operateUser) {
        Assert.isTrue(uniqueId.equals(UNIQUE_ID), "uniqueId为空");
        processManager.straightPass(taskId, StringUtils.isBlank(operateUser) ? "" : operateUser);
        return BaseResult.of();
    }

    @GetMapping("/syncAppMonitor")
    public BaseResult<Object> syncAppMonitor(Long startTime, Long endTime) {
        return pushSummaryGrpcClient.syncAppMonitor(startTime, endTime);
    }
}
