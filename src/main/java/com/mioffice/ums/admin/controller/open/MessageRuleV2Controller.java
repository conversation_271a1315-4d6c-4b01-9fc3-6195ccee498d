package com.mioffice.ums.admin.controller.open;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.mioffice.ums.admin.entity.dto.IdDTO;
import com.mioffice.ums.admin.entity.dto.MessageRuleV2JobDTO;
import com.mioffice.ums.admin.entity.info.MessageJob;
import com.mioffice.ums.admin.entity.info.MessageJobExecuteRecord;
import com.mioffice.ums.admin.enums.MessageJobTypeEnum;
import com.mioffice.ums.admin.enums.MessageSubsequentJobTypeEnum;
import com.mioffice.ums.admin.remote.grpc.MessageGrpcClient;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.MessageJobExecuteRecordService;
import com.mioffice.ums.admin.service.MessageRuleJobService;
import com.mioffice.ums.admin.utils.JsonUtils;
import com.mioffice.ums.admin.utils.X5RequestContext;
import com.mioffice.ums.admin.x5client.X5Request;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 05 15,2023
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(value = "/ums-api/open/v2", consumes = "application/x-www-form-urlencoded")
public class MessageRuleV2Controller {

    private MessageRuleJobService messageRuleJobService;

    private MessageJobExecuteRecordService messageJobExecuteRecordService;

    private final MessageGrpcClient messageGrpcClient;

    /**
     * 增加消息规则
     */
    @PostMapping(value = "/message/job/add")
    public BaseResult ruleAdd() {
        X5Request request = X5RequestContext.get();
        MessageRuleV2JobDTO messageRuleJob = JsonUtils.parse(request.getBody(), MessageRuleV2JobDTO.class);
        messageRuleJob.setAppId(request.getHeader().getAppId());
        return messageRuleJobService.ruleJobAddV2(messageRuleJob);
    }

    /**
     * 修改消息规则
     **/
    @PostMapping(value = "/message/job/update")
    public BaseResult ruleUpdate() {
        X5Request request = X5RequestContext.get();
        MessageRuleV2JobDTO messageRuleJob = JsonUtils.parse(request.getBody(), MessageRuleV2JobDTO.class);
        messageRuleJob.setAppId(request.getHeader().getAppId());
        return messageRuleJobService.ruleJobUpdateV2(messageRuleJob);
    }

    @PostMapping(value = "/message/job/execution/record")
    public BaseResult ruleExecutionRecords() {
        X5Request request = X5RequestContext.get();
        IdDTO idDTO = JsonUtils.parse(request.getBody(), IdDTO.class);
        List<MessageJobExecuteRecord> messageJobExecuteRecordList = messageJobExecuteRecordService.list(Wrappers
                .<MessageJobExecuteRecord>lambdaQuery()
                .eq(MessageJobExecuteRecord::getMessageJobId, idDTO.getId()));
        return BaseResult.of(messageJobExecuteRecordList);
    }

    @PostMapping(value = "/message/job/unclick/user")
    public BaseResult ruleUnclickUsers() {
        X5Request request = X5RequestContext.get();
        IdDTO idDTO = JsonUtils.parse(request.getBody(), IdDTO.class);
        MessageJob job = messageRuleJobService.getById(idDTO.getId());
        Preconditions.checkArgument(Objects.nonNull(job), "id参数错误，对应的规则不存在");

        if (MessageSubsequentJobTypeEnum.NONE.getCode().equals(job.getSubsequentJobType())) {
            Preconditions.checkArgument(Objects.nonNull(idDTO.getExtraId()), "extraId必传");
            Set<String> unClickUserSet = messageJobExecuteRecordService.getUnClickUserByExtraIdList(idDTO.getExtraId(),
                    Lists.newArrayList());
            return BaseResult.of(unClickUserSet);
        } else {
            List<String> extraIdList =
                    messageJobExecuteRecordService.list(Wrappers.<MessageJobExecuteRecord>lambdaQuery()
                                    .eq(MessageJobExecuteRecord::getMessageJobId, idDTO.getId())
                                    .eq(MessageJobExecuteRecord::getReturnCode, 200))
                            .stream()
                            .map(MessageJobExecuteRecord::getExtraId)
                            .collect(Collectors.toList());

            String parentExtraId = extraIdList.get(0);
            List<String> subExtraIdList = Lists.newArrayList();
            if (extraIdList.size() > 1) {
                subExtraIdList = extraIdList.stream().skip(1).collect(Collectors.toList());
            }

            if (MessageJobTypeEnum.URGE_NORMAL.getCode().equals(job.getJobType())) {
                Set<String> unClickUserSet = messageJobExecuteRecordService.getUnClickUserByExtraIdList(parentExtraId,
                        subExtraIdList);
                return BaseResult.of(unClickUserSet);
            } else {
                String extraIdPrefix = "urgeManagerByRuleSuccess";
                if (MessageJobTypeEnum.URGE_CUSTOM.getCode().equals(job.getJobType())) {
                    extraIdPrefix = "urgeCustomSuccess";
                }

                String finalExtraIdPrefix = extraIdPrefix;

                String finalParentExtraId = Joiner.on("-")
                        .join(extraIdPrefix, job.getId(), parentExtraId);

                List<String> finalSubExtraIdList = subExtraIdList
                        .stream()
                        .map(subExtraId -> Joiner.on("-")
                                .join(finalExtraIdPrefix, job.getId(), subExtraId))
                        .collect(Collectors.toList());

                Set<String> unClickUserSet =
                        messageJobExecuteRecordService.getUnClickUserByExtraIdList(finalParentExtraId,
                                finalSubExtraIdList);
                return BaseResult.of(unClickUserSet);
            }
        }
    }

    @PostMapping(value = "/message/job/unread/user")
    public BaseResult ruleUnreadUsers() {
        X5Request request = X5RequestContext.get();
        IdDTO idDTO = JsonUtils.parse(request.getBody(), IdDTO.class);
        MessageJob job = messageRuleJobService.getById(idDTO.getId());
        Preconditions.checkArgument(Objects.nonNull(job), "id参数错误，对应的规则不存在");

        if (MessageSubsequentJobTypeEnum.NONE.getCode().equals(job.getSubsequentJobType())) {
            Preconditions.checkArgument(Objects.nonNull(idDTO.getExtraId()), "extraId必传");
            List<String> result = messageGrpcClient.getAllUnRead(idDTO.getExtraId(), Lists.newArrayList());
            return BaseResult.of(result);
        } else {
            List<String> extraIdList =
                    messageJobExecuteRecordService.list(Wrappers.<MessageJobExecuteRecord>lambdaQuery()
                                    .eq(MessageJobExecuteRecord::getMessageJobId, idDTO.getId())
                                    .eq(MessageJobExecuteRecord::getReturnCode, 200))
                            .stream()
                            .map(MessageJobExecuteRecord::getExtraId)
                            .collect(Collectors.toList());

            String parentExtraId = extraIdList.get(0);
            List<String> subExtraIdList = Lists.newArrayList();
            if (extraIdList.size() > 1) {
                subExtraIdList = extraIdList.stream().skip(1).collect(Collectors.toList());
            }

            if (MessageJobTypeEnum.URGE_NORMAL.getCode().equals(job.getJobType())) {
                List<String> result = messageGrpcClient.getAllUnRead(parentExtraId, subExtraIdList);
                return BaseResult.of(result);
            } else {
                String extraIdPrefix = "urgeManagerByRuleSuccess";
                if (MessageJobTypeEnum.URGE_CUSTOM.getCode().equals(job.getJobType())) {
                    extraIdPrefix = "urgeCustomSuccess";
                }

                String finalExtraIdPrefix = extraIdPrefix;

                String finalParentExtraId = Joiner.on("-")
                        .join(extraIdPrefix, job.getId(), parentExtraId);

                List<String> finalSubExtraIdList = subExtraIdList
                        .stream()
                        .map(subExtraId -> Joiner.on("-")
                                .join(finalExtraIdPrefix, job.getId(), subExtraId))
                        .collect(Collectors.toList());

                List<String> result = messageGrpcClient.getAllUnRead(finalParentExtraId, finalSubExtraIdList);
                return BaseResult.of(result);
            }
        }
    }

}
