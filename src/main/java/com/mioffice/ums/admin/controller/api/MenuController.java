package com.mioffice.ums.admin.controller.api;

import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.vo.MenuVO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.MenuService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 菜单
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/5 10:28
 */
@RestController
@RequestMapping("/ums-api/api")
public class MenuController {

    private final MenuService menuService;

    public MenuController(MenuService menuService) {
        this.menuService = menuService;
    }

    @GetMapping("/menu/list")
    public BaseResult<List<MenuVO>> getMenuList(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO
    ) {
        return BaseResult.of(menuService.getMenuListByLoginUser(userBO.getUsername()));
    }

}
