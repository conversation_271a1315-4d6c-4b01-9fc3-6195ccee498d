package com.mioffice.ums.admin.controller.api;

import com.mioffice.ums.admin.entity.vo.ChooseListVO;
import com.mioffice.ums.admin.entity.vo.ChooseVO;
import com.mioffice.ums.admin.message.member.ChooseIdEnum;
import com.mioffice.ums.admin.result.BaseResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 筛选推送
 * </p>
 *
 * <AUTHOR>
 * @date 2020.11.11
 */

@RestController
@RequestMapping("/ums-api/api")
public class ChooseController {

    @GetMapping("/choose/id/list")
    public BaseResult<ChooseListVO> chooseList() {

        List<ChooseVO> chooseVOList = Stream.of(ChooseIdEnum.values())
                .filter(p -> p.getId() > 0)
                .map(chooseIdEnum -> new ChooseVO(chooseIdEnum.getId().longValue(), chooseIdEnum.getDesc()))
                .collect(Collectors.toList());

        return BaseResult.of(new ChooseListVO(chooseVOList));
    }
}
