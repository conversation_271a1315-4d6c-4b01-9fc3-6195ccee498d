package com.mioffice.ums.admin.controller.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.MessageDraftDTO;
import com.mioffice.ums.admin.entity.dto.MessageDraftQueryDTO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.MessageDraftService;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 草稿箱
 * </p>
 *
 * @author: shi tie tou
 * @date: 2020/8/14 1:52
 */
@RestController
@RequestMapping("/ums-api/api")
public class MessageDraftController {

    private final MessageDraftService messageDraftService;

    public MessageDraftController(MessageDraftService messageDraftService) {
        this.messageDraftService = messageDraftService;
    }

    @PostMapping("/task/draft/page")
    public BaseResult<IPage<MessageDraftDTO>> page(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody MessageDraftQueryDTO messageDraftQueryDTO) {
        return BaseResult.of(messageDraftService.draftPage(messageDraftQueryDTO, userBO));
    }

    @PostMapping("/task/delete")
    public BaseResult<Integer> delete(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody MessageDraftQueryDTO messageDraftQueryDTO) {
        Assert.notNull(messageDraftQueryDTO.getTaskId(), "消息任务ID为空");
        int affectRows = messageDraftService.deleteDraftTask(messageDraftQueryDTO, userBO);
        if (affectRows == -1) {
            return new BaseResult<>(ResultCode.FORBIDDEN);
        }
        return BaseResult.of(affectRows);
    }
}
