package com.mioffice.ums.admin.controller.api;

import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.AppBo;
import com.mioffice.ums.admin.entity.bo.AppStatusBo;
import com.mioffice.ums.admin.entity.bo.AppTopicBO;
import com.mioffice.ums.admin.entity.bo.AppTopicDetailBO;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.AppWhiteStatusDto;
import com.mioffice.ums.admin.entity.vo.AppDetailVO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.AppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2020/9/21
 */
@Slf4j
@RestController
@RequestMapping("/ums-api/api/app")
public class AppController {
    @Autowired
    private AppService appService;

    @PostMapping("apply")
    public BaseResult<AppStatusBo> apply(@Valid @RequestBody AppBo appBo,
                                         @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO) {
        if (appBo.getId() != null) {
            return new BaseResult<AppStatusBo>(ResultCode.PARAM_ERROR).setMessage("申请应用传入非法 id");
        }
        return appService.apply(appBo, userBO);
    }

    @PostMapping("update")
    public BaseResult<AppStatusBo> update(@Valid @RequestBody AppBo appBo,
                                          @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO) {
        if (appBo.getId() == null) {
            return new BaseResult<AppStatusBo>(ResultCode.PARAM_ERROR).setMessage("申请应用传入非法 id");
        }
        return appService.apply(appBo, userBO);
    }

    @GetMapping("detail")
    public BaseResult<AppDetailVO> detail(@Valid @NotNull Long appSysId,
                                          @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO) {
        return appService.detail(appSysId, userBO);
    }

    @PutMapping("/white/status")
    public BaseResult<Boolean> updateWhiteStatus(@Valid @RequestBody AppWhiteStatusDto appWhiteStatusDto) {
        return appService.updateWhiteStatus(appWhiteStatusDto);
    }

    @PostMapping("/topic/apply")
    public BaseResult<Boolean> applyTopic(@Valid @RequestBody AppTopicBO appTopicBO,
                                          @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO) {
        return appService.applyTopic(appTopicBO, userBO);
    }

    @PostMapping("/topic/cancel")
    public BaseResult<Boolean> cancelTopic(@RequestBody AppTopicBO appTopicBO,
                                           @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO) {
        return appService.cancelTopic(appTopicBO.getAppId(), userBO);
    }

    @GetMapping("/topic/info")
    public BaseResult<AppTopicDetailBO> getTopic(@RequestParam String appId) {
        return appService.getAppTopic(appId);
    }

}
