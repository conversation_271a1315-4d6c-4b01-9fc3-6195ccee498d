package com.mioffice.ums.admin.controller.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.MessageApprovalDTO;
import com.mioffice.ums.admin.entity.dto.MessageApprovalQueryDTO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.MessageApprovalService;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 消息审核
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/14 20:16
 */
@RestController
@RequestMapping("/ums-api/api")
public class MessageApprovalController {

    private final MessageApprovalService messageApprovalService;

    public MessageApprovalController(MessageApprovalService messageApprovalService) {
        this.messageApprovalService = messageApprovalService;
    }

    @PostMapping("/task/approval/page")
    public BaseResult<IPage<MessageApprovalDTO>> page(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody MessageApprovalQueryDTO messageApprovalQueryDTO) {
        return BaseResult.of(messageApprovalService.messageApprovalPage(messageApprovalQueryDTO, userBO));
    }

    @PostMapping("/task/approval/pass")
    public BaseResult<Object> pass(
            @RequestBody MessageApprovalQueryDTO messageApprovalQueryDTO,
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO
    ) {
        Assert.notNull(messageApprovalQueryDTO.getTaskId(), "消息任务ID为空");
        return messageApprovalService.approved(messageApprovalQueryDTO, userBO);
    }

    @PostMapping("/task/approval/reject")
    public BaseResult<Object> reject(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody MessageApprovalQueryDTO messageApprovalQueryDTO) {
        Assert.notNull(messageApprovalQueryDTO.getTaskId(), "消息任务ID为空");
        Assert.hasLength(messageApprovalQueryDTO.getRemark(), "请填写拒绝原因");
        return messageApprovalService.approveReject(messageApprovalQueryDTO, userBO);
    }

}
