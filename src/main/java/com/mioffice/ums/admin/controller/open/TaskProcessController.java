package com.mioffice.ums.admin.controller.open;

import com.mioffice.ums.admin.entity.dto.TaskDeleteDTO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/19 5:53 下午
 * version: 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/ums-api/open")
public class TaskProcessController {

    private final TaskService taskService;

    public TaskProcessController(TaskService taskService) {
        this.taskService = taskService;
    }

    @PostMapping("/task/delete")
    public BaseResult<Object> taskDelete(
            @RequestHeader("token") String token,
            @RequestBody TaskDeleteDTO taskDeleteDTO
    ) {
        Assert.isTrue(token.equals("b8b6e58b-ed41-4e04-b249-48bd1a4939cc"), "令牌错误");
        Assert.notEmpty(taskDeleteDTO.getIds(), "ids为空");
        List<Long> ids = taskDeleteDTO.getIds();
        return taskService.taskDelete(ids);
    }

}
