package com.mioffice.ums.admin.controller.api;

import com.mioffice.ums.admin.entity.dto.DepartmentDTO;
import com.mioffice.ums.admin.entity.dto.DepartmentSummaryDTO;
import com.mioffice.ums.admin.enums.BotTenantEnum;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.DeptService;
import lombok.Data;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 部门查询
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/13 17:45
 */
@RestController
@RequestMapping("/ums-api/api")
public class DeptController {

    private final DeptService deptService;

    public DeptController(DeptService deptService) {
        this.deptService = deptService;
    }

    @GetMapping("/dept/all")
    public BaseResult<List<DepartmentDTO>> getDeptTree(
            @RequestParam(value = "botTenant",
                    required = false,
                    defaultValue = "0")
            Integer botTenant) {
        return BaseResult.of(deptService.getDeptTree(BotTenantEnum.getByCode(botTenant.byteValue())));
    }

    @GetMapping("/dept/search")
    public BaseResult<List<DepartmentDTO>> searchDeptTree(@RequestParam("searchKey") String searchKey,
                                                          @RequestParam(value = "botTenant",
                                                                  required = false,
                                                                  defaultValue = "0")
                                                          Integer botTenant) {
        return BaseResult.of(deptService.getDeptBySearchWord(searchKey, BotTenantEnum.getByCode(botTenant.byteValue())));
    }

    @GetMapping("/dept/count")
    public BaseResult<Map<String, List<DepartmentSummaryDTO>>> getDeptCount(@RequestParam("deptIds") String deptIds,
                                                                            @RequestParam(value = "botTenant",
                                                                                    required = false,
                                                                                    defaultValue = "0")
                                                                            Integer botTenant) {
        return BaseResult.of(deptService.getDeptCount(deptIds, BotTenantEnum.getByCode(botTenant.byteValue())));
    }

    @PostMapping("/dept/count")
    public BaseResult<Map<String, List<DepartmentSummaryDTO>>> getDeptCount(@RequestBody DeptCountVo req) {
        String deptIds = req.getDeptIds();
        return BaseResult.of(
                deptService.getDeptCount(deptIds,
                        BotTenantEnum.getByCode(Objects.nonNull(req.getBotTenant()) ?
                                req.getBotTenant() : (byte) 0)));
    }

    @Data
    public static class DeptCountVo {
        private String deptIds;
        private Byte botTenant;
    }
}
