package com.mioffice.ums.admin.controller.api;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.ImportPageDTO;
import com.mioffice.ums.admin.entity.dto.NameListAddDTO;
import com.mioffice.ums.admin.entity.dto.NameListDeleteDTO;
import com.mioffice.ums.admin.entity.dto.UploadDTO;
import com.mioffice.ums.admin.entity.info.ParseTmpInfo;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.exception.FileOperateException;
import com.mioffice.ums.admin.manager.MiCloudFdsManager;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.ImportNameListService;
import com.mioffice.ums.admin.utils.FileUtil;
import com.xiaomi.infra.galaxy.fds.model.FDSObjectMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/11 1:08 上午
 * version: 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/ums-api/api")
public class FileUploadController {

    private final ImportNameListService importNameListService;

    private final MiCloudFdsManager miCloudFdsManager;

    private static final Long IMAGE_FILE_MAX = 5000 * 1000L;

    private static final Long EXCEL_FILE_MAX = 20000 * 1000L;

    public FileUploadController(ImportNameListService importNameListService, MiCloudFdsManager miCloudFdsManager) {
        this.importNameListService = importNameListService;
        this.miCloudFdsManager = miCloudFdsManager;
    }

    @PostMapping("/excel/upload")
    public BaseResult<Object> uploadExcel(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam(required = false) MultipartFile file,
            @RequestParam(value = "excelId", required = false) String excelId
    ) {
        if(Objects.nonNull(file)) {
            Assert.isTrue(file.getSize() < EXCEL_FILE_MAX, "文件过大");
            if (StringUtils.isNotBlank(excelId)) {
                log.info("begin time = [{}]", System.currentTimeMillis());
                return importNameListService.upload(userBO, file, excelId);
            } else {
                String excelIdNew = UUID.randomUUID().toString();
                log.info("begin time = [{}]", System.currentTimeMillis());
                return importNameListService.upload(userBO, file, excelIdNew);
            }
        }else{
            return BaseResult.of(UUID.randomUUID().toString());
        }
    }

    @GetMapping("/excel/rate")
    public BaseResult<Object> rate(
            @RequestParam(value = "excelId") String excelId
    ) {
        Assert.notNull(excelId, "请传excelId");
        if (StringUtils.isBlank(excelId)) {
            return new BaseResult<>(ResultCode.PARAM_ERROR).setMessage("excelId错误");
        }
        return importNameListService.rate(excelId);
    }

    @GetMapping("/excel/page")
    public BaseResult<ImportPageDTO<ParseTmpInfo>> page(
            @RequestParam(value = "page") Long page,
            @RequestParam(value = "size") Long size,
            @RequestParam(value = "excelId", required = false) String excelId,
            @RequestParam(value = "key", required = false) String key,
            @RequestParam(value = "displayType", required = false,defaultValue = "4") Byte displayType
    ) {
        Assert.notNull(page, "请传page");
        Assert.notNull(size, "请传size");
        return importNameListService.page(page, size, excelId, key,PublishScopeEnum.getByCode(displayType));
    }

    @PostMapping("/excel/del")
    public BaseResult<Object> del(
            @RequestBody NameListDeleteDTO nameListDeleteDTO
    ) {
        Assert.notNull(nameListDeleteDTO.getExcelId(), "excelId为空");
        Assert.notNull(nameListDeleteDTO.getIdList(), "删除的名单id为空");
        return importNameListService.delete(nameListDeleteDTO);
    }

    @PostMapping("/excel/add")
    public BaseResult<Object> add(
            @RequestBody NameListAddDTO nameListAddDTO
    ) {
        Assert.notNull(nameListAddDTO.getUserNameList(), "userNameList为空");
        return importNameListService.add(nameListAddDTO);
    }

    @GetMapping("/excel/error/export")
    public BaseResult<Map<String, Object>> download(String excelId) {
        Assert.notNull(excelId, "excelId为空");
        BaseResult<Map<String, Object>> download;
        try {
            download = importNameListService.download(excelId);
        } catch (IOException e) {
            log.info("excel = [{}] 的存储数据导出异常", excelId);
            return new BaseResult<Map<String, Object>>().setCode(2000)
                    .setMessage("excelId为" + excelId + "的任务数据导出失败");
        }
        return download;
    }

    @PostMapping(value = "/image/upload")
    public BaseResult<UploadDTO> upload(MultipartFile file) throws FileOperateException {

        org.springframework.util.Assert.isTrue(file.getSize() < IMAGE_FILE_MAX, "文件过大");
        String originalFilename = file.getOriginalFilename();

        org.springframework.util.Assert.notNull(originalFilename, "解析文件名出错");

        try {
            String fileType = FileUtil.getFileExt(originalFilename);
            String folderName = "notice/".concat(DateUtil.today()).concat("/");
            String newFilename = FileUtil.getRandomName(originalFilename);

            FDSObjectMetadata fdsObjectMetadata = new FDSObjectMetadata();
            fdsObjectMetadata.setContentType("image/".concat(fileType.toLowerCase()));
            String url =
                    miCloudFdsManager.uploadFile(folderName + newFilename, file.getInputStream(), fdsObjectMetadata);
            return BaseResult.of(new UploadDTO(file.getOriginalFilename(), miCloudFdsManager.getHostUrl().concat(url)));
        } catch (Exception e) {
            log.warn("文件上传失败", e);
            throw new FileOperateException("文件上传失败，请联系管理员");
        }
    }
}
