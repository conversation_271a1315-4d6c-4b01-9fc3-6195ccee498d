package com.mioffice.ums.admin.controller.api;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.ApprovalConfigDTO;
import com.mioffice.ums.admin.entity.dto.RoleDTO;
import com.mioffice.ums.admin.entity.dto.TaskApprovalNodeQueryDTO;
import com.mioffice.ums.admin.entity.dto.UserRoleDTO;
import com.mioffice.ums.admin.entity.dto.UserRoleQueryDTO;
import com.mioffice.ums.admin.entity.vo.ApprovalTimeLine;
import com.mioffice.ums.admin.entity.vo.TaskApprovalNodeVO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.ApprovalConfigService;
import com.mioffice.ums.admin.service.AuthConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 配置中心
 * </p>
 *
 * <AUTHOR>
 * @date 2020/8/31 2:28 下午
 */
@RestController
@RequestMapping("/ums-api/api")
public class ConfigCenterController {

    private final AuthConfigService authConfigService;

    private final ApprovalConfigService approvalConfigService;

    @NacosValue(value = "${ums.admin.bpm-start-taskid:}", autoRefreshed = true)
    private String bpmStartTaskId;

    public ConfigCenterController(AuthConfigService authConfigService, ApprovalConfigService approvalConfigService) {
        this.authConfigService = authConfigService;
        this.approvalConfigService = approvalConfigService;
    }

    @PreAuthorize("hasRole('ROLE_SYS_SUPER_ADMIN')")
    @GetMapping("/auth/role/list")
    public BaseResult<List<RoleDTO>> getRoleList() {
        return authConfigService.queryRoleList();
    }

    @PreAuthorize("hasRole('ROLE_SYS_SUPER_ADMIN')")
    @PostMapping("/auth/user/list")
    public BaseResult<IPage<UserRoleDTO>> getUserRoleList(@RequestBody UserRoleQueryDTO userRoleQueryDTO) {
        return authConfigService.queryUserRolePage(userRoleQueryDTO);
    }

    @PreAuthorize("hasRole('ROLE_SYS_SUPER_ADMIN')")
    @PostMapping("/auth/user/add")
    public BaseResult<Object> addUserRole(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody UserRoleDTO userRoleDTO) {
        Assert.hasLength(userRoleDTO.getRoleTypeArrStr(), "用户没有分配权限");
        Assert.hasLength(userRoleDTO.getUsername(), "用户账号为空");
        Assert.hasLength(userRoleDTO.getName(), "用户姓名为空");
        return authConfigService.addUserRole(userRoleDTO, userBO.getUsername());
    }

    @PreAuthorize("hasRole('ROLE_SYS_SUPER_ADMIN')")
    @PostMapping("/auth/user/update")
    public BaseResult<Object> updateUserRole(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody UserRoleDTO userRoleDTO) {
        Assert.hasLength(userRoleDTO.getRoleTypeArrStr(), "用户没有分配权限");
        Assert.hasLength(userRoleDTO.getUsername(), "用户账号为空");
        Assert.hasLength(userRoleDTO.getName(), "用户姓名为空");
        return authConfigService.updateUserRole(userRoleDTO, userBO.getUsername());
    }

    @PreAuthorize("hasRole('ROLE_SYS_SUPER_ADMIN')")
    @PostMapping("/auth/user/delete")
    public BaseResult<Object> deleteUserRole(@RequestBody UserRoleDTO userRoleDTO) {
        return authConfigService.deleteUserRole(userRoleDTO);
    }

    @PreAuthorize("hasRole('ROLE_SYS_SUPER_ADMIN')")
    @GetMapping("/approval/get")
    public BaseResult<ApprovalConfigDTO> getApprovalConfig() {
        return approvalConfigService.getApprovalConfig();
    }

    @PreAuthorize("hasRole('ROLE_SYS_SUPER_ADMIN')")
    @PostMapping("/approval/update")
    public BaseResult<Object> updateApprovalConfig(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody ApprovalConfigDTO approvalConfigDTO
    ) {
        Assert.hasLength(approvalConfigDTO.getFlowChartUrl(), "流程图为空");
        Assert.notNull(approvalConfigDTO.getThreshold(), "没有设置高级阈值");
        Assert.notNull(approvalConfigDTO.getBaseThreshold(), "没有设置基础阈值");
        Assert.isTrue(Objects.nonNull(approvalConfigDTO.getEmailApprovalMetaNode()) && !approvalConfigDTO.getEmailApprovalMetaNode().isEmpty(), "邮件审核节点为空");
        Assert.isTrue(Objects.nonNull(approvalConfigDTO.getLarkApprovalMetaNode()) && !approvalConfigDTO.getLarkApprovalMetaNode().isEmpty(), "小米办公审核节点为空");
        Assert.isTrue(Objects.nonNull(approvalConfigDTO.getSmsApprovalMetaNode()) && !approvalConfigDTO.getSmsApprovalMetaNode().isEmpty(), "短信审核节点为空");

        return approvalConfigService.updateApprovalConfig(approvalConfigDTO, userBO.getUsername());
    }

    @GetMapping("/approval/info")
    public BaseResult<Map<String, Object>> getApprovalThreshold() {
        return approvalConfigService.getApprovalInfo(null);
    }

    @PostMapping("/approval/node")
    public BaseResult<TaskApprovalNodeVO> getApprovalNode(
            @RequestBody TaskApprovalNodeQueryDTO approvalNodeQueryDTO,
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO
    ) {
        Assert.notNull(approvalNodeQueryDTO.getPublishScope(), "获取审批流程异常，发布范围为空");
        Assert.notNull(approvalNodeQueryDTO.getChannel(), "获取审批流程异常，发布渠道为空");
        return approvalConfigService.getBpmApproval(approvalNodeQueryDTO, userBO.getUsername());
    }

    @GetMapping("/approval/timeline")
    public BaseResult<List<ApprovalTimeLine>> getApprovalTimeline(
            @RequestParam("taskId") Long taskId,
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO
    ) {
        Assert.notNull(taskId, "taskId为空");
        if (taskId < Long.parseLong(bpmStartTaskId)) {
            return approvalConfigService.getApprovalTimeLine(taskId, userBO);
        }
        return approvalConfigService.getBpmApprovalTimeLine(taskId, userBO);
    }
}
