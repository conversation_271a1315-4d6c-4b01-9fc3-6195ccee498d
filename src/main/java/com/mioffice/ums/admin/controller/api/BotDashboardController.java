package com.mioffice.ums.admin.controller.api;

import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.vo.BotDashboardVO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.BotDashboardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 机器人看板
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/1 3:34 下午
 */
@Slf4j
@RestController
@RequestMapping("/ums-api/api")
public class BotDashboardController {

    private final BotDashboardService botDashboardService;

    public BotDashboardController(BotDashboardService botDashboardService) {
        this.botDashboardService = botDashboardService;
    }

    @GetMapping("/data/analysis/bot")
    public BaseResult<BotDashboardVO> getBotDashboard(
            @RequestParam(value = "deptIdList", required = false) String deptIdList,
            @RequestParam(value = "manageUsernameList", required = false) String manageUsernameList,
            @RequestParam(value = "start") String start,
            @RequestParam(value = "end") String end,
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO
    ) {
        Assert.hasLength(start, "开始时间为空");
        Assert.hasLength(end, "截止时间为空");
        return BaseResult.of(botDashboardService.getBotDashboardVO(deptIdList, manageUsernameList, start, end, userBO.getUsername()));
    }

    @GetMapping("/data/analysis/bot/managers")
    public BaseResult<List<EmployeeInfo>> get(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam(value = "deptIdList", required = false) String deptIdList,
            @RequestParam(value = "searchWord", required = false) String searchWord
    ) {
        return BaseResult.of(botDashboardService.getBotDashboardManagerList(deptIdList, userBO.getUsername(), searchWord));
    }

}
