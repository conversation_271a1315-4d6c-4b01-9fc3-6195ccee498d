package com.mioffice.ums.admin.controller.api;

import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.PreSendDTO;
import com.mioffice.ums.admin.entity.dto.UserInfoDTO;
import com.mioffice.ums.admin.enums.BotTenantEnum;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.UserService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import javax.websocket.server.PathParam;

/**
 * 用户接口api
 *
 * <AUTHOR>
 * @Date 2020/7/1 10:04
 */
@RestController
@RequestMapping("/ums-api/api")
public class UserController {

    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }

    @GetMapping("/user/login")
    public BaseResult<UserInfoDTO> getUserInfo(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO
    ) {
        String username = userBO.getUsername();
        return BaseResult.of(userService.getLoginUser(username));
    }

    @GetMapping("/user/search")
    public BaseResult<List<UserInfoDTO>> searchUser(@PathParam("searchWord") String searchWord,
                                                    @RequestParam(value = "botTenant",required = false,defaultValue =
                                                            "0") Byte botTenant) {
        return BaseResult.of(userService.searchUser(searchWord,
                BotTenantEnum.BOT_TENANT_MI.equals(BotTenantEnum.getByCode(botTenant))));
    }

    @GetMapping("/user/preSendList")
    public BaseResult<List<PreSendDTO>> getPreSendList(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO
    ) {
        return BaseResult.of(userService.getPreSendList((long) -1, userBO.getUsername()));
    }
}
