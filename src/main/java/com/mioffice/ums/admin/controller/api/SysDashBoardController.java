package com.mioffice.ums.admin.controller.api;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.vo.AppMessageChannelVO;
import com.mioffice.ums.admin.entity.vo.AppMessageCountVO;
import com.mioffice.ums.admin.entity.vo.AppSummaryVO;
import com.mioffice.ums.admin.entity.vo.ListVO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.SysDashBoardService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 系统看板接口
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.26
 */
@RestController
@RequestMapping("/ums-api/api")
@Slf4j
public class SysDashBoardController {

    private final SysDashBoardService sysDashBoardService;

    public SysDashBoardController(SysDashBoardService sysDashBoardService) {
        this.sysDashBoardService = sysDashBoardService;
    }

    @GetMapping("/data/analysis/sys/message/sort")
    @PreAuthorize("hasRole('ROLE_SYS_ADMIN') || hasRole('ROLE_SYS_SUPER_ADMIN')")
    public BaseResult<ListVO<AppMessageCountVO>> getSysMessageTop(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam(value = "start") String start,
            @RequestParam(value = "end") String end
    ) {

        long beginTime = DateUtil.beginOfDay(DateUtil.parse(start, DatePattern.NORM_DATE_PATTERN)).getTime();
        long endTime = DateUtil.endOfDay(DateUtil.parse(end, DatePattern.NORM_DATE_PATTERN)).getTime();
        return sysDashBoardService.getSysMessageTop(userBO, beginTime, endTime);
    }

    @GetMapping("/data/analysis/sys/message/channel")
    @PreAuthorize("hasRole('ROLE_SYS_ADMIN') || hasRole('ROLE_SYS_SUPER_ADMIN')")
    public BaseResult<ListVO<AppMessageChannelVO>> getSysMessageChannelCount(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam(value = "appSysId") String appSysId,
            @RequestParam(value = "start") String start,
            @RequestParam(value = "end") String end
    ) {
        long beginTime = DateUtil.beginOfDay(DateUtil.parse(start, DatePattern.NORM_DATE_PATTERN)).getTime();
        long endTime = DateUtil.endOfDay(DateUtil.parse(end, DatePattern.NORM_DATE_PATTERN)).getTime();

        List<String> sysList = StringUtils.isBlank(appSysId) ? Collections.emptyList() : Arrays.asList(appSysId.split(","));
        List<Long> sysIdList = sysList.stream().map(Long::valueOf).collect(Collectors.toList());

        return sysDashBoardService.getSysMessageChannelCount(userBO, sysIdList, beginTime, endTime);

    }

    @GetMapping("/app/summary/list")
    public BaseResult<ListVO<AppSummaryVO>> getAppSummaryList() {
        return sysDashBoardService.getSysSummaryList();
    }
}
