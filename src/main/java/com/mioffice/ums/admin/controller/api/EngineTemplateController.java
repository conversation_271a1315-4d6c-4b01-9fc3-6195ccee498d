package com.mioffice.ums.admin.controller.api;

import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.AddTemplateDTO;
import com.mioffice.ums.admin.entity.dto.DeleteTemplateDTO;
import com.mioffice.ums.admin.entity.dto.UpdateTemplateDTO;
import com.mioffice.ums.admin.entity.vo.AddTemplateVO;
import com.mioffice.ums.admin.entity.vo.BotListVO;
import com.mioffice.ums.admin.entity.vo.TemplateDetailVO;
import com.mioffice.ums.admin.entity.vo.TemplateListVO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.EngineTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/22 9:13 上午
 * version: 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/ums-api/api")
public class EngineTemplateController {

    private static final String CHANNEL_NOT_NULL = "channel不能为空";
    private static final String APP_SYS_ID_NOT_NULL = "appSysId不能为空";

    private final EngineTemplateService engineTemplateService;

    public EngineTemplateController(EngineTemplateService engineTemplateService) {
        this.engineTemplateService = engineTemplateService;
    }

    @GetMapping("/template/detail")
    public BaseResult<TemplateDetailVO> getTemplateDetail(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("id") Long id
    ) {
        Assert.notNull(id, "模板id不能为空");
        return engineTemplateService.getTemplateDetail(userBO, id);
    }

    @PostMapping("/template/delete")
    public BaseResult<Object> deleteTemplate(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody DeleteTemplateDTO deleteTemplateDTO
    ) {
        Assert.notEmpty(deleteTemplateDTO.getIds(), "模板id不能为空");
        return engineTemplateService.deleteTemplate(userBO, deleteTemplateDTO.getIds());
    }

    @GetMapping("/template/page")
    public BaseResult<TemplateListVO> getTemplatePage(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("page") Long page,
            @RequestParam("size") Long size,
            @RequestParam("channel") Byte channel,
            @RequestParam("appSysId") Long appSysId,
            @RequestParam(value = "templateName", required = false) String templateName
    ) {
        Assert.notNull(page, "page不能为空");
        Assert.notNull(size, "size不能为空");
        Assert.notNull(channel, CHANNEL_NOT_NULL);
        Assert.notNull(appSysId, APP_SYS_ID_NOT_NULL);
        return engineTemplateService.getTemplatePage(userBO, page, size, channel, appSysId, templateName);
    }

    @PostMapping("/template/add")
    public BaseResult<AddTemplateVO> addTemplate(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody AddTemplateDTO addTemplateDTO
    ) {
        Assert.notNull(addTemplateDTO.getAppSysId(), APP_SYS_ID_NOT_NULL);
        Assert.notNull(addTemplateDTO.getChannel(), CHANNEL_NOT_NULL);
        Assert.notEmpty(addTemplateDTO.getBotBizIds(), "botBizId不能为空");
        Assert.hasText(addTemplateDTO.getTemplateName(), "templateName不能为空");
        Assert.hasText(addTemplateDTO.getTemplateContent(), "templateContent不能为空");
        return engineTemplateService.addTemplate(userBO, addTemplateDTO);
    }

    @PostMapping("/template/update")
    public BaseResult<Object> updateTemplate(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody UpdateTemplateDTO updateTemplateDTO
    ) {

        Assert.notNull(updateTemplateDTO.getId(), "模板id不能为空");

        return engineTemplateService.updateTemplate(userBO, updateTemplateDTO);
    }

    @GetMapping("/app/bot/list")
    public BaseResult<List<BotListVO>> getBotList(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("appSysId") Long appSysId,
            @RequestParam("channel") Integer channel
    ) {
        Assert.notNull(appSysId, APP_SYS_ID_NOT_NULL);
        Assert.notNull(channel, CHANNEL_NOT_NULL);
        return engineTemplateService.getBotList(userBO, appSysId, channel);
    }

}
