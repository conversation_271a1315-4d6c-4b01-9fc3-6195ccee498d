package com.mioffice.ums.admin.controller.open;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.Assert;
import com.google.gson.Gson;
import com.mioffice.ums.admin.entity.bo.HrodNotifyUserBO;
import com.mioffice.ums.admin.manager.ReportLineManager;
import com.mioffice.ums.admin.notify.subject.HrTerminalSubject;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.x5client.X5Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * hrod 事件监听
 * </p>
 *
 * <AUTHOR>
 * @date 2021/9/17 上午10:20
 */
@Slf4j
@RestController
@RequestMapping("/ums-api/open")
public class HrNotifyController {

    @Autowired
    private Gson gson;
    @Autowired
    private HrTerminalSubject hrTerminalSubject;
    @Autowired
    private ReportLineManager reportLineManager;

    @PostMapping("/hrod/terminal")
    public BaseResult<Object> terminal(@RequestBody String body) {
        Assert.notBlank(body, "内容为空");
        String realBody = Base64.decodeStr(body);
        X5Response x5Response = gson.fromJson(realBody, X5Response.class);
        hrTerminalSubject.notifyObservers(gson.fromJson(String.valueOf(x5Response.getBody()), HrodNotifyUserBO.class));
        return BaseResult.of();
    }

    @PostMapping("/hrod/terminal/test")
    public BaseResult<Object> test(@RequestBody HrodNotifyUserBO hrodNotifyUserBO) {
        Assert.notNull(hrodNotifyUserBO, "内容为空");
        hrTerminalSubject.notifyObservers(hrodNotifyUserBO);
        return BaseResult.of();
    }
}
