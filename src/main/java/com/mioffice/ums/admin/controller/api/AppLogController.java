package com.mioffice.ums.admin.controller.api;

import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.AppLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/23 8:49 上午
 * version: 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/ums-api/api")
public class AppLogController {

    private final AppLogService appLogService;

    public AppLogController(AppLogService appLogService) {
        this.appLogService = appLogService;
    }

    @GetMapping("/app/log/export")
    public BaseResult<Map<String, Object>> download(String extraId) {
        cn.hutool.core.lang.Assert.notNull(extraId, "extraId不能为空");
        BaseResult<Map<String, Object>> download;
        try {
            download = appLogService.download(extraId);
        } catch (Exception e) {
            log.info("excel = [{}] 的存储数据导出异常", extraId);
            return new BaseResult<Map<String, Object>>().setCode(500).setMessage("extraId为" + extraId + "的任务数据导出失败");
        }
        return download;
    }
}
