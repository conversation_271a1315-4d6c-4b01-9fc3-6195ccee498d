package com.mioffice.ums.admin.controller.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.BaseTaskDTO;
import com.mioffice.ums.admin.entity.dto.EmailTaskDTO;
import com.mioffice.ums.admin.entity.dto.LarkTaskDTO;
import com.mioffice.ums.admin.entity.dto.LarkTaskDetailDTO;
import com.mioffice.ums.admin.entity.dto.MessagePrePushDTO;
import com.mioffice.ums.admin.entity.dto.MyTaskRecordDTO;
import com.mioffice.ums.admin.entity.dto.SmsTaskDTO;
import com.mioffice.ums.admin.entity.dto.TaskHistoryPageDTO;
import com.mioffice.ums.admin.entity.dto.TaskResultDTO;
import com.mioffice.ums.admin.enums.LarkButtonEnum;
import com.mioffice.ums.admin.enums.TaskStatusEnum;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.MessageService;
import com.mioffice.ums.admin.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/13 7:52 下午
 * version: 1.0.0
 */
@RestController
@RequestMapping("/ums-api/api")
@Slf4j
public class MessageController {

    private static final String PAGE_PARAM_ERROR_STR = "page缺失";

    private static final String SIZE_PARAM_ERROR_STR = "size缺失";

    private static final String SUBMIT_TYPE_PARAM_ERROR_STR = "submitType提交类型缺失";

    private static final String TASK_ID_PARAM_ERROR_STR = "taskId缺失";

    private final MessageService messageService;

    public MessageController(MessageService messageService) {
        this.messageService = messageService;
    }

    @PostMapping("/task/lark/create")
    public BaseResult<TaskResultDTO> addLarkMessageTask(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody LarkTaskDTO larkTaskDTO
    ) {

        assertBaseTaskParam(larkTaskDTO);
        Assert.notNull(larkTaskDTO.getBotId(), "botId机器人id缺失");
        Assert.notNull(larkTaskDTO.getLarkMessageType(), "larkMessageType消息类型缺失");

        LarkButtonEnum larkButtonEnum = LarkButtonEnum.getByType(larkTaskDTO.getButton());
        if (larkButtonEnum == LarkButtonEnum.LAND_PAGE) {
            Assert.hasText(larkTaskDTO.getLandingPageUrl(), "落地页地址不能为空");
            Assert.isTrue(larkTaskDTO.getLandingPageUrl().startsWith("http:") ||
                    larkTaskDTO.getLandingPageUrl().startsWith("https:"), "落地页地址格式错误");
        } else if (larkButtonEnum == LarkButtonEnum.SCHEDULE) {
            Assert.hasText(larkTaskDTO.getScheduleStartDate(), "日程的起始时间不能为空");
            Assert.hasText(larkTaskDTO.getScheduleEndDate(), "日程的结束时间不能为空");
            Assert.isTrue(Optional.ofNullable(larkTaskDTO.getScheduleDescription()).orElse("").length() < 256,
                    "日程描述超过256个字符");
        }

        return messageService.createLarkTask(userBO, larkTaskDTO);
    }

    @PostMapping("/task/lark/rush")
    public BaseResult<TaskResultDTO> rushLarkMessageTask(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody LarkTaskDTO larkTaskDTO
    ) {
        assertBaseTaskParam(larkTaskDTO);
        Assert.notNull(larkTaskDTO.getTaskId(), "缺失要加推的taskId");
        Assert.notNull(larkTaskDTO.getBotId(), "botId机器人id缺失");
        Assert.notNull(larkTaskDTO.getLarkMessageType(), "larkMessageType消息类型缺失");

        LarkButtonEnum larkButtonEnum = LarkButtonEnum.getByType(larkTaskDTO.getButton());
        if (larkButtonEnum == LarkButtonEnum.LAND_PAGE) {
            Assert.hasText(larkTaskDTO.getLandingPageUrl(), "落地页地址不能为空");
            Assert.isTrue(larkTaskDTO.getLandingPageUrl().startsWith("http:") ||
                    larkTaskDTO.getLandingPageUrl().startsWith("https:"), "落地页地址格式错误");
        } else if (larkButtonEnum == LarkButtonEnum.SCHEDULE) {
            Assert.hasText(larkTaskDTO.getScheduleStartDate(), "日程的起始时间不能为空");
            Assert.hasText(larkTaskDTO.getScheduleEndDate(), "日程的结束时间不能为空");
            Assert.isTrue(Optional.ofNullable(larkTaskDTO.getScheduleDescription()).orElse("").length() < 256,
                    "日程描述超过256个字符");
        }

        return messageService.rushLarkTask(userBO, larkTaskDTO);
    }

    @PreAuthorize("hasRole('ROLE_SYS_SUPER_ADMIN') or hasRole('ROLE_SYS_ADMIN')")
    @GetMapping("/task/history/page")
    public BaseResult<TaskHistoryPageDTO> queryTaskHistoryByPage(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("page") Long page,
            @RequestParam("size") Long size,
            @RequestParam(value = "key", required = false) String key,
            @RequestParam(value = "channel", required = false) String channel,
            @RequestParam(value = "taskStatus", required = false) String taskStatus,
            @RequestParam(value = "beginDate", required = false) String beginDate,
            @RequestParam(value = "endDate", required = false) String endDate,
            @RequestParam(value = "publishScope", required = false) String publishScope,
            @RequestParam(value = "createUsername", required = false) String createUsername
    ) {
        Assert.notNull(page, PAGE_PARAM_ERROR_STR);
        Assert.notNull(size, SIZE_PARAM_ERROR_STR);
        List<Byte> channelTmpList = new ArrayList<>();
        parseStringToByteList(channel, channelTmpList);
        List<Byte> publishScopeList = new ArrayList<>();
        parseStringToByteList(publishScope, publishScopeList);
        List<String> createUsernameList = new ArrayList<>();
        if (StringUtils.isNotBlank(createUsername)) {
            String[] createUsernameSplit = createUsername.split(",");
            createUsernameList = Arrays.asList(createUsernameSplit);
        }
        List<Byte> taskStatusList = new ArrayList<>();
        parseStringToByteList(taskStatus, taskStatusList);
        return messageService.queryTaskHistoryByPage(page, size, key, channel, taskStatusList, beginDate, endDate,
                publishScope, createUsernameList, userBO);
    }

    @PostMapping("/task/lark/update")
    public BaseResult<TaskResultDTO> updateLarkMessageTask(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody LarkTaskDTO larkTaskDTO
    ) {
        Assert.notNull(larkTaskDTO.getTaskId(), "taskId提交类型缺少");
        Assert.notNull(larkTaskDTO.getSubmitType(), SUBMIT_TYPE_PARAM_ERROR_STR);

        Assert.notNull(larkTaskDTO.getButton(), "button值缺失");
        Assert.notNull(larkTaskDTO.getPublishScope(), "publishScope发布范围缺失");

        LarkButtonEnum larkButtonEnum = LarkButtonEnum.getByType(larkTaskDTO.getButton());
        if (larkButtonEnum == LarkButtonEnum.LAND_PAGE) {
            Assert.hasText(larkTaskDTO.getLandingPageUrl(), "落地页地址不能为空");
            Assert.isTrue(larkTaskDTO.getLandingPageUrl().startsWith("http:") ||
                    larkTaskDTO.getLandingPageUrl().startsWith("https:"), "落地页地址格式错误");
        } else if (larkButtonEnum == LarkButtonEnum.SCHEDULE) {
            Assert.hasText(larkTaskDTO.getScheduleStartDate(), "日程的起始时间不能为空");
            Assert.hasText(larkTaskDTO.getScheduleEndDate(), "日程的结束时间不能为空");
        }

        log.info("updateLarkMessageTask = [{}]", JsonUtils.toJson(larkTaskDTO));

        return messageService.updateLarkTask(userBO, larkTaskDTO);
    }

    @PostMapping(value = {"/push/message/lark/pre", "/push/message/pre"})
    public BaseResult<Object> prePushLarkMsg(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody MessagePrePushDTO messagePrePushDTO
    ) {
        Assert.notNull(messagePrePushDTO.getTaskId(), TASK_ID_PARAM_ERROR_STR);
        Assert.isTrue(!messagePrePushDTO.getUsernameList().isEmpty(), "usernameList不能为空");

        return messageService.prePushMsg(userBO, messagePrePushDTO);
    }

    @GetMapping("/task/my/page")
    public BaseResult<IPage<MyTaskRecordDTO>> queryMyTaskByPage(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("page") Long page,
            @RequestParam("size") Long size,
            @RequestParam(value = "key", required = false) String key,
            @RequestParam(value = "channel", required = false) String channel,
            @RequestParam(value = "taskStatus", required = false) String taskStatus,
            @RequestParam(value = "beginDate", required = false) String beginDate,
            @RequestParam(value = "endDate", required = false) String endDate,
            @RequestParam(value = "publishScope", required = false) String publishScope
    ) {
        Assert.notNull(page, PAGE_PARAM_ERROR_STR);
        Assert.notNull(size, SIZE_PARAM_ERROR_STR);
        List<Byte> channelTmpList = new ArrayList<>();
        parseStringToByteList(channel, channelTmpList);
        List<Byte> publishScopeList = new ArrayList<>();
        parseStringToByteList(publishScope, publishScopeList);
        List<Byte> taskStatusList = new ArrayList<>();
        parseStringToByteList(taskStatus, taskStatusList);
        return messageService.queryMyTaskByPage(page, size, key, channelTmpList, taskStatusList, beginDate, endDate,
                userBO, publishScopeList);
    }

    private void parseStringToByteList(@RequestParam(value = "channel", required = false) String channel,
                                       List<Byte> channelTmpList) {
        if (StringUtils.isNotBlank(channel)) {
            String[] channelTmp = channel.split(",");
            for (String s : channelTmp) {
                channelTmpList.add((byte) Integer.parseInt(s));
            }
        }
    }

    @GetMapping("/task/message/page")
    public BaseResult queryTaskMessageByPage(
            @RequestParam("page") Long page,
            @RequestParam("size") Integer size,
            @RequestParam("taskId") Long taskId,
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO
    ) {
        Assert.notNull(page, PAGE_PARAM_ERROR_STR);
        Assert.notNull(size, SIZE_PARAM_ERROR_STR);
        Assert.notNull(taskId, TASK_ID_PARAM_ERROR_STR);
        return messageService.queryTaskMessageByPage(page, size, taskId, userBO);
    }

    @GetMapping("/task/summary/list")
    public BaseResult<Object> summaryList(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO
    ) {
        return messageService.getSummaryList(userBO);
    }

    @PostMapping("/task/stop")
    public BaseResult<Object> stopMessageTask(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody TaskResultDTO taskResultDTO
    ) {
        Assert.notNull(taskResultDTO.getTaskId(), TASK_ID_PARAM_ERROR_STR);
        Assert.notNull(taskResultDTO.getTaskStatus(), "status 缺失");

        TaskStatusEnum taskStatusEnum = TaskStatusEnum.getByCode(taskResultDTO.getTaskStatus());
        Assert.isTrue(taskStatusEnum == TaskStatusEnum.INTERRUPT || taskStatusEnum == TaskStatusEnum.CANCEL,
                "状态错误");

        return messageService.stopMessageTask(userBO, taskResultDTO);
    }

    @GetMapping("/task/lark/detail")
    public BaseResult<LarkTaskDetailDTO> taskLarkDetail(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("taskId") Long taskId
    ) {
        Assert.notNull(taskId, "taskId 缺失");
        return messageService.taskLarkDetail(taskId, userBO);
    }

    @GetMapping("/task/lark/retract")
    public BaseResult<TaskResultDTO> taskLarkRetract(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("taskId") Long taskId) {
        Assert.notNull(taskId, "taskId 缺失");
        return messageService.taskLarkRetract(taskId, userBO);
    }

    @GetMapping("/task/detail")
    public BaseResult<Object> taskDetail(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("taskId") Long taskId
    ) {
        Assert.notNull(taskId, "taskId 缺失");
        return messageService.taskDetail(taskId, userBO);
    }

    @GetMapping("/task/result/export")
    public BaseResult<Map<String, Object>> download(
            Long taskId,
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO
    ) {
        Assert.notNull(taskId, "taskId为空");
        try {
            return messageService.download(taskId, userBO);
        } catch (IOException e) {
            log.info("excel = [{}] 的存储数据导出异常", taskId);
        }
        return BaseResult.of();
    }

    @PostMapping("/task/email/create")
    public BaseResult<TaskResultDTO> addEmailMessageTask(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody EmailTaskDTO emailTaskDTO
    ) {

        log.info("emailTaskDTO = [{}]", JsonUtils.toJson(emailTaskDTO));

        assertBaseTaskParam(emailTaskDTO);
        Assert.hasText(emailTaskDTO.getSender(), "邮件机器人不能为空");
        Assert.notNull(emailTaskDTO.getEmailType(), "邮件类型不能为空");
        Assert.hasText(emailTaskDTO.getHtmlBody(), "邮件体不为空");

        return messageService.createEmailTask(userBO, emailTaskDTO);
    }

    @PostMapping("/task/email/update")
    public BaseResult<TaskResultDTO> updateLarkMessageTask(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody EmailTaskDTO emailTaskDTO
    ) {
        Assert.notNull(emailTaskDTO.getTaskId(), "taskId 不能为空");
        Assert.notNull(emailTaskDTO.getSubmitType(), SUBMIT_TYPE_PARAM_ERROR_STR);

        return messageService.updateEmailTask(userBO, emailTaskDTO);
    }

    @PostMapping("/task/sms/create")
    public BaseResult<TaskResultDTO> addSmsMessageTask(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody SmsTaskDTO smsTaskDTO
    ) {
        assertBaseTaskParam(smsTaskDTO);
        Assert.hasText(smsTaskDTO.getSignCode(), "签名码不能为空");

        return messageService.createSmsTask(userBO, smsTaskDTO);
    }

    @PostMapping("/task/sms/update")
    public BaseResult<TaskResultDTO> updateSmsMessageTask(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody SmsTaskDTO smsTaskDTO
    ) {
        Assert.notNull(smsTaskDTO.getTaskId(), "taskId 不能为空");
        Assert.notNull(smsTaskDTO.getSubmitType(), SUBMIT_TYPE_PARAM_ERROR_STR);

        return messageService.updateSmsTask(userBO, smsTaskDTO);
    }

    private void assertBaseTaskParam(BaseTaskDTO baseTaskDTO) {
        Assert.notNull(baseTaskDTO.getSubmitType(), "submitType提交类型缺少");
        Assert.notNull(baseTaskDTO.getPublishScope(), "publishScope发布范围缺失");
        Assert.hasText(baseTaskDTO.getTitleCn(), "title标题缺失");
        Assert.hasText(baseTaskDTO.getContentCn(), "content内容缺失");
    }

}
