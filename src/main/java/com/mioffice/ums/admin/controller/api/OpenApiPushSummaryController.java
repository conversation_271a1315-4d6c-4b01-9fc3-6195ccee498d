package com.mioffice.ums.admin.controller.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.entity.vo.OpenPushSummaryVo;
import com.mioffice.ums.admin.entity.vo.OpenTaskLogVo;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.OpenApiPushSummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 开放api的推送详情
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/23 7:36 下午
 */
@RestController
@RequestMapping("/ums-api/api")
public class OpenApiPushSummaryController {

    @Autowired
    private OpenApiPushSummaryService openApiPushSummaryService;

    @GetMapping("/app/log/page")
    public BaseResult<IPage<OpenTaskLogVo>> geOpenTaskLog(
            @RequestParam("page") Integer page,
            @RequestParam("size") Integer size,
            @RequestParam("extraId") String extraId,
            @RequestParam("channels") String channels,
            @RequestParam("systemId") Long systemId
    ) {
        Assert.notNull(systemId, "请求参数systemId为空");
        return BaseResult.of(openApiPushSummaryService.getAppTaskLog(extraId, systemId, channels, page, size));
    }

    @GetMapping("/app/push/count/summary")
    public BaseResult<OpenPushSummaryVo> geOpenPushSummary(
            @RequestParam(value = "systemId", required = false) String systemId,
            @RequestParam(value = "start", required = false) String startTime,
            @RequestParam(value = "end", required = false) String endTime
    ) {
        return BaseResult.of(openApiPushSummaryService.getUseSummary(systemId, startTime, endTime));
    }
}
