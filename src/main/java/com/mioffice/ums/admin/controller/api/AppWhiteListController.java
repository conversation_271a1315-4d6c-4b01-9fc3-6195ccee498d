package com.mioffice.ums.admin.controller.api;

import com.mioffice.ums.admin.entity.dto.AddWhiteListDTO;
import com.mioffice.ums.admin.entity.dto.DelWhiteListDTO;
import com.mioffice.ums.admin.entity.dto.QueryAppWhiteListDTO;
import com.mioffice.ums.admin.entity.vo.AppWhiteListPageVo;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.AppWhiteListService;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * AppWhiteListController
 *
 * <AUTHOR>
 * @since 2021-12-23
 */
@RestController
@RequestMapping("/ums-api/api/app/whitelist")
public class AppWhiteListController {
    private final AppWhiteListService appWhiteListService;

    public AppWhiteListController(AppWhiteListService appWhiteListService) {
        this.appWhiteListService = appWhiteListService;
    }

    @GetMapping("/page")
    public BaseResult<AppWhiteListPageVo> getAppWhiteListPage(@Valid QueryAppWhiteListDTO queryAppWhiteListDTO) {
        return appWhiteListService.queryAppWhiteList(queryAppWhiteListDTO);
    }

    @PostMapping("/add")
    public BaseResult<Integer> addAppWhiteList(@Valid @RequestBody AddWhiteListDTO addWhiteListDTO) {
        return appWhiteListService.addAppWhiteList(addWhiteListDTO);
    }

    @DeleteMapping("/delete")
    public BaseResult<Integer> delAppWhiteList(@Valid @RequestBody DelWhiteListDTO delWhiteListDTO) {
        return appWhiteListService.delAppWhiteList(delWhiteListDTO);
    }
}
