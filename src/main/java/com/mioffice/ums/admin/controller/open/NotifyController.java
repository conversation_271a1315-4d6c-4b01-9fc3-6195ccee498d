package com.mioffice.ums.admin.controller.open;

import com.google.gson.reflect.TypeToken;
import static com.mioffice.ums.admin.constants.RuleConventionConstants.EXTRA_ID_KEY;
import static com.mioffice.ums.admin.constants.RuleConventionConstants.JOB_ID_KEY;
import static com.mioffice.ums.admin.constants.RuleConventionConstants.TASK_ID_KEY;
import static com.mioffice.ums.admin.constants.RuleConventionConstants.URGE_DATA_KEY;
import com.mioffice.ums.admin.entity.bo.BtnActionBo;
import com.mioffice.ums.admin.entity.bo.BtnNotifBO;
import com.mioffice.ums.admin.enums.BtnActionEnum;
import com.mioffice.ums.admin.manager.LarkDecryptManager;
import com.mioffice.ums.admin.manager.RefreshCardManager;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.AppService;
import com.mioffice.ums.admin.service.BotApiService;
import com.mioffice.ums.admin.service.BotApprovalService;
import com.mioffice.ums.admin.service.MessageApprovalService;
import com.mioffice.ums.admin.service.MessageJobUrgeService;
import com.mioffice.ums.admin.service.MessageService;
import com.mioffice.ums.admin.service.ScheduleService;
import com.mioffice.ums.admin.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.14
 */
@Slf4j
@RestController
@RequestMapping("/ums-api/open")
public class NotifyController {

    private static final String CHALLENGE_STR = "challenge";

    private final LarkDecryptManager larkDecryptManager;

    private final MessageApprovalService messageApprovalService;

    private final BotApprovalService botApprovalService;

    private final BotApiService botApiService;

    private final ScheduleService scheduleService;

    private final RefreshCardManager refreshCardManager;

    private final MessageService messageService;

    private final AppService appService;

    private final MessageJobUrgeService messageJobUrgeService;

    public NotifyController(LarkDecryptManager larkDecryptManager, MessageApprovalService messageApprovalService,
                            BotApprovalService botApprovalService, BotApiService botApiService,
                            ScheduleService scheduleService, RefreshCardManager refreshCardManager,
                            MessageService messageService, AppService appService,
                            MessageJobUrgeService messageJobUrgeService) {
        this.larkDecryptManager = larkDecryptManager;
        this.messageApprovalService = messageApprovalService;
        this.botApprovalService = botApprovalService;
        this.botApiService = botApiService;
        this.scheduleService = scheduleService;
        this.refreshCardManager = refreshCardManager;
        this.messageService = messageService;
        this.appService = appService;
        this.messageJobUrgeService = messageJobUrgeService;
    }

    @PostMapping("/notify/{appId}")
    public Map<String, Object> notify(
            @PathVariable(name = "appId") String appId,
            @RequestBody Map<String, Object> params
    ) {
        log.info("notify 回调接口 appId = [{}]， params = [{}]", appId, JsonUtils.toJson(params));
        if (params.containsKey("encrypt")) {
            //密文，需要解析
            return this.doNotifyEncrypt(appId, params);
        } else {
            // 明文
            return this.doNotify(appId, params);
        }
    }

    private Map<String, Object> doNotifyEncrypt(String appId, Map<String, Object> params) {
        String encrypt = params.get("encrypt").toString();
        Map<String, Object> result = new HashMap<>(5);
        try {
            String jsonString = larkDecryptManager.decrypt(appId, encrypt);
            Map<String, Object> jsonParams = JsonUtils.toMap(jsonString);
            log.info("notify 解密成功 appId = [{}], jsonParams = [{}]", appId, jsonString);
            result.put(CHALLENGE_STR, jsonParams.get(CHALLENGE_STR));
        } catch (Exception e) {
            log.warn("notify 解密失败 appId = [{}] ", appId, e);
            result.put("400", e.getMessage());
        }

        return result;
    }

    private Map<String, Object> doNotify(String appId, Map<String, Object> params) {

        Map<String, Object> result = new HashMap<>(5);

        if (params.containsKey(CHALLENGE_STR)) {
            result.put(CHALLENGE_STR, params.get(CHALLENGE_STR));
            // 添加回调地址产生的回调
            botApiService.putBotCalendarCacheStatus(appId);
            return result;
        }

        // 卡片事件回调
        BtnActionBo btnActionBo = JsonUtils.parse(JsonUtils.toJson(params), new TypeToken<BtnActionBo>() {
        }.getType());
        if (Objects.nonNull(btnActionBo.getAction())) {
            BtnActionEnum actionEnum = BtnActionEnum.get(btnActionBo.getAction().getValue().get("action").toString());
            BtnNotifBO btnNotifBO = new BtnNotifBO(appId, btnActionBo, actionEnum, result);
            switch (actionEnum) {
                case ADD_SCHEDULE:
                    // 添加日程
                    if (scheduleService.addSchedule(btnNotifBO)) {
                        result = refreshCardManager.refresh(btnNotifBO, "已添加日程", "Schedule added");
                    }
                    break;
                case BOT_APPROVE_AGREE:
                    result = refreshCardManager.refresh(btnNotifBO,
                            botApprovalService.larkApprovedCallBack(btnNotifBO).getMsg());
                    break;
                case BOT_APPROVE_REFUSE:
                    result = refreshCardManager.refresh(btnNotifBO,
                            botApprovalService.larkRejectedCallBack(btnNotifBO).getMsg());
                    break;
                case APPROVE_AGREE:
                    // 审批通过
                    result = refreshCardManager.refresh(btnNotifBO,
                            messageApprovalService.larkApprovedCallBack(btnNotifBO).getMsg());
                    break;
                case APPROVE_REFUSE:
                    // 审批拒绝
                    result = refreshCardManager.refresh(btnNotifBO,
                            messageApprovalService.larkRejectedCallBack(btnNotifBO).getMsg());
                    break;
                case STOP_APP:
                    if (appService.stopApp(btnNotifBO)) {
                        result = refreshCardManager.refresh(btnNotifBO, "该应用已停用");
                    }
                case URGE_CUSTOM:
                    BtnActionBo actionBo = btnNotifBO.getBtnActionBo();
                    Optional<Object> urgeMessageExtraId =
                            Optional.ofNullable(actionBo.getAction()).map(BtnActionBo.Action::getValue)
                                    .map(p -> p.get(EXTRA_ID_KEY));

                    Optional<Object> msgJobId =
                            Optional.ofNullable(actionBo.getAction()).map(BtnActionBo.Action::getValue)
                                    .map(p -> p.get(JOB_ID_KEY));

                    Optional<String> oprId = Optional.ofNullable(actionBo).map(BtnActionBo::getUserId);

                    if (urgeMessageExtraId.isPresent() &&
                            oprId.isPresent() &&
                            msgJobId.isPresent() &&
                            StringUtils.isNotBlank(msgJobId.get().toString())) {
                        try {
                            messageJobUrgeService.urgeCustom(urgeMessageExtraId.get().toString(), appId,
                                    Long.parseLong(msgJobId.get().toString()),
                                    oprId.get());
                            result = refreshCardManager.process(btnNotifBO, p -> {
                                p.remove("value");
                                Object text = p.get("text");
                                Optional.ofNullable(text)
                                        .ifPresent(t -> ((Map<String, Object>) t).put("content", "已提醒"));
                            });
                        } catch (Exception e) {
                            log.error("催办出错", e);
                        }
                    }
                    break;
                case URGE_MANAGER:
                    urgeMessageExtraId = Optional.ofNullable(btnNotifBO.getBtnActionBo().getAction())
                            .map(BtnActionBo.Action::getValue).map(p -> p.get(EXTRA_ID_KEY));

                    msgJobId =
                            Optional.ofNullable(btnNotifBO.getBtnActionBo().getAction())
                                    .map(BtnActionBo.Action::getValue)
                                    .map(p -> p.get(JOB_ID_KEY));

                    Optional<Object> taskId =
                            Optional.ofNullable(btnNotifBO.getBtnActionBo().getAction())
                                    .map(BtnActionBo.Action::getValue)
                                    .map(p -> p.get(TASK_ID_KEY));

                    Optional<Object> urgeData = Optional.ofNullable(btnNotifBO.getBtnActionBo().getAction())
                            .map(BtnActionBo.Action::getValue).map(p -> p.get(URGE_DATA_KEY));

                    oprId =
                            Optional.ofNullable(btnNotifBO.getBtnActionBo()).map(BtnActionBo::getUserId);

                    if (urgeMessageExtraId.isPresent() &&
                            oprId.isPresent() &&
                            urgeData.isPresent() && StringUtils.isNotBlank(urgeData.get().toString())) {
                        try {
                            messageJobUrgeService.urgeManager(urgeMessageExtraId.get().toString(),
                                    appId,
                                    msgJobId.isPresent() ? Long.parseLong(msgJobId.get().toString()) : null,
                                    taskId.isPresent() ? Long.parseLong(taskId.get().toString()) : null,
                                    urgeData.get().toString(),
                                    oprId.get());
                            result = refreshCardManager.process(btnNotifBO, p -> {
                                p.remove("value");
                                Object text = p.get("text");
                                Optional.ofNullable(text)
                                        .ifPresent(t -> ((Map<String, Object>) t).put("content", "已提醒"));
                            });
                        } catch (Exception e) {
                            log.error("催办出错", e);
                        }
                    }
                    break;
                default:
                    log.warn("不支持按钮动作，丢弃 = [{}]", actionEnum);
                    break;
            }
        }
        log.info("result = [{}]", JsonUtils.toJson(result));
        return result;
    }

    @PostMapping("/retry/sendingTimeout")
    public BaseResult<Objects> retrySendingTimeoutTask() {
        messageService.triggerRetrySendingTimeoutTask();
        return BaseResult.of();
    }
}
