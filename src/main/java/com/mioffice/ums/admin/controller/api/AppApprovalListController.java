package com.mioffice.ums.admin.controller.api;

import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.AppApplyCancelDTO;
import com.mioffice.ums.admin.entity.dto.AppApprovalPassOrRejectDTO;
import com.mioffice.ums.admin.entity.vo.AppApprovalListVO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.AppApprovalListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/21 3:16 上午
 * version: 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/ums-api/api")
public class AppApprovalListController {

    private static final String ID_NOT_NULL = "id不能为空";

    private final AppApprovalListService appApprovalListService;

    public AppApprovalListController(AppApprovalListService appApprovalListService) {
        this.appApprovalListService = appApprovalListService;
    }

    @GetMapping("/app/approval/page")
    public BaseResult<AppApprovalListVO> appApprovalPage(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("page") Long page,
            @RequestParam("size") Long size,
            @RequestParam(value = "appName", required = false) String appName,
            @RequestParam(value = "managerUsernameList", required = false) String managerUsernameList,
            @RequestParam(value = "channel", required = false) String channel,
            @RequestParam(value = "beginDate", required = false) String beginDate,
            @RequestParam(value = "endDate", required = false) String endDate
    ) {
        Assert.notNull(page, "page为空");
        Assert.notNull(size, "size为空");
        return appApprovalListService.appApprovalPage(userBO, page, size, appName, managerUsernameList, channel, beginDate, endDate);
    }

    @PostMapping("/app/approval/pass")
    public BaseResult<Object> appApprovalPass(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody AppApprovalPassOrRejectDTO appApprovalPassOrRejectDTO
    ) {
        Assert.notNull(appApprovalPassOrRejectDTO.getId(), ID_NOT_NULL);
        return appApprovalListService.appApprovalPass(userBO, appApprovalPassOrRejectDTO.getId());
    }

    @PostMapping("/app/approval/reject")
    public BaseResult<Object> appApprovalReject(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody AppApprovalPassOrRejectDTO appApprovalPassOrRejectDTO
    ) {
        Assert.notNull(appApprovalPassOrRejectDTO.getId(), ID_NOT_NULL);
        Assert.hasText(appApprovalPassOrRejectDTO.getReason(), "审批拒绝原因不能为空");
        return appApprovalListService.appApprovalReject(userBO, appApprovalPassOrRejectDTO.getId(), appApprovalPassOrRejectDTO.getReason());
    }

    @PostMapping("/app/apply/cancel")
    public BaseResult<Object> appApplyCancel(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody AppApplyCancelDTO appApplyCancelDTO
    ) {
        Assert.notNull(appApplyCancelDTO.getId(), ID_NOT_NULL);
        return appApprovalListService.appApplyCancel(userBO, appApplyCancelDTO.getId());
    }
}
