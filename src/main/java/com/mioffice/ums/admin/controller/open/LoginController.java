package com.mioffice.ums.admin.controller.open;

import com.mioffice.ums.admin.cas.user.UserHelper;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.xiaomi.info.infra.security.TokenHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jasig.cas.client.authentication.AttributePrincipal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.net.URLDecoder;
import java.util.Map;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * CAS登录
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/5 10:29
 */
@Slf4j
@Controller
@RequestMapping("/ums-api/open")
public class LoginController {

    @Value("${cas.server-url-prefix}")
    private String casHost;

    @Value("${cas.client-host-url}")
    private String frontHost;

    private final UserHelper userHelper;
    private final TokenHelper tokenHelper;

    public LoginController(UserHelper userHelper, TokenHelper tokenHelper) {
        this.userHelper = userHelper;
        this.tokenHelper = tokenHelper;
    }

    @GetMapping("/cas")
    public void cas(String targetPath, HttpServletRequest request, HttpServletResponse response) throws IOException {

        AttributePrincipal userPrincipal = (AttributePrincipal) request.getUserPrincipal();

        Map<String, Object> attrs = userPrincipal.getAttributes();
        UserBO user = new UserBO();

        user.setUsername(userPrincipal.getName());
        user.setDepartmentName((String) attrs.get("departmentName"));
        user.setName(URLDecoder.decode((String) attrs.get("name"), "UTF-8"));
        user.setEmail(URLDecoder.decode((String) attrs.get("email"), "UTF-8"));

        userHelper.set(user);

        // 3.生成 jwt token
        String token = tokenHelper.generateToken(userPrincipal.getName());
        int expiresIn = tokenHelper.getExpiresIn();
        // 4.使用 token 和过期时间生成 cookie
        Cookie cookie = buildTokenCookie(token, expiresIn);
        response.addCookie(cookie);

        log.info("targetPath = [{}]", targetPath);
        if (StringUtils.isNotBlank(targetPath)) {
            response.sendRedirect(frontHost + targetPath);
        } else {
            response.sendRedirect(frontHost + "/ums/");
        }

    }

    @ResponseBody
    @GetMapping("/unLogin")
    public BaseResult<Object> unLogin(HttpServletRequest request) {
        return new BaseResult<>(ResultCode.AUTH_ERROR.getCode(), "未登录");
    }

    @GetMapping("/logout")
    public void logout(HttpServletRequest request, HttpServletResponse response) {
        try {
            Cookie cookie = clearTokenCookie();
            response.addCookie(cookie);
            response.sendRedirect(casHost + "logout?service=" + frontHost + "/ums/");
        } catch (IOException e) {
            log.error("/logout接口重定向异常", e);
        }
    }

    /**
     * cookie 构造
     **/
    private Cookie buildTokenCookie(String token, int maxAge) {
        Cookie cookie = new Cookie("token", token);
        cookie.setMaxAge(maxAge);
        cookie.setPath("/");
        return cookie;
    }

    private Cookie clearTokenCookie() {
        Cookie cookie = new Cookie("token", null);
        cookie.setMaxAge(0);
        cookie.setPath("/");
        return cookie;
    }
}
