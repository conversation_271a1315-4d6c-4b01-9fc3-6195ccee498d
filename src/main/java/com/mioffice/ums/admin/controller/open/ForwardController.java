package com.mioffice.ums.admin.controller.open;

import com.google.common.base.Splitter;
import static com.mioffice.ums.admin.constants.RuleConventionConstants.CLICK_KEY_PREFIX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jasig.cas.client.authentication.AttributePrincipal;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.net.URLDecoder;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping("/ums-api/open")
public class ForwardController {

    @Resource
    RedisTemplate redisTemplate;

    @GetMapping("/forward")
    public String forward(@RequestParam String sig, HttpServletRequest request,
                          HttpServletResponse response,
                          Model model) {

        AttributePrincipal userPrincipal = (AttributePrincipal) request.getUserPrincipal();
        if (Objects.nonNull(userPrincipal) && StringUtils.isNotBlank(userPrincipal.getName())) {
            String loginOprId = userPrincipal.getName().trim();
            try {
                String oprIdAndUrl = new String(Base64.getDecoder().decode(sig));
                List<String> list = Splitter.on("|").splitToList(oprIdAndUrl);
                if (list.size() == 3) {
                    String extraId = list.get(0);
                    String oprId = list.get(1);
                    String targetUrl = URLDecoder.decode(list.get(2),"UTF-8");
                    if (oprId.equals(loginOprId)) {
                        model.addAttribute("url", targetUrl);
                        redisTemplate.opsForSet().add(CLICK_KEY_PREFIX + extraId, oprId);
                        return "forward/forward";
                    }
                }
            } catch (Exception e) {
                log.error("forward:", e);
            }
        }
        return "forward/403";
    }
}
