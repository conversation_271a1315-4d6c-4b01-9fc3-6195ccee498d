package com.mioffice.ums.admin.controller.api;

import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.bo.UsernameAndNameBO;
import com.mioffice.ums.admin.entity.dto.AppStartOrStopDTO;
import com.mioffice.ums.admin.entity.dto.AppUseApplyDTO;
import com.mioffice.ums.admin.entity.vo.AppSystemListVO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.AppSystemListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/ums-api/api")
public class AppSystemListController {

    private final AppSystemListService appSystemListService;

    public AppSystemListController(AppSystemListService appSystemListService) {
        this.appSystemListService = appSystemListService;
    }

    @GetMapping("/app/page")
    public BaseResult<AppSystemListVO> getAppPage(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("page") Long page,
            @RequestParam("size") Long size,
            @RequestParam(value = "appName", required = false) String appName,
            @RequestParam(value = "managerUsernameList", required = false) String managerUsernameList,
            @RequestParam(value = "applyUsername", required = false) String applyUsername,
            @RequestParam(value = "channel", required = false) String channel,
            @RequestParam(value = "appSysStatus", required = false) String appSysStatus,
            @RequestParam(value = "beginDate", required = false) String beginDate,
            @RequestParam(value = "endDate", required = false) String endDate
    ) {
        Assert.notNull(page, "page为空");
        Assert.notNull(size, "size为空");
        return appSystemListService.getAppPage(userBO, page, size, appName, managerUsernameList, applyUsername, channel, appSysStatus, beginDate, endDate);
    }

    @PostMapping("/app/on")
    public BaseResult<Object> appStart(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody AppStartOrStopDTO appStartOrStopDTO
    ) {
        Assert.notNull(appStartOrStopDTO.getId(), "id为空");
        return appSystemListService.appStart(userBO, appStartOrStopDTO.getId());
    }

    @PostMapping("/app/off")
    public BaseResult<Object> appStop(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody AppStartOrStopDTO appStartOrStopDTO
    ) {
        Assert.notNull(appStartOrStopDTO.getId(), "id为空");
        return appSystemListService.appStop(userBO, appStartOrStopDTO.getId());
    }

    @PostMapping("/app/use/apply")
    public BaseResult<Object> appUseApply(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody AppUseApplyDTO appUseApplyDTO
    ) {
        Assert.notNull(appUseApplyDTO.getId(), "id为空");
        return appSystemListService.appUseApply(userBO, appUseApplyDTO.getId());
    }

    @GetMapping("/app/manager/list")
    public BaseResult<List<UsernameAndNameBO>> getManagers(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("searchWord") String searchWord
    ) {
        Assert.hasText(searchWord, "searchWord为空");
        return appSystemListService.getManagers(userBO, searchWord);
    }

    @GetMapping("/app/apply/user/list")
    public BaseResult<List<UsernameAndNameBO>> getAppApplyUsers(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("searchWord") String searchWord
    ) {
        Assert.hasText(searchWord, "searchWord为空");
        return appSystemListService.getAppApplyUsers(userBO, searchWord);
    }
}
