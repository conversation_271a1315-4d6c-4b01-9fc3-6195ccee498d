package com.mioffice.ums.admin.controller.api;

import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.AllBotDTO;
import com.mioffice.ums.admin.entity.dto.BotDTO;
import com.mioffice.ums.admin.entity.dto.BotUseDetailDTO;
import com.mioffice.ums.admin.entity.dto.MyBotDTO;
import com.mioffice.ums.admin.entity.dto.NotifyUrlVerifyDTO;
import com.mioffice.ums.admin.entity.dto.QueryBotDTO;
import com.mioffice.ums.admin.entity.dto.RobotVerifyDTO;
import com.mioffice.ums.admin.entity.info.BotInfo;
import com.mioffice.ums.admin.enums.BotTypeEnum;
import com.mioffice.ums.admin.enums.CalendarEnableEnum;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.BotApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/ums-api/api")
public class BotApiController {

    private final BotApiService botApiService;

    public BotApiController(BotApiService botApiService) {
        this.botApiService = botApiService;
    }

    @GetMapping("bot/get")
    public BaseResult<QueryBotDTO> queryBotById(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("id") Long id
    ) {
        Assert.notNull(id, "id为空");
        return botApiService.queryBotById(id, userBO.getUsername());
    }

    @GetMapping("/bot/all/page")
    public BaseResult<AllBotDTO> getAllBotByPage(
            @RequestParam("page") Long page,
            @RequestParam("size") Long size,
            @RequestParam(value = "botName", required = false) String botName,
            @RequestParam(value = "deptId", required = false) String deptId,
            @RequestParam(value = "owner", required = false) String owner,
            @RequestParam(value = "botStatus", required = false) String botStatus,
            @RequestParam(value = "botType", required = false) String botType,
            @RequestParam(value = "botTenant", required = false) Byte botTenant
    ) {
        Assert.notNull(page, "page为空");
        Assert.notNull(size, "size为空");
        return botApiService.queryAllBotsByPage(page, size, botName, deptId, owner, botStatus, botType, botTenant);
    }

    @PostMapping("/bot/add")
    public BaseResult<BotInfo> addRobot(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody BotDTO botDTO
    ) {
        if (Objects.isNull(botDTO.getBotTenant())) {
            botDTO.setBotTenant((byte) 0);
        }
        Assert.hasLength(botDTO.getBotKey(), "机器人的ID为空");
        if (botDTO.getBotType() != BotTypeEnum.BOT_TYPE_SMS.getCode()) {
            Assert.hasLength(botDTO.getBotSecret(), "机器人的密钥为空");
        }
        Assert.hasLength(botDTO.getBotName(), "机器人名称为空");
        Assert.isTrue(botDTO.getDeptIds() != null && !botDTO.getDeptIds().isEmpty(), "所属部门为空");
        Assert.isTrue(botDTO.getManageUsernameList() != null && !botDTO.getManageUsernameList().isEmpty(),
                "管理员为空");
        if (botDTO.getCalendarStatus() == CalendarEnableEnum.ON.getCode()) {
            Assert.isTrue(StringUtils.isNotBlank(botDTO.getBotEncryptKey())
                            && StringUtils.isNotBlank(botDTO.getBotVerificationToken()),
                    " Encrypt Key和Verification Token不能为空");
        }

        return botApiService.addRobotInfo(botDTO, userBO.getUsername());
    }

    @PostMapping("/bot/validate")
    public BaseResult<RobotVerifyDTO> validate(@RequestBody BotDTO botDTO) {
        Assert.hasLength(botDTO.getBotKey(), "机器人的ID为空");
        Assert.hasLength(botDTO.getBotSecret(), "机器人的密钥为空");
        return botApiService.verify(botDTO);
    }

    @PreAuthorize("hasRole('ROLE_SYS_SUPER_ADMIN') or hasRole('ROLE_SYS_ADMIN') or  hasRole('ROLE_BOT_ADMIN')")
    @PostMapping("/bot/stop")
    public BaseResult<BotInfo> stop(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody BotDTO botDTO) {
        Assert.notNull(botDTO.getId(), "机器人id为空");
        return botApiService.stopBot(botDTO, userBO.getUsername());
    }

    @PreAuthorize("hasRole('ROLE_SYS_SUPER_ADMIN') or hasRole('ROLE_SYS_ADMIN') or hasRole('ROLE_BOT_ADMIN')")
    @PostMapping("/bot/start")
    public BaseResult<BotInfo> start(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody BotDTO botDTO) {
        Assert.notNull(botDTO.getId(), "机器人id为空");
        return botApiService.startBot(botDTO, userBO.getUsername());
    }

    @GetMapping("/bot/my")
    public BaseResult<MyBotDTO> myBot(@RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO) {
        return botApiService.myBot(userBO.getUsername());
    }

    @PreAuthorize("hasRole('ROLE_SYS_SUPER_ADMIN') or hasRole('ROLE_SYS_ADMIN') or hasRole('ROLE_BOT_ADMIN')")
    @PostMapping("/bot/update")
    public BaseResult<BotInfo> update(@RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
                                      @RequestBody BotDTO botDTO) {
        Assert.notNull(botDTO.getBotTenant(),"租户为空");
        Assert.notNull(botDTO.getId(), "id为空");
        Assert.isTrue(botDTO.getManageUsernameList() != null && !botDTO.getManageUsernameList().isEmpty(),
                "管理员为空");
        Assert.isTrue(botDTO.getDeptIds() != null && !botDTO.getDeptIds().isEmpty(), "所属部门为空");
        if (botDTO.getCalendarStatus() == CalendarEnableEnum.ON.getCode()) {
            Assert.isTrue(StringUtils.isNotBlank(botDTO.getBotEncryptKey())
                            && StringUtils.isNotBlank(botDTO.getBotVerificationToken()),
                    " Encrypt Key和Verification Token不能为空");
        }
        return botApiService.updateBot(botDTO, userBO.getUsername());
    }

    @PostMapping("/notifyUrl")
    public BaseResult<Map<String, Object>> notifyUrl(@RequestBody NotifyUrlVerifyDTO notifyUrlVerifyDTO) {
        Assert.hasLength(notifyUrlVerifyDTO.getBotKey(), "机器人ID为空");
        return botApiService.getNotifyUrl(notifyUrlVerifyDTO);
    }

    @PostMapping("/validateCalendar")
    public BaseResult<Map<String, Object>> validateCalendar(@RequestBody NotifyUrlVerifyDTO notifyUrlVerifyDTO) {
        Assert.hasLength(notifyUrlVerifyDTO.getBotKey(), "机器人ID为空");
        return botApiService.verifyCalendarStatus(notifyUrlVerifyDTO);
    }

    @GetMapping("/bot/use/page")
    public BaseResult<BotUseDetailDTO> queryBotUseByPage(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("botId") Long botId,
            @RequestParam("page") Long page,
            @RequestParam("size") Long size
    ) {
        Assert.notNull(botId, "botId为空");
        Assert.notNull(page, "page为空");
        Assert.notNull(size, "size为空");
        return botApiService.queryBotUseByPage(page, size, botId, userBO);
    }

    @GetMapping("/bot/summary/my/list")
    public BaseResult<List<BotDTO>> myBotList(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO
    ) {
        return botApiService.getMyLarkBotList(userBO.getUsername());
    }

    @GetMapping("/email/bot/list")
    public BaseResult<Object> getEmailBot(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO
    ) {
        return botApiService.getMyEmailBotList(userBO.getUsername());
    }

    @GetMapping("/sms/bot/list")
    public BaseResult<Object> getSmsBot(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO
    ) {
        return botApiService.getMyMessageBotList(userBO.getUsername());
    }

    @PreAuthorize("hasRole('ROLE_SYS_SUPER_ADMIN') or hasRole('ROLE_SYS_ADMIN') or  hasRole('ROLE_BOT_ADMIN')")
    @GetMapping("bot/getBotList")
    public BaseResult<List<BotInfo>> getBotList(
            @RequestAttribute(ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("id") Long id
    ) {
        return BaseResult.of(botApiService.getBotList(id, userBO.getUsername()));
    }
}
