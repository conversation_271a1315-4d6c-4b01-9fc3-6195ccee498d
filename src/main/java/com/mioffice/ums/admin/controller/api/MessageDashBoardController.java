package com.mioffice.ums.admin.controller.api;

import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.bo.UserInfoBaseBO;
import com.mioffice.ums.admin.entity.vo.MessageDashBoardVO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.MessageDashBoardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/7 4:34 下午
 * version: 1.0.0
 */
@RestController
@RequestMapping("/ums-api/api")
@Slf4j
public class MessageDashBoardController {

    private final MessageDashBoardService messageDashBoardService;

    public MessageDashBoardController(MessageDashBoardService messageDashBoardService) {
        this.messageDashBoardService = messageDashBoardService;
    }

    @GetMapping("/data/analysis/message")
    public BaseResult<MessageDashBoardVO> getDataAnalysisMessage(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam(value = "deptIdList", required = false) String deptIdList,
            @RequestParam(value = "publishUsernameList", required = false) String publishUsernameList,
            @RequestParam(value = "start") String start,
            @RequestParam(value = "end") String end
    ) {
        return messageDashBoardService.getDataAnalysisMessage(userBO, deptIdList, publishUsernameList, start, end);
    }

    @GetMapping("data/analysis/message/publisher/systemAdmin")
    public BaseResult<List<UserInfoBaseBO>> getPublisherSystemAdmin(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam(value = "searchWord") String searchWord
    ) {
        return messageDashBoardService.getPublisherSystemAdmin(userBO, searchWord);
    }

    @GetMapping("data/analysis/message/publisher/superAdmin")
    public BaseResult<List<UserInfoBaseBO>> getPublisherSuperAdmin(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam(value = "deptIds") String deptIds,
            @RequestParam(value = "searchWord") String searchWord
    ) {
        return messageDashBoardService.getPublisherSuperAdmin(userBO, deptIds, searchWord);
    }

}
