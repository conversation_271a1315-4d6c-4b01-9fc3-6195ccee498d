package com.mioffice.ums.admin.controller.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.BotGroupPushListAddDTO;
import com.mioffice.ums.admin.entity.dto.BotGroupPushListDeleteDTO;
import com.mioffice.ums.admin.entity.info.LarkGroupPushInfo;
import com.mioffice.ums.admin.entity.vo.BotGroupListResultVO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.BotGroupApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * @since 2020/11/26 下午3:40
 * version: 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/ums-api/api")
public class BotGroupApiController {

    @Resource
    private BotGroupApiService botGroupApiService;

    @GetMapping("/bot/group/list")
    public BaseResult<BotGroupListResultVO> fetchBotGroupList(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam(value = "botId") Long botId
    ) {
        Assert.notNull(botId, "botId非法");
        return botGroupApiService.fetchBotGroupList(botId, userBO);
    }

    @GetMapping("/bot/group/page")
    public BaseResult<IPage<LarkGroupPushInfo>> page(
            @RequestParam(value = "page") Long page,
            @RequestParam(value = "size") Long size,
            @RequestParam(value = "tagId", required = false) String tagId,
            @RequestParam(value = "key", required = false) String key
    ) {
        cn.hutool.core.lang.Assert.notNull(page, "请传page");
        cn.hutool.core.lang.Assert.notNull(size, "请传size");
        return botGroupApiService.page(page, size, tagId, key);
    }

    @PostMapping("/bot/group/del")
    public BaseResult<Object> del(
            @RequestBody BotGroupPushListDeleteDTO botGroupPushListDeleteDTO
    ) {
        cn.hutool.core.lang.Assert.notNull(botGroupPushListDeleteDTO.getTagId(), "tagId为空");
        cn.hutool.core.lang.Assert.notNull(botGroupPushListDeleteDTO.getIdList(), "删除的推送群名单id为空");

        return botGroupApiService.delete(botGroupPushListDeleteDTO.getTagId(), botGroupPushListDeleteDTO.getIdList());
    }

    @PostMapping("/bot/group/add")
    public BaseResult<Object> add(
            @RequestBody BotGroupPushListAddDTO botGroupPushListAddDTO
    ) {
        cn.hutool.core.lang.Assert.notEmpty(botGroupPushListAddDTO.getBotGroupRecordList(), "botGroupRecordList为空");
        return botGroupApiService.add(botGroupPushListAddDTO);
    }
}
