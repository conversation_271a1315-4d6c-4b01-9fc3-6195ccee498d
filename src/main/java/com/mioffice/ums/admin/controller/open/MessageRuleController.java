package com.mioffice.ums.admin.controller.open;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mioffice.ums.admin.entity.dto.IdDTO;
import com.mioffice.ums.admin.entity.dto.MessageRuleJobDTO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.service.MessageRuleJobService;
import com.mioffice.ums.admin.utils.X5RequestContext;
import com.mioffice.ums.admin.x5client.X5Request;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * <AUTHOR>
 * @create 11 30,2022
 */

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(value = "/ums-api/open", consumes = "application/x-www-form-urlencoded")
public class MessageRuleController {

    private MessageRuleJobService messageRuleJobService;

    private ObjectMapper objectMapper;

    /**
     * 增加消息规则
     */
    @PostMapping(value = "/message/job/add")
    public BaseResult ruleAdd() {
        X5Request request = X5RequestContext.get();
        MessageRuleJobDTO messageRuleJob = getParam(request.getBody(), MessageRuleJobDTO.class);
        messageRuleJob.setAppId(request.getHeader().getAppId());
        return messageRuleJobService.ruleJobAdd(messageRuleJob);
    }

    /**
     * 修改消息规则
     */
    @PostMapping(value = "/message/job/update")
    public BaseResult ruleUpdate() {
        X5Request request = X5RequestContext.get();
        MessageRuleJobDTO messageRuleJob = getParam(request.getBody(), MessageRuleJobDTO.class);
        messageRuleJob.setAppId(request.getHeader().getAppId());
        return messageRuleJobService.ruleJobUpdate(messageRuleJob);
    }

    @PostMapping(value = "/message/job/del")
    public BaseResult ruleDel() {
        X5Request request = X5RequestContext.get();
        IdDTO id = getParam(request.getBody(), IdDTO.class);
        Assert.notNull(id, "缺少参数");
        return messageRuleJobService.delRuleJob(id);
    }

    @PostMapping("message/job/detail")
    public BaseResult<MessageRuleJobDTO> detail() {
        X5Request request = X5RequestContext.get();
        IdDTO id = getParam(request.getBody(), IdDTO.class);
        Assert.notNull(id, "缺少参数");
        return messageRuleJobService.ruleJobDetail(id);
    }

    @PostMapping("message/job/stop")
    public BaseResult stop() {
        X5Request request = X5RequestContext.get();
        IdDTO id = getParam(request.getBody(), IdDTO.class);
        Assert.notNull(id, "缺少参数");
        return messageRuleJobService.stopRuleJob(id);
    }

    @PostMapping("message/job/start")
    public BaseResult start() {
        X5Request request = X5RequestContext.get();
        IdDTO id = getParam(request.getBody(), IdDTO.class);
        Assert.notNull(id, "缺少参数");
        return messageRuleJobService.startRuleJob(id);
    }

    private <T> T getParam(String encodeStr, Class<T> clazz) {
        try {
            return objectMapper.readValue(encodeStr, clazz);
        } catch (IOException e) {
            throw new AccessDeniedException(e.getMessage());
        }
    }

}
