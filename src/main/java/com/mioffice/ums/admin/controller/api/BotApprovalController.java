package com.mioffice.ums.admin.controller.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.admin.cas.user.ApiAuthInterceptors;
import com.mioffice.ums.admin.entity.bo.UserBO;
import com.mioffice.ums.admin.entity.dto.BotApplyApprovalPageDTO;
import com.mioffice.ums.admin.entity.dto.BotApplyDTO;
import com.mioffice.ums.admin.entity.dto.BotApplyDetailDTO;
import com.mioffice.ums.admin.entity.dto.BotApplyListDTO;
import com.mioffice.ums.admin.entity.dto.BotApprovalPassQueryDTO;
import com.mioffice.ums.admin.entity.dto.BotApprovalRejectQueryDTO;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.BotApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/31 2:15 下午
 * version: 1.0.0
 */
@RestController
@RequestMapping("/ums-api/api")
@Slf4j
public class BotApprovalController {

    private final BotApprovalService botApprovalService;

    public BotApprovalController(BotApprovalService botApprovalService) {
        this.botApprovalService = botApprovalService;
    }

    @PostMapping("/bot/apply")
    public BaseResult<Object> botApply(
            @RequestBody BotApplyDTO botApplyDTO,
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO
    ) {
        Assert.notNull(botApplyDTO.getBotId(), "机器人ID为空");
        Assert.hasLength(botApplyDTO.getApplyDesc(), "请填写使用说明");
        Assert.notEmpty(botApplyDTO.getUseUsernameList(), "请填写使用人");
        return botApprovalService.botApply(botApplyDTO, userBO);
    }

    @GetMapping("/bot/apply/my/page")
    public BaseResult<IPage<BotApplyListDTO>> botApplyListMy(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("page") Long page,
            @RequestParam("size") Long size
    ) {
        Assert.notNull(page, "page为空");
        Assert.notNull(size, "size为空");
        return BaseResult.of(botApprovalService.botApplyListMy(userBO, page, size));
    }

    @GetMapping("/bot/approval/page")
    public BaseResult<IPage<BotApplyApprovalPageDTO>> botApprovalPage(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("page") Long page,
            @RequestParam("size") Long size
    ) {
        Assert.notNull(page, "page为空");
        Assert.notNull(size, "size为空");
        return BaseResult.of(botApprovalService.botApprovalPage(userBO, page, size));
    }

    @GetMapping("/bot/apply/detail")
    public BaseResult<BotApplyDetailDTO> botApplyDetail(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestParam("id") Long id
    ) {
        Assert.notNull(id, "id为空");
        BotApplyDetailDTO botApplyDetailDTO = botApprovalService.botApplyDetail(id);
        if (Objects.nonNull(botApplyDetailDTO)) {
            return BaseResult.of(botApplyDetailDTO);
        } else {
            return new BaseResult<BotApplyDetailDTO>().setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setMessage("审批数据错误");
        }
    }

    @PostMapping("/bot/approval/reject")
    public BaseResult<Object> botApprovalReject(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody BotApprovalRejectQueryDTO botApprovalRejectQueryDTO
    ) {
        Assert.notNull(botApprovalRejectQueryDTO.getId(), "请输入id");
        Assert.hasLength(botApprovalRejectQueryDTO.getRemark(), "请输入驳回理由");
        return botApprovalService.botApprovalReject(userBO, botApprovalRejectQueryDTO);
    }

    @PostMapping("/bot/approval/pass")
    public BaseResult<Object> botApprovalPass(
            @RequestAttribute(name = ApiAuthInterceptors.USER_ATTR) UserBO userBO,
            @RequestBody BotApprovalPassQueryDTO botApprovalPassQueryDTO
    ) {
        Assert.notNull(botApprovalPassQueryDTO.getId(), "请输入id");
        return botApprovalService.botApprovalPass(userBO, botApprovalPassQueryDTO);
    }
}
