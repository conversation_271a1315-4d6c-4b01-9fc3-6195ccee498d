package com.mioffice.ums.admin.controller.open;

import cn.hutool.core.lang.Assert;
import com.mioffice.ums.admin.result.BaseResult;
import com.mioffice.ums.admin.result.ResultCode;
import com.mioffice.ums.admin.service.SendWeeklyMsgService;
import com.mioffice.ums.admin.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <p>
 * 处理飞书回调
 * </p>
 *
 * <AUTHOR>
 * @date 2021/3/23 8:03 下午
 */
@Slf4j
@RestController
@RequestMapping("/ums-api/open")
public class LarkCallbackController {

    @Autowired
    private SendWeeklyMsgService sendWeeklyMsgService;

    @GetMapping("/accept")
    public BaseResult<Object> redirectAfterLarkLogin(@RequestParam("code") String code, @RequestParam("state") String state) {
        log.info("accept 回调接口， code = [{}]", code);
        try {
            Assert.notBlank(code, "授权码为空");
            sendWeeklyMsgService.processAccessToken(code, state);
            return BaseResult.of(code);
        } catch (Exception e) {
            return new BaseResult<>(ResultCode.SERVER_INNER_ERROR, e.toString());
        }
    }

    @PostMapping("/groupCallBack/{appId}")
    public Map<String, Object> notify(
            @PathVariable(name = "appId") String appId,
            @RequestBody Map<String, Object> params
    ) {
        log.info("notify 回调接口， params = [{}]", JsonUtils.toJson(params));
        return sendWeeklyMsgService.processLarkCallBack(appId, params);
    }
}
