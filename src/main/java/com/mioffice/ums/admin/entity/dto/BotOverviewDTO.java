package com.mioffice.ums.admin.entity.dto;

import lombok.Data;

/**
 * <p>
 * 我的机器人概况
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/8/12 14:53
 */
@Data
public class BotOverviewDTO {

    /**
     * 我的机器人的总数
     */
    private Long botTotalNum;

    /**
     * 当月推送次数
     */
    private Long curMonthPushCount;

    /**
     * 至今推送次数
     */
    private Long pushCountUpToNow;

    /**
     * 没有发送数量的机器人总数
     *
     * @param botTotalNum
     */
    public static BotOverviewDTO createDefault(Long botTotalNum) {
        BotOverviewDTO botOverviewDTO = new BotOverviewDTO();
        botOverviewDTO.botTotalNum = botTotalNum;
        botOverviewDTO.curMonthPushCount = 0L;
        botOverviewDTO.pushCountUpToNow = 0L;

        return botOverviewDTO;
    }

}
