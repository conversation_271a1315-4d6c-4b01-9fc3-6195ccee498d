package com.mioffice.ums.admin.process;

import cn.hutool.core.date.DateUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.infra.oaucf.bpm.enums.ProcessInstanceStatus;
import com.mi.oa.infra.oaucf.bpm.rep.CreateProcInstResp;
import com.mi.oa.infra.oaucf.bpm.rep.QueryProcInstResp;
import com.mi.oa.infra.oaucf.bpm.req.CreateProcInstReq;
import com.mi.oa.infra.oaucf.bpm.req.TerminateProcInstReq;
import com.mi.oa.infra.oaucf.bpm.service.ProcessInstanceService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mioffice.ums.admin.entity.info.ApprovalInfo;
import com.mioffice.ums.admin.entity.info.BotInfo;
import com.mioffice.ums.admin.entity.info.EmailTaskInfo;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.LarkTaskInfo;
import com.mioffice.ums.admin.entity.info.OperationInfo;
import com.mioffice.ums.admin.entity.info.PublishScopeInfo;
import com.mioffice.ums.admin.entity.info.SmsTaskInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.entity.info.UserBotInfo;
import com.mioffice.ums.admin.entity.vo.hrod.HrodUser;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.enums.TaskChannelEnum;
import com.mioffice.ums.admin.enums.UserBotRoleEnum;
import com.mioffice.ums.admin.manager.ProcessDefinitionResource;
import com.mioffice.ums.admin.manager.ReportLineManager;
import com.mioffice.ums.admin.mapper.ApprovalInfoMapper;
import com.mioffice.ums.admin.mapper.BotInfoMapper;
import com.mioffice.ums.admin.mapper.EmailTaskInfoMapper;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.LarkTaskInfoMapper;
import com.mioffice.ums.admin.mapper.OperationInfoMapper;
import com.mioffice.ums.admin.mapper.PublishScopeInfoMapper;
import com.mioffice.ums.admin.mapper.SmsTaskInfoMapper;
import com.mioffice.ums.admin.mapper.UserBotInfoMapper;
import com.mioffice.ums.admin.service.ApprovalConfigService;
import com.mioffice.ums.admin.service.TaskService;
import static com.mioffice.ums.admin.service.impl.ApprovalConfigServiceImpl.SPECIAL_CHANNEL;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * @ClassName BpmMessageProcess
 * @Description BPM集成
 * <AUTHOR>
 * @Date 2023/3/1 10:08
 **/
@Slf4j
@Component
public class BpmMessageProcess implements MessageProcess {

    @NacosValue(value = "${ums.admin.bpmn-id:}", autoRefreshed = true)
    private String bpmnId;

    @NacosValue(value = "${ums.admin.ums-task-link:}", autoRefreshed = true)
    private String umsTaskLink;

    @NacosValue(value = "${ums.admin.bpm-start-taskid:}", autoRefreshed = true)
    private String bpmStartTaskId;

    @Resource
    private PublishScopeInfoMapper scopeInfoMapper;

    @Resource
    private ProcessInstanceService processInstanceService;

    @Resource
    private ReportLineManager reportLineManager;

    @Resource
    private LarkTaskInfoMapper larkTaskInfoMapper;

    @Resource
    private EmailTaskInfoMapper emailTaskInfoMapper;

    @Resource
    private SmsTaskInfoMapper smsTaskInfoMapper;

    @Resource
    private UserBotInfoMapper userBotInfoMapper;

    @Resource
    private EmployeeInfoMapper employeeInfoMapper;

    @Resource
    private ApprovalInfoMapper approvalInfoMapper;

    @Resource
    private ApprovalConfigService approvalConfigService;

    @Resource
    private OperationInfoMapper operationInfoMapper;

    @Resource
    private BotInfoMapper botInfoMapper;

    @Autowired
    private TaskService taskService;

    @Override
    public boolean createProcess(TaskChannelEnum taskChannelEnum, TaskInfo taskInfo, String createUsername) {
        // 流程变量
        Map<String, Object> variables = new LinkedHashMap<>();
        Map<Byte, ApprovalInfo> approvalInfoMap = approvalInfoMapper.selectList(
                        Wrappers.<ApprovalInfo>lambdaQuery()
                                .ne(ApprovalInfo::getChannel, SPECIAL_CHANNEL)
                ).stream()
                .collect(Collectors.toMap(ApprovalInfo::getChannel, Function.identity(), (x1, x2) -> x2));
        Long baseThreshold = approvalInfoMap.get(TaskChannelEnum.CHANNEL_LARK.getCode()).getBaseThreshold();
        Long fullThreshold = approvalInfoMap.get(TaskChannelEnum.CHANNEL_LARK.getCode()).getThreshold();
        String untilLevel1Leader =
                reportLineManager.getUntilLevelNLeader(createUsername, 1).stream().map(HrodUser::getOprId)
                        .collect(Collectors.joining(","));
        String untilLevel2Leader =
                reportLineManager.getUntilLevelNLeader(createUsername, 2).stream().map(HrodUser::getOprId)
                        .collect(Collectors.joining(","));
        boolean isGroupPush = isGroupPush(taskInfo);
        String botManagers = "";
        ApprovalInfo specialApprovalInfo = null;
        String metaProcess = "";
        String botCustomApprover = "";
        boolean isWhiteBot = false;
        Long botId = null;
        if (taskChannelEnum == TaskChannelEnum.CHANNEL_LARK) {
            LarkTaskInfo larkTaskInfo = larkTaskInfoMapper.selectOne(
                    Wrappers.<LarkTaskInfo>lambdaQuery().eq(LarkTaskInfo::getTaskId, taskInfo.getId()));
            if (Objects.nonNull(larkTaskInfo)) {
                botManagers = getBotManagerList(larkTaskInfo);
                botId = larkTaskInfo.getBotId();
                specialApprovalInfo = approvalInfoMapper.selectOne(
                        Wrappers.<ApprovalInfo>lambdaQuery()
                                .eq(ApprovalInfo::getChannel, ProcessDefinitionResource.SPECIAL_RESOURCE_CHANNEL)
                                .eq(ApprovalInfo::getBotId, botId));
                if (Objects.nonNull(specialApprovalInfo)) {
                    isWhiteBot = true;
                    metaProcess = specialApprovalInfo.getApprovalMetaNode();
                    botCustomApprover = String.join(",", approvalConfigService.getCustomApprover(metaProcess,
                            createUsername));
                }
            }
        } else if (taskChannelEnum == TaskChannelEnum.CHANNEL_EMAIL) {
            EmailTaskInfo emailTaskInfo = emailTaskInfoMapper.selectOne(
                    Wrappers.<EmailTaskInfo>lambdaQuery().eq(EmailTaskInfo::getTaskId, taskInfo.getId()));
            if (Objects.nonNull(emailTaskInfo)) {
                botId = emailTaskInfo.getBotId();
            }
        } else {
            SmsTaskInfo smsTaskInfo =
                    smsTaskInfoMapper.selectOne(
                            Wrappers.<SmsTaskInfo>lambdaQuery().eq(SmsTaskInfo::getTaskId, taskInfo.getId()));
            if (Objects.nonNull(smsTaskInfo)) {
                botId = smsTaskInfo.getBotId();
            }
        }
        String itOperators = operationInfoMapper
                .selectList(Wrappers.lambdaQuery())
                .stream()
                .map(OperationInfo::getOperationUsername)
                .collect(Collectors.joining(","));

        variables.put("bot_manager", botManagers);
        variables.put("level1_leader", untilLevel1Leader);
        variables.put("bot_custom_approver", botCustomApprover);
        variables.put("is_white_bot", isWhiteBot);
        variables.put("level2_leader", untilLevel2Leader);
        variables.put("channel", Byte.toUnsignedInt(taskChannelEnum.getCode()));
        variables.put("is_group_push", isGroupPush);
        variables.put("base_threshold", baseThreshold);
        variables.put("full_threshold", fullThreshold);
        variables.put("it_operators", itOperators);

        // 表单数据
        String titleCn = taskInfo.getTitleCn();
        String botName = StringUtils.EMPTY;
        if (Objects.nonNull(botId)) {
            BotInfo botInfo = botInfoMapper.selectById(botId);
            if (Objects.nonNull(botInfo)) {
                botName = botInfo.getBotName();
            }
        }
        String publishScope = taskService.isRush(taskInfo) ? "【加推】" +
                PublishScopeEnum.getByCode(taskInfo.getPublishScope()).getMsg() :
                PublishScopeEnum.getByCode(taskInfo.getPublishScope()).getMsg();

        long publishCount = 0;
        List<PublishScopeInfo> publishScopeInfoList = scopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery()
                        .eq(PublishScopeInfo::getTaskId, taskInfo.getId())
                        .select(PublishScopeInfo::getAllCount)
        );
        if (!publishScopeInfoList.isEmpty()) {
            publishCount =
                    publishScopeInfoList.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).orElse(0L);
        }
        String publishTime = DateUtil.date(taskInfo.getPublishTime()).toString();
        String taskLink = umsTaskLink.replace("{taskid}", taskInfo.getId().toString());

        Map<String, Object> formData = new LinkedHashMap<>();
        formData.put("textarea_213b83e46150", titleCn);
        formData.put("input_70388f65154b", botName);
        formData.put("input_68bd96c81632", publishScope);
        formData.put("inputFloat_103691776f0b", publishCount);
        formData.put("datepicker_14f83dd6fb6c", publishTime);
        formData.put("input_a69ad35874ef", taskChannelEnum.getMsg());
        formData.put("link_f1067d6db59d", taskLink);

        // 流程发起
        CreateProcInstReq createProcInstReq = CreateProcInstReq.builder()
                .modelCode(bpmnId)
                .processInstanceName("ums消息发布审批")
                .variables(variables)
                .businessKey(taskInfo.getBpmInstanceId())
                .startUserId(createUsername)
                .formData(formData)
                .build();

        BaseResp<CreateProcInstResp> resp = processInstanceService.create(createProcInstReq);

        return resp.getCode() == 0;
    }

    @Override
    public boolean terminate(String createUsername, TaskInfo taskInfo) {
        TerminateProcInstReq terminateProcInstReq = TerminateProcInstReq.builder()
                .businessKey(taskInfo.getBpmInstanceId())
                .operator(createUsername)
                .comment(createUsername + "主动终止流程")
                .build();

        BaseResp<Void> resp = processInstanceService.terminate(terminateProcInstReq);

        return resp.getCode() == 0;
    }

    @Override
    public boolean isApprovalFinish(TaskInfo taskInfo) {
        if (taskInfo.getId() < Long.parseLong(bpmStartTaskId)) {
            return true;
        }
        BaseResp<QueryProcInstResp> resp = processInstanceService.get(taskInfo.getBpmInstanceId());
        if (0 == resp.getCode()) {
            return resp.getData().getProcessInstanceStatus() == ProcessInstanceStatus.COMPLETED ||
                    resp.getData().getProcessInstanceStatus() == ProcessInstanceStatus.TERMINATED;
        }
        return true;
    }

    private String getBotManagerList(LarkTaskInfo larkTaskInfo) {
        List<UserBotInfo> managerBotInfoList = userBotInfoMapper.selectList(
                Wrappers.<UserBotInfo>lambdaQuery()
                        .eq(UserBotInfo::getBotId, larkTaskInfo.getBotId())
                        .eq(UserBotInfo::getUserType, UserBotRoleEnum.USER_BOT_MANAGER.getCode())
        );

        List<String> managerUsernameList =
                managerBotInfoList.stream().map(UserBotInfo::getUsername).collect(Collectors.toList());

        List<String> onJobUsernameList = employeeInfoMapper.selectList(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .in(EmployeeInfo::getUsername, managerUsernameList)
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        ).stream().map(EmployeeInfo::getUsername).distinct().collect(Collectors.toList());

        if (onJobUsernameList.isEmpty()) {
            throw new IllegalArgumentException("创建审批失败，机器人管理员为空");
        }
        return String.join(",", onJobUsernameList);
    }

    private boolean isGroupPush(TaskInfo taskInfo) {
        return taskInfo.getPublishScope() == PublishScopeEnum.GROUP_PUSH.getCode().intValue();
    }
}
