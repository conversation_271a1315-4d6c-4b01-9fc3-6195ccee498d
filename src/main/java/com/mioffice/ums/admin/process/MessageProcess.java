package com.mioffice.ums.admin.process;

import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.enums.TaskChannelEnum;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.02
 */
public interface MessageProcess {

    /**
     * 创建审批
     *
     * @param taskChannelEnum 任务的渠道
     * @param taskInfo 任务
     * @param createUsername 创建人
     * @return boolean
     */
    boolean createProcess(TaskChannelEnum taskChannelEnum, TaskInfo taskInfo, String createUsername);

    /**
     * 中断审批
     *
     * @param createUsername 创建人
     * @param taskInfo 任务
     * @return boolean
     */
    boolean terminate(String createUsername, TaskInfo taskInfo);

    /**
     * 审批是否完成
     * @param taskInfo 任务
     * @return
     */
    boolean isApprovalFinish(TaskInfo taskInfo);
}
