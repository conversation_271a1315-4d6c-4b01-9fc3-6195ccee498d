package com.mioffice.ums.admin.process;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonParser;
import com.mioffice.ums.admin.entity.bo.ProcessCreation;
import com.mioffice.ums.admin.entity.dto.ApprovalNodeDTO;
import com.mioffice.ums.admin.entity.info.ApprovalInfo;
import com.mioffice.ums.admin.entity.info.EmployeeInfo;
import com.mioffice.ums.admin.entity.info.LarkTaskInfo;
import com.mioffice.ums.admin.entity.info.OperationInfo;
import com.mioffice.ums.admin.entity.info.PublishScopeInfo;
import com.mioffice.ums.admin.entity.info.TaskInfo;
import com.mioffice.ums.admin.entity.info.UserBotInfo;
import com.mioffice.ums.admin.enums.ApprovalNodeEnum;
import com.mioffice.ums.admin.enums.HrApplyStatusEnum;
import com.mioffice.ums.admin.enums.ProcessAuditEnum;
import com.mioffice.ums.admin.enums.PublishScopeEnum;
import com.mioffice.ums.admin.enums.TaskChannelEnum;
import com.mioffice.ums.admin.enums.UserBotRoleEnum;
import com.mioffice.ums.admin.manager.ProcessDefinitionResource;
import com.mioffice.ums.admin.manager.ProcessManager;
import com.mioffice.ums.admin.manager.ReportLineManager;
import com.mioffice.ums.admin.mapper.ApprovalInfoMapper;
import com.mioffice.ums.admin.mapper.EmployeeInfoMapper;
import com.mioffice.ums.admin.mapper.LarkTaskInfoMapper;
import com.mioffice.ums.admin.mapper.OperationInfoMapper;
import com.mioffice.ums.admin.mapper.PublishScopeInfoMapper;
import com.mioffice.ums.admin.mapper.TaskInfoMapper;
import com.mioffice.ums.admin.mapper.UserBotInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 消息对接审批
 * </p>
 * <AUTHOR>
 * @date 2020/9/3 4:56 下午
 */
@Slf4j
//@Component
public class DefaultMessageProcess implements MessageProcess {

    private final ProcessManager processManager;

    private final ApprovalInfoMapper approvalInfoMapper;

    private final ReportLineManager reportLineManager;

    private final LarkTaskInfoMapper larkTaskInfoMapper;

    private final PublishScopeInfoMapper publishScopeInfoMapper;

    private final UserBotInfoMapper userBotInfoMapper;

    private final OperationInfoMapper operationInfoMapper;

    private final TaskInfoMapper taskInfoMapper;

    private final EmployeeInfoMapper employeeInfoMapper;

    public DefaultMessageProcess(ProcessManager processManager, ApprovalInfoMapper approvalInfoMapper, ReportLineManager reportLineManager, LarkTaskInfoMapper larkTaskInfoMapper, PublishScopeInfoMapper publishScopeInfoMapper, UserBotInfoMapper userBotInfoMapper, OperationInfoMapper operationInfoMapper, TaskInfoMapper taskInfoMapper, EmployeeInfoMapper employeeInfoMapper) {
        this.processManager = processManager;
        this.approvalInfoMapper = approvalInfoMapper;
        this.reportLineManager = reportLineManager;
        this.larkTaskInfoMapper = larkTaskInfoMapper;
        this.publishScopeInfoMapper = publishScopeInfoMapper;
        this.userBotInfoMapper = userBotInfoMapper;
        this.operationInfoMapper = operationInfoMapper;
        this.taskInfoMapper = taskInfoMapper;
        this.employeeInfoMapper = employeeInfoMapper;
    }

    @Override
    public boolean createProcess(TaskChannelEnum taskChannelEnum, TaskInfo taskInfo, String createUsername) {
        try {
            //  先检查有没有特殊审批
            ApprovalInfo specialApprovalInfo = null;
            String metaProcess = "";
            String approvalProcess = "";
            if (taskChannelEnum == TaskChannelEnum.CHANNEL_LARK) {
                LarkTaskInfo larkTaskInfo =
                        larkTaskInfoMapper.selectOne(Wrappers.<LarkTaskInfo>lambdaQuery().eq(LarkTaskInfo::getTaskId, taskInfo.getId()));
                Long botId = larkTaskInfo.getBotId();
                specialApprovalInfo = approvalInfoMapper.selectOne(
                        Wrappers.<ApprovalInfo>lambdaQuery()
                                .eq(ApprovalInfo::getChannel, ProcessDefinitionResource.SPECIAL_RESOURCE_CHANNEL)
                                .eq(ApprovalInfo::getBotId, botId)
                );
            }
            if (Objects.nonNull(specialApprovalInfo)) {
                metaProcess = specialApprovalInfo.getApprovalMetaNode();
                approvalProcess = specialApprovalInfo.getApprovalProcessNode();
            } else {
                ApprovalInfo approvalInfo = approvalInfoMapper.selectOne(
                        Wrappers.<ApprovalInfo>lambdaQuery()
                                .eq(ApprovalInfo::getChannel, taskChannelEnum.getCode()));
                metaProcess = approvalInfo.getApprovalMetaNode();
                approvalProcess = approvalInfo.getApprovalProcessNode();
            }

            Assert.hasLength(metaProcess, "审核人员节点不存在");
            Assert.hasLength(approvalProcess, "审核流程不存在");

            Map<String, Object> variables = getVariables(metaProcess, taskInfo.getId(), createUsername, taskChannelEnum);

            ProcessCreation processCreation = new ProcessCreation(ProcessDefinitionResource.PROCESS_MESSAGE_RELEASE,
                    taskInfo.getId(), approvalProcess, variables, createUsername, taskChannelEnum.getCode());

            processManager.createProcessInstance(processCreation);

            return true;
        } catch (Exception e) {
            log.warn("taskId为[{}]的任务创建审批流程失败, 原因: [{}]", taskInfo.getId(), e);
        }
        return false;
    }

    @Override
    public boolean terminate(String createUsername, TaskInfo taskInfo) {
        try {
            processManager.terminate(taskInfo.getId(), createUsername);
            return true;
        } catch (Exception e) {
            log.warn("taskId为[{}]的任务终结审批流程失败", taskInfo.getId());
            return false;
        }
    }

    @Override
    public boolean isApprovalFinish(TaskInfo taskInfo) {
        return false;
    }

    private Map<String, Object> getVariables(String metaProcess, long taskId, String createUsername, TaskChannelEnum taskChannelEnum) {
        Map<String, Object> variables = new HashMap<>(16);
        JsonArray jsonArray = new JsonParser().parse(metaProcess).getAsJsonArray();
        Gson gson = new Gson();
        ProcessAuditEnum auditType = getAuditLevel(taskId);
        boolean isGroupPush = isGroupPush(taskId);
        if (isGroupPush) {
            // 群推送只走一个节点
            JsonArray newJsonArray = new JsonArray();
            newJsonArray.add(jsonArray.get(0));
            jsonArray = newJsonArray;
        } else if (taskChannelEnum.getCode() != TaskChannelEnum.CHANNEL_LARK.getCode()) {
            // 如果不是飞书消息，一级审核只走一个节点，二级审核只走二个节点，三级审核走全部
            if (auditType.getType() == ProcessAuditEnum.FIRST_LEVEL_AUDIT.getType()) {
                JsonArray newJsonArray = new JsonArray();
                newJsonArray.add(jsonArray.get(0));
                jsonArray = newJsonArray;
            } else if (auditType.getType() == ProcessAuditEnum.SECOND_LEVEL_AUDIT.getType()) {
                if (jsonArray.size() >= 2) {
                    JsonArray newJsonArray = new JsonArray();
                    newJsonArray.add(jsonArray.get(0));
                    newJsonArray.add(jsonArray.get(1));
                    jsonArray = newJsonArray;
                }
            }
        } else {
            // 飞书消息，一级审核走二个节点，二级审核只走三个节点，三级审核走全部
            if (auditType.getType() == ProcessAuditEnum.FIRST_LEVEL_AUDIT.getType()) {
                if (jsonArray.size() >= 2) {
                    JsonArray newJsonArray = new JsonArray();
                    newJsonArray.add(jsonArray.get(0));
                    newJsonArray.add(jsonArray.get(1));
                    jsonArray = newJsonArray;
                }
            } else if (auditType.getType() == ProcessAuditEnum.SECOND_LEVEL_AUDIT.getType()) {
                if (jsonArray.size() >= 3) {
                    JsonArray newJsonArray = new JsonArray();
                    newJsonArray.add(jsonArray.get(0));
                    newJsonArray.add(jsonArray.get(1));
                    newJsonArray.add(jsonArray.get(2));
                    jsonArray = newJsonArray;
                }
            }
        }
        AtomicInteger customizeIndex = new AtomicInteger(1);
        jsonArray.forEach(
                jsonObject -> {
                    ApprovalNodeDTO approvalNodeDTO = gson.fromJson(jsonObject, ApprovalNodeDTO.class);
                    if (ApprovalNodeEnum.APPROVAL_TYPE_CUSTOMIZE.getName().equals(approvalNodeDTO.getName())) {
                        variables.put(approvalNodeDTO.getName().concat(String.valueOf(customizeIndex.getAndIncrement())), getApprovalUserList(approvalNodeDTO.getName(), approvalNodeDTO, taskId, createUsername));
                    } else {
                        variables.put(approvalNodeDTO.getName(), getApprovalUserList(approvalNodeDTO.getName(), approvalNodeDTO, taskId, createUsername));
                    }
                }
        );
        return variables;
    }

    private String getApprovalUserList(String name, ApprovalNodeDTO approvalNodeDTO, long taskId, String createUsername) {
        if (ApprovalNodeEnum.APPROVAL_TYPE_MANAGERS.getName().equals(name)) {
            return this.getBotManagerList(taskId);
        } else if (ApprovalNodeEnum.APPROVAL_TYPE_LEADER.getName().equals(name)) {
            String leader = reportLineManager.getDirectLeader(createUsername, "").getSupUserName();
            if (leader == null || leader.isEmpty()) {
                log.warn("{} 未获取到直属leader", createUsername);
                throw new RuntimeException("未获取到直属leader");
            }
            return leader;
        } else if (ApprovalNodeEnum.APPROVAL_TYPE_LEVEL_2_LEADER.getName().equals(name)) {
            return reportLineManager.get2thDeptLeader(createUsername, "").getSupUserName();
        } else if (ApprovalNodeEnum.APPROVAL_TYPE_CUSTOMIZE.getName().equals(name)) {
            List<ApprovalNodeDTO.Customize> customizes = approvalNodeDTO.getCustomizeUserList();
            return customizes.stream().map(ApprovalNodeDTO.Customize::getUsername).distinct().collect(Collectors.joining(","));
        } else if (ApprovalNodeEnum.APPROVAL_TYPE_OPERATORS.getName().equals(name)) {
            List<OperationInfo> operationInfoList = operationInfoMapper.selectList(Wrappers.lambdaQuery());
            if (operationInfoList.isEmpty()) {
                throw new IllegalArgumentException("创建审批失败，运营的人员为空");
            }
            List<String> operationUsernameList = operationInfoList.stream().map(OperationInfo::getOperationUsername).collect(Collectors.toList());
            return String.join(",", operationUsernameList);
        } else if (ApprovalNodeEnum.APPROVAL_TYPE_LEVEL_1_LEADER.getName().equals(name)) {
            return reportLineManager.getDeptVp(createUsername, "").getSupUserName();
        }
        throw new RuntimeException("审批人为空");
    }

    /**
     * 是否超过
     *
     * @param taskId
     * @return
     */
    private ProcessAuditEnum getAuditLevel(long taskId) {
        List<PublishScopeInfo> publishScopeInfoList = publishScopeInfoMapper.selectList(
                Wrappers.<PublishScopeInfo>lambdaQuery()
                        .eq(PublishScopeInfo::getTaskId, taskId)
        );

        long allCount = 0L;
        if (!publishScopeInfoList.isEmpty()) {
            allCount = publishScopeInfoList.stream().map(PublishScopeInfo::getAllCount).reduce(Long::sum).orElse(0L);
        }
        // 1.因为邮件，SMS,飞书共用一个阈值，所以取一个就行
        ApprovalInfo approvalInfo = approvalInfoMapper.selectOne(
                Wrappers.<ApprovalInfo>lambdaQuery()
                        .eq(ApprovalInfo::getChannel, TaskChannelEnum.CHANNEL_LARK.getCode()));

        long advancedThreshold = approvalInfo.getThreshold();
        long baseThreshold = approvalInfo.getBaseThreshold();

        if (allCount < baseThreshold) {
            return ProcessAuditEnum.FIRST_LEVEL_AUDIT;
        }

        if (allCount < advancedThreshold) {
            return ProcessAuditEnum.SECOND_LEVEL_AUDIT;
        }

        return ProcessAuditEnum.THIRD_LEVEL_AUDIT;

    }

    private boolean isGroupPush(long taskId) {
        TaskInfo taskInfo = taskInfoMapper.selectById(taskId);
        return taskInfo.getPublishScope() == PublishScopeEnum.GROUP_PUSH.getCode().intValue();
    }

    public String getBotManagerList(long taskId) {
        LarkTaskInfo larkTaskInfo = larkTaskInfoMapper.selectOne(
                Wrappers.<LarkTaskInfo>lambdaQuery()
                        .eq(LarkTaskInfo::getTaskId, taskId)
        );
        List<UserBotInfo> managerBotInfoList = userBotInfoMapper.selectList(
                Wrappers.<UserBotInfo>lambdaQuery()
                        .eq(UserBotInfo::getBotId, larkTaskInfo.getBotId())
                        .eq(UserBotInfo::getUserType, UserBotRoleEnum.USER_BOT_MANAGER.getCode())
        );

        List<String> managerUsernameList = managerBotInfoList.stream().map(UserBotInfo::getUsername).collect(Collectors.toList());

        List<String> onJobUsernameList = employeeInfoMapper.selectList(
                Wrappers.<EmployeeInfo>lambdaQuery()
                        .in(EmployeeInfo::getUsername, managerUsernameList)
                        .eq(EmployeeInfo::getHrStatus, HrApplyStatusEnum.VALID.getCode())
        ).stream().map(EmployeeInfo::getUsername).distinct().collect(Collectors.toList());

        if (onJobUsernameList.isEmpty()) {
            throw new IllegalArgumentException("创建审批失败，机器人管理员为空");
        }
        return String.join(",", onJobUsernameList);
    }
}
