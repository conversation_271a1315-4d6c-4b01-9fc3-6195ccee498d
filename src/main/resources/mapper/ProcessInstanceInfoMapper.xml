<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.ProcessInstanceInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.ProcessInstanceInfo">
        <!--@mbg.generated-->
        <!--@Table process_instance_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="biz_id" jdbcType="BIGINT" property="bizId"/>
        <result column="process_id" jdbcType="VARCHAR" property="processId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="variables" jdbcType="VARCHAR" property="variables"/>
        <result column="version" jdbcType="BIGINT" property="version"/>
        <result column="approval_node" jdbcType="VARCHAR" property="approvalNode"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, create_username, create_time, update_username, update_time, biz_id, process_id,
        `status`, `variables`, version, approval_node
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update process_instance_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="biz_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.bizId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="process_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.processId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`variables` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.variables,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.version,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="approval_node = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.approvalNode,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update process_instance_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="biz_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.bizId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.bizId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="process_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.processId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.processId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.status != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`variables` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.variables != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.variables,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="version = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.version != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.version,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="approval_node = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.approvalNode != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.approvalNode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into process_instance_info
        (create_username, create_time, update_username, update_time, biz_id, process_id,
        `status`, `variables`, version, approval_node)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.createUsername,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateUsername,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.bizId,jdbcType=BIGINT}, #{item.processId,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR},
            #{item.variables,jdbcType=VARCHAR}, #{item.version,jdbcType=BIGINT}, #{item.approvalNode,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.ProcessInstanceInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into process_instance_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            create_username,
            create_time,
            update_username,
            update_time,
            biz_id,
            process_id,
            `status`,
            `variables`,
            version,
            approval_node,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{createUsername,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateUsername,jdbcType=VARCHAR},
            #{updateTime,jdbcType=TIMESTAMP},
            #{bizId,jdbcType=BIGINT},
            #{processId,jdbcType=VARCHAR},
            #{status,jdbcType=VARCHAR},
            #{variables,jdbcType=VARCHAR},
            #{version,jdbcType=BIGINT},
            #{approvalNode,jdbcType=VARCHAR},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            create_username = #{createUsername,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_username = #{updateUsername,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            biz_id = #{bizId,jdbcType=BIGINT},
            process_id = #{processId,jdbcType=VARCHAR},
            `status` = #{status,jdbcType=VARCHAR},
            `variables` = #{variables,jdbcType=VARCHAR},
            version = #{version,jdbcType=BIGINT},
            approval_node = #{approvalNode,jdbcType=VARCHAR},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.ProcessInstanceInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into process_instance_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="createUsername != null">
                create_username,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUsername != null">
                update_username,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="bizId != null">
                biz_id,
            </if>
            <if test="processId != null">
                process_id,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="variables != null">
                `variables`,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="approvalNode != null">
                approval_node,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="createUsername != null">
                #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUsername != null">
                #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="bizId != null">
                #{bizId,jdbcType=BIGINT},
            </if>
            <if test="processId != null">
                #{processId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="variables != null">
                #{variables,jdbcType=VARCHAR},
            </if>
            <if test="version != null">
                #{version,jdbcType=BIGINT},
            </if>
            <if test="approvalNode != null">
                #{approvalNode,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="createUsername != null">
                create_username = #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUsername != null">
                update_username = #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="bizId != null">
                biz_id = #{bizId,jdbcType=BIGINT},
            </if>
            <if test="processId != null">
                process_id = #{processId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=VARCHAR},
            </if>
            <if test="variables != null">
                `variables` = #{variables,jdbcType=VARCHAR},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=BIGINT},
            </if>
            <if test="approvalNode != null">
                approval_node = #{approvalNode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
</mapper>