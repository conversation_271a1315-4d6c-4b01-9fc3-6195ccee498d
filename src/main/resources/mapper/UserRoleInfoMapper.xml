<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.UserRoleInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.UserRoleInfo">
        <!--@mbg.generated-->
        <!--@Table user_role_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="role_type" jdbcType="TINYINT" property="roleType"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, role_type, username, `name`, update_username, create_username, create_time, update_time
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update user_role_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="role_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.roleType,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.username,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update user_role_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="role_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.roleType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.roleType,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.username != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.username,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.name != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into user_role_info
        (role_type, username, `name`, update_username, create_username, create_time, update_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.roleType,jdbcType=TINYINT}, #{item.username,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR},
            #{item.updateUsername,jdbcType=VARCHAR}, #{item.createUsername,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT})
        </foreach>
    </insert>

    <select id="selectUserRoleList" resultType="com.mioffice.ums.admin.entity.dto.UserRoleDTO">
        select name, username, group_concat(role_type) as roleTypeStr, max(update_time) as maxTime from user_role_info
        <where>
            1 = 1
            <if test="roleTypeList != null and roleTypeList.size() != 0 ">
                and role_type in
                <foreach collection="roleTypeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="keyWord != null and keyWord != ''">
                and (name like #{keyWord} or username like #{keyWord})
            </if>
            and role_type != 0
        </where>
        group by username
        order by maxTime desc
    </select>

    <select id="selectUserRoleCount" resultType="com.mioffice.ums.admin.entity.dto.RoleDTO">
        select role_type as roleType, count(*) as count
        from user_role_info
        group by role_type
    </select>

    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.UserRoleInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into user_role_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            role_type,
            username,
            `name`,
            update_username,
            create_username,
            create_time,
            update_time,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{roleType,jdbcType=TINYINT},
            #{username,jdbcType=VARCHAR},
            #{name,jdbcType=VARCHAR},
            #{updateUsername,jdbcType=VARCHAR},
            #{createUsername,jdbcType=VARCHAR},
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            role_type = #{roleType,jdbcType=TINYINT},
            username = #{username,jdbcType=VARCHAR},
            `name` = #{name,jdbcType=VARCHAR},
            update_username = #{updateUsername,jdbcType=VARCHAR},
            create_username = #{createUsername,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.UserRoleInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into user_role_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="roleType != null">
                role_type,
            </if>
            <if test="username != null">
                username,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="updateUsername != null">
                update_username,
            </if>
            <if test="createUsername != null">
                create_username,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="roleType != null">
                #{roleType,jdbcType=TINYINT},
            </if>
            <if test="username != null">
                #{username,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="updateUsername != null">
                #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="createUsername != null">
                #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="roleType != null">
                role_type = #{roleType,jdbcType=TINYINT},
            </if>
            <if test="username != null">
                username = #{username,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="updateUsername != null">
                update_username = #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="createUsername != null">
                create_username = #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
</mapper>