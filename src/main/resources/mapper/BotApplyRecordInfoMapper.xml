<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.BotApplyRecordInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.BotApplyRecordInfo">
        <!--@mbg.generated-->
        <!--@Table bot_apply_record_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="bot_id" jdbcType="BIGINT" property="botId"/>
        <result column="apply_status" jdbcType="TINYINT" property="applyStatus"/>
        <result column="apply_desc" jdbcType="LONGVARCHAR" property="applyDesc"/>
        <result column="approval_username" jdbcType="VARCHAR" property="approvalUsername"/>
        <result column="approval_name" jdbcType="VARCHAR" property="approvalName"/>
        <result column="approval_desc" jdbcType="LONGVARCHAR" property="approvalDesc"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, bot_id, apply_status, apply_desc, approval_username, approval_name, approval_desc,
        update_username, create_username, create_time, update_time
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update bot_apply_record_info

        <trim prefix="set" suffixOverrides=",">
            <trim prefix="bot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="apply_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.applyStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="apply_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.applyDesc,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="approval_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.approvalUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="approval_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.approvalName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="approval_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.approvalDesc,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>

        where id in

        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update bot_apply_record_info

        <trim prefix="set" suffixOverrides=",">
            <trim prefix="bot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="apply_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.applyStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.applyStatus,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="apply_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.applyDesc != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.applyDesc,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="approval_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.approvalUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.approvalUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="approval_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.approvalName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.approvalName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="approval_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.approvalDesc != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.approvalDesc,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>

        where id in

        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into bot_apply_record_info
        (bot_id, apply_status, apply_desc, approval_username, approval_name, approval_desc,
        update_username, create_username, create_time, update_time)
        values

        <foreach collection="list" item="item" separator=",">
            (#{item.botId,jdbcType=BIGINT}, #{item.applyStatus,jdbcType=TINYINT},
            #{item.applyDesc,jdbcType=LONGVARCHAR},
            #{item.approvalUsername,jdbcType=VARCHAR}, #{item.approvalName,jdbcType=VARCHAR},
            #{item.approvalDesc,jdbcType=LONGVARCHAR}, #{item.updateUsername,jdbcType=VARCHAR},
            #{item.createUsername,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=BIGINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.BotApplyRecordInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into bot_apply_record_info

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,

            </if>

            bot_id,
            apply_status,
            apply_desc,
            approval_username,
            approval_name,
            approval_desc,
            update_username,
            create_username,
            create_time,
            update_time,
        </trim>

        values

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},

            </if>

            #{botId,jdbcType=BIGINT},
            #{applyStatus,jdbcType=TINYINT},
            #{applyDesc,jdbcType=LONGVARCHAR},
            #{approvalUsername,jdbcType=VARCHAR},
            #{approvalName,jdbcType=VARCHAR},
            #{approvalDesc,jdbcType=LONGVARCHAR},
            #{updateUsername,jdbcType=VARCHAR},
            #{createUsername,jdbcType=VARCHAR},
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
        </trim>

        on duplicate key update

        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},

            </if>

            bot_id = #{botId,jdbcType=BIGINT},
            apply_status = #{applyStatus,jdbcType=TINYINT},
            apply_desc = #{applyDesc,jdbcType=LONGVARCHAR},
            approval_username = #{approvalUsername,jdbcType=VARCHAR},
            approval_name = #{approvalName,jdbcType=VARCHAR},
            approval_desc = #{approvalDesc,jdbcType=LONGVARCHAR},
            update_username = #{updateUsername,jdbcType=VARCHAR},
            create_username = #{createUsername,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.BotApplyRecordInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into bot_apply_record_info

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,

            </if>
            <if test="botId != null">

                bot_id,

            </if>
            <if test="applyStatus != null">

                apply_status,

            </if>
            <if test="applyDesc != null">

                apply_desc,

            </if>
            <if test="approvalUsername != null">

                approval_username,

            </if>
            <if test="approvalName != null">

                approval_name,

            </if>
            <if test="approvalDesc != null">

                approval_desc,

            </if>
            <if test="updateUsername != null">

                update_username,

            </if>
            <if test="createUsername != null">

                create_username,

            </if>
            <if test="createTime != null">

                create_time,

            </if>
            <if test="updateTime != null">

                update_time,
            </if>
        </trim>

        values

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},

            </if>
            <if test="botId != null">

                #{botId,jdbcType=BIGINT},

            </if>
            <if test="applyStatus != null">

                #{applyStatus,jdbcType=TINYINT},

            </if>
            <if test="applyDesc != null">

                #{applyDesc,jdbcType=LONGVARCHAR},

            </if>
            <if test="approvalUsername != null">

                #{approvalUsername,jdbcType=VARCHAR},

            </if>
            <if test="approvalName != null">

                #{approvalName,jdbcType=VARCHAR},

            </if>
            <if test="approvalDesc != null">

                #{approvalDesc,jdbcType=LONGVARCHAR},

            </if>
            <if test="updateUsername != null">

                #{updateUsername,jdbcType=VARCHAR},

            </if>
            <if test="createUsername != null">

                #{createUsername,jdbcType=VARCHAR},

            </if>
            <if test="createTime != null">

                #{createTime,jdbcType=BIGINT},

            </if>
            <if test="updateTime != null">

                #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>

        on duplicate key update

        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},

            </if>
            <if test="botId != null">

                bot_id = #{botId,jdbcType=BIGINT},

            </if>
            <if test="applyStatus != null">

                apply_status = #{applyStatus,jdbcType=TINYINT},

            </if>
            <if test="applyDesc != null">

                apply_desc = #{applyDesc,jdbcType=LONGVARCHAR},

            </if>
            <if test="approvalUsername != null">

                approval_username = #{approvalUsername,jdbcType=VARCHAR},

            </if>
            <if test="approvalName != null">

                approval_name = #{approvalName,jdbcType=VARCHAR},

            </if>
            <if test="approvalDesc != null">

                approval_desc = #{approvalDesc,jdbcType=LONGVARCHAR},

            </if>
            <if test="updateUsername != null">

                update_username = #{updateUsername,jdbcType=VARCHAR},

            </if>
            <if test="createUsername != null">

                create_username = #{createUsername,jdbcType=VARCHAR},

            </if>
            <if test="createTime != null">

                create_time = #{createTime,jdbcType=BIGINT},

            </if>
            <if test="updateTime != null">

                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <resultMap id="applyMyMap" type="com.mioffice.ums.admin.entity.dto.BotApplyListDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="bot_id" jdbcType="BIGINT" property="botId"/>
        <result column="bot_name" jdbcType="VARCHAR" property="botName"/>
        <result column="apply_status" jdbcType="TINYINT" property="applyStatus"/>
        <result column="apply_desc" jdbcType="VARCHAR" property="applyDesc"/>
        <result column="bot_type" jdbcType="TINYINT" property="botType"/>

        <collection ofType="map" property="manageUsernameList">
            <result column="username" jdbcType="VARCHAR" property="username"/>
            <result column="name" jdbcType="VARCHAR" property="name"/>
        </collection>
        <collection ofType="map" property="useUsernameList">
            <result column="apply_user_username" jdbcType="VARCHAR" property="username"/>
            <result column="apply_user_name" jdbcType="VARCHAR" property="name"/>
        </collection>
        <collection ofType="java.lang.String" property="deptIds">
            <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        </collection>
        <collection ofType="java.lang.String" property="deptCnNameList">
            <result column="cn_name" jdbcType="VARCHAR" property="cnName"/>
        </collection>
    </resultMap>
    <select id="selectApplyPageRecords" resultMap="applyMyMap">

        select
        bari.id as id,
        bari.bot_id as bot_id,
        employee_info.name as name,
        employee_info.username as username,
        apply_user_username,
        apply_user_name,
        bot_name,
        bot_type,
        apply_status,
        apply_desc,
        department_info.cn_name as cn_name,
        department_info.dept_id as dept_id
        from
        (
        select *
        from bot_apply_record_info
        where bot_apply_record_info.id in

        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>

        ) bari
        join
        bot_info
        on bari.bot_id = bot_info.id
        join
        dept_bot_info
        on dept_bot_info.bot_id = bot_info.id
        join
        user_bot_info
        on user_bot_info.bot_id = bot_info.id
        join
        department_info
        on dept_bot_info.dept_id = department_info.dept_id
        join
        employee_info
        on employee_info.username = user_bot_info.username
        join
        bot_apply_apply_user_info
        on bot_apply_apply_user_info.bot_apply_record_id = bari.id
        where
        user_type = 2
        order by bari.id desc

    </select>

    <resultMap id="approvalMap" type="com.mioffice.ums.admin.entity.dto.BotApplyApprovalPageDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="bot_id" jdbcType="BIGINT" property="botId"/>
        <result column="bot_name" jdbcType="VARCHAR" property="botName"/>
        <result column="apply_desc" jdbcType="VARCHAR" property="applyDesc"/>
        <result column="bot_type" jdbcType="TINYINT" property="botType"/>
        <collection ofType="map" column="id" select="selectApplyUser" property="useUsernameList">
            <result column="username" jdbcType="VARCHAR" property="username"/>
            <result column="name" jdbcType="VARCHAR" property="name"/>
        </collection>
    </resultMap>
    <select id="selectApplyUser" parameterType="Long" resultType="map">

        select apply_user_username as username,
               apply_user_name     as name
        from bot_apply_apply_user_info
        where bot_apply_record_id = #{id}

    </select>

    <select id="selectApproval" resultMap="approvalMap">

        select bot_apply_record_info.id,
               bot_id,
               bot_name,
               bot_type,
               apply_desc

        from (bot_apply_record_info
            join
            bot_info
            on
                bot_apply_record_info.bot_id = bot_info.id)
        where apply_status = 1
          and bot_id in (select bot_id
                         from user_bot_info
                         where username = #{username,jdbcType=VARCHAR}
                           and user_type = 2)
        order by bot_apply_record_info.id desc

    </select>

    <resultMap id="botApplyDetail" type="com.mioffice.ums.admin.entity.dto.BotApplyDetailDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="bot_id" jdbcType="BIGINT" property="botId"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="bot_name" jdbcType="VARCHAR" property="botName"/>
        <result column="apply_status" jdbcType="TINYINT" property="applyStatus"/>
        <result column="apply_desc" jdbcType="VARCHAR" property="applyDesc"/>
        <result column="approval_username" jdbcType="VARCHAR" property="approvalResult.approvalUsername"/>
        <result column="approval_name" jdbcType="VARCHAR" property="approvalResult.approvalName"/>
        <result column="approval_desc" jdbcType="VARCHAR" property="approvalResult.approvalDesc"/>

        <collection ofType="map" property="useUsernameList">
            <result column="apply_user_username" jdbcType="VARCHAR" property="username"/>
            <result column="apply_user_name" jdbcType="VARCHAR" property="name"/>
        </collection>
        <collection ofType="java.lang.String" property="deptIds">
            <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        </collection>
        <collection ofType="java.lang.String" property="deptCnNameList">
            <result column="cn_name" jdbcType="VARCHAR" property="cnName"/>
        </collection>
    </resultMap>
    <select id="selectDetail" resultMap="botApplyDetail">

        select bari.id                 as id,
               bari.create_time        as create_time,
               bot_info.id             as bot_id,
               apply_user_username,
               apply_user_name,
               bot_name,
               apply_status,
               apply_desc,
               approval_username,
               approval_name,
               approval_desc,
               department_info.cn_name as cn_name,
               department_info.dept_id as dept_id
        from (select *
              from bot_apply_record_info
              where bot_apply_record_info.id = #{id,jdbcType=BIGINT}) bari
                 join
             bot_info
             on bari.bot_id = bot_info.id
                 join
             dept_bot_info
             on dept_bot_info.bot_id = bot_info.id
                 join
             department_info
             on dept_bot_info.dept_id = department_info.dept_id
                 join
             employee_info
             on employee_info.username = bari.create_username
                 join
             bot_apply_apply_user_info
             on bot_apply_apply_user_info.bot_apply_record_id = bari.id

    </select>
</mapper>