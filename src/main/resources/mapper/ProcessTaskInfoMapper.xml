<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.ProcessTaskInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.ProcessTaskInfo">
        <!--@mbg.generated-->
        <!--@Table process_task_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="process_id" jdbcType="VARCHAR" property="processId"/>
        <result column="instance_id" jdbcType="VARCHAR" property="instanceId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="task_id" jdbcType="VARCHAR" property="taskId"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="approver_id" jdbcType="VARCHAR" property="approverId"/>
        <result column="approver_name" jdbcType="VARCHAR" property="approverName"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="approve_time" jdbcType="TIMESTAMP" property="approveTime"/>
        <result column="approve_duration" jdbcType="BIGINT" property="approveDuration"/>
        <result column="approve_result_value" jdbcType="INTEGER" property="approveResultValue"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, create_username, create_time, update_username, update_time, process_id, instance_id,
        `status`, task_id, task_name, approver_id, approver_name, remark, approve_time, approve_duration,
        approve_result_value
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update process_task_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="process_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.processId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="instance_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.instanceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="task_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="approver_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.approverId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="approver_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.approverName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="approve_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.approveTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="approve_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.approveDuration,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="approve_result_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.approveResultValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update process_task_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="process_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.processId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.processId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="instance_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.instanceId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.instanceId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.status != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="approver_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.approverId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.approverId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="approver_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.approverName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.approverName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.remark != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="approve_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.approveTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.approveTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="approve_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.approveDuration != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.approveDuration,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="approve_result_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.approveResultValue != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.approveResultValue,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into process_task_info
        (create_username, create_time, update_username, update_time, process_id, instance_id,
        `status`, task_id, task_name, approver_id, approver_name, remark, approve_time,
        approve_duration, approve_result_value)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.createUsername,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateUsername,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.processId,jdbcType=VARCHAR}, #{item.instanceId,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR},
            #{item.taskId,jdbcType=VARCHAR}, #{item.taskName,jdbcType=VARCHAR}, #{item.approverId,jdbcType=VARCHAR},
            #{item.approverName,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR},
            #{item.approveTime,jdbcType=TIMESTAMP},
            #{item.approveDuration,jdbcType=BIGINT}, #{item.approveResultValue,jdbcType=INTEGER}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.ProcessTaskInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into process_task_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            create_username,
            create_time,
            update_username,
            update_time,
            process_id,
            instance_id,
            `status`,
            task_id,
            task_name,
            approver_id,
            approver_name,
            remark,
            approve_time,
            approve_duration,
            approve_result_value,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{createUsername,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateUsername,jdbcType=VARCHAR},
            #{updateTime,jdbcType=TIMESTAMP},
            #{processId,jdbcType=VARCHAR},
            #{instanceId,jdbcType=VARCHAR},
            #{status,jdbcType=VARCHAR},
            #{taskId,jdbcType=VARCHAR},
            #{taskName,jdbcType=VARCHAR},
            #{approverId,jdbcType=VARCHAR},
            #{approverName,jdbcType=VARCHAR},
            #{remark,jdbcType=VARCHAR},
            #{approveTime,jdbcType=TIMESTAMP},
            #{approveDuration,jdbcType=BIGINT},
            #{approveResultValue,jdbcType=INTEGER},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            create_username = #{createUsername,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_username = #{updateUsername,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            process_id = #{processId,jdbcType=VARCHAR},
            instance_id = #{instanceId,jdbcType=VARCHAR},
            `status` = #{status,jdbcType=VARCHAR},
            task_id = #{taskId,jdbcType=VARCHAR},
            task_name = #{taskName,jdbcType=VARCHAR},
            approver_id = #{approverId,jdbcType=VARCHAR},
            approver_name = #{approverName,jdbcType=VARCHAR},
            remark = #{remark,jdbcType=VARCHAR},
            approve_time = #{approveTime,jdbcType=TIMESTAMP},
            approve_duration = #{approveDuration,jdbcType=BIGINT},
            approve_result_value = #{approveResultValue,jdbcType=INTEGER},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.ProcessTaskInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into process_task_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="createUsername != null">
                create_username,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUsername != null">
                update_username,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="processId != null">
                process_id,
            </if>
            <if test="instanceId != null">
                instance_id,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="taskName != null">
                task_name,
            </if>
            <if test="approverId != null">
                approver_id,
            </if>
            <if test="approverName != null">
                approver_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="approveTime != null">
                approve_time,
            </if>
            <if test="approveDuration != null">
                approve_duration,
            </if>
            <if test="approveResultValue != null">
                approve_result_value,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="createUsername != null">
                #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUsername != null">
                #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="processId != null">
                #{processId,jdbcType=VARCHAR},
            </if>
            <if test="instanceId != null">
                #{instanceId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="taskName != null">
                #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="approverId != null">
                #{approverId,jdbcType=VARCHAR},
            </if>
            <if test="approverName != null">
                #{approverName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="approveTime != null">
                #{approveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="approveDuration != null">
                #{approveDuration,jdbcType=BIGINT},
            </if>
            <if test="approveResultValue != null">
                #{approveResultValue,jdbcType=INTEGER},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="createUsername != null">
                create_username = #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUsername != null">
                update_username = #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="processId != null">
                process_id = #{processId,jdbcType=VARCHAR},
            </if>
            <if test="instanceId != null">
                instance_id = #{instanceId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="taskName != null">
                task_name = #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="approverId != null">
                approver_id = #{approverId,jdbcType=VARCHAR},
            </if>
            <if test="approverName != null">
                approver_name = #{approverName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="approveTime != null">
                approve_time = #{approveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="approveDuration != null">
                approve_duration = #{approveDuration,jdbcType=BIGINT},
            </if>
            <if test="approveResultValue != null">
                approve_result_value = #{approveResultValue,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
</mapper>