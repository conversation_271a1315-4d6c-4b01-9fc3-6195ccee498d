<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.DeptBotInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.DeptBotInfo">
        <!--@mbg.generated-->
        <!--@Table dept_bot_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="bot_id" jdbcType="BIGINT" property="botId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, create_username, create_time, update_username, update_time, dept_id, bot_id
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update dept_bot_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botId,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update dept_bot_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deptId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into dept_bot_info
        (create_username, create_time, update_username, update_time, dept_id, bot_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.createUsername,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT},
            #{item.updateUsername,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=BIGINT}, #{item.deptId,jdbcType=VARCHAR}, #{item.botId,jdbcType=BIGINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.DeptBotInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into dept_bot_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            create_username,
            create_time,
            update_username,
            update_time,
            dept_id,
            bot_id,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{createUsername,jdbcType=VARCHAR},
            #{createTime,jdbcType=BIGINT},
            #{updateUsername,jdbcType=VARCHAR},
            #{updateTime,jdbcType=BIGINT},
            #{deptId,jdbcType=VARCHAR},
            #{botId,jdbcType=BIGINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            create_username = #{createUsername,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=BIGINT},
            update_username = #{updateUsername,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=BIGINT},
            dept_id = #{deptId,jdbcType=VARCHAR},
            bot_id = #{botId,jdbcType=BIGINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.DeptBotInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into dept_bot_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="createUsername != null">
                create_username,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUsername != null">
                update_username,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="botId != null">
                bot_id,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="createUsername != null">
                #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateUsername != null">
                #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="botId != null">
                #{botId,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="createUsername != null">
                create_username = #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateUsername != null">
                update_username = #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="botId != null">
                bot_id = #{botId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <select id="getDeptBotRank" resultType="com.mioffice.ums.admin.entity.vo.BotDashboardVO$DeptBotRank">
        SELECT d2.cn_name as deptName, count(distinct d1.bot_id) as botCount, count(distinct t1.id) as taskCount
        FROM `dept_bot_info` d1
        LEFT JOIN `department_info` d2 on d1.dept_id = d2.dept_id
        LEFT JOIN `lark_task_info` l1 on d1.bot_id = l1.bot_id
        LEFT JOIN
        (SELECT id FROM `task_info`
        where task_status in
        <foreach collection="statusList" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=TINYINT}
        </foreach>
        ) t1 on t1.id = l1.task_id
        GROUP BY d1.dept_id
    </select>

</mapper>