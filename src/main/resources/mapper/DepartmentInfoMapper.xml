<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.DepartmentInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.DepartmentInfo">
        <!--@mbg.generated-->
        <!--@Table department_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="cn_name" jdbcType="VARCHAR" property="cnName"/>
        <result column="en_name" jdbcType="VARCHAR" property="enName"/>
        <result column="tree_node_num" jdbcType="VARCHAR" property="treeNodeNum"/>
        <result column="tree_node_num_end" jdbcType="VARCHAR" property="treeNodeNumEnd"/>
        <result column="parent_node_id" jdbcType="VARCHAR" property="parentNodeId"/>
        <result column="tree_level_num" jdbcType="TINYINT" property="treeLevelNum"/>
        <result column="complete_dept_name" jdbcType="VARCHAR" property="completeDeptName"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dept_id, `status`, cn_name, en_name, tree_node_num, tree_node_num_end, parent_node_id,
        tree_level_num, complete_dept_name
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update department_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cn_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.cnName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="en_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.enName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tree_node_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.treeNodeNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tree_node_num_end = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.treeNodeNumEnd,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="parent_node_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.parentNodeId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tree_level_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.treeLevelNum,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="complete_dept_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.completeDeptName,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update department_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deptId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.status != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="cn_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.cnName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.cnName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="en_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.enName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.enName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tree_node_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.treeNodeNum != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.treeNodeNum,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tree_node_num_end = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.treeNodeNumEnd != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.treeNodeNumEnd,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="parent_node_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.parentNodeId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.parentNodeId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tree_level_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.treeLevelNum != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.treeLevelNum,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="complete_dept_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.completeDeptName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.completeDeptName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into department_info
        (dept_id, `status`, cn_name, en_name, tree_node_num, tree_node_num_end, parent_node_id,
        tree_level_num, complete_dept_name)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.deptId,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR}, #{item.cnName,jdbcType=VARCHAR},
            #{item.enName,jdbcType=VARCHAR}, #{item.treeNodeNum,jdbcType=VARCHAR},
            #{item.treeNodeNumEnd,jdbcType=VARCHAR},
            #{item.parentNodeId,jdbcType=VARCHAR}, #{item.treeLevelNum,jdbcType=TINYINT},
            #{item.completeDeptName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.DepartmentInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into department_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            dept_id,
            `status`,
            cn_name,
            en_name,
            tree_node_num,
            tree_node_num_end,
            parent_node_id,
            tree_level_num,
            complete_dept_name,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{deptId,jdbcType=VARCHAR},
            #{status,jdbcType=VARCHAR},
            #{cnName,jdbcType=VARCHAR},
            #{enName,jdbcType=VARCHAR},
            #{treeNodeNum,jdbcType=VARCHAR},
            #{treeNodeNumEnd,jdbcType=VARCHAR},
            #{parentNodeId,jdbcType=VARCHAR},
            #{treeLevelNum,jdbcType=TINYINT},
            #{completeDeptName,jdbcType=VARCHAR},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            dept_id = #{deptId,jdbcType=VARCHAR},
            `status` = #{status,jdbcType=VARCHAR},
            cn_name = #{cnName,jdbcType=VARCHAR},
            en_name = #{enName,jdbcType=VARCHAR},
            tree_node_num = #{treeNodeNum,jdbcType=VARCHAR},
            tree_node_num_end = #{treeNodeNumEnd,jdbcType=VARCHAR},
            parent_node_id = #{parentNodeId,jdbcType=VARCHAR},
            tree_level_num = #{treeLevelNum,jdbcType=TINYINT},
            complete_dept_name = #{completeDeptName,jdbcType=VARCHAR},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.DepartmentInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into department_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="cnName != null">
                cn_name,
            </if>
            <if test="enName != null">
                en_name,
            </if>
            <if test="treeNodeNum != null">
                tree_node_num,
            </if>
            <if test="treeNodeNumEnd != null">
                tree_node_num_end,
            </if>
            <if test="parentNodeId != null">
                parent_node_id,
            </if>
            <if test="treeLevelNum != null">
                tree_level_num,
            </if>
            <if test="completeDeptName != null">
                complete_dept_name,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="cnName != null">
                #{cnName,jdbcType=VARCHAR},
            </if>
            <if test="enName != null">
                #{enName,jdbcType=VARCHAR},
            </if>
            <if test="treeNodeNum != null">
                #{treeNodeNum,jdbcType=VARCHAR},
            </if>
            <if test="treeNodeNumEnd != null">
                #{treeNodeNumEnd,jdbcType=VARCHAR},
            </if>
            <if test="parentNodeId != null">
                #{parentNodeId,jdbcType=VARCHAR},
            </if>
            <if test="treeLevelNum != null">
                #{treeLevelNum,jdbcType=TINYINT},
            </if>
            <if test="completeDeptName != null">
                #{completeDeptName,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=VARCHAR},
            </if>
            <if test="cnName != null">
                cn_name = #{cnName,jdbcType=VARCHAR},
            </if>
            <if test="enName != null">
                en_name = #{enName,jdbcType=VARCHAR},
            </if>
            <if test="treeNodeNum != null">
                tree_node_num = #{treeNodeNum,jdbcType=VARCHAR},
            </if>
            <if test="treeNodeNumEnd != null">
                tree_node_num_end = #{treeNodeNumEnd,jdbcType=VARCHAR},
            </if>
            <if test="parentNodeId != null">
                parent_node_id = #{parentNodeId,jdbcType=VARCHAR},
            </if>
            <if test="treeLevelNum != null">
                tree_level_num = #{treeLevelNum,jdbcType=TINYINT},
            </if>
            <if test="completeDeptName != null">
                complete_dept_name = #{completeDeptName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getByDeptIds" resultType="com.mioffice.ums.admin.entity.vo.DeptIdNameVO">
        select dept_id, cn_name as dept_name from department_info where dept_id in
        <foreach collection="deptIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getCompleteNameByDeptIds" resultType="com.mioffice.ums.admin.entity.vo.DeptIdNameVO">
        select dept_id, complete_dept_name as dept_name from department_info where dept_id in
        <foreach collection="deptIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>