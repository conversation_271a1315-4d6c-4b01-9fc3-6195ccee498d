<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.EmailTaskInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.EmailTaskInfo">
        <!--@mbg.generated-->
        <!--@Table email_task_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="sender" jdbcType="VARCHAR" property="sender"/>
        <result column="template_id" jdbcType="VARCHAR" property="templateId"/>
        <result column="email_type" jdbcType="TINYINT" property="emailType"/>
        <result column="html_body" jdbcType="LONGVARCHAR" property="htmlBody"/>
        <result column="button_extra" jdbcType="LONGVARCHAR" property="buttonExtra"/>
        <result column="button" jdbcType="TINYINT" property="button"/>
        <result column="bot_id" jdbcType="BIGINT" property="botId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, create_time, update_time, task_id, sender, template_id, email_type, html_body,
        button_extra, button, bot_id
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update email_task_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="sender = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.sender,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="template_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.templateId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="email_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.emailType,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="html_body = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.htmlBody,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="button_extra = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.buttonExtra,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="button = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.button,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="bot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botId,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update email_task_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sender = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sender != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.sender,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="template_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.templateId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.templateId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="email_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.emailType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.emailType,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="html_body = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.htmlBody != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.htmlBody,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="button_extra = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.buttonExtra != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.buttonExtra,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="button = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.button != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.button,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into email_task_info
        (create_time, update_time, task_id, sender, template_id, email_type, html_body, button_extra,
        button, bot_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, #{item.taskId,jdbcType=BIGINT},
            #{item.sender,jdbcType=VARCHAR}, #{item.templateId,jdbcType=VARCHAR}, #{item.emailType,jdbcType=TINYINT},
            #{item.htmlBody,jdbcType=LONGVARCHAR}, #{item.buttonExtra,jdbcType=LONGVARCHAR},
            #{item.button,jdbcType=TINYINT}, #{item.botId,jdbcType=BIGINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.EmailTaskInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into email_task_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            create_time,
            update_time,
            task_id,
            sender,
            template_id,
            email_type,
            html_body,
            button_extra,
            button,
            bot_id,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
            #{taskId,jdbcType=BIGINT},
            #{sender,jdbcType=VARCHAR},
            #{templateId,jdbcType=VARCHAR},
            #{emailType,jdbcType=TINYINT},
            #{htmlBody,jdbcType=LONGVARCHAR},
            #{buttonExtra,jdbcType=LONGVARCHAR},
            #{button,jdbcType=TINYINT},
            #{botId,jdbcType=BIGINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
            task_id = #{taskId,jdbcType=BIGINT},
            sender = #{sender,jdbcType=VARCHAR},
            template_id = #{templateId,jdbcType=VARCHAR},
            email_type = #{emailType,jdbcType=TINYINT},
            html_body = #{htmlBody,jdbcType=LONGVARCHAR},
            button_extra = #{buttonExtra,jdbcType=LONGVARCHAR},
            button = #{button,jdbcType=TINYINT},
            bot_id = #{botId,jdbcType=BIGINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.EmailTaskInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into email_task_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="sender != null">
                sender,
            </if>
            <if test="templateId != null">
                template_id,
            </if>
            <if test="emailType != null">
                email_type,
            </if>
            <if test="htmlBody != null">
                html_body,
            </if>
            <if test="buttonExtra != null">
                button_extra,
            </if>
            <if test="button != null">
                button,
            </if>
            <if test="botId != null">
                bot_id,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="sender != null">
                #{sender,jdbcType=VARCHAR},
            </if>
            <if test="templateId != null">
                #{templateId,jdbcType=VARCHAR},
            </if>
            <if test="emailType != null">
                #{emailType,jdbcType=TINYINT},
            </if>
            <if test="htmlBody != null">
                #{htmlBody,jdbcType=LONGVARCHAR},
            </if>
            <if test="buttonExtra != null">
                #{buttonExtra,jdbcType=LONGVARCHAR},
            </if>
            <if test="button != null">
                #{button,jdbcType=TINYINT},
            </if>
            <if test="botId != null">
                #{botId,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=BIGINT},
            </if>
            <if test="sender != null">
                sender = #{sender,jdbcType=VARCHAR},
            </if>
            <if test="templateId != null">
                template_id = #{templateId,jdbcType=VARCHAR},
            </if>
            <if test="emailType != null">
                email_type = #{emailType,jdbcType=TINYINT},
            </if>
            <if test="htmlBody != null">
                html_body = #{htmlBody,jdbcType=LONGVARCHAR},
            </if>
            <if test="buttonExtra != null">
                button_extra = #{buttonExtra,jdbcType=LONGVARCHAR},
            </if>
            <if test="button != null">
                button = #{button,jdbcType=TINYINT},
            </if>
            <if test="botId != null">
                bot_id = #{botId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <select id="selectEmailPushCount" resultType="com.mioffice.ums.admin.entity.bo.PushCountBo">
        select bot_id as botId, count(*) as pushCount from email_task_info where 1 = 1
        <if test="taskIdList != null and taskIdList.size() != 0">
            and task_id in
            <foreach close=")" collection="taskIdList" item="item" open="(" separator=", ">
                #{item}
            </foreach>
        </if>
        <if test="botIdList != null and botIdList.size() != 0">
            and bot_id in
            <foreach close=")" collection="botIdList" item="item" open="(" separator=", ">
                #{item}
            </foreach>
        </if>
        group by bot_id
    </select>
</mapper>