<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.UserBotInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.UserBotInfo">
        <!--@mbg.generated-->
        <!--@Table user_bot_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="bot_id" jdbcType="BIGINT" property="botId"/>
        <result column="user_type" jdbcType="TINYINT" property="userType"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, create_username, create_time, update_username, update_time, username, bot_id,
        user_type
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update user_bot_info

        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.username,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="user_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.userType,jdbcType=TINYINT}
                </foreach>
            </trim>
        </trim>

        where id in

        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update user_bot_info

        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.username != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.username,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="user_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.userType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.userType,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
        </trim>

        where id in

        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into user_bot_info
        (create_username, create_time, update_username, update_time, username, bot_id, user_type
        )
        values

        <foreach collection="list" item="item" separator=",">
            (#{item.createUsername,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT},
            #{item.updateUsername,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=BIGINT}, #{item.username,jdbcType=VARCHAR}, #{item.botId,jdbcType=BIGINT},
            #{item.userType,jdbcType=TINYINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.UserBotInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into user_bot_info

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,

            </if>

            create_username,
            create_time,
            update_username,
            update_time,
            username,
            bot_id,
            user_type,
        </trim>

        values

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},

            </if>

            #{createUsername,jdbcType=VARCHAR},
            #{createTime,jdbcType=BIGINT},
            #{updateUsername,jdbcType=VARCHAR},
            #{updateTime,jdbcType=BIGINT},
            #{username,jdbcType=VARCHAR},
            #{botId,jdbcType=BIGINT},
            #{userType,jdbcType=TINYINT},
        </trim>

        on duplicate key update

        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},

            </if>

            create_username = #{createUsername,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=BIGINT},
            update_username = #{updateUsername,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=BIGINT},
            username = #{username,jdbcType=VARCHAR},
            bot_id = #{botId,jdbcType=BIGINT},
            user_type = #{userType,jdbcType=TINYINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.UserBotInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into user_bot_info

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,

            </if>
            <if test="createUsername != null">

                create_username,

            </if>
            <if test="createTime != null">

                create_time,

            </if>
            <if test="updateUsername != null">

                update_username,

            </if>
            <if test="updateTime != null">

                update_time,

            </if>
            <if test="username != null">

                username,

            </if>
            <if test="botId != null">

                bot_id,

            </if>
            <if test="userType != null">

                user_type,
            </if>
        </trim>

        values

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},

            </if>
            <if test="createUsername != null">

                #{createUsername,jdbcType=VARCHAR},

            </if>
            <if test="createTime != null">

                #{createTime,jdbcType=BIGINT},

            </if>
            <if test="updateUsername != null">

                #{updateUsername,jdbcType=VARCHAR},

            </if>
            <if test="updateTime != null">

                #{updateTime,jdbcType=BIGINT},

            </if>
            <if test="username != null">

                #{username,jdbcType=VARCHAR},

            </if>
            <if test="botId != null">

                #{botId,jdbcType=BIGINT},

            </if>
            <if test="userType != null">

                #{userType,jdbcType=TINYINT},
            </if>
        </trim>

        on duplicate key update

        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},

            </if>
            <if test="createUsername != null">

                create_username = #{createUsername,jdbcType=VARCHAR},

            </if>
            <if test="createTime != null">

                create_time = #{createTime,jdbcType=BIGINT},

            </if>
            <if test="updateUsername != null">

                update_username = #{updateUsername,jdbcType=VARCHAR},

            </if>
            <if test="updateTime != null">

                update_time = #{updateTime,jdbcType=BIGINT},

            </if>
            <if test="username != null">

                username = #{username,jdbcType=VARCHAR},

            </if>
            <if test="botId != null">

                bot_id = #{botId,jdbcType=BIGINT},

            </if>
            <if test="userType != null">

                user_type = #{userType,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <select id="selectManageUserList" resultType="com.mioffice.ums.admin.entity.bo.UserInfoBO">
        select employee_info.username, name, bot_id as botId
        from user_bot_info
        join employee_info
        on employee_info.username = user_bot_info.username
        where user_type = 2
        <if test="botIdList != null and botIdList.size() != 0">
            and bot_id in
            <foreach close=")" collection="botIdList" item="item" open="(" separator=", ">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>