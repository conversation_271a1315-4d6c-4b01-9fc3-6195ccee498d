<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.MessageJobExecuteRecordMapper">

    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.MessageJobExecuteRecord">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="messageJobId" column="message_job_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="BIGINT"/>
        <result property="extraId" column="extra_id" jdbcType="VARCHAR"/>
        <result property="returnCode" column="return_code" jdbcType="INTEGER"/>
        <result property="errorMsg" column="error_msg" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,message_job_id,create_time,
        update_time,extra_id,return_code,
        error_msg
    </sql>
</mapper>
