<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.OperationInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.OperationInfo">
        <!--@mbg.generated-->
        <!--@Table operation_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="operation_username" jdbcType="VARCHAR" property="operationUsername"/>
        <result column="operation_name" jdbcType="VARCHAR" property="operationName"/>
        <result column="operation_email" jdbcType="VARCHAR" property="operationEmail"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, operation_username, operation_name, operation_email, create_time, update_time
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update operation_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="operation_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.operationUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.operationName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_email = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.operationEmail,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update operation_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="operation_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.operationUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.operationUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="operation_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.operationName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.operationName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="operation_email = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.operationEmail != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.operationEmail,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into operation_info
        (operation_username, operation_name, operation_email, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.operationUsername,jdbcType=VARCHAR}, #{item.operationName,jdbcType=VARCHAR},
            #{item.operationEmail,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=BIGINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.OperationInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into operation_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            operation_username,
            operation_name,
            operation_email,
            create_time,
            update_time,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{operationUsername,jdbcType=VARCHAR},
            #{operationName,jdbcType=VARCHAR},
            #{operationEmail,jdbcType=VARCHAR},
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            operation_username = #{operationUsername,jdbcType=VARCHAR},
            operation_name = #{operationName,jdbcType=VARCHAR},
            operation_email = #{operationEmail,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.OperationInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into operation_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="operationUsername != null">
                operation_username,
            </if>
            <if test="operationName != null">
                operation_name,
            </if>
            <if test="operationEmail != null">
                operation_email,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="operationUsername != null">
                #{operationUsername,jdbcType=VARCHAR},
            </if>
            <if test="operationName != null">
                #{operationName,jdbcType=VARCHAR},
            </if>
            <if test="operationEmail != null">
                #{operationEmail,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="operationUsername != null">
                operation_username = #{operationUsername,jdbcType=VARCHAR},
            </if>
            <if test="operationName != null">
                operation_name = #{operationName,jdbcType=VARCHAR},
            </if>
            <if test="operationEmail != null">
                operation_email = #{operationEmail,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
</mapper>