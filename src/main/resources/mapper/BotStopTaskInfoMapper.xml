<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.BotStopTaskInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.BotStopTaskInfo">
        <!--@mbg.generated-->
        <!--@Table bot_stop_task_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="bot_id" jdbcType="BIGINT" property="botId"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="old_task_status" jdbcType="TINYINT" property="oldTaskStatus"/>
        <result column="new_task_status" jdbcType="TINYINT" property="newTaskStatus"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, bot_id, task_id, old_task_status, new_task_status, create_time, update_time
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update bot_stop_task_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="bot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="old_task_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.oldTaskStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="new_task_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.newTaskStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update bot_stop_task_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="bot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="old_task_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.oldTaskStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.oldTaskStatus,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="new_task_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.newTaskStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.newTaskStatus,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into bot_stop_task_info
        (bot_id, task_id, old_task_status, new_task_status, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.botId,jdbcType=BIGINT}, #{item.taskId,jdbcType=BIGINT}, #{item.oldTaskStatus,jdbcType=TINYINT},
            #{item.newTaskStatus,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=BIGINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.BotStopTaskInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into bot_stop_task_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            bot_id,
            task_id,
            old_task_status,
            new_task_status,
            create_time,
            update_time,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{botId,jdbcType=BIGINT},
            #{taskId,jdbcType=BIGINT},
            #{oldTaskStatus,jdbcType=TINYINT},
            #{newTaskStatus,jdbcType=TINYINT},
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            bot_id = #{botId,jdbcType=BIGINT},
            task_id = #{taskId,jdbcType=BIGINT},
            old_task_status = #{oldTaskStatus,jdbcType=TINYINT},
            new_task_status = #{newTaskStatus,jdbcType=TINYINT},
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.BotStopTaskInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into bot_stop_task_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="botId != null">
                bot_id,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="oldTaskStatus != null">
                old_task_status,
            </if>
            <if test="newTaskStatus != null">
                new_task_status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="botId != null">
                #{botId,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="oldTaskStatus != null">
                #{oldTaskStatus,jdbcType=TINYINT},
            </if>
            <if test="newTaskStatus != null">
                #{newTaskStatus,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="botId != null">
                bot_id = #{botId,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=BIGINT},
            </if>
            <if test="oldTaskStatus != null">
                old_task_status = #{oldTaskStatus,jdbcType=TINYINT},
            </if>
            <if test="newTaskStatus != null">
                new_task_status = #{newTaskStatus,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
</mapper>