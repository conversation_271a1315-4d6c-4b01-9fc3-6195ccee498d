<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.ParseTmpInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.ParseTmpInfo">
        <!--@mbg.generated-->
        <!--@Table parse_tmp_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="excel_id" jdbcType="VARCHAR" property="excelId"/>
        <result column="emp_no" jdbcType="VARCHAR" property="empNo"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_desc" jdbcType="VARCHAR" property="deptDesc"/>
        <result column="mi_dept_level2" jdbcType="VARCHAR" property="miDeptLevel2"/>
        <result column="mi_dept_level2_desc" jdbcType="VARCHAR" property="miDeptLevel2Desc"/>
        <result column="mi_dept_level3" jdbcType="VARCHAR" property="miDeptLevel3"/>
        <result column="mi_dept_level3_desc" jdbcType="VARCHAR" property="miDeptLevel3Desc"/>
        <result column="mi_dept_level4" jdbcType="VARCHAR" property="miDeptLevel4"/>
        <result column="mi_dept_level4_desc" jdbcType="VARCHAR" property="miDeptLevel4Desc"/>
        <result column="data_status" jdbcType="TINYINT" property="dataStatus"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="index_no" jdbcType="INTEGER" property="indexNo"/>
        <result column="publish_scope" jdbcType="TINYINT" property="publishScope"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, task_id, excel_id, emp_no, email, username, `name`, phone, dept_id, dept_desc,
        mi_dept_level2, mi_dept_level2_desc, mi_dept_level3, mi_dept_level3_desc, mi_dept_level4,
        mi_dept_level4_desc, data_status, create_time, update_time, index_no,publish_scope
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update parse_tmp_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="excel_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.excelId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="emp_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.empNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="email = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.username,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.phone,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="dept_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deptDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level2 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel2,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level2_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel2Desc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level3 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel3,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level3_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel3Desc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level4 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel4,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level4_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel4Desc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="data_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.dataStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="index_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.indexNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="publish_scope = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.publish_scope,jdbcType=TINYINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update parse_tmp_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="excel_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.excelId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.excelId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="emp_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.empNo != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.empNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="email = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.email != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.username != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.username,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.name != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.phone != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.phone,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deptId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="dept_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deptDesc != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.deptDesc,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level2 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel2 != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel2,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level2_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel2Desc != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel2Desc,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level3 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel3 != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel3,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level3_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel3Desc != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel3Desc,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level4 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel4 != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel4,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level4_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel4Desc != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel4Desc,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="data_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dataStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.dataStatus,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="index_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.indexNo != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.indexNo,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="publish_scope = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.publishScope != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.publishScope,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into parse_tmp_info
        (task_id, excel_id, emp_no, email, username, `name`, phone, dept_id, dept_desc, mi_dept_level2,
        mi_dept_level2_desc, mi_dept_level3, mi_dept_level3_desc, mi_dept_level4, mi_dept_level4_desc,
        data_status, create_time, update_time, index_no,publish_scope)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.taskId,jdbcType=BIGINT}, #{item.excelId,jdbcType=VARCHAR}, #{item.empNo,jdbcType=VARCHAR},
            #{item.email,jdbcType=VARCHAR}, #{item.username,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR},
            #{item.phone,jdbcType=VARCHAR}, #{item.deptId,jdbcType=VARCHAR}, #{item.deptDesc,jdbcType=VARCHAR},
            #{item.miDeptLevel2,jdbcType=VARCHAR}, #{item.miDeptLevel2Desc,jdbcType=VARCHAR},
            #{item.miDeptLevel3,jdbcType=VARCHAR}, #{item.miDeptLevel3Desc,jdbcType=VARCHAR},
            #{item.miDeptLevel4,jdbcType=VARCHAR}, #{item.miDeptLevel4Desc,jdbcType=VARCHAR},
            #{item.dataStatus,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT},
            #{item.indexNo,jdbcType=INTEGER},
            #{item.publishScope,jdbcType=TINYINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.ParseTmpInfo"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into parse_tmp_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            task_id,
            excel_id,
            emp_no,
            email,
            username,
            `name`,
            phone,
            dept_id,
            dept_desc,
            mi_dept_level2,
            mi_dept_level2_desc,
            mi_dept_level3,
            mi_dept_level3_desc,
            mi_dept_level4,
            mi_dept_level4_desc,
            data_status,
            create_time,
            update_time,
            index_no,
            publish_scope,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{taskId,jdbcType=BIGINT},
            #{excelId,jdbcType=VARCHAR},
            #{empNo,jdbcType=VARCHAR},
            #{email,jdbcType=VARCHAR},
            #{username,jdbcType=VARCHAR},
            #{name,jdbcType=VARCHAR},
            #{phone,jdbcType=VARCHAR},
            #{deptId,jdbcType=VARCHAR},
            #{deptDesc,jdbcType=VARCHAR},
            #{miDeptLevel2,jdbcType=VARCHAR},
            #{miDeptLevel2Desc,jdbcType=VARCHAR},
            #{miDeptLevel3,jdbcType=VARCHAR},
            #{miDeptLevel3Desc,jdbcType=VARCHAR},
            #{miDeptLevel4,jdbcType=VARCHAR},
            #{miDeptLevel4Desc,jdbcType=VARCHAR},
            #{dataStatus,jdbcType=TINYINT},
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
            #{indexNo,jdbcType=INTEGER},
            #{publishScope,jdbcType=TINYINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            task_id = #{taskId,jdbcType=BIGINT},
            excel_id = #{excelId,jdbcType=VARCHAR},
            emp_no = #{empNo,jdbcType=VARCHAR},
            email = #{email,jdbcType=VARCHAR},
            username = #{username,jdbcType=VARCHAR},
            `name` = #{name,jdbcType=VARCHAR},
            phone = #{phone,jdbcType=VARCHAR},
            dept_id = #{deptId,jdbcType=VARCHAR},
            dept_desc = #{deptDesc,jdbcType=VARCHAR},
            mi_dept_level2 = #{miDeptLevel2,jdbcType=VARCHAR},
            mi_dept_level2_desc = #{miDeptLevel2Desc,jdbcType=VARCHAR},
            mi_dept_level3 = #{miDeptLevel3,jdbcType=VARCHAR},
            mi_dept_level3_desc = #{miDeptLevel3Desc,jdbcType=VARCHAR},
            mi_dept_level4 = #{miDeptLevel4,jdbcType=VARCHAR},
            mi_dept_level4_desc = #{miDeptLevel4Desc,jdbcType=VARCHAR},
            data_status = #{dataStatus,jdbcType=TINYINT},
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
            index_no = #{indexNo,jdbcType=INTEGER},
            publish_scope = #{publishScope,jdbcType=TINYINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.ParseTmpInfo"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into parse_tmp_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="excelId != null">
                excel_id,
            </if>
            <if test="empNo != null">
                emp_no,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="username != null">
                username,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="deptDesc != null">
                dept_desc,
            </if>
            <if test="miDeptLevel2 != null">
                mi_dept_level2,
            </if>
            <if test="miDeptLevel2Desc != null">
                mi_dept_level2_desc,
            </if>
            <if test="miDeptLevel3 != null">
                mi_dept_level3,
            </if>
            <if test="miDeptLevel3Desc != null">
                mi_dept_level3_desc,
            </if>
            <if test="miDeptLevel4 != null">
                mi_dept_level4,
            </if>
            <if test="miDeptLevel4Desc != null">
                mi_dept_level4_desc,
            </if>
            <if test="dataStatus != null">
                data_status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="indexNo != null">
                index_no,
            </if>
            <if test="publishScope != null">
                publish_scope,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="excelId != null">
                #{excelId,jdbcType=VARCHAR},
            </if>
            <if test="empNo != null">
                #{empNo,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="username != null">
                #{username,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="deptDesc != null">
                #{deptDesc,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel2 != null">
                #{miDeptLevel2,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel2Desc != null">
                #{miDeptLevel2Desc,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel3 != null">
                #{miDeptLevel3,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel3Desc != null">
                #{miDeptLevel3Desc,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel4 != null">
                #{miDeptLevel4,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel4Desc != null">
                #{miDeptLevel4Desc,jdbcType=VARCHAR},
            </if>
            <if test="dataStatus != null">
                #{dataStatus,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="indexNo != null">
                #{indexNo,jdbcType=INTEGER},
            </if>
            <if test="publishScope != null">
                #{publishScope,jdbcType=TINYINT}
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=BIGINT},
            </if>
            <if test="excelId != null">
                excel_id = #{excelId,jdbcType=VARCHAR},
            </if>
            <if test="empNo != null">
                emp_no = #{empNo,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="username != null">
                username = #{username,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="deptDesc != null">
                dept_desc = #{deptDesc,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel2 != null">
                mi_dept_level2 = #{miDeptLevel2,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel2Desc != null">
                mi_dept_level2_desc = #{miDeptLevel2Desc,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel3 != null">
                mi_dept_level3 = #{miDeptLevel3,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel3Desc != null">
                mi_dept_level3_desc = #{miDeptLevel3Desc,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel4 != null">
                mi_dept_level4 = #{miDeptLevel4,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel4Desc != null">
                mi_dept_level4_desc = #{miDeptLevel4Desc,jdbcType=VARCHAR},
            </if>
            <if test="dataStatus != null">
                data_status = #{dataStatus,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="indexNo != null">
                index_no = #{indexNo,jdbcType=INTEGER},
            </if>
            <if test="publishScope != null">
                publish_scope = #{publishScope,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <insert id="insertAutoDuplicate" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into parse_tmp_info
        (task_id, excel_id, emp_no, email, username, `name`, phone, dept_id, dept_desc, mi_dept_level2,
        mi_dept_level2_desc, mi_dept_level3, mi_dept_level3_desc, mi_dept_level4, mi_dept_level4_desc,
        data_status, create_time, update_time, index_no,publish_scope)
        values
        <foreach collection="list" item="item" separator=",">
            (

            <choose>
                <when test="item.taskId == null">
                    0,
                </when>
                <otherwise>
                    #{item.taskId,jdbcType=BIGINT},
                </otherwise>
            </choose>

            <choose>
                <when test="item.excelId == null">
                    '',
                </when>
                <otherwise>
                    #{item.excelId,jdbcType=VARCHAR},
                </otherwise>
            </choose>
            <choose>
                <when test="item.empNo == null">
                    '',
                </when>
                <otherwise>
                    #{item.empNo,jdbcType=VARCHAR},
                </otherwise>
            </choose>

            <choose>
                <when test="item.email == null ">
                    '',
                </when>
                <otherwise>
                    #{item.email,jdbcType=VARCHAR},
                </otherwise>
            </choose>

            <choose>
                <when test="item.username == null">
                    '',
                </when>
                <otherwise>
                    #{item.username,jdbcType=VARCHAR},
                </otherwise>
            </choose>

            <choose>
                <when test="item.name == null">
                    '',
                </when>
                <otherwise>
                    #{item.name,jdbcType=VARCHAR},
                </otherwise>
            </choose>

            <choose>
                <when test="item.phone == null ">
                    '',
                </when>
                <otherwise>
                    #{item.phone,jdbcType=VARCHAR},
                </otherwise>
            </choose>

            <choose>
                <when test="item.deptId == null">
                    '',
                </when>
                <otherwise>
                    #{item.deptId,jdbcType=VARCHAR},
                </otherwise>
            </choose>

            <choose>
                <when test="item.deptDesc == null">
                    '',
                </when>
                <otherwise>
                    #{item.deptDesc,jdbcType=VARCHAR},
                </otherwise>
            </choose>

            <choose>
                <when test="item.miDeptLevel2 == null">
                    '',
                </when>
                <otherwise>
                    #{item.miDeptLevel2,jdbcType=VARCHAR},
                </otherwise>
            </choose>

            <choose>
                <when test="item.miDeptLevel2Desc == null">
                    '',
                </when>
                <otherwise>
                    #{item.miDeptLevel2Desc,jdbcType=VARCHAR},
                </otherwise>
            </choose>

            <choose>
                <when test="item.miDeptLevel3 == null">
                    '',
                </when>
                <otherwise>
                    #{item.miDeptLevel3,jdbcType=VARCHAR},
                </otherwise>
            </choose>

            <choose>
                <when test="item.miDeptLevel3Desc == null ">
                    '',
                </when>
                <otherwise>
                    #{item.miDeptLevel3Desc,jdbcType=VARCHAR},
                </otherwise>
            </choose>

            <choose>
                <when test="item.miDeptLevel4 == null">
                    '',
                </when>
                <otherwise>
                    #{item.miDeptLevel4,jdbcType=VARCHAR},
                </otherwise>
            </choose>

            <choose>
                <when test="item.miDeptLevel4Desc == null">
                    '',
                </when>
                <otherwise>
                    #{item.miDeptLevel4Desc,jdbcType=VARCHAR},
                </otherwise>
            </choose>

            <choose>
                <when test="item.dataStatus == null">
                    0,
                </when>
                <otherwise>
                    #{item.dataStatus,jdbcType=TINYINT},
                </otherwise>
            </choose>

            <choose>
                <when test="item.createTime == null ">
                    0,
                </when>
                <otherwise>
                    #{item.createTime,jdbcType=BIGINT},
                </otherwise>
            </choose>

            <choose>
                <when test="item.updateTime == null">
                    0,
                </when>
                <otherwise>
                    #{item.updateTime,jdbcType=BIGINT},
                </otherwise>
            </choose>

            <choose>
                <when test="item.indexNo == null">
                    0,
                </when>
                <otherwise>
                    #{item.indexNo,jdbcType=INTEGER},
                </otherwise>
            </choose>

            <choose>
                <when test="item.publishScope == null">
                    4
                </when>
                <otherwise>
                    #{item.publishScope,jdbcType=TINYINT}
                </otherwise>
            </choose>

            )
        </foreach>
        on duplicate key update
        update_time= #{updateTime,jdbcType=BIGINT}
    </insert>
</mapper>
