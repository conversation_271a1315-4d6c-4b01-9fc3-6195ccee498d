<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.SyncRecordLatestTimeMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.SyncRecordLatestTime">
        <!--@mbg.generated-->
        <!--@Table sync_record_latest_time-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="belong_to" jdbcType="VARCHAR" property="belongTo"/>
        <result column="sync_type" jdbcType="VARCHAR" property="syncType"/>
        <result column="latest_time" jdbcType="BIGINT" property="latestTime"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, belong_to, sync_type, latest_time, create_time, update_time
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update sync_record_latest_time
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="belong_to = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.belongTo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sync_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.syncType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="latest_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.latestTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update sync_record_latest_time
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="belong_to = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.belongTo != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.belongTo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sync_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.syncType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.syncType,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="latest_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.latestTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.latestTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into sync_record_latest_time
        (belong_to, sync_type, latest_time, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.belongTo,jdbcType=VARCHAR}, #{item.syncType,jdbcType=VARCHAR}, #{item.latestTime,jdbcType=BIGINT},
            #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.SyncRecordLatestTime" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into sync_record_latest_time
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            belong_to,
            sync_type,
            latest_time,
            create_time,
            update_time,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{belongTo,jdbcType=VARCHAR},
            #{syncType,jdbcType=VARCHAR},
            #{latestTime,jdbcType=BIGINT},
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            belong_to = #{belongTo,jdbcType=VARCHAR},
            sync_type = #{syncType,jdbcType=VARCHAR},
            latest_time = #{latestTime,jdbcType=BIGINT},
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.SyncRecordLatestTime" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into sync_record_latest_time
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="belongTo != null">
                belong_to,
            </if>
            <if test="syncType != null">
                sync_type,
            </if>
            <if test="latestTime != null">
                latest_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="belongTo != null">
                #{belongTo,jdbcType=VARCHAR},
            </if>
            <if test="syncType != null">
                #{syncType,jdbcType=VARCHAR},
            </if>
            <if test="latestTime != null">
                #{latestTime,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="belongTo != null">
                belong_to = #{belongTo,jdbcType=VARCHAR},
            </if>
            <if test="syncType != null">
                sync_type = #{syncType,jdbcType=VARCHAR},
            </if>
            <if test="latestTime != null">
                latest_time = #{latestTime,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
</mapper>