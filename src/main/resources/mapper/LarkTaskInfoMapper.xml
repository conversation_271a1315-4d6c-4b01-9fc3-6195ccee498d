<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.LarkTaskInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.LarkTaskInfo">
        <!--@mbg.generated-->
        <!--@Table lark_task_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="bot_id" jdbcType="BIGINT" property="botId"/>
        <result column="lark_message_type" jdbcType="TINYINT" property="larkMessageType"/>
        <result column="button" jdbcType="TINYINT" property="button"/>
        <result column="schedule_start_time" jdbcType="BIGINT" property="scheduleStartTime"/>
        <result column="schedule_end_time" jdbcType="BIGINT" property="scheduleEndTime"/>
        <result column="schedule_description" jdbcType="VARCHAR" property="scheduleDescription"/>
        <result column="landing_page_url" jdbcType="LONGVARCHAR" property="landingPageUrl"/>
        <result column="button_name" jdbcType="VARCHAR" property="buttonName"/>
        <result column="button_name_en" jdbcType="VARCHAR" property="buttonNameEn"/>
        <result column="at_id" jdbcType="VARCHAR" property="atId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, create_time, update_time, task_id, bot_id, lark_message_type, button, schedule_start_time,
        schedule_end_time, schedule_description, landing_page_url, button_name, button_name_en,
        at_id
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update lark_task_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="bot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="lark_message_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.larkMessageType,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="button = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.button,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="schedule_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.scheduleStartTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="schedule_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.scheduleEndTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="schedule_description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.scheduleDescription,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="landing_page_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.landingPageUrl,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="button_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.buttonName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="button_name_en = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.buttonNameEn,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="at_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.atId,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update lark_task_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="lark_message_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.larkMessageType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.larkMessageType,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="button = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.button != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.button,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="schedule_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.scheduleStartTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.scheduleStartTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="schedule_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.scheduleEndTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.scheduleEndTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="schedule_description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.scheduleDescription != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.scheduleDescription,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="landing_page_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.landingPageUrl != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.landingPageUrl,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="button_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.buttonName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.buttonName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="button_name_en = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.buttonNameEn != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.buttonNameEn,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="at_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.atId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.atId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into lark_task_info
        (create_time, update_time, task_id, bot_id, lark_message_type, button, schedule_start_time,
        schedule_end_time, schedule_description, landing_page_url, button_name, button_name_en,
        at_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, #{item.taskId,jdbcType=BIGINT},
            #{item.botId,jdbcType=BIGINT}, #{item.larkMessageType,jdbcType=TINYINT}, #{item.button,jdbcType=TINYINT},
            #{item.scheduleStartTime,jdbcType=BIGINT}, #{item.scheduleEndTime,jdbcType=BIGINT},
            #{item.scheduleDescription,jdbcType=VARCHAR}, #{item.landingPageUrl,jdbcType=LONGVARCHAR},
            #{item.buttonName,jdbcType=VARCHAR}, #{item.buttonNameEn,jdbcType=VARCHAR}, #{item.atId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.LarkTaskInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into lark_task_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            create_time,
            update_time,
            task_id,
            bot_id,
            lark_message_type,
            button,
            schedule_start_time,
            schedule_end_time,
            schedule_description,
            landing_page_url,
            button_name,
            button_name_en,
            at_id,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
            #{taskId,jdbcType=BIGINT},
            #{botId,jdbcType=BIGINT},
            #{larkMessageType,jdbcType=TINYINT},
            #{button,jdbcType=TINYINT},
            #{scheduleStartTime,jdbcType=BIGINT},
            #{scheduleEndTime,jdbcType=BIGINT},
            #{scheduleDescription,jdbcType=VARCHAR},
            #{landingPageUrl,jdbcType=LONGVARCHAR},
            #{buttonName,jdbcType=VARCHAR},
            #{buttonNameEn,jdbcType=VARCHAR},
            #{atId,jdbcType=VARCHAR},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
            task_id = #{taskId,jdbcType=BIGINT},
            bot_id = #{botId,jdbcType=BIGINT},
            lark_message_type = #{larkMessageType,jdbcType=TINYINT},
            button = #{button,jdbcType=TINYINT},
            schedule_start_time = #{scheduleStartTime,jdbcType=BIGINT},
            schedule_end_time = #{scheduleEndTime,jdbcType=BIGINT},
            schedule_description = #{scheduleDescription,jdbcType=VARCHAR},
            landing_page_url = #{landingPageUrl,jdbcType=LONGVARCHAR},
            button_name = #{buttonName,jdbcType=VARCHAR},
            button_name_en = #{buttonNameEn,jdbcType=VARCHAR},
            at_id = #{atId,jdbcType=VARCHAR},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.LarkTaskInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into lark_task_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="botId != null">
                bot_id,
            </if>
            <if test="larkMessageType != null">
                lark_message_type,
            </if>
            <if test="button != null">
                button,
            </if>
            <if test="scheduleStartTime != null">
                schedule_start_time,
            </if>
            <if test="scheduleEndTime != null">
                schedule_end_time,
            </if>
            <if test="scheduleDescription != null">
                schedule_description,
            </if>
            <if test="landingPageUrl != null">
                landing_page_url,
            </if>
            <if test="buttonName != null">
                button_name,
            </if>
            <if test="buttonNameEn != null">
                button_name_en,
            </if>
            <if test="atId != null">
                at_id,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="botId != null">
                #{botId,jdbcType=BIGINT},
            </if>
            <if test="larkMessageType != null">
                #{larkMessageType,jdbcType=TINYINT},
            </if>
            <if test="button != null">
                #{button,jdbcType=TINYINT},
            </if>
            <if test="scheduleStartTime != null">
                #{scheduleStartTime,jdbcType=BIGINT},
            </if>
            <if test="scheduleEndTime != null">
                #{scheduleEndTime,jdbcType=BIGINT},
            </if>
            <if test="scheduleDescription != null">
                #{scheduleDescription,jdbcType=VARCHAR},
            </if>
            <if test="landingPageUrl != null">
                #{landingPageUrl,jdbcType=LONGVARCHAR},
            </if>
            <if test="buttonName != null">
                #{buttonName,jdbcType=VARCHAR},
            </if>
            <if test="buttonNameEn != null">
                #{buttonNameEn,jdbcType=VARCHAR},
            </if>
            <if test="atId != null">
                #{atId,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=BIGINT},
            </if>
            <if test="botId != null">
                bot_id = #{botId,jdbcType=BIGINT},
            </if>
            <if test="larkMessageType != null">
                lark_message_type = #{larkMessageType,jdbcType=TINYINT},
            </if>
            <if test="button != null">
                button = #{button,jdbcType=TINYINT},
            </if>
            <if test="scheduleStartTime != null">
                schedule_start_time = #{scheduleStartTime,jdbcType=BIGINT},
            </if>
            <if test="scheduleEndTime != null">
                schedule_end_time = #{scheduleEndTime,jdbcType=BIGINT},
            </if>
            <if test="scheduleDescription != null">
                schedule_description = #{scheduleDescription,jdbcType=VARCHAR},
            </if>
            <if test="landingPageUrl != null">
                landing_page_url = #{landingPageUrl,jdbcType=LONGVARCHAR},
            </if>
            <if test="buttonName != null">
                button_name = #{buttonName,jdbcType=VARCHAR},
            </if>
            <if test="buttonNameEn != null">
                button_name_en = #{buttonNameEn,jdbcType=VARCHAR},
            </if>
            <if test="atId != null">
                at_id = #{atId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="selectLarkPushCount" resultType="com.mioffice.ums.admin.entity.bo.PushCountBo">
        select bot_id as botId, count(*) as pushCount from lark_task_info where 1 = 1
        <if test="taskIdList != null and taskIdList.size() != 0">
            and task_id in
            <foreach close=")" collection="taskIdList" item="item" open="(" separator=", ">
                #{item}
            </foreach>
        </if>
        <if test="botIdList != null and botIdList.size() != 0">
            and bot_id in
            <foreach close=")" collection="botIdList" item="item" open="(" separator=", ">
                #{item}
            </foreach>
        </if>
        group by bot_id
    </select>
</mapper>