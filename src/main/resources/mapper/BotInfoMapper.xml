<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.BotInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.BotInfo">
        <!--@mbg.generated-->
        <!--@Table bot_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="bot_name" jdbcType="VARCHAR" property="botName"/>
        <result column="bot_icon_url" jdbcType="VARCHAR" property="botIconUrl"/>
        <result column="bot_key" jdbcType="VARCHAR" property="botKey"/>
        <result column="bot_secret" jdbcType="VARCHAR" property="botSecret"/>
        <result column="calendar_status" jdbcType="TINYINT" property="calendarStatus"/>
        <result column="bot_status" jdbcType="TINYINT" property="botStatus"/>
        <result column="bot_lark_status" jdbcType="TINYINT" property="botLarkStatus"/>
        <result column="notify_url" jdbcType="LONGVARCHAR" property="notifyUrl"/>
        <result column="bot_encrypt_key" jdbcType="VARCHAR" property="botEncryptKey"/>
        <result column="bot_verification_token" jdbcType="VARCHAR" property="botVerificationToken"/>
        <result column="relation_system" jdbcType="VARCHAR" property="relationSystem"/>
        <result column="bot_biz_id" jdbcType="VARCHAR" property="botBizId"/>
        <result column="bot_type" jdbcType="TINYINT" property="botType"/>
        <result column="bot_range_scope" jdbcType="TINYINT" property="botRangeScope"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, create_username, update_username, update_time, create_time, bot_name, bot_icon_url,
        bot_key, bot_secret, calendar_status, bot_status, bot_lark_status, notify_url, bot_encrypt_key,
        bot_verification_token, relation_system, bot_biz_id, bot_type, bot_range_scope
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update bot_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="bot_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bot_icon_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botIconUrl,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bot_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botKey,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bot_secret = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botSecret,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="calendar_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.calendarStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="bot_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="bot_lark_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botLarkStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="notify_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.notifyUrl,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="bot_encrypt_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botEncryptKey,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bot_verification_token = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botVerificationToken,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="relation_system = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.relationSystem,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bot_biz_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botBizId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bot_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botType,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="bot_range_scope = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botRangeScope,jdbcType=TINYINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update bot_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_icon_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botIconUrl != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botIconUrl,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botKey != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botKey,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_secret = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botSecret != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botSecret,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="calendar_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.calendarStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.calendarStatus,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botStatus,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_lark_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botLarkStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botLarkStatus,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="notify_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.notifyUrl != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.notifyUrl,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_encrypt_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botEncryptKey != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botEncryptKey,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_verification_token = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botVerificationToken != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botVerificationToken,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="relation_system = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.relationSystem != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.relationSystem,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_biz_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botBizId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botBizId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botType,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_range_scope = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botRangeScope != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botRangeScope,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into bot_info
        (create_username, update_username, update_time, create_time, bot_name, bot_icon_url,
        bot_key, bot_secret, calendar_status, bot_status, bot_lark_status, notify_url,
        bot_encrypt_key, bot_verification_token, relation_system, bot_biz_id, bot_type,
        bot_range_scope)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.createUsername,jdbcType=VARCHAR}, #{item.updateUsername,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, #{item.botName,jdbcType=VARCHAR},
            #{item.botIconUrl,jdbcType=VARCHAR}, #{item.botKey,jdbcType=VARCHAR}, #{item.botSecret,jdbcType=VARCHAR},
            #{item.calendarStatus,jdbcType=TINYINT}, #{item.botStatus,jdbcType=TINYINT},
            #{item.botLarkStatus,jdbcType=TINYINT},
            #{item.notifyUrl,jdbcType=LONGVARCHAR}, #{item.botEncryptKey,jdbcType=VARCHAR},
            #{item.botVerificationToken,jdbcType=VARCHAR}, #{item.relationSystem,jdbcType=VARCHAR},
            #{item.botBizId,jdbcType=VARCHAR}, #{item.botType,jdbcType=TINYINT}, #{item.botRangeScope,jdbcType=TINYINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.BotInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into bot_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            create_username,
            update_username,
            update_time,
            create_time,
            bot_name,
            bot_icon_url,
            bot_key,
            bot_secret,
            calendar_status,
            bot_status,
            bot_lark_status,
            notify_url,
            bot_encrypt_key,
            bot_verification_token,
            relation_system,
            bot_biz_id,
            bot_type,
            bot_range_scope,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{createUsername,jdbcType=VARCHAR},
            #{updateUsername,jdbcType=VARCHAR},
            #{updateTime,jdbcType=BIGINT},
            #{createTime,jdbcType=BIGINT},
            #{botName,jdbcType=VARCHAR},
            #{botIconUrl,jdbcType=VARCHAR},
            #{botKey,jdbcType=VARCHAR},
            #{botSecret,jdbcType=VARCHAR},
            #{calendarStatus,jdbcType=TINYINT},
            #{botStatus,jdbcType=TINYINT},
            #{botLarkStatus,jdbcType=TINYINT},
            #{notifyUrl,jdbcType=LONGVARCHAR},
            #{botEncryptKey,jdbcType=VARCHAR},
            #{botVerificationToken,jdbcType=VARCHAR},
            #{relationSystem,jdbcType=VARCHAR},
            #{botBizId,jdbcType=VARCHAR},
            #{botType,jdbcType=TINYINT},
            #{botRangeScope,jdbcType=TINYINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            create_username = #{createUsername,jdbcType=VARCHAR},
            update_username = #{updateUsername,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=BIGINT},
            create_time = #{createTime,jdbcType=BIGINT},
            bot_name = #{botName,jdbcType=VARCHAR},
            bot_icon_url = #{botIconUrl,jdbcType=VARCHAR},
            bot_key = #{botKey,jdbcType=VARCHAR},
            bot_secret = #{botSecret,jdbcType=VARCHAR},
            calendar_status = #{calendarStatus,jdbcType=TINYINT},
            bot_status = #{botStatus,jdbcType=TINYINT},
            bot_lark_status = #{botLarkStatus,jdbcType=TINYINT},
            notify_url = #{notifyUrl,jdbcType=LONGVARCHAR},
            bot_encrypt_key = #{botEncryptKey,jdbcType=VARCHAR},
            bot_verification_token = #{botVerificationToken,jdbcType=VARCHAR},
            relation_system = #{relationSystem,jdbcType=VARCHAR},
            bot_biz_id = #{botBizId,jdbcType=VARCHAR},
            bot_type = #{botType,jdbcType=TINYINT},
            bot_range_scope = #{botRangeScope,jdbcType=TINYINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.BotInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into bot_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="createUsername != null">
                create_username,
            </if>
            <if test="updateUsername != null">
                update_username,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="botName != null">
                bot_name,
            </if>
            <if test="botIconUrl != null">
                bot_icon_url,
            </if>
            <if test="botKey != null">
                bot_key,
            </if>
            <if test="botSecret != null">
                bot_secret,
            </if>
            <if test="calendarStatus != null">
                calendar_status,
            </if>
            <if test="botStatus != null">
                bot_status,
            </if>
            <if test="botLarkStatus != null">
                bot_lark_status,
            </if>
            <if test="notifyUrl != null">
                notify_url,
            </if>
            <if test="botEncryptKey != null">
                bot_encrypt_key,
            </if>
            <if test="botVerificationToken != null">
                bot_verification_token,
            </if>
            <if test="relationSystem != null">
                relation_system,
            </if>
            <if test="botBizId != null">
                bot_biz_id,
            </if>
            <if test="botType != null">
                bot_type,
            </if>
            <if test="botRangeScope != null">
                bot_range_scope,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="createUsername != null">
                #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateUsername != null">
                #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="botName != null">
                #{botName,jdbcType=VARCHAR},
            </if>
            <if test="botIconUrl != null">
                #{botIconUrl,jdbcType=VARCHAR},
            </if>
            <if test="botKey != null">
                #{botKey,jdbcType=VARCHAR},
            </if>
            <if test="botSecret != null">
                #{botSecret,jdbcType=VARCHAR},
            </if>
            <if test="calendarStatus != null">
                #{calendarStatus,jdbcType=TINYINT},
            </if>
            <if test="botStatus != null">
                #{botStatus,jdbcType=TINYINT},
            </if>
            <if test="botLarkStatus != null">
                #{botLarkStatus,jdbcType=TINYINT},
            </if>
            <if test="notifyUrl != null">
                #{notifyUrl,jdbcType=LONGVARCHAR},
            </if>
            <if test="botEncryptKey != null">
                #{botEncryptKey,jdbcType=VARCHAR},
            </if>
            <if test="botVerificationToken != null">
                #{botVerificationToken,jdbcType=VARCHAR},
            </if>
            <if test="relationSystem != null">
                #{relationSystem,jdbcType=VARCHAR},
            </if>
            <if test="botBizId != null">
                #{botBizId,jdbcType=VARCHAR},
            </if>
            <if test="botType != null">
                #{botType,jdbcType=TINYINT},
            </if>
            <if test="botRangeScope != null">
                #{botRangeScope,jdbcType=TINYINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="createUsername != null">
                create_username = #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateUsername != null">
                update_username = #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="botName != null">
                bot_name = #{botName,jdbcType=VARCHAR},
            </if>
            <if test="botIconUrl != null">
                bot_icon_url = #{botIconUrl,jdbcType=VARCHAR},
            </if>
            <if test="botKey != null">
                bot_key = #{botKey,jdbcType=VARCHAR},
            </if>
            <if test="botSecret != null">
                bot_secret = #{botSecret,jdbcType=VARCHAR},
            </if>
            <if test="calendarStatus != null">
                calendar_status = #{calendarStatus,jdbcType=TINYINT},
            </if>
            <if test="botStatus != null">
                bot_status = #{botStatus,jdbcType=TINYINT},
            </if>
            <if test="botLarkStatus != null">
                bot_lark_status = #{botLarkStatus,jdbcType=TINYINT},
            </if>
            <if test="notifyUrl != null">
                notify_url = #{notifyUrl,jdbcType=LONGVARCHAR},
            </if>
            <if test="botEncryptKey != null">
                bot_encrypt_key = #{botEncryptKey,jdbcType=VARCHAR},
            </if>
            <if test="botVerificationToken != null">
                bot_verification_token = #{botVerificationToken,jdbcType=VARCHAR},
            </if>
            <if test="relationSystem != null">
                relation_system = #{relationSystem,jdbcType=VARCHAR},
            </if>
            <if test="botBizId != null">
                bot_biz_id = #{botBizId,jdbcType=VARCHAR},
            </if>
            <if test="botType != null">
                bot_type = #{botType,jdbcType=TINYINT},
            </if>
            <if test="botRangeScope != null">
                bot_range_scope = #{botRangeScope,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <select id="getBotPushCount" resultType="com.mioffice.ums.admin.entity.vo.BotDashboardVO$TaskCountRank">
        SELECT b1.id as botId, b1.bot_name as botName, b1.bot_status as botStatus, b1.bot_lark_status as
        botLarkStatus,count(t1.id) as taskCount FROM `bot_info` b1
        LEFT JOIN `lark_task_info` l1 on b1.id = l1.bot_id
        LEFT JOIN
        (SELECT id FROM `task_info`
        where task_status in
        <foreach close=")" collection="taskStatusList" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=TINYINT}
        </foreach>
        ) t1 on t1.id = l1.task_id
        <where>
            1 = 1
            <if test="botIdList != null and botIdList.size() != 0">
                and b1.id in
                <foreach close=")" collection="botIdList" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        group by b1.bot_name
    </select>

    <select id="getBotSummary" resultType="com.mioffice.ums.admin.entity.vo.BotDashboardVO$BotSummary">
        select sum(if((b1.bot_status = 2 or b1.bot_status = 0) and b1.bot_lark_status = 0, 1, 0)) as initBotCount,
        sum(if(b1.bot_status = 2 and b1.bot_lark_status = 2, 1, 0)) as onBotCount,
        sum(if(b1.bot_status = 1 or (b1.bot_status = 2 and b1.bot_lark_status = 1), 1, 0)) as offBotCount
        from bot_info b1
        <where>
            1 = 1
            <if test="botIdList != null and botIdList.size() != 0">
                and b1.id in
                <foreach close=")" collection="botIdList" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getBotKeyName" resultType="com.mioffice.ums.admin.entity.vo.BotIdNameVO">
        select bot_biz_id ,bot_name from bot_info
        where bot_key in
        <foreach close=")" collection="botIdList" index="index" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getBotInfoBiz" resultType="com.mioffice.ums.admin.entity.vo.BotInfoBizVO">
        select bot_key as bot_app_id ,bot_name, bot_biz_id from bot_info
        where bot_biz_id in
        <foreach close=")" collection="botBizList" index="index" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>