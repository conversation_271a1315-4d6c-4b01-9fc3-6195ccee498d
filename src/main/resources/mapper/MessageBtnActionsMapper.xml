<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.MessageButtonActionsMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.MessageButtonActions">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="action_name" jdbcType="VARCHAR" property="actionName"/>
        <result column="msg_job_id" jdbcType="BIGINT" property="msgJobId"/>
        <result column="receives_type" jdbcType="INTEGER" property="receivesType"/>
        <result column="receives_param" jdbcType="VARCHAR" property="receivesParam"/>
        <result column="message_content" jdbcType="VARCHAR" property="messageContent"/>
        <result column="message_title" jdbcType="VARCHAR" property="messageTitle"/>
        <result column="btn_text" jdbcType="VARCHAR" property="btnText"/>
        <result column="btn_url" jdbcType="VARCHAR" property="btnUrl"/>
        <result column="expand_info" jdbcType="VARCHAR" property="expandInfo"/>
        <result column="message_title_en" jdbcType="VARCHAR" property="messageTitleEn"/>
        <result column="message_content_en" jdbcType="VARCHAR" property="messageContentEn"/>
        <result column="btn_text_en" jdbcType="VARCHAR" property="btnTextEn"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        action_name,
        msg_job_id,
        receives_type,
        receives_param,
        message_content,
        message_title,
        btn_text,
        btn_url,
        expand_info,
        message_title_en,
        message_content_en,
        btn_text_en
    </sql>

    <insert id="batchInsert" parameterType="com.mioffice.ums.admin.entity.info.MessageButtonActions">
        INSERT INTO message_btn_actions
        (
        action_name,
        msg_job_id,
        receives_type,
        receives_param,
        message_content,
        message_title,
        btn_text,
        btn_url,
        expand_info,
        message_title_en,
        message_content_en,
        btn_text_en
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.actionName,jdbcType = VARCHAR},
            #{item.msgJobId,jdbcType = BIGINT},
            #{item.receivesType,jdbcType = INTEGER},
            #{item.receivesParam,jdbcType = VARCHAR},
            #{item.messageContent,jdbcType = VARCHAR},
            #{item.messageTitle,jdbcType = VARCHAR},
            #{item.btnText,jdbcType = VARCHAR},
            #{item.btnUrl,jdbcType = VARCHAR},
            #{item.expandInfo,jdbcType = VARCHAR},
            #{item.messageTitleEn,jdbcType=VARCHAR},
            #{item.messageContentEn,jdbcType=VARCHAR},
            #{item.btnTextEn,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

</mapper>
