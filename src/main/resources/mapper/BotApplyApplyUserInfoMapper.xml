<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.BotApplyApplyUserInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.BotApplyApplyUserInfo">
        <!--@mbg.generated-->
        <!--@Table bot_apply_apply_user_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="apply_user_username" jdbcType="VARCHAR" property="applyUserUsername"/>
        <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName"/>
        <result column="bot_apply_record_id" jdbcType="BIGINT" property="botApplyRecordId"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, apply_user_username, apply_user_name, bot_apply_record_id, create_time, update_time
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update bot_apply_apply_user_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="apply_user_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.applyUserUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="apply_user_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.applyUserName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bot_apply_record_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botApplyRecordId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update bot_apply_apply_user_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="apply_user_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.applyUserUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.applyUserUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="apply_user_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.applyUserName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.applyUserName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_apply_record_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botApplyRecordId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botApplyRecordId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into bot_apply_apply_user_info
        (apply_user_username, apply_user_name, bot_apply_record_id, create_time, update_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.applyUserUsername,jdbcType=VARCHAR}, #{item.applyUserName,jdbcType=VARCHAR},
            #{item.botApplyRecordId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=BIGINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.BotApplyApplyUserInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into bot_apply_apply_user_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            apply_user_username,
            apply_user_name,
            bot_apply_record_id,
            create_time,
            update_time,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{applyUserUsername,jdbcType=VARCHAR},
            #{applyUserName,jdbcType=VARCHAR},
            #{botApplyRecordId,jdbcType=BIGINT},
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            apply_user_username = #{applyUserUsername,jdbcType=VARCHAR},
            apply_user_name = #{applyUserName,jdbcType=VARCHAR},
            bot_apply_record_id = #{botApplyRecordId,jdbcType=BIGINT},
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.BotApplyApplyUserInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into bot_apply_apply_user_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="applyUserUsername != null">
                apply_user_username,
            </if>
            <if test="applyUserName != null">
                apply_user_name,
            </if>
            <if test="botApplyRecordId != null">
                bot_apply_record_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="applyUserUsername != null">
                #{applyUserUsername,jdbcType=VARCHAR},
            </if>
            <if test="applyUserName != null">
                #{applyUserName,jdbcType=VARCHAR},
            </if>
            <if test="botApplyRecordId != null">
                #{botApplyRecordId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="applyUserUsername != null">
                apply_user_username = #{applyUserUsername,jdbcType=VARCHAR},
            </if>
            <if test="applyUserName != null">
                apply_user_name = #{applyUserName,jdbcType=VARCHAR},
            </if>
            <if test="botApplyRecordId != null">
                bot_apply_record_id = #{botApplyRecordId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
</mapper>