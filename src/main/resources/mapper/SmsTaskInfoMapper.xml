<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.SmsTaskInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.SmsTaskInfo">
        <!--@mbg.generated-->
        <!--@Table sms_task_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="sign_code" jdbcType="VARCHAR" property="signCode"/>
        <result column="bot_id" jdbcType="BIGINT" property="botId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, create_time, update_time, task_id, sign_code, bot_id
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update sms_task_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="sign_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.signCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botId,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update sms_task_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sign_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.signCode != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.signCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into sms_task_info
        (create_time, update_time, task_id, sign_code, bot_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, #{item.taskId,jdbcType=BIGINT},
            #{item.signCode,jdbcType=VARCHAR}, #{item.botId,jdbcType=BIGINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.SmsTaskInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into sms_task_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            create_time,
            update_time,
            task_id,
            sign_code,
            bot_id,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
            #{taskId,jdbcType=BIGINT},
            #{signCode,jdbcType=VARCHAR},
            #{botId,jdbcType=BIGINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
            task_id = #{taskId,jdbcType=BIGINT},
            sign_code = #{signCode,jdbcType=VARCHAR},
            bot_id = #{botId,jdbcType=BIGINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.SmsTaskInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into sms_task_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="signCode != null">
                sign_code,
            </if>
            <if test="botId != null">
                bot_id,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="signCode != null">
                #{signCode,jdbcType=VARCHAR},
            </if>
            <if test="botId != null">
                #{botId,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=BIGINT},
            </if>
            <if test="signCode != null">
                sign_code = #{signCode,jdbcType=VARCHAR},
            </if>
            <if test="botId != null">
                bot_id = #{botId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <select id="selectSmsPushCount" resultType="com.mioffice.ums.admin.entity.bo.PushCountBo">
        select bot_id as botId, count(*) as pushCount from sms_task_info where 1 = 1
        <if test="taskIdList != null and taskIdList.size() != 0">
            and task_id in
            <foreach close=")" collection="taskIdList" item="item" open="(" separator=", ">
                #{item}
            </foreach>
        </if>
        <if test="botIdList != null and botIdList.size() != 0">
            and bot_id in
            <foreach close=")" collection="botIdList" item="item" open="(" separator=", ">
                #{item}
            </foreach>
        </if>
        group by bot_id
    </select>
</mapper>