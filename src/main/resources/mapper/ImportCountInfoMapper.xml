<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.ImportCountInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.ImportCountInfo">
        <!--@mbg.generated-->
        <!--@Table import_count_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="excel_id" jdbcType="VARCHAR" property="excelId"/>
        <result column="total_count" jdbcType="BIGINT" property="totalCount"/>
        <result column="save_count" jdbcType="BIGINT" property="saveCount"/>
        <result column="error_count" jdbcType="BIGINT" property="errorCount"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, excel_id, total_count, save_count, error_count, create_time, update_time
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update import_count_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="excel_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.excelId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="total_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.totalCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="save_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.saveCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="error_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.errorCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update import_count_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="excel_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.excelId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.excelId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="total_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.totalCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.totalCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="save_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.saveCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.saveCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="error_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.errorCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.errorCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into import_count_info
        (excel_id, total_count, save_count, error_count, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.excelId,jdbcType=VARCHAR}, #{item.totalCount,jdbcType=BIGINT}, #{item.saveCount,jdbcType=BIGINT},
            #{item.errorCount,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.ImportCountInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into import_count_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            excel_id,
            total_count,
            save_count,
            error_count,
            create_time,
            update_time,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{excelId,jdbcType=VARCHAR},
            #{totalCount,jdbcType=BIGINT},
            #{saveCount,jdbcType=BIGINT},
            #{errorCount,jdbcType=BIGINT},
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            excel_id = #{excelId,jdbcType=VARCHAR},
            total_count = #{totalCount,jdbcType=BIGINT},
            save_count = #{saveCount,jdbcType=BIGINT},
            error_count = #{errorCount,jdbcType=BIGINT},
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.ImportCountInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into import_count_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="excelId != null">
                excel_id,
            </if>
            <if test="totalCount != null">
                total_count,
            </if>
            <if test="saveCount != null">
                save_count,
            </if>
            <if test="errorCount != null">
                error_count,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="excelId != null">
                #{excelId,jdbcType=VARCHAR},
            </if>
            <if test="totalCount != null">
                #{totalCount,jdbcType=BIGINT},
            </if>
            <if test="saveCount != null">
                #{saveCount,jdbcType=BIGINT},
            </if>
            <if test="errorCount != null">
                #{errorCount,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="excelId != null">
                excel_id = #{excelId,jdbcType=VARCHAR},
            </if>
            <if test="totalCount != null">
                total_count = #{totalCount,jdbcType=BIGINT},
            </if>
            <if test="saveCount != null">
                save_count = #{saveCount,jdbcType=BIGINT},
            </if>
            <if test="errorCount != null">
                error_count = #{errorCount,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateSaveNumberById">
        update import_count_info
        set save_count = save_count + #{saveNumber}
        where excel_id = #{excelId}
    </update>
    <update id="updateErrorNumberById">
        update import_count_info
        set error_count = error_count + #{errorNumber}
        where excel_id = #{excelId}
    </update>
</mapper>