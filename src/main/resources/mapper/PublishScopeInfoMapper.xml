<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.PublishScopeInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.PublishScopeInfo">
        <!--@mbg.generated-->
        <!--@Table publish_scope_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="scope_type" jdbcType="TINYINT" property="scopeType"/>
        <result column="scope_key" jdbcType="VARCHAR" property="scopeKey"/>
        <result column="all_count" jdbcType="BIGINT" property="allCount"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, create_time, update_time, task_id, scope_type, scope_key, all_count
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update publish_scope_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="scope_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.scopeType,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="scope_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.scopeKey,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="all_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.allCount,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update publish_scope_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="scope_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.scopeType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.scopeType,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="scope_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.scopeKey != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.scopeKey,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="all_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.allCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.allCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into publish_scope_info
        (create_time, update_time, task_id, scope_type, scope_key, all_count)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, #{item.taskId,jdbcType=BIGINT},
            #{item.scopeType,jdbcType=TINYINT}, #{item.scopeKey,jdbcType=VARCHAR}, #{item.allCount,jdbcType=BIGINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.PublishScopeInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into publish_scope_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            create_time,
            update_time,
            task_id,
            scope_type,
            scope_key,
            all_count,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
            #{taskId,jdbcType=BIGINT},
            #{scopeType,jdbcType=TINYINT},
            #{scopeKey,jdbcType=VARCHAR},
            #{allCount,jdbcType=BIGINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
            task_id = #{taskId,jdbcType=BIGINT},
            scope_type = #{scopeType,jdbcType=TINYINT},
            scope_key = #{scopeKey,jdbcType=VARCHAR},
            all_count = #{allCount,jdbcType=BIGINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.PublishScopeInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into publish_scope_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="scopeType != null">
                scope_type,
            </if>
            <if test="scopeKey != null">
                scope_key,
            </if>
            <if test="allCount != null">
                all_count,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="scopeType != null">
                #{scopeType,jdbcType=TINYINT},
            </if>
            <if test="scopeKey != null">
                #{scopeKey,jdbcType=VARCHAR},
            </if>
            <if test="allCount != null">
                #{allCount,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=BIGINT},
            </if>
            <if test="scopeType != null">
                scope_type = #{scopeType,jdbcType=TINYINT},
            </if>
            <if test="scopeKey != null">
                scope_key = #{scopeKey,jdbcType=VARCHAR},
            </if>
            <if test="allCount != null">
                all_count = #{allCount,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateSaveNumberById">
        update publish_scope_info
        set import_save_number = import_save_number + #{saveNumber}
        where scope_key = #{excelId}
    </update>
    <update id="updateErrorNumberById">
        update publish_scope_info
        set import_error_number = import_error_number + #{errorNumber}
        where scope_key = #{excelId}
    </update>
</mapper>