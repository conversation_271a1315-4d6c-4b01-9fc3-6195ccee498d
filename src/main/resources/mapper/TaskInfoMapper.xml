<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.TaskInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.TaskInfo">
        <!--@mbg.generated-->
        <!--@Table task_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="biz_id" jdbcType="VARCHAR" property="bizId"/>
        <result column="extra_id" jdbcType="VARCHAR" property="extraId"/>
        <result column="engine_template_id" jdbcType="BIGINT" property="engineTemplateId"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="channel" jdbcType="TINYINT" property="channel"/>
        <result column="publish_time" jdbcType="BIGINT" property="publishTime"/>
        <result column="publish_scope" jdbcType="TINYINT" property="publishScope"/>
        <result column="task_status" jdbcType="TINYINT" property="taskStatus"/>
        <result column="title_cn" jdbcType="VARCHAR" property="titleCn"/>
        <result column="title_en" jdbcType="VARCHAR" property="titleEn"/>
        <result column="content_cn" jdbcType="LONGVARCHAR" property="contentCn"/>
        <result column="content_en" jdbcType="LONGVARCHAR" property="contentEn"/>
        <result column="error_log" jdbcType="LONGVARCHAR" property="errorLog"/>
        <result column="task_engine_status" jdbcType="TINYINT" property="taskEngineStatus"/>
        <result column="task_total_count" jdbcType="BIGINT" property="taskTotalCount"/>
        <result column="task_push_count" jdbcType="BIGINT" property="taskPushCount"/>
        <result column="task_push_all_time" jdbcType="BIGINT" property="taskPushAllTime"/>
        <result column="bpm_instance_id" jdbcType="VARCHAR" property="bpmInstanceId"/>
        <result column="task_read_count" jdbcType="BIGINT" property="taskReadCount"/>
        <result column="task_retract_count" jdbcType="BIGINT" property="taskRetractCount"/>
        <result column="parent_task_id" jdbcType="BIGINT" property="parentTaskId"/>
        <result column="publish_success_time" jdbcType="BIGINT" property="publishSuccessTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, biz_id, extra_id, engine_template_id, create_username, create_time, update_username,
        update_time, channel, publish_time, publish_scope, task_status, title_cn, title_en,
        content_cn, content_en, error_log, task_engine_status, task_total_count, task_push_count,
        task_push_all_time,bpm_instance_id,task_read_count,task_retract_count,parent_task_id,publish_success_time
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update task_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="biz_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.bizId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="extra_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.extraId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="engine_template_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.engineTemplateId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="channel = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="publish_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.publishTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="publish_scope = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.publishScope,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="task_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="title_cn = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.titleCn,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="title_en = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.titleEn,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="content_cn = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.contentCn,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="content_en = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.contentEn,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="error_log = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.errorLog,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="task_engine_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskEngineStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="task_total_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskTotalCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="task_push_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskPushCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="task_push_all_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskPushAllTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="bpm_instance_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.bpmInstanceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="task_read_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskReadCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="task_retract_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskRetractCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="parent_task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.parentTaskId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="publish_success_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.publishSuccessTime,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update task_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="biz_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.bizId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.bizId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="extra_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.extraId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.extraId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="engine_template_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.engineTemplateId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.engineTemplateId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="channel = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.channel != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="publish_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.publishTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.publishTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="publish_scope = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.publishScope != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.publishScope,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskStatus,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="title_cn = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.titleCn != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.titleCn,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="title_en = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.titleEn != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.titleEn,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="content_cn = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contentCn != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.contentCn,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="content_en = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contentEn != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.contentEn,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="error_log = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.errorLog != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.errorLog,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_engine_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskEngineStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskEngineStatus,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_total_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskTotalCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskTotalCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_push_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskPushCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskPushCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_push_all_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskPushAllTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskPushAllTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bpm_instance_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.bpmInstanceId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.bpmInstanceId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_read_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskReadCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskReadCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_retract_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskRetractCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskRetractCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="parent_task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.parentTaskId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.parentTaskId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="publish_success_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.publishSuccessTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.publishSuccessTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <select id="selectByBotIdAndStatus" resultMap="BaseResultMap">
        select ti.*
        from lark_task_info as lti
        join task_info ti on lti.task_id = ti.id
        where lti.bot_id=#{botId,jdbcType=BIGINT}
        and ti.task_status in
        <foreach close=")" collection="statusList" item="item" open="(" separator=",">
            #{item,jdbcType=TINYINT}
        </foreach>
    </select>
    <resultMap id="taskDetailCommon" type="com.mioffice.ums.admin.entity.vo.BaseTaskVO">
        <id column="id" jdbcType="BIGINT" property="taskId"/>
        <result column="channel" jdbcType="TINYINT" property="channel"/>
        <result column="publish_scope" jdbcType="TINYINT" property="publishScope"/>
        <result column="title_cn" jdbcType="VARCHAR" property="titleCn"/>
        <result column="title_en" jdbcType="VARCHAR" property="titleEn"/>
        <result column="content_cn" jdbcType="VARCHAR" property="contentCn"/>
        <result column="content_en" jdbcType="VARCHAR" property="contentEn"/>
        <result column="publish_time" jdbcType="BIGINT" property="publishTime"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="task_status" jdbcType="TINYINT" property="taskStatus"/>
        <result column="task_engine_status" jdbcType="TINYINT" property="taskEngineStatus"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="create_userEmail" jdbcType="VARCHAR" property="createUserEmail"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="extra_id" jdbcType="VARCHAR" property="extraId"/>
        <result column="parent_task_id" jdbcType="BIGINT" property="parentTaskId"/>

        <collection ofType="String" property="scopeKey">
            <result column="scope_key" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>
    <select id="selectTaskDetailCommon" resultMap="taskDetailCommon">
        select ti.id          as id,
               channel,
               publish_scope,
               title_cn,
               title_en,
               content_cn,
               content_en,
               publish_time,
               ti.create_time as create_time,
               ti.update_time as update_time,
               task_status,
               task_engine_status,
               create_username,
               name           as create_name,
               email          as create_userEmail,
               scope_key,
               extra_id,
               parent_task_id
        from (select *
              from task_info
              where id = #{taskId,jdbcType=BIGINT}) ti
                 join
             publish_scope_info psi
             on ti.id = psi.task_id
                 join
             employee_info
             on create_username = employee_info.username
    </select>
    <select id="selectListByMap" resultMap="BaseResultMap">
        select ti.*
        from task_info ti
        <if test="botManagerUsername != null and botManagerUsername != ''">
            left join lark_task_info lti on ti.id = lti.task_id
            join user_bot_info ubi on lti.bot_id = ubi.bot_id
        </if>
        <if test="userDeptId != null and userDeptId != ''">
            join employee_info ei on ti.create_username = ei.username
        </if>
        where ti.task_status = #{status,jdbcType=TINYINT}
        and ti.id in
        <foreach close=")" collection="taskIdList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
        <if test="botManagerUsername != null and botManagerUsername != ''">
            and ubi.username = #{botManagerUsername,jdbcType=VARCHAR}
            and ubi.user_type = 2
        </if>
        <if test="userDeptId != null and userDeptId != ''">
            and ei.mi_dept_level2 = #{userDeptId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectTaskIdSystemAdmin" resultType="java.lang.Long">
        select task_info.id
        from task_info
                 join
             employee_info ei
             on task_info.create_username = ei.username
        where mi_dept_level2 = #{deptId,jdbcType=VARCHAR}
        group by task_info.id
    </select>

    <select id="getPushScope" resultType="com.mioffice.ums.admin.entity.vo.BotDashboardVO$PushScopeSummary">
        select sum(if(t1.publish_scope = 1, 1, 0)) as allPushCount,
        sum(if(t1.publish_scope = 2, 1, 0)) as deptPushCount,
        sum(if(t1.publish_scope = 3, 1, 0)) as filterPushCount,
        sum(if(t1.publish_scope = 4, 1, 0)) as customPushCount,
        sum(if(t1.publish_scope = 5, 1, 0)) as groupPushCount,
        sum(if(t1.publish_scope = 1 or t1.publish_scope = 2 or t1.publish_scope = 3 or t1.publish_scope = 4 or
        t1.publish_scope = 5, 1, 0)) as totalPushCount
        from task_info t1
        <where>
            1 = 1
            <if test="taskIdList != null and taskIdList.size() != 0">
                and t1.id in
                <foreach close=")" collection="taskIdList" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and t1.publish_time &gt;= #{startTime,jdbcType=BIGINT}
            </if>
            <if test="endTime != null">
                and t1.publish_time &lt;= #{endTime,jdbcType=BIGINT}
            </if>
            and t1.task_status in
            <foreach close=")" collection="taskStatusList" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=TINYINT}
            </foreach>
        </where>
    </select>
    <resultMap id="MessageDashBoardMetaData" type="com.mioffice.ums.admin.entity.vo.MessageDashBoardVO">
        <result column="summary_total_count" jdbcType="BIGINT" property="summary.totalCount"/>
        <result column="summary_approving_count" jdbcType="BIGINT" property="summary.approvingCount"/>
        <result column="summary_approved_count" jdbcType="BIGINT" property="summary.approvedCount"/>
        <result column="summary_approve_back_count" jdbcType="BIGINT" property="summary.approveBackCount"/>
        <result column="summary_cancel_count" jdbcType="BIGINT" property="summary.cancelCount"/>
        <result column="summary_publishing_count" jdbcType="BIGINT" property="summary.publishingCount"/>
        <result column="summary_publish_failed_count" jdbcType="BIGINT" property="summary.publishFailedCount"/>
        <result column="summary_interrupt_count" jdbcType="BIGINT" property="summary.interruptCount"/>
        <result column="summary_publish_success_count" jdbcType="BIGINT" property="summary.publishSuccessCount"/>
        <result column="summary_publish_exception_count" jdbcType="BIGINT" property="summary.publishExceptionCount"/>
        <result column="summary_bot_stop_count" jdbcType="BIGINT" property="summary.botStopCount"/>
    </resultMap>
    <select id="selectMessageDashBoardCount" resultMap="MessageDashBoardMetaData">
        select
        count(task_status != 1 or null) as summary_total_count,
        count(task_status = 2 or null) as summary_approving_count,
        count(task_status = 4 or null) as summary_approved_count,
        count(task_status = 3 or null) as summary_approve_back_count,
        count(task_status = 7 or null) as summary_cancel_count,
        count(task_status = 5 or null) as summary_publishing_count,
        count(task_status = 9 or null) as summary_publish_failed_count,
        count(task_status = 6 or null) as summary_interrupt_count,
        count(task_status = 8 or null) as summary_publish_success_count,
        count(task_status = 10 or null) as summary_publish_exception_count,
        count(task_status = 11 or null) as summary_bot_stop_count
        from
        task_info
        join
        employee_info
        on task_info.create_username = employee_info.username
        where
        task_info.create_time &gt; #{start,jdbcType=BIGINT}
        and
        task_info.create_time <![CDATA[ < ]]> #{end,jdbcType=BIGINT}
        <if test="deptIds != null and deptIds.size() != 0">
            and employee_info.mi_dept_level2 in
            <foreach close=")" collection="deptIds" item="deptId" open="(" separator=", ">
                #{deptId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="publishUsernames != null and publishUsernames.size() != 0">
            and task_info.create_username in
            <foreach close=")" collection="publishUsernames" item="item" open="(" separator=", ">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <resultMap id="CostTimeFromSqlByDay" type="com.mioffice.ums.admin.entity.bo.CostTimeFromSqlBO">
        <id column="date_group" jdbcType="VARCHAR" property="time"/>
        <result column="lark_count" jdbcType="BIGINT" property="lark.count"/>
        <result column="email_count" jdbcType="BIGINT" property="email.count"/>
        <result column="message_count" jdbcType="BIGINT" property="message.count"/>
    </resultMap>
    <resultMap id="DateAndExtraIdList" type="com.mioffice.ums.admin.entity.bo.DateAndExtraIdListBO">
        <result column="date_group_tmp" jdbcType="VARCHAR" property="date"/>
        <result column="channel" jdbcType="TINYINT" property="channel"/>
        <collection ofType="java.lang.String" property="extraIdList">
            <result column="extra_id" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>
    <select id="selectExtraIdListByDay" resultMap="DateAndExtraIdList">
        select
        date_group_tmp,
        extra_id,
        channel
        from
        (
        select FROM_UNIXTIME(task_info.create_time/1000,'%Y-%m-%d') as date_group_tmp,
        extra_id, channel
        from task_info
        join
        employee_info
        on task_info.create_username = employee_info.username
        where
        task_info.task_status in (5,6,8,10)
        and
        task_info.create_time &gt; #{start,jdbcType=BIGINT}
        and
        task_info.create_time <![CDATA[ < ]]> #{end,jdbcType=BIGINT}
        <if test="deptIds != null and deptIds.size() != 0">
            and employee_info.mi_dept_level2 in
            <foreach close=")" collection="deptIds" item="deptId" open="(" separator=", ">
                #{deptId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="publishUsernames != null and publishUsernames.size() != 0">
            and task_info.create_username in
            <foreach close=")" collection="publishUsernames" item="item" open="(" separator=", ">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        group by extra_id, date_group_tmp
        ) pub
        where date_group_tmp in
        <foreach close=")" collection="dateGroupList" item="dateGroup" open="(" separator=", ">
            #{dateGroup, jdbcType=VARCHAR}
        </foreach>
        group by extra_id, date_group_tmp
    </select>
    <select id="selectMessageDashBoardGroupByDay" resultMap="CostTimeFromSqlByDay">
        select FROM_UNIXTIME(task_info.create_time/1000,'%Y-%m-%d') as date_group,
        count(channel = 1 or null) as lark_count,
        count(channel = 2 or null) as email_count,
        count(channel = 3 or null) as message_count
        from task_info
        join
        employee_info
        on task_info.create_username = employee_info.username
        where
        task_info.task_status in (5,6,8,10)
        and
        task_info.create_time &gt; #{start,jdbcType=BIGINT}
        and
        task_info.create_time <![CDATA[ < ]]> #{end,jdbcType=BIGINT}
        <if test="deptIds != null and deptIds.size() != 0">
            and employee_info.mi_dept_level2 in
            <foreach close=")" collection="deptIds" item="deptId" open="(" separator=", ">
                #{deptId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="publishUsernames != null and publishUsernames.size() != 0">
            and task_info.create_username in
            <foreach close=")" collection="publishUsernames" item="item" open="(" separator=", ">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        group by date_group;
    </select>
    <resultMap id="CostTimeFromSqlChannelCostTime" type="com.mioffice.ums.admin.entity.bo.ChannelCostTimeFromSqlBO">
        <result column="channel" jdbcType="TINYINT" property="channel"/>
        <result column="all_count" jdbcType="BIGINT" property="all.count"/>
        <result column="dept_count" jdbcType="BIGINT" property="dept.count"/>
        <result column="custom_count" jdbcType="BIGINT" property="custom.count"/>
        <result column="choose_count" jdbcType="BIGINT" property="choose.count"/>
        <result column="larkGroup_count" jdbcType="BIGINT" property="larkGroup.count"/>
    </resultMap>
    <resultMap id="ChannelAndExtraIdList" type="com.mioffice.ums.admin.entity.bo.ChannelAndExtraIdListBO">
        <result column="channel" jdbcType="TINYINT" property="channel"/>
        <result column="publish_scope" jdbcType="TINYINT" property="publishScope"/>
        <collection ofType="java.lang.String" property="extraIdList">
            <result column="extra_id" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>
    <select id="selectExtraIdListChannelCostTime" resultMap="ChannelAndExtraIdList">
        select channel,
        extra_id,
        publish_scope
        from
        (select channel,
        extra_id,
        publish_scope
        from task_info
        join
        employee_info
        on task_info.create_username = employee_info.username
        where
        task_info.task_status in (5,6,8,10)
        and
        task_info.create_time &gt; #{start,jdbcType=BIGINT}
        and
        task_info.create_time <![CDATA[ < ]]> #{end,jdbcType=BIGINT}
        <if test="deptIds != null and deptIds.size() != 0">
            and employee_info.mi_dept_level2 in
            <foreach close=")" collection="deptIds" item="deptId" open="(" separator=", ">
                #{deptId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="publishUsernames != null and publishUsernames.size() != 0">
            and task_info.create_username in
            <foreach close=")" collection="publishUsernames" item="item" open="(" separator=", ">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        group by channel, extra_id) pub
        where
        channel in
        <foreach close=")" collection="channelList" item="channel" open="(" separator=", ">
            #{channel, jdbcType=VARCHAR}
        </foreach>
        group by channel, extra_id
    </select>
    <select id="selectMessageDashBoardChannelCostTime" resultMap="CostTimeFromSqlChannelCostTime">
        select channel,
        count(publish_scope = 1 or null) as all_count,
        count(publish_scope = 2 or null) as dept_count,
        count(publish_scope = 3 or null) as choose_count,
        count(publish_scope = 4 or null) as custom_count,
        count(publish_scope = 5 or null) as larkGroup_count
        from task_info
        join
        employee_info
        on task_info.create_username = employee_info.username
        where
        task_info.task_status in (5,6,8,10)
        and
        task_info.create_time &gt; #{start,jdbcType=BIGINT}
        and
        task_info.create_time <![CDATA[ < ]]> #{end,jdbcType=BIGINT}
        <if test="deptIds != null and deptIds.size() != 0">
            and employee_info.mi_dept_level2 in
            <foreach close=")" collection="deptIds" item="deptId" open="(" separator=", ">
                #{deptId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="publishUsernames != null and publishUsernames.size() != 0">
            and task_info.create_username in
            <foreach close=")" collection="publishUsernames" item="item" open="(" separator=", ">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        group by channel;
    </select>
    <resultMap id="CostTimeFromSqlByMonth" type="com.mioffice.ums.admin.entity.bo.CostTimeFromSqlBO">
        <id column="date_group" jdbcType="VARCHAR" property="time"/>
        <result column="lark_count" jdbcType="BIGINT" property="lark.count"/>
        <result column="email_count" jdbcType="BIGINT" property="email.count"/>
        <result column="message_count" jdbcType="BIGINT" property="message.count"/>

    </resultMap>
    <select id="selectExtraIdListByMonth" resultMap="DateAndExtraIdList">
        select
        date_group_tmp,
        extra_id,
        channel
        from
        (
        select FROM_UNIXTIME(task_info.create_time/1000,'%Y-%m') as date_group_tmp,
        extra_id, channel
        from task_info
        join
        employee_info
        on task_info.create_username = employee_info.username
        where
        task_info.task_status in (5,6,8,10)
        and
        task_info.create_time &gt; #{start,jdbcType=BIGINT}
        and
        task_info.create_time <![CDATA[ < ]]> #{end,jdbcType=BIGINT}
        <if test="deptIds != null and deptIds.size() != 0">
            and employee_info.mi_dept_level2 in
            <foreach close=")" collection="deptIds" item="deptId" open="(" separator=", ">
                #{deptId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="publishUsernames != null and publishUsernames.size() != 0">
            and task_info.create_username in
            <foreach close=")" collection="publishUsernames" item="item" open="(" separator=", ">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        group by extra_id, date_group_tmp
        ) pub
        where date_group_tmp in
        <foreach close=")" collection="dateGroupList" item="dateGroup" open="(" separator=", ">
            #{dateGroup, jdbcType=VARCHAR}
        </foreach>
        group by extra_id, date_group_tmp
    </select>
    <select id="selectMessageDashBoardGroupByMonth" resultMap="CostTimeFromSqlByMonth">
        select FROM_UNIXTIME(task_info.create_time/1000,'%Y-%m') as date_group,
        count(channel = 1 or null) as lark_count,
        count(channel = 2 or null) as email_count,
        count(channel = 3 or null) as message_count
        from task_info
        join
        employee_info
        on task_info.create_username = employee_info.username
        where
        task_info.task_status in (5,6,8,10)
        and
        task_info.create_time &gt; #{start,jdbcType=BIGINT}
        and
        task_info.create_time <![CDATA[ < ]]> #{end,jdbcType=BIGINT}
        <if test="deptIds != null and deptIds.size() != 0">
            and employee_info.mi_dept_level2 in
            <foreach close=")" collection="deptIds" item="deptId" open="(" separator=", ">
                #{deptId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="publishUsernames != null and publishUsernames.size() != 0">
            and task_info.create_username in
            <foreach close=")" collection="publishUsernames" item="item" open="(" separator=", ">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        group by date_group;
    </select>
    <resultMap id="MessageStatusStatistics" type="com.mioffice.ums.admin.entity.vo.MessageDashBoardVO">
        <result column="all_total_count" jdbcType="BIGINT" property="messageStatusStatistics.all.totalCount"/>
        <result column="all_approving_count" jdbcType="BIGINT" property="messageStatusStatistics.all.approvingCount"/>
        <result column="all_approved_count" jdbcType="BIGINT" property="messageStatusStatistics.all.approvedCount"/>
        <result column="all_approve_back_count" jdbcType="BIGINT"
                property="messageStatusStatistics.all.approveBackCount"/>
        <result column="all_cancel_count" jdbcType="BIGINT" property="messageStatusStatistics.all.cancelCount"/>
        <result column="all_publishing_count" jdbcType="BIGINT" property="messageStatusStatistics.all.publishingCount"/>
        <result column="all_publish_failed_count" jdbcType="BIGINT"
                property="messageStatusStatistics.all.publishFailedCount"/>
        <result column="all_interrupt_count" jdbcType="BIGINT" property="messageStatusStatistics.all.interruptCount"/>
        <result column="all_publish_success_count" jdbcType="BIGINT"
                property="messageStatusStatistics.all.publishSuccessCount"/>
        <result column="all_publish_exception_count" jdbcType="BIGINT"
                property="messageStatusStatistics.all.publishExceptionCount"/>
        <result column="all_bot_stop_count" jdbcType="BIGINT" property="messageStatusStatistics.all.botStopCount"/>
        <result column="dept_total_count" jdbcType="BIGINT" property="messageStatusStatistics.dept.totalCount"/>
        <result column="dept_approving_count" jdbcType="BIGINT" property="messageStatusStatistics.dept.approvingCount"/>
        <result column="dept_approved_count" jdbcType="BIGINT" property="messageStatusStatistics.dept.approvedCount"/>
        <result column="dept_approve_back_count" jdbcType="BIGINT"
                property="messageStatusStatistics.dept.approveBackCount"/>
        <result column="dept_cancel_count" jdbcType="BIGINT" property="messageStatusStatistics.dept.cancelCount"/>
        <result column="dept_publishing_count" jdbcType="BIGINT"
                property="messageStatusStatistics.dept.publishingCount"/>
        <result column="dept_publish_failed_count" jdbcType="BIGINT"
                property="messageStatusStatistics.dept.publishFailedCount"/>
        <result column="dept_interrupt_count" jdbcType="BIGINT" property="messageStatusStatistics.dept.interruptCount"/>
        <result column="dept_publish_success_count" jdbcType="BIGINT"
                property="messageStatusStatistics.dept.publishSuccessCount"/>
        <result column="dept_publish_exception_count" jdbcType="BIGINT"
                property="messageStatusStatistics.dept.publishExceptionCount"/>
        <result column="dept_bot_stop_count" jdbcType="BIGINT" property="messageStatusStatistics.dept.botStopCount"/>
        <result column="custom_total_count" jdbcType="BIGINT" property="messageStatusStatistics.custom.totalCount"/>
        <result column="custom_approving_count" jdbcType="BIGINT"
                property="messageStatusStatistics.custom.approvingCount"/>
        <result column="custom_approved_count" jdbcType="BIGINT"
                property="messageStatusStatistics.custom.approvedCount"/>
        <result column="custom_approve_back_count" jdbcType="BIGINT"
                property="messageStatusStatistics.custom.approveBackCount"/>
        <result column="custom_cancel_count" jdbcType="BIGINT" property="messageStatusStatistics.custom.cancelCount"/>
        <result column="custom_publishing_count" jdbcType="BIGINT"
                property="messageStatusStatistics.custom.publishingCount"/>
        <result column="custom_publish_failed_count" jdbcType="BIGINT"
                property="messageStatusStatistics.custom.publishFailedCount"/>
        <result column="custom_interrupt_count" jdbcType="BIGINT"
                property="messageStatusStatistics.custom.interruptCount"/>
        <result column="custom_publish_success_count" jdbcType="BIGINT"
                property="messageStatusStatistics.custom.publishSuccessCount"/>
        <result column="custom_publish_exception_count" jdbcType="BIGINT"
                property="messageStatusStatistics.custom.publishExceptionCount"/>
        <result column="custom_bot_stop_count" jdbcType="BIGINT"
                property="messageStatusStatistics.custom.botStopCount"/>

        <result column="group_total_count" jdbcType="BIGINT" property="messageStatusStatistics.larkGroup.totalCount"/>
        <result column="group_approving_count" jdbcType="BIGINT"
                property="messageStatusStatistics.larkGroup.approvingCount"/>
        <result column="group_approved_count" jdbcType="BIGINT"
                property="messageStatusStatistics.larkGroup.approvedCount"/>
        <result column="group_approve_back_count" jdbcType="BIGINT"
                property="messageStatusStatistics.larkGroup.approveBackCount"/>
        <result column="group_cancel_count" jdbcType="BIGINT" property="messageStatusStatistics.larkGroup.cancelCount"/>
        <result column="group_publishing_count" jdbcType="BIGINT"
                property="messageStatusStatistics.larkGroup.publishingCount"/>
        <result column="group_publish_failed_count" jdbcType="BIGINT"
                property="messageStatusStatistics.larkGroup.publishFailedCount"/>
        <result column="group_interrupt_count" jdbcType="BIGINT"
                property="messageStatusStatistics.larkGroup.interruptCount"/>
        <result column="group_publish_success_count" jdbcType="BIGINT"
                property="messageStatusStatistics.larkGroup.publishSuccessCount"/>
        <result column="group_publish_exception_count" jdbcType="BIGINT"
                property="messageStatusStatistics.larkGroup.publishExceptionCount"/>
        <result column="group_bot_stop_count" jdbcType="BIGINT"
                property="messageStatusStatistics.larkGroup.botStopCount"/>

        <result column="choose_total_count" jdbcType="BIGINT" property="messageStatusStatistics.choose.totalCount"/>
        <result column="choose_approving_count" jdbcType="BIGINT"
                property="messageStatusStatistics.choose.approvingCount"/>
        <result column="choose_approved_count" jdbcType="BIGINT"
                property="messageStatusStatistics.choose.approvedCount"/>
        <result column="choose_approve_back_count" jdbcType="BIGINT"
                property="messageStatusStatistics.choose.approveBackCount"/>
        <result column="choose_cancel_count" jdbcType="BIGINT" property="messageStatusStatistics.choose.cancelCount"/>
        <result column="choose_publishing_count" jdbcType="BIGINT"
                property="messageStatusStatistics.choose.publishingCount"/>
        <result column="choose_publish_failed_count" jdbcType="BIGINT"
                property="messageStatusStatistics.choose.publishFailedCount"/>
        <result column="choose_interrupt_count" jdbcType="BIGINT"
                property="messageStatusStatistics.choose.interruptCount"/>
        <result column="choose_publish_success_count" jdbcType="BIGINT"
                property="messageStatusStatistics.choose.publishSuccessCount"/>
        <result column="choose_publish_exception_count" jdbcType="BIGINT"
                property="messageStatusStatistics.choose.publishExceptionCount"/>
        <result column="choose_bot_stop_count" jdbcType="BIGINT"
                property="messageStatusStatistics.choose.botStopCount"/>
    </resultMap>
    <select id="selectMessageStatusStatistics" resultMap="MessageStatusStatistics">
        select
        count((task_status != 1 and publish_scope = 1) or null) as all_total_count,
        count((task_status = 2 and publish_scope = 1) or null) as all_approving_count,
        count((task_status = 4 and publish_scope = 1) or null) as all_approved_count,
        count((task_status = 3 and publish_scope = 1) or null) as all_approve_back_count,
        count((task_status = 7 and publish_scope = 1) or null) as all_cancel_count,
        count((task_status = 5 and publish_scope = 1) or null) as all_publishing_count,
        count((task_status = 9 and publish_scope = 1) or null) as all_publish_failed_count,
        count((task_status = 6 and publish_scope = 1) or null) as all_interrupt_count,
        count((task_status = 8 and publish_scope = 1) or null) as all_publish_success_count,
        count((task_status = 10 and publish_scope = 1) or null) as all_publish_exception_count,
        count((task_status = 11 and publish_scope = 1) or null) as all_bot_stop_count,
        count((task_status != 1 and publish_scope = 2) or null) as dept_total_count,
        count((task_status = 2 and publish_scope = 2) or null) as dept_approving_count,
        count((task_status = 4 and publish_scope = 2) or null) as dept_approved_count,
        count((task_status = 3 and publish_scope = 2) or null) as dept_approve_back_count,
        count((task_status = 7 and publish_scope = 2) or null) as dept_cancel_count,
        count((task_status = 5 and publish_scope = 2) or null) as dept_publishing_count,
        count((task_status = 9 and publish_scope = 2) or null) as dept_publish_failed_count,
        count((task_status = 6 and publish_scope = 2) or null) as dept_interrupt_count,
        count((task_status = 8 and publish_scope = 2) or null) as dept_publish_success_count,
        count((task_status = 10 and publish_scope = 2) or null) as dept_publish_exception_count,
        count((task_status = 11 and publish_scope = 2) or null) as dept_bot_stop_count,
        count((task_status != 1 and publish_scope = 4) or null) as custom_total_count,
        count((task_status = 2 and publish_scope = 4) or null) as custom_approving_count,
        count((task_status = 4 and publish_scope = 4) or null) as custom_approved_count,
        count((task_status = 3 and publish_scope = 4) or null) as custom_approve_back_count,
        count((task_status = 7 and publish_scope = 4) or null) as custom_cancel_count,
        count((task_status = 5 and publish_scope = 4) or null) as custom_publishing_count,
        count((task_status = 9 and publish_scope = 4) or null) as custom_publish_failed_count,
        count((task_status = 6 and publish_scope = 4) or null) as custom_interrupt_count,
        count((task_status = 8 and publish_scope = 4) or null) as custom_publish_success_count,
        count((task_status = 10 and publish_scope = 4) or null) as custom_publish_exception_count,
        count((task_status = 11 and publish_scope = 4) or null) as custom_bot_stop_count,
        count((task_status != 1 and publish_scope = 3) or null) as choose_total_count,
        count((task_status = 2 and publish_scope = 3) or null) as choose_approving_count,
        count((task_status = 4 and publish_scope = 3) or null) as choose_approved_count,
        count((task_status = 3 and publish_scope = 3) or null) as choose_approve_back_count,
        count((task_status = 7 and publish_scope = 3) or null) as choose_cancel_count,
        count((task_status = 5 and publish_scope = 3) or null) as choose_publishing_count,
        count((task_status = 9 and publish_scope = 3) or null) as choose_publish_failed_count,
        count((task_status = 6 and publish_scope = 3) or null) as choose_interrupt_count,
        count((task_status = 8 and publish_scope = 3) or null) as choose_publish_success_count,
        count((task_status = 10 and publish_scope = 3) or null) as choose_publish_exception_count,
        count((task_status = 11 and publish_scope = 3) or null) as choose_bot_stop_count,
        count((task_status != 1 and publish_scope = 5) or null) as group_total_count,
        count((task_status = 2 and publish_scope = 5) or null) as group_approving_count,
        count((task_status = 4 and publish_scope = 5) or null) as group_approved_count,
        count((task_status = 3 and publish_scope = 5) or null) as group_approve_back_count,
        count((task_status = 7 and publish_scope = 5) or null) as group_cancel_count,
        count((task_status = 5 and publish_scope = 5) or null) as group_publishing_count,
        count((task_status = 9 and publish_scope = 5) or null) as group_publish_failed_count,
        count((task_status = 6 and publish_scope = 5) or null) as group_interrupt_count,
        count((task_status = 8 and publish_scope = 5) or null) as group_publish_success_count,
        count((task_status = 10 and publish_scope = 5) or null) as group_publish_exception_count,
        count((task_status = 11 and publish_scope = 5) or null) as group_bot_stop_count
        from
        task_info
        join
        employee_info
        on task_info.create_username = employee_info.username
        where
        task_info.create_time &gt; #{start,jdbcType=BIGINT}
        and
        task_info.create_time <![CDATA[ < ]]> #{end,jdbcType=BIGINT}
        <if test="deptIds != null and deptIds.size() != 0">
            and employee_info.mi_dept_level2 in
            <foreach close=")" collection="deptIds" item="deptId" open="(" separator=", ">
                #{deptId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="publishUsernames != null and publishUsernames.size() != 0">
            and task_info.create_username in
            <foreach close=")" collection="publishUsernames" item="item" open="(" separator=", ">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

</mapper>
