<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.ApprovalInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.ApprovalInfo">
        <!--@mbg.generated-->
        <!--@Table approval_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="channel" jdbcType="TINYINT" property="channel"/>
        <result column="threshold" jdbcType="BIGINT" property="threshold"/>
        <result column="flow_chart_url" jdbcType="LONGVARCHAR" property="flowChartUrl"/>
        <result column="approval_meta_node" jdbcType="LONGVARCHAR" property="approvalMetaNode"/>
        <result column="approval_process_node" jdbcType="LONGVARCHAR" property="approvalProcessNode"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="bot_id" jdbcType="BIGINT" property="botId"/>
        <result column="base_threshold" jdbcType="BIGINT" property="baseThreshold"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, channel, threshold, flow_chart_url, approval_meta_node, approval_process_node,
        update_username, create_username, create_time, update_time, bot_id, base_threshold
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update approval_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="channel = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="threshold = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.threshold,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="flow_chart_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.flowChartUrl,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="approval_meta_node = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.approvalMetaNode,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="approval_process_node = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.approvalProcessNode,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="bot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.botId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="base_threshold = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.baseThreshold,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update approval_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="channel = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.channel != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="threshold = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.threshold != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.threshold,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="flow_chart_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.flowChartUrl != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.flowChartUrl,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="approval_meta_node = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.approvalMetaNode != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.approvalMetaNode,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="approval_process_node = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.approvalProcessNode != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.approvalProcessNode,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bot_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.botId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.botId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="base_threshold = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.baseThreshold != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.baseThreshold,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into approval_info
        (channel, threshold, flow_chart_url, approval_meta_node, approval_process_node, update_username,
        create_username, create_time, update_time, bot_id, base_threshold)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.channel,jdbcType=TINYINT}, #{item.threshold,jdbcType=BIGINT},
            #{item.flowChartUrl,jdbcType=LONGVARCHAR},
            #{item.approvalMetaNode,jdbcType=LONGVARCHAR}, #{item.approvalProcessNode,jdbcType=LONGVARCHAR},
            #{item.updateUsername,jdbcType=VARCHAR}, #{item.createUsername,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, #{item.botId,jdbcType=BIGINT},
            #{item.baseThreshold,jdbcType=BIGINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.ApprovalInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into approval_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            channel,
            threshold,
            flow_chart_url,
            approval_meta_node,
            approval_process_node,
            update_username,
            create_username,
            create_time,
            update_time,
            bot_id,
            base_threshold,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{channel,jdbcType=TINYINT},
            #{threshold,jdbcType=BIGINT},
            #{flowChartUrl,jdbcType=LONGVARCHAR},
            #{approvalMetaNode,jdbcType=LONGVARCHAR},
            #{approvalProcessNode,jdbcType=LONGVARCHAR},
            #{updateUsername,jdbcType=VARCHAR},
            #{createUsername,jdbcType=VARCHAR},
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
            #{botId,jdbcType=BIGINT},
            #{baseThreshold,jdbcType=BIGINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            channel = #{channel,jdbcType=TINYINT},
            threshold = #{threshold,jdbcType=BIGINT},
            flow_chart_url = #{flowChartUrl,jdbcType=LONGVARCHAR},
            approval_meta_node = #{approvalMetaNode,jdbcType=LONGVARCHAR},
            approval_process_node = #{approvalProcessNode,jdbcType=LONGVARCHAR},
            update_username = #{updateUsername,jdbcType=VARCHAR},
            create_username = #{createUsername,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
            bot_id = #{botId,jdbcType=BIGINT},
            base_threshold = #{baseThreshold,jdbcType=BIGINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.ApprovalInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into approval_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="channel != null">
                channel,
            </if>
            <if test="threshold != null">
                threshold,
            </if>
            <if test="flowChartUrl != null">
                flow_chart_url,
            </if>
            <if test="approvalMetaNode != null">
                approval_meta_node,
            </if>
            <if test="approvalProcessNode != null">
                approval_process_node,
            </if>
            <if test="updateUsername != null">
                update_username,
            </if>
            <if test="createUsername != null">
                create_username,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="botId != null">
                bot_id,
            </if>
            <if test="baseThreshold != null">
                base_threshold,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="channel != null">
                #{channel,jdbcType=TINYINT},
            </if>
            <if test="threshold != null">
                #{threshold,jdbcType=BIGINT},
            </if>
            <if test="flowChartUrl != null">
                #{flowChartUrl,jdbcType=LONGVARCHAR},
            </if>
            <if test="approvalMetaNode != null">
                #{approvalMetaNode,jdbcType=LONGVARCHAR},
            </if>
            <if test="approvalProcessNode != null">
                #{approvalProcessNode,jdbcType=LONGVARCHAR},
            </if>
            <if test="updateUsername != null">
                #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="createUsername != null">
                #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="botId != null">
                #{botId,jdbcType=BIGINT},
            </if>
            <if test="baseThreshold != null">
                #{baseThreshold,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="channel != null">
                channel = #{channel,jdbcType=TINYINT},
            </if>
            <if test="threshold != null">
                threshold = #{threshold,jdbcType=BIGINT},
            </if>
            <if test="flowChartUrl != null">
                flow_chart_url = #{flowChartUrl,jdbcType=LONGVARCHAR},
            </if>
            <if test="approvalMetaNode != null">
                approval_meta_node = #{approvalMetaNode,jdbcType=LONGVARCHAR},
            </if>
            <if test="approvalProcessNode != null">
                approval_process_node = #{approvalProcessNode,jdbcType=LONGVARCHAR},
            </if>
            <if test="updateUsername != null">
                update_username = #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="createUsername != null">
                create_username = #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="botId != null">
                bot_id = #{botId,jdbcType=BIGINT},
            </if>
            <if test="baseThreshold != null">
                base_threshold = #{baseThreshold,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
</mapper>