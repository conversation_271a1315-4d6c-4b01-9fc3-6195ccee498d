<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.EarlyWarningInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.EarlyWarningInfo">
        <!--@mbg.generated-->
        <!--@Table early_warning_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="extra_id" jdbcType="VARCHAR" property="extraId"/>
        <result column="error_type" jdbcType="TINYINT" property="errorType"/>
        <result column="all_count" jdbcType="BIGINT" property="allCount"/>
        <result column="cost_time" jdbcType="BIGINT" property="costTime"/>
        <result column="out_range_count" jdbcType="BIGINT" property="outRangeCount"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, task_id, extra_id, error_type, all_count, cost_time, out_range_count, create_username,
        create_time, update_username, update_time
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update early_warning_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="extra_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.extraId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="error_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.errorType,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="all_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.allCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="cost_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.costTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="out_range_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.outRangeCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update early_warning_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="extra_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.extraId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.extraId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="error_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.errorType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.errorType,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="all_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.allCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.allCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="cost_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.costTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.costTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="out_range_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.outRangeCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.outRangeCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into early_warning_info
        (task_id, extra_id, error_type, all_count, cost_time, out_range_count, create_username,
        create_time, update_username, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.taskId,jdbcType=BIGINT}, #{item.extraId,jdbcType=VARCHAR}, #{item.errorType,jdbcType=TINYINT},
            #{item.allCount,jdbcType=BIGINT}, #{item.costTime,jdbcType=BIGINT}, #{item.outRangeCount,jdbcType=BIGINT},
            #{item.createUsername,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT},
            #{item.updateUsername,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=BIGINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.EarlyWarningInfo"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into early_warning_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            task_id,
            extra_id,
            error_type,
            all_count,
            cost_time,
            out_range_count,
            create_username,
            create_time,
            update_username,
            update_time,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{taskId,jdbcType=BIGINT},
            #{extraId,jdbcType=VARCHAR},
            #{errorType,jdbcType=TINYINT},
            #{allCount,jdbcType=BIGINT},
            #{costTime,jdbcType=BIGINT},
            #{outRangeCount,jdbcType=BIGINT},
            #{createUsername,jdbcType=VARCHAR},
            #{createTime,jdbcType=BIGINT},
            #{updateUsername,jdbcType=VARCHAR},
            #{updateTime,jdbcType=BIGINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            task_id = #{taskId,jdbcType=BIGINT},
            extra_id = #{extraId,jdbcType=VARCHAR},
            error_type = #{errorType,jdbcType=TINYINT},
            all_count = #{allCount,jdbcType=BIGINT},
            cost_time = #{costTime,jdbcType=BIGINT},
            out_range_count = #{outRangeCount,jdbcType=BIGINT},
            create_username = #{createUsername,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=BIGINT},
            update_username = #{updateUsername,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=BIGINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.EarlyWarningInfo"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into early_warning_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="extraId != null">
                extra_id,
            </if>
            <if test="errorType != null">
                error_type,
            </if>
            <if test="allCount != null">
                all_count,
            </if>
            <if test="costTime != null">
                cost_time,
            </if>
            <if test="outRangeCount != null">
                out_range_count,
            </if>
            <if test="createUsername != null">
                create_username,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUsername != null">
                update_username,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="extraId != null">
                #{extraId,jdbcType=VARCHAR},
            </if>
            <if test="errorType != null">
                #{errorType,jdbcType=TINYINT},
            </if>
            <if test="allCount != null">
                #{allCount,jdbcType=BIGINT},
            </if>
            <if test="costTime != null">
                #{costTime,jdbcType=BIGINT},
            </if>
            <if test="outRangeCount != null">
                #{outRangeCount,jdbcType=BIGINT},
            </if>
            <if test="createUsername != null">
                #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateUsername != null">
                #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=BIGINT},
            </if>
            <if test="extraId != null">
                extra_id = #{extraId,jdbcType=VARCHAR},
            </if>
            <if test="errorType != null">
                error_type = #{errorType,jdbcType=TINYINT},
            </if>
            <if test="allCount != null">
                all_count = #{allCount,jdbcType=BIGINT},
            </if>
            <if test="costTime != null">
                cost_time = #{costTime,jdbcType=BIGINT},
            </if>
            <if test="outRangeCount != null">
                out_range_count = #{outRangeCount,jdbcType=BIGINT},
            </if>
            <if test="createUsername != null">
                create_username = #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateUsername != null">
                update_username = #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <select id="selectWarningCount" resultType="com.mioffice.ums.admin.entity.bo.AlarmCountBo">
        select from_unixtime(ewi.create_time / 1000, #{dateFormat,jdbcType=VARCHAR}) as createDate, count(*) as count
        from early_warning_info ewi
        join task_info ti on ti.id = ewi.task_id
        <if test="username != null and username != ''">
            left join lark_task_info lti on ewi.task_id = lti.task_id
            join user_bot_info ubi on lti.bot_id = ubi.bot_id
        </if>
        <if test="deptId != null and deptId != ''">
            join employee_info ei on ti.create_username = ei.username
        </if>
        where ewi.create_time between #{startTime,jdbcType=BIGINT} and #{endTime,jdbcType=BIGINT}
        <if test="username != null and username != ''">
            and ubi.username = #{username,jdbcType=VARCHAR}
            and ubi.user_type = 2
        </if>
        <if test="deptId != null and deptId != ''">
            and ei.mi_dept_level2 = #{deptId,jdbcType=VARCHAR}
        </if>
        group by createDate;
    </select>
    <select id="selectAlarmPage" resultType="com.mioffice.ums.admin.entity.bo.AlarmBo">
        select ewi.create_time as alarmTime,
        ti.id as taskId,
        ti.title_cn as titleCn,
        ei.username as createUsername,
        ei.name as createName,
        ti.channel as channel,
        ti.publish_scope as publishScope,
        ewi.all_count as totalCount,
        ewi.error_type as errorType,
        ewi.cost_time as costTime,
        ewi.out_range_count as outRangeCount
        from early_warning_info ewi
        join task_info ti on ti.id = ewi.task_id
        join employee_info ei on ei.username = ti.create_username
        <if test="username != null and username != ''">
            left join lark_task_info lti on ewi.task_id = lti.task_id
            join user_bot_info ubi on lti.bot_id = ubi.bot_id
        </if>
        where 1=1
        <if test="username != null and username != ''">
            and ubi.username = #{username,jdbcType=VARCHAR}
            and ubi.user_type = 2
        </if>
        <if test="deptId != null and deptId != ''">
            and ei.mi_dept_level2 = #{deptId,jdbcType=VARCHAR}
        </if>
        order by ewi.create_time desc
    </select>
</mapper>