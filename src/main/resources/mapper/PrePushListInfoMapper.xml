<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.PrePushListInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.PrePushListInfo">
        <!--@mbg.generated-->
        <!--@Table pre_push_list_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="belong_to" jdbcType="BIGINT" property="belongTo"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="default_flag" jdbcType="TINYINT" property="defaultFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, username, email, `name`, belong_to, update_username, create_username, create_time,
        update_time, default_flag
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update pre_push_list_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.username,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="email = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="belong_to = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.belongTo,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="default_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.defaultFlag,jdbcType=TINYINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update pre_push_list_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.username != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.username,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="email = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.email != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.name != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="belong_to = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.belongTo != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.belongTo,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="default_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.defaultFlag != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.defaultFlag,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into pre_push_list_info
        (username, email, `name`, belong_to, update_username, create_username, create_time,
        update_time, default_flag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.username,jdbcType=VARCHAR}, #{item.email,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR},
            #{item.belongTo,jdbcType=BIGINT}, #{item.updateUsername,jdbcType=VARCHAR},
            #{item.createUsername,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, #{item.defaultFlag,jdbcType=TINYINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.PrePushListInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into pre_push_list_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            username,
            email,
            `name`,
            belong_to,
            update_username,
            create_username,
            create_time,
            update_time,
            default_flag,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{username,jdbcType=VARCHAR},
            #{email,jdbcType=VARCHAR},
            #{name,jdbcType=VARCHAR},
            #{belongTo,jdbcType=BIGINT},
            #{updateUsername,jdbcType=VARCHAR},
            #{createUsername,jdbcType=VARCHAR},
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
            #{defaultFlag,jdbcType=TINYINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            username = #{username,jdbcType=VARCHAR},
            email = #{email,jdbcType=VARCHAR},
            `name` = #{name,jdbcType=VARCHAR},
            belong_to = #{belongTo,jdbcType=BIGINT},
            update_username = #{updateUsername,jdbcType=VARCHAR},
            create_username = #{createUsername,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
            default_flag = #{defaultFlag,jdbcType=TINYINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.PrePushListInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into pre_push_list_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="username != null">
                username,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="belongTo != null">
                belong_to,
            </if>
            <if test="updateUsername != null">
                update_username,
            </if>
            <if test="createUsername != null">
                create_username,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="defaultFlag != null">
                default_flag,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="username != null">
                #{username,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="belongTo != null">
                #{belongTo,jdbcType=BIGINT},
            </if>
            <if test="updateUsername != null">
                #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="createUsername != null">
                #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="defaultFlag != null">
                #{defaultFlag,jdbcType=TINYINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="username != null">
                username = #{username,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="belongTo != null">
                belong_to = #{belongTo,jdbcType=BIGINT},
            </if>
            <if test="updateUsername != null">
                update_username = #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="createUsername != null">
                create_username = #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="defaultFlag != null">
                default_flag = #{defaultFlag,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
</mapper>