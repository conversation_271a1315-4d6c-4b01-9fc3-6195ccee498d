<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.ReceiveMemberInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.ReceiveMemberInfo">
        <!--@mbg.generated-->
        <!--@Table receive_member_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_desc" jdbcType="VARCHAR" property="deptDesc"/>
        <result column="mi_dept_level2" jdbcType="VARCHAR" property="miDeptLevel2"/>
        <result column="mi_dept_level2_desc" jdbcType="VARCHAR" property="miDeptLevel2Desc"/>
        <result column="mi_dept_level3" jdbcType="VARCHAR" property="miDeptLevel3"/>
        <result column="mi_dept_level3_desc" jdbcType="VARCHAR" property="miDeptLevel3Desc"/>
        <result column="mi_dept_level4" jdbcType="VARCHAR" property="miDeptLevel4"/>
        <result column="mi_dept_level4_desc" jdbcType="VARCHAR" property="miDeptLevel4Desc"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="chat_id" jdbcType="VARCHAR" property="chatId"/>
        <result column="extra_content" jdbcType="LONGVARCHAR" property="extraContent"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, create_time, update_time, task_id, user_id, username, email, phone, dept_id,
        dept_desc, mi_dept_level2, mi_dept_level2_desc, mi_dept_level3, mi_dept_level3_desc,
        mi_dept_level4, mi_dept_level4_desc, `name`, chat_id, extra_content
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update receive_member_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.username,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="email = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.phone,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="dept_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deptDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level2 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel2,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level2_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel2Desc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level3 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel3,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level3_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel3Desc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level4 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel4,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level4_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel4Desc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="chat_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.chatId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="extra_content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.extraContent,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into receive_member_info
        (create_time, update_time, task_id, user_id, username, email, phone, dept_id, dept_desc,
        mi_dept_level2, mi_dept_level2_desc, mi_dept_level3, mi_dept_level3_desc, mi_dept_level4,
        mi_dept_level4_desc, `name`, chat_id, extra_content)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, #{item.taskId,jdbcType=BIGINT},
            #{item.userId,jdbcType=VARCHAR}, #{item.username,jdbcType=VARCHAR}, #{item.email,jdbcType=VARCHAR},
            #{item.phone,jdbcType=VARCHAR}, #{item.deptId,jdbcType=VARCHAR}, #{item.deptDesc,jdbcType=VARCHAR},
            #{item.miDeptLevel2,jdbcType=VARCHAR}, #{item.miDeptLevel2Desc,jdbcType=VARCHAR},
            #{item.miDeptLevel3,jdbcType=VARCHAR}, #{item.miDeptLevel3Desc,jdbcType=VARCHAR},
            #{item.miDeptLevel4,jdbcType=VARCHAR}, #{item.miDeptLevel4Desc,jdbcType=VARCHAR},
            #{item.name,jdbcType=VARCHAR}, #{item.chatId,jdbcType=VARCHAR}, #{item.extraContent,jdbcType=LONGVARCHAR}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.ReceiveMemberInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into receive_member_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            create_time,
            update_time,
            task_id,
            user_id,
            username,
            email,
            phone,
            dept_id,
            dept_desc,
            mi_dept_level2,
            mi_dept_level2_desc,
            mi_dept_level3,
            mi_dept_level3_desc,
            mi_dept_level4,
            mi_dept_level4_desc,
            `name`,
            chat_id,
            extra_content,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
            #{taskId,jdbcType=BIGINT},
            #{userId,jdbcType=VARCHAR},
            #{username,jdbcType=VARCHAR},
            #{email,jdbcType=VARCHAR},
            #{phone,jdbcType=VARCHAR},
            #{deptId,jdbcType=VARCHAR},
            #{deptDesc,jdbcType=VARCHAR},
            #{miDeptLevel2,jdbcType=VARCHAR},
            #{miDeptLevel2Desc,jdbcType=VARCHAR},
            #{miDeptLevel3,jdbcType=VARCHAR},
            #{miDeptLevel3Desc,jdbcType=VARCHAR},
            #{miDeptLevel4,jdbcType=VARCHAR},
            #{miDeptLevel4Desc,jdbcType=VARCHAR},
            #{name,jdbcType=VARCHAR},
            #{chatId,jdbcType=VARCHAR},
            #{extraContent,jdbcType=LONGVARCHAR},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
            task_id = #{taskId,jdbcType=BIGINT},
            user_id = #{userId,jdbcType=VARCHAR},
            username = #{username,jdbcType=VARCHAR},
            email = #{email,jdbcType=VARCHAR},
            phone = #{phone,jdbcType=VARCHAR},
            dept_id = #{deptId,jdbcType=VARCHAR},
            dept_desc = #{deptDesc,jdbcType=VARCHAR},
            mi_dept_level2 = #{miDeptLevel2,jdbcType=VARCHAR},
            mi_dept_level2_desc = #{miDeptLevel2Desc,jdbcType=VARCHAR},
            mi_dept_level3 = #{miDeptLevel3,jdbcType=VARCHAR},
            mi_dept_level3_desc = #{miDeptLevel3Desc,jdbcType=VARCHAR},
            mi_dept_level4 = #{miDeptLevel4,jdbcType=VARCHAR},
            mi_dept_level4_desc = #{miDeptLevel4Desc,jdbcType=VARCHAR},
            `name` = #{name,jdbcType=VARCHAR},
            chat_id = #{chatId,jdbcType=VARCHAR},
            extra_content = #{extraContent,jdbcType=LONGVARCHAR},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.ReceiveMemberInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into receive_member_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="username != null">
                username,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="deptDesc != null">
                dept_desc,
            </if>
            <if test="miDeptLevel2 != null">
                mi_dept_level2,
            </if>
            <if test="miDeptLevel2Desc != null">
                mi_dept_level2_desc,
            </if>
            <if test="miDeptLevel3 != null">
                mi_dept_level3,
            </if>
            <if test="miDeptLevel3Desc != null">
                mi_dept_level3_desc,
            </if>
            <if test="miDeptLevel4 != null">
                mi_dept_level4,
            </if>
            <if test="miDeptLevel4Desc != null">
                mi_dept_level4_desc,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="chatId != null">
                chat_id,
            </if>
            <if test="extraContent != null">
                extra_content,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="username != null">
                #{username,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="deptDesc != null">
                #{deptDesc,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel2 != null">
                #{miDeptLevel2,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel2Desc != null">
                #{miDeptLevel2Desc,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel3 != null">
                #{miDeptLevel3,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel3Desc != null">
                #{miDeptLevel3Desc,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel4 != null">
                #{miDeptLevel4,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel4Desc != null">
                #{miDeptLevel4Desc,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="chatId != null">
                #{chatId,jdbcType=VARCHAR},
            </if>
            <if test="extraContent != null">
                #{extraContent,jdbcType=LONGVARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="username != null">
                username = #{username,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="deptDesc != null">
                dept_desc = #{deptDesc,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel2 != null">
                mi_dept_level2 = #{miDeptLevel2,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel2Desc != null">
                mi_dept_level2_desc = #{miDeptLevel2Desc,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel3 != null">
                mi_dept_level3 = #{miDeptLevel3,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel3Desc != null">
                mi_dept_level3_desc = #{miDeptLevel3Desc,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel4 != null">
                mi_dept_level4 = #{miDeptLevel4,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel4Desc != null">
                mi_dept_level4_desc = #{miDeptLevel4Desc,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="chatId != null">
                chat_id = #{chatId,jdbcType=VARCHAR},
            </if>
            <if test="extraContent != null">
                extra_content = #{extraContent,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
</mapper>