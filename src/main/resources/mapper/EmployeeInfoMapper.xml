<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.EmployeeInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.EmployeeInfo">
        <!--@mbg.generated-->
        <!--@Table employee_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="emp_id" jdbcType="VARCHAR" property="empId"/>
        <result column="emp_type" jdbcType="VARCHAR" property="empType"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="sex" jdbcType="VARCHAR" property="sex"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="mi_company_country" jdbcType="VARCHAR" property="miCompanyCountry"/>
        <result column="work_city" jdbcType="VARCHAR" property="workCity"/>
        <result column="work_address" jdbcType="VARCHAR" property="workAddress"/>
        <result column="birthday" jdbcType="BIGINT" property="birthday"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_desc" jdbcType="VARCHAR" property="deptDesc"/>
        <result column="last_hire_dt" jdbcType="TIMESTAMP" property="lastHireDt"/>
        <result column="record_update_dt" jdbcType="TIMESTAMP" property="recordUpdateDt"/>
        <result column="termination_dt" jdbcType="BIGINT" property="terminationDt"/>
        <result column="hr_status" jdbcType="VARCHAR" property="hrStatus"/>
        <result column="mi_dept_level2" jdbcType="VARCHAR" property="miDeptLevel2"/>
        <result column="mi_dept_level2_desc" jdbcType="VARCHAR" property="miDeptLevel2Desc"/>
        <result column="mi_dept_level3" jdbcType="VARCHAR" property="miDeptLevel3"/>
        <result column="mi_dept_level3_desc" jdbcType="VARCHAR" property="miDeptLevel3Desc"/>
        <result column="mi_dept_level4" jdbcType="VARCHAR" property="miDeptLevel4"/>
        <result column="mi_dept_level4_desc" jdbcType="VARCHAR" property="miDeptLevel4Desc"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, username, emp_id, emp_type, email, `name`, sex, country, mi_company_country,
        work_city, work_address, birthday, dept_id, dept_desc, last_hire_dt, record_update_dt,
        termination_dt, hr_status, mi_dept_level2, mi_dept_level2_desc, mi_dept_level3, mi_dept_level3_desc,
        mi_dept_level4, mi_dept_level4_desc, create_time, update_time, phone
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update employee_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.username,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="emp_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.empId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="emp_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.empType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="email = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sex = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.sex,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="country = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.country,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_company_country = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miCompanyCountry,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="work_city = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.workCity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="work_address = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.workAddress,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="birthday = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.birthday,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="dept_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deptDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_hire_dt = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.lastHireDt,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="record_update_dt = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.recordUpdateDt,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="termination_dt = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.terminationDt,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="hr_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.hrStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level2 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel2,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level2_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel2Desc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level3 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel3,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level3_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel3Desc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level4 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel4,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level4_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel4Desc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.phone,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update employee_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.username != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.username,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="emp_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.empId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.empId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="emp_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.empType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.empType,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="email = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.email != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.name != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sex = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sex != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.sex,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="country = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.country != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.country,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_company_country = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miCompanyCountry != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miCompanyCountry,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="work_city = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.workCity != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.workCity,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="work_address = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.workAddress != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.workAddress,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="birthday = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.birthday != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.birthday,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deptId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="dept_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deptDesc != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.deptDesc,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="last_hire_dt = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lastHireDt != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.lastHireDt,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="record_update_dt = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.recordUpdateDt != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.recordUpdateDt,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="termination_dt = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.terminationDt != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.terminationDt,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="hr_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hrStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.hrStatus,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level2 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel2 != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel2,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level2_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel2Desc != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel2Desc,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level3 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel3 != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel3,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level3_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel3Desc != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel3Desc,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level4 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel4 != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel4,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level4_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel4Desc != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel4Desc,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.phone != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.phone,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into employee_info
        (username, emp_id, emp_type, email, `name`, sex, country, mi_company_country, work_city,
        work_address, birthday, dept_id, dept_desc, last_hire_dt, record_update_dt, termination_dt,
        hr_status, mi_dept_level2, mi_dept_level2_desc, mi_dept_level3, mi_dept_level3_desc,
        mi_dept_level4, mi_dept_level4_desc, create_time, update_time, phone)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.username,jdbcType=VARCHAR}, #{item.empId,jdbcType=VARCHAR}, #{item.empType,jdbcType=VARCHAR},
            #{item.email,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.sex,jdbcType=VARCHAR},
            #{item.country,jdbcType=VARCHAR}, #{item.miCompanyCountry,jdbcType=VARCHAR},
            #{item.workCity,jdbcType=VARCHAR},
            #{item.workAddress,jdbcType=VARCHAR}, #{item.birthday,jdbcType=BIGINT}, #{item.deptId,jdbcType=VARCHAR},
            #{item.deptDesc,jdbcType=VARCHAR}, #{item.lastHireDt,jdbcType=TIMESTAMP},
            #{item.recordUpdateDt,jdbcType=TIMESTAMP},
            #{item.terminationDt,jdbcType=BIGINT}, #{item.hrStatus,jdbcType=VARCHAR},
            #{item.miDeptLevel2,jdbcType=VARCHAR},
            #{item.miDeptLevel2Desc,jdbcType=VARCHAR}, #{item.miDeptLevel3,jdbcType=VARCHAR},
            #{item.miDeptLevel3Desc,jdbcType=VARCHAR}, #{item.miDeptLevel4,jdbcType=VARCHAR},
            #{item.miDeptLevel4Desc,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=BIGINT}, #{item.phone,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.EmployeeInfo"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into employee_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            username,
            emp_id,
            emp_type,
            email,
            `name`,
            sex,
            country,
            mi_company_country,
            work_city,
            work_address,
            birthday,
            dept_id,
            dept_desc,
            last_hire_dt,
            record_update_dt,
            termination_dt,
            hr_status,
            mi_dept_level2,
            mi_dept_level2_desc,
            mi_dept_level3,
            mi_dept_level3_desc,
            mi_dept_level4,
            mi_dept_level4_desc,
            create_time,
            update_time,
            phone,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{username,jdbcType=VARCHAR},
            #{empId,jdbcType=VARCHAR},
            #{empType,jdbcType=VARCHAR},
            #{email,jdbcType=VARCHAR},
            #{name,jdbcType=VARCHAR},
            #{sex,jdbcType=VARCHAR},
            #{country,jdbcType=VARCHAR},
            #{miCompanyCountry,jdbcType=VARCHAR},
            #{workCity,jdbcType=VARCHAR},
            #{workAddress,jdbcType=VARCHAR},
            #{birthday,jdbcType=BIGINT},
            #{deptId,jdbcType=VARCHAR},
            #{deptDesc,jdbcType=VARCHAR},
            #{lastHireDt,jdbcType=TIMESTAMP},
            #{recordUpdateDt,jdbcType=TIMESTAMP},
            #{terminationDt,jdbcType=BIGINT},
            #{hrStatus,jdbcType=VARCHAR},
            #{miDeptLevel2,jdbcType=VARCHAR},
            #{miDeptLevel2Desc,jdbcType=VARCHAR},
            #{miDeptLevel3,jdbcType=VARCHAR},
            #{miDeptLevel3Desc,jdbcType=VARCHAR},
            #{miDeptLevel4,jdbcType=VARCHAR},
            #{miDeptLevel4Desc,jdbcType=VARCHAR},
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
            #{phone,jdbcType=VARCHAR},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            username = #{username,jdbcType=VARCHAR},
            emp_id = #{empId,jdbcType=VARCHAR},
            emp_type = #{empType,jdbcType=VARCHAR},
            email = #{email,jdbcType=VARCHAR},
            `name` = #{name,jdbcType=VARCHAR},
            sex = #{sex,jdbcType=VARCHAR},
            country = #{country,jdbcType=VARCHAR},
            mi_company_country = #{miCompanyCountry,jdbcType=VARCHAR},
            work_city = #{workCity,jdbcType=VARCHAR},
            work_address = #{workAddress,jdbcType=VARCHAR},
            birthday = #{birthday,jdbcType=BIGINT},
            dept_id = #{deptId,jdbcType=VARCHAR},
            dept_desc = #{deptDesc,jdbcType=VARCHAR},
            last_hire_dt = #{lastHireDt,jdbcType=TIMESTAMP},
            record_update_dt = #{recordUpdateDt,jdbcType=TIMESTAMP},
            termination_dt = #{terminationDt,jdbcType=BIGINT},
            hr_status = #{hrStatus,jdbcType=VARCHAR},
            mi_dept_level2 = #{miDeptLevel2,jdbcType=VARCHAR},
            mi_dept_level2_desc = #{miDeptLevel2Desc,jdbcType=VARCHAR},
            mi_dept_level3 = #{miDeptLevel3,jdbcType=VARCHAR},
            mi_dept_level3_desc = #{miDeptLevel3Desc,jdbcType=VARCHAR},
            mi_dept_level4 = #{miDeptLevel4,jdbcType=VARCHAR},
            mi_dept_level4_desc = #{miDeptLevel4Desc,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
            phone = #{phone,jdbcType=VARCHAR},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.EmployeeInfo"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into employee_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="username != null">
                username,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="empType != null">
                emp_type,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="country != null">
                country,
            </if>
            <if test="miCompanyCountry != null">
                mi_company_country,
            </if>
            <if test="workCity != null">
                work_city,
            </if>
            <if test="workAddress != null">
                work_address,
            </if>
            <if test="birthday != null">
                birthday,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="deptDesc != null">
                dept_desc,
            </if>
            <if test="lastHireDt != null">
                last_hire_dt,
            </if>
            <if test="recordUpdateDt != null">
                record_update_dt,
            </if>
            <if test="terminationDt != null">
                termination_dt,
            </if>
            <if test="hrStatus != null">
                hr_status,
            </if>
            <if test="miDeptLevel2 != null">
                mi_dept_level2,
            </if>
            <if test="miDeptLevel2Desc != null">
                mi_dept_level2_desc,
            </if>
            <if test="miDeptLevel3 != null">
                mi_dept_level3,
            </if>
            <if test="miDeptLevel3Desc != null">
                mi_dept_level3_desc,
            </if>
            <if test="miDeptLevel4 != null">
                mi_dept_level4,
            </if>
            <if test="miDeptLevel4Desc != null">
                mi_dept_level4_desc,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="phone != null">
                phone,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="username != null">
                #{username,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=VARCHAR},
            </if>
            <if test="empType != null">
                #{empType,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="country != null">
                #{country,jdbcType=VARCHAR},
            </if>
            <if test="miCompanyCountry != null">
                #{miCompanyCountry,jdbcType=VARCHAR},
            </if>
            <if test="workCity != null">
                #{workCity,jdbcType=VARCHAR},
            </if>
            <if test="workAddress != null">
                #{workAddress,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=BIGINT},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="deptDesc != null">
                #{deptDesc,jdbcType=VARCHAR},
            </if>
            <if test="lastHireDt != null">
                #{lastHireDt,jdbcType=TIMESTAMP},
            </if>
            <if test="recordUpdateDt != null">
                #{recordUpdateDt,jdbcType=TIMESTAMP},
            </if>
            <if test="terminationDt != null">
                #{terminationDt,jdbcType=BIGINT},
            </if>
            <if test="hrStatus != null">
                #{hrStatus,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel2 != null">
                #{miDeptLevel2,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel2Desc != null">
                #{miDeptLevel2Desc,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel3 != null">
                #{miDeptLevel3,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel3Desc != null">
                #{miDeptLevel3Desc,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel4 != null">
                #{miDeptLevel4,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel4Desc != null">
                #{miDeptLevel4Desc,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="username != null">
                username = #{username,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=VARCHAR},
            </if>
            <if test="empType != null">
                emp_type = #{empType,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="country != null">
                country = #{country,jdbcType=VARCHAR},
            </if>
            <if test="miCompanyCountry != null">
                mi_company_country = #{miCompanyCountry,jdbcType=VARCHAR},
            </if>
            <if test="workCity != null">
                work_city = #{workCity,jdbcType=VARCHAR},
            </if>
            <if test="workAddress != null">
                work_address = #{workAddress,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                birthday = #{birthday,jdbcType=BIGINT},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="deptDesc != null">
                dept_desc = #{deptDesc,jdbcType=VARCHAR},
            </if>
            <if test="lastHireDt != null">
                last_hire_dt = #{lastHireDt,jdbcType=TIMESTAMP},
            </if>
            <if test="recordUpdateDt != null">
                record_update_dt = #{recordUpdateDt,jdbcType=TIMESTAMP},
            </if>
            <if test="terminationDt != null">
                termination_dt = #{terminationDt,jdbcType=BIGINT},
            </if>
            <if test="hrStatus != null">
                hr_status = #{hrStatus,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel2 != null">
                mi_dept_level2 = #{miDeptLevel2,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel2Desc != null">
                mi_dept_level2_desc = #{miDeptLevel2Desc,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel3 != null">
                mi_dept_level3 = #{miDeptLevel3,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel3Desc != null">
                mi_dept_level3_desc = #{miDeptLevel3Desc,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel4 != null">
                mi_dept_level4 = #{miDeptLevel4,jdbcType=VARCHAR},
            </if>
            <if test="miDeptLevel4Desc != null">
                mi_dept_level4_desc = #{miDeptLevel4Desc,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="selectGroupCountByDeptId" resultType="com.mioffice.ums.admin.entity.bo.DeptCountBO">
        select mi_dept_level3 as deptId,
        count(*) as totalCount
        from employee_info
        where mi_dept_level3 in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
        group by mi_dept_level3
    </select>

    <select id="selectByUserName" resultType="com.mioffice.ums.admin.entity.vo.UserVo">
        select username, name from employee_info where username in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>