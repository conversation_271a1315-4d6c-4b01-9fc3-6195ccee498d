<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.MessageJobMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.MessageJob">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="rule_id" jdbcType="BIGINT" property="ruleId"/>
        <result column="job_id" jdbcType="BIGINT" property="jobId"/>
        <result column="corn_expr" jdbcType="VARCHAR" property="cronExpr"/>
        <result column="lark_channel" jdbcType="TINYINT" property="larkChannel"/>
        <result column="lark_bot_id" jdbcType="VARCHAR" property="larkBotBizId"/>
        <result column="lark_template_id" jdbcType="VARCHAR" property="larkTemplateBizId"/>
        <result column="mail_channel" jdbcType="TINYINT" property="mailChannel"/>
        <result column="mail_bot_id" jdbcType="VARCHAR" property="mailBotBizId"/>
        <result column="mail_template_id" jdbcType="VARCHAR" property="mailTemplateBizId"/>
        <result column="sms_channel" jdbcType="TINYINT" property="smsChannel"/>
        <result column="sms_bot_id" jdbcType="VARCHAR" property="smsBotBizId"/>
        <result column="sms_template_id" jdbcType="VARCHAR" property="smsTemplateBizId"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="job_param" jdbcType="VARCHAR" property="jobParam"/>
        <result column="job_type" jdbcType="INTEGER" property="jobType"/>
        <result column="lark_title_tmp" jdbcType="VARCHAR" property="larkTitleTmp"/>
        <result column="lark_content_tmp" jdbcType="VARCHAR" property="larkContentTmp"/>
        <result column="lark_btn_text" jdbcType="VARCHAR" property="larkBtnText"/>
        <result column="lark_btn_url" jdbcType="VARCHAR" property="larkBtnUrl"/>
        <result column="lark_title_tmp_en" jdbcType="VARCHAR" property="larkTitleTmpEn"/>
        <result column="lark_content_tmp_en" jdbcType="VARCHAR" property="larkContentTmpEn"/>
        <result column="lark_btn_text_en" jdbcType="VARCHAR" property="larkBtnTextEn"/>
        <result column="subsequent_job_type" jdbcType="INTEGER" property="subsequentJobType"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        rule_id,
        job_id,
        corn_expr,
        lark_channel,
        lark_bot_id,
        lark_template_id,
        sms_channel,
        sms_bot_id,
        sms_template_id,
        mail_channel,
        mail_bot_id,
        mail_template_id,
        create_username,
        create_time,
        update_username,
        update_time,
        job_param,
        job_type,
        lark_title_tmp,
        lark_content_tmp,
        lark_btn_text,
        lark_btn_url,
        lark_title_tmp_en,
        lark_content_tmp_en,
        lark_btn_text_en,
        subsequent_job_type
    </sql>

</mapper>
