<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.LarkGroupPushInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.LarkGroupPushInfo">
        <!--@mbg.generated-->
        <!--@Table lark_group_push_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tag_id" jdbcType="VARCHAR" property="tagId"/>
        <result column="chat_id" jdbcType="VARCHAR" property="chatId"/>
        <result column="chat_name" jdbcType="VARCHAR" property="chatName"/>
        <result column="owner_user_type" jdbcType="TINYINT" property="ownerUserType"/>
        <result column="owner_user_id" jdbcType="VARCHAR" property="ownerUserId"/>
        <result column="owner_name" jdbcType="VARCHAR" property="ownerName"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tag_id, chat_id, chat_name, owner_user_type, owner_user_id, owner_name, create_time,
        update_time
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update lark_group_push_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="tag_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.tagId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="chat_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.chatId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="chat_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.chatName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="owner_user_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.ownerUserType,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="owner_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.ownerUserId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="owner_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.ownerName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update lark_group_push_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="tag_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tagId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.tagId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="chat_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.chatId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.chatId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="chat_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.chatName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.chatName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="owner_user_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.ownerUserType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.ownerUserType,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="owner_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.ownerUserId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.ownerUserId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="owner_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.ownerName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.ownerName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into lark_group_push_info
        (tag_id, chat_id, chat_name, owner_user_type, owner_user_id, owner_name, create_time,
        update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.tagId,jdbcType=VARCHAR}, #{item.chatId,jdbcType=VARCHAR}, #{item.chatName,jdbcType=VARCHAR},
            #{item.ownerUserType,jdbcType=TINYINT}, #{item.ownerUserId,jdbcType=VARCHAR},
            #{item.ownerName,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.LarkGroupPushInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into lark_group_push_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            tag_id,
            chat_id,
            chat_name,
            owner_user_type,
            owner_user_id,
            owner_name,
            create_time,
            update_time,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{tagId,jdbcType=VARCHAR},
            #{chatId,jdbcType=VARCHAR},
            #{chatName,jdbcType=VARCHAR},
            #{ownerUserType,jdbcType=TINYINT},
            #{ownerUserId,jdbcType=VARCHAR},
            #{ownerName,jdbcType=VARCHAR},
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            tag_id = #{tagId,jdbcType=VARCHAR},
            chat_id = #{chatId,jdbcType=VARCHAR},
            chat_name = #{chatName,jdbcType=VARCHAR},
            owner_user_type = #{ownerUserType,jdbcType=TINYINT},
            owner_user_id = #{ownerUserId,jdbcType=VARCHAR},
            owner_name = #{ownerName,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.LarkGroupPushInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into lark_group_push_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="tagId != null">
                tag_id,
            </if>
            <if test="chatId != null">
                chat_id,
            </if>
            <if test="chatName != null">
                chat_name,
            </if>
            <if test="ownerUserType != null">
                owner_user_type,
            </if>
            <if test="ownerUserId != null">
                owner_user_id,
            </if>
            <if test="ownerName != null">
                owner_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="tagId != null">
                #{tagId,jdbcType=VARCHAR},
            </if>
            <if test="chatId != null">
                #{chatId,jdbcType=VARCHAR},
            </if>
            <if test="chatName != null">
                #{chatName,jdbcType=VARCHAR},
            </if>
            <if test="ownerUserType != null">
                #{ownerUserType,jdbcType=TINYINT},
            </if>
            <if test="ownerUserId != null">
                #{ownerUserId,jdbcType=VARCHAR},
            </if>
            <if test="ownerName != null">
                #{ownerName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="tagId != null">
                tag_id = #{tagId,jdbcType=VARCHAR},
            </if>
            <if test="chatId != null">
                chat_id = #{chatId,jdbcType=VARCHAR},
            </if>
            <if test="chatName != null">
                chat_name = #{chatName,jdbcType=VARCHAR},
            </if>
            <if test="ownerUserType != null">
                owner_user_type = #{ownerUserType,jdbcType=TINYINT},
            </if>
            <if test="ownerUserId != null">
                owner_user_id = #{ownerUserId,jdbcType=VARCHAR},
            </if>
            <if test="ownerName != null">
                owner_name = #{ownerName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
</mapper>