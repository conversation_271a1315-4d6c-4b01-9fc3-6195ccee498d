<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.admin.mapper.TaskMonitorInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.admin.entity.info.TaskMonitorInfo">
        <!--@mbg.generated-->
        <!--@Table task_monitor_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="extra_id" jdbcType="VARCHAR" property="extraId"/>
        <result column="all_count" jdbcType="BIGINT" property="allCount"/>
        <result column="push_count" jdbcType="BIGINT" property="pushCount"/>
        <result column="todo_count" jdbcType="BIGINT" property="todoCount"/>
        <result column="fail_count" jdbcType="BIGINT" property="failCount"/>
        <result column="interrupt_count" jdbcType="BIGINT" property="interruptCount"/>
        <result column="cost_time" jdbcType="BIGINT" property="costTime"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, task_id, extra_id, all_count, push_count, todo_count, fail_count, interrupt_count,
        cost_time, create_username, create_time, update_username, update_time
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update task_monitor_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="extra_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.extraId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="all_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.allCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="push_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.pushCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="todo_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.todoCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="fail_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.failCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="interrupt_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.interruptCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="cost_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.costTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update task_monitor_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="task_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="extra_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.extraId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.extraId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="all_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.allCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.allCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="push_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pushCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.pushCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="todo_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.todoCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.todoCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="fail_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.failCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.failCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="interrupt_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.interruptCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.interruptCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="cost_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.costTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.costTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into task_monitor_info
        (task_id, extra_id, all_count, push_count, todo_count, fail_count, interrupt_count,
        cost_time, create_username, create_time, update_username, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.taskId,jdbcType=BIGINT}, #{item.extraId,jdbcType=VARCHAR}, #{item.allCount,jdbcType=BIGINT},
            #{item.pushCount,jdbcType=BIGINT}, #{item.todoCount,jdbcType=BIGINT}, #{item.failCount,jdbcType=BIGINT},
            #{item.interruptCount,jdbcType=BIGINT}, #{item.costTime,jdbcType=BIGINT},
            #{item.createUsername,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=BIGINT}, #{item.updateUsername,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=BIGINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.TaskMonitorInfo"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into task_monitor_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            task_id,
            extra_id,
            all_count,
            push_count,
            todo_count,
            fail_count,
            interrupt_count,
            cost_time,
            create_username,
            create_time,
            update_username,
            update_time,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{taskId,jdbcType=BIGINT},
            #{extraId,jdbcType=VARCHAR},
            #{allCount,jdbcType=BIGINT},
            #{pushCount,jdbcType=BIGINT},
            #{todoCount,jdbcType=BIGINT},
            #{failCount,jdbcType=BIGINT},
            #{interruptCount,jdbcType=BIGINT},
            #{costTime,jdbcType=BIGINT},
            #{createUsername,jdbcType=VARCHAR},
            #{createTime,jdbcType=BIGINT},
            #{updateUsername,jdbcType=VARCHAR},
            #{updateTime,jdbcType=BIGINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            task_id = #{taskId,jdbcType=BIGINT},
            extra_id = #{extraId,jdbcType=VARCHAR},
            all_count = #{allCount,jdbcType=BIGINT},
            push_count = #{pushCount,jdbcType=BIGINT},
            todo_count = #{todoCount,jdbcType=BIGINT},
            fail_count = #{failCount,jdbcType=BIGINT},
            interrupt_count = #{interruptCount,jdbcType=BIGINT},
            cost_time = #{costTime,jdbcType=BIGINT},
            create_username = #{createUsername,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=BIGINT},
            update_username = #{updateUsername,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=BIGINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.admin.entity.info.TaskMonitorInfo"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into task_monitor_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="extraId != null">
                extra_id,
            </if>
            <if test="allCount != null">
                all_count,
            </if>
            <if test="pushCount != null">
                push_count,
            </if>
            <if test="todoCount != null">
                todo_count,
            </if>
            <if test="failCount != null">
                fail_count,
            </if>
            <if test="interruptCount != null">
                interrupt_count,
            </if>
            <if test="costTime != null">
                cost_time,
            </if>
            <if test="createUsername != null">
                create_username,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUsername != null">
                update_username,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="extraId != null">
                #{extraId,jdbcType=VARCHAR},
            </if>
            <if test="allCount != null">
                #{allCount,jdbcType=BIGINT},
            </if>
            <if test="pushCount != null">
                #{pushCount,jdbcType=BIGINT},
            </if>
            <if test="todoCount != null">
                #{todoCount,jdbcType=BIGINT},
            </if>
            <if test="failCount != null">
                #{failCount,jdbcType=BIGINT},
            </if>
            <if test="interruptCount != null">
                #{interruptCount,jdbcType=BIGINT},
            </if>
            <if test="costTime != null">
                #{costTime,jdbcType=BIGINT},
            </if>
            <if test="createUsername != null">
                #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateUsername != null">
                #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=BIGINT},
            </if>
            <if test="extraId != null">
                extra_id = #{extraId,jdbcType=VARCHAR},
            </if>
            <if test="allCount != null">
                all_count = #{allCount,jdbcType=BIGINT},
            </if>
            <if test="pushCount != null">
                push_count = #{pushCount,jdbcType=BIGINT},
            </if>
            <if test="todoCount != null">
                todo_count = #{todoCount,jdbcType=BIGINT},
            </if>
            <if test="failCount != null">
                fail_count = #{failCount,jdbcType=BIGINT},
            </if>
            <if test="interruptCount != null">
                interrupt_count = #{interruptCount,jdbcType=BIGINT},
            </if>
            <if test="costTime != null">
                cost_time = #{costTime,jdbcType=BIGINT},
            </if>
            <if test="createUsername != null">
                create_username = #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateUsername != null">
                update_username = #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <select id="selectSendingTaskId" resultType="java.lang.Long">
        select distinct task_id
        from task_monitor_info
        where task_id not in (select distinct task_id
                              from task_monitor_info
                              where todo_count = 0)
    </select>
</mapper>