#!/bin/bash
#执行命令
# sh mitag.sh '环境参数' 'tag附加信心'
# sh mitag.sh 'tag附加信心（非环境参数）'
#项目名
project="ums-admin"
#tag前缀
prefix=""
#tag附加信息
msg=""

test_prefix="test-"${project}
pre_prefix="pre-"${project}
pro_prefix="pro-"${project}

if `git status | grep "master" &>/dev/null`; then
    prefix=${pro_prefix}
fi
# 默认第一个参数为环境参数，第二个参数为tag附加信息；如果第一个参数不为pre、pro，则设置tag附加信息为第一个参数值。
if [ "$1" == "pre" ]; then
    prefix=${pre_prefix}
fi
if [ "$1" == "pro" ]; then
    prefix=${pro_prefix}
fi
if [ "${prefix}" == "" ]; then
    prefix=${test_prefix}
    msg="-"$1
else
    msg="-"$2
fi
if [ "${msg}" == "-" ]; then
    msg="-"$(date +'%Hh%Mm')
fi

function mi_tag() {
    git push
    git pull --tags
    local new_tag=$(echo ${prefix}-$(date +'%Y%m%d')-$(git tag -l "${prefix}-$(date +'%Y%m%d')-*" | wc -l | xargs printf '%02d')${msg})
    echo ${new_tag}
    git tag ${new_tag}
    git push origin $new_tag
}

mi_tag;

