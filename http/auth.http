### 获取 app_access_token
POST https://open.f.mioffice.cn/open-apis/auth/v3/app_access_token/internal/
Content-Type: application/json

{
  "app_id": "cli_9fa93b4012ed5063",
  "app_secret": "YV3nwrI5JTNU3KpLnL12kdgGeckuVL57"
}

### 获取 user_access_token
POST https://open.f.mioffice.cn/open-apis/authen/v1/access_token
Content-Type: application/json
Authorization: Bearer t-2c725bfd6e351e0842b084f6d2cdadb2ff5d1d5a

{
  "code": "4Ya9eiBULRHHiQVuKWhW0f"
}

### 获取 user_access_token
### code 是一次性的
POST https://open.f.mioffice.cn/connect/qrconnect/oauth2/access_token/
Content-Type: application/json

{
  "app_id": "cli_9fa93b4012ed5063",
  "app_secret": "YV3nwrI5JTNU3KpLnL12kdgGeckuVL57",
  "app_access_token":"t-21a53a649378c0ab7f0b49861c787c4b119ea1cd",
  "grant_type":"authorization_code",
  "code":"YmAotay0o8fNV53B2usfXf"
}


### 创建文档接口
#POST https://open.f.mioffice.cn/open-apis/drive/explorer/v2/file/fldk46Rx3nhW9W5iZlUsKLL0lmf
POST https://open.f.mioffice.cn/open-apis/drive/explorer/v2/file/fldk46Rx3nhW9W5iZlUsKLL0lmf
Authorization: Bearer u-dLpRsTaGxIxowyBxlgZQsd
Content-Type: application/json; charset=utf-8

{
  "title": "测试",
  "type": "sheet"
}

### 编辑文档接口
POST https://open.f.mioffice.cn/open-apis/doc/v2/dock48B0VFkqZF2WWjVj7k3Qrld/batch_update
Authorization: Bearer u-dLpRsTaGxIxowyBxlgZQsd
Content-Type: application/json; charset=utf-8

{
  "requestType": "string",
  "Revision": 2,
  "Requests": ""
}





### 获取doc文档
GET https://open.f.mioffice.cn/open-apis/doc/v2/shtk4Bu06G76lZtqWUwYCWOlsnh/sheet_meta

#GET https://open.f.mioffice.cn/open-apis/doc/v2/dock4l0hADz74BuwKizcySxmx4e/raw_content
#GET https://open.f.mioffice.cn/open-apis/doc/v2/dock48B0VFkqZF2WWjVj7k3Qrld/raw_content
Authorization: Bearer u-BI9p4naifaGZHWyha4xrPf
Content-Type: application/json; charset=utf-8

### 获取sheet文档
GET https://open.f.mioffice.cn/open-apis/sheet/v2/spreadsheets/shtk4yNU6DP6LCrw2ua8QKZRvUe/metainfo
#GET https://open.f.mioffice.cn/open-apis/doc/v2/dock4l0hADz74BuwKizcySxmx4e/raw_content
#GET https://open.f.mioffice.cn/open-apis/doc/v2/dock48B0VFkqZF2WWjVj7k3Qrld/raw_content
Authorization: Bearer u-zCZZGKSvhDHhw3IcKTsB7f
Content-Type: application/json; charset=utf-8

### 获取sheet文档
GET https://open.f.mioffice.cn/open-apis/sheet/v2/spreadsheets/shtk4yNU6DP6LCrw2ua8QKZRvUe/values/v70PHa!A14:B2
#GET https://open.f.mioffice.cn/open-apis/doc/v2/dock4l0hADz74BuwKizcySxmx4e/raw_content
#GET https://open.f.mioffice.cn/open-apis/doc/v2/dock48B0VFkqZF2WWjVj7k3Qrld/raw_content
Authorization: Bearer u-mGkucpM8ntW5X6HrmGf2Xb
Content-Type: application/json; charset=utf-8

### 获取sheet文档
GET https://open.f.mioffice.cn/open-apis/sheet/v2/spreadsheets/shtk4yNU6DP6LCrw2ua8QKZRvUe/values_batch_get?ranges=v70PHa!A13:L13
Authorization: Bearer u-v7iGxGfDjjXvnnx1uP0ref
Content-Type: application/json; charset=utf-8

### refresh user_access_token
POST https://open.f.mioffice.cn/open-apis/authen/v1/access_token
Content-Type: application/json

{
  "app_access_token":"t-9a6cc355d3e67951d917727684b2f34b72d8e7cb",
  "grant_type":"authorization_code",
  "code":"Q4NDz1tsqqQvhQjS0HTZOg"
}