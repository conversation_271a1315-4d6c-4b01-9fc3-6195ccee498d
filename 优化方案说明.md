# TaskInfo 查询优化方案

## 问题分析

### 原始逻辑存在的问题
1. **重复查询**: 在 `getBotOverview` 和 `getBotPushDetail` 方法中，分别查询本月和历史数据，导致多次数据库查询
2. **查询频次高**: 每次都需要执行两次 TaskInfo 查询（本月 + 历史）
3. **性能瓶颈**: 虽然数据量不大（TaskInfo 2万+，子表千级别），但频繁的数据库查询影响性能

### 数据量情况
- TaskInfo 主表：2万+ 数据
- 子表（lark_task_info, email_task_info, sms_task_info）：千级别数据

## 优化方案

### 核心思路
**一次性查询 + 内存计算**：将原来的两次数据库查询优化为一次查询，然后在内存中进行时间范围过滤和分组。

### 具体优化内容

#### 1. getBotOverview 方法优化

**优化前**:
```java
// 查询本月任务
List<TaskInfo> taskInfoListCurMonth = taskInfoMapper.selectList(
    Wrappers.<TaskInfo>lambdaQuery()
            .in(TaskInfo::getTaskStatus, effectiveStatus)
            .ge(TaskInfo::getCreateTime, currentMonthStart)
            .le(TaskInfo::getCreateTime, currentMonthEnd)
            .select(TaskInfo::getId)
);

// 查询所有历史任务
List<TaskInfo> taskInfoListUpToNow = taskInfoMapper.selectList(
    Wrappers.<TaskInfo>lambdaQuery()
            .in(TaskInfo::getTaskStatus, effectiveStatus)
            .select(TaskInfo::getId)
);
```

**优化后**:
```java
// 一次性查询所有有效状态的任务
List<TaskInfo> allEffectiveTaskInfoList = taskInfoMapper.selectList(
    Wrappers.<TaskInfo>lambdaQuery()
            .in(TaskInfo::getTaskStatus, effectiveStatus)
            .select(TaskInfo::getId, TaskInfo::getCreateTime)
);

// 在内存中按时间分组
List<Long> taskIdsCurMonth = allEffectiveTaskInfoList.stream()
        .filter(task -> task.getCreateTime() >= currentMonthStart && task.getCreateTime() <= currentMonthEnd)
        .map(TaskInfo::getId)
        .distinct()
        .collect(Collectors.toList());

List<Long> taskIdsUpToNow = allEffectiveTaskInfoList.stream()
        .map(TaskInfo::getId)
        .distinct()
        .collect(Collectors.toList());
```

#### 2. getBotPushDetail 方法优化

采用相同的优化策略，一次性查询 + 内存分组统计：

```java
// 优化前：6 次子表查询
Map<Long, Long> larkCurMonthPushMap = larkTaskInfoMapper.selectLarkPushCount(taskInfoIdListCurMonth, myBotIds);
Map<Long, Long> larkUpToNowPushMap = larkTaskInfoMapper.selectLarkPushCount(taskIdListUpToNow, myBotIds);
// ... 其他子表类似

// 优化后：3 次查询 + 内存分组
List<LarkTaskInfo> allLarkRecords = larkTaskInfoMapper.selectList(...);
larkUpToNowPushMap = allLarkRecords.stream()
        .collect(Collectors.groupingBy(LarkTaskInfo::getBotId, Collectors.counting()));
larkCurMonthPushMap = allLarkRecords.stream()
        .filter(record -> curMonthTaskIdSet.contains(record.getTaskId()))
        .collect(Collectors.groupingBy(LarkTaskInfo::getBotId, Collectors.counting()));
```

#### 3. 终极优化：一次性查询 + 内存过滤

**问题发现**：
- `getBotOverview` 方法需要查询 3 个子表（lark_task_info, email_task_info, sms_task_info）
- 每个时间范围需要 3 次查询，总共 **6 次子表查询**（3个子表 × 2个时间范围）

**优化策略**：
一次性查询所有符合条件的子表记录，然后在内存中按时间过滤统计

```java
// 优化前：6 次子表查询
// 本月查询
Integer larkCurMonth = larkTaskInfoMapper.selectCount(taskIdsCurMonth, myBotIds);
Integer emailCurMonth = emailTaskInfoMapper.selectCount(taskIdsCurMonth, myBotIds);
Integer smsCurMonth = smsTaskInfoMapper.selectCount(taskIdsCurMonth, myBotIds);
// 历史查询
Integer larkUpToNow = larkTaskInfoMapper.selectCount(taskIdsUpToNow, myBotIds);
Integer emailUpToNow = emailTaskInfoMapper.selectCount(taskIdsUpToNow, myBotIds);
Integer smsUpToNow = smsTaskInfoMapper.selectCount(taskIdsUpToNow, myBotIds);

// 优化后：3 次查询 + 内存过滤
List<LarkTaskInfo> allLarkRecords = larkTaskInfoMapper.selectList(...);
List<EmailTaskInfo> allEmailRecords = emailTaskInfoMapper.selectList(...);
List<SmsTaskInfo> allSmsRecords = smsTaskInfoMapper.selectList(...);

// 内存中按时间过滤
long curMonthCount = allRecords.stream()
    .filter(record -> curMonthTaskIdSet.contains(record.getTaskId()))
    .count();
```

**优势**：
1. **真正减少查询次数**：从 6 次减少到 3 次
2. **利用索引效率高**：`(task_id, bot_id)` 联合索引
3. **代码可读性好**：逻辑清晰，易于理解和维护
4. **扩展性强**：如果需要其他时间范围统计，只需要改内存过滤逻辑

#### 4. 最终优化：提取公共机器人查询逻辑

**问题发现**：
- `getBotOverview` 和 `getBotPushDetail` 方法中都有相同的机器人查询逻辑
- 每个方法都查询用户机器人和公共机器人，总共 **4 次额外查询**（2次用户机器人 + 2次公共机器人）

**优化策略**：
提取公共方法 `getUserAccessibleBotIds`，避免重复查询

```java
// 优化前：两个方法中重复的查询
// getBotOverview 方法
List<UserBotInfo> myBotList = userBotInfoMapper.selectList(...);
List<Long> publicBots = botInfoMapper.selectList(...);

// getBotPushDetail 方法
List<UserBotInfo> myBotList = userBotInfoMapper.selectList(...); // 重复查询
List<Long> publicBots = botInfoMapper.selectList(...); // 重复查询

// 优化后：提取公共方法
private List<Long> getUserAccessibleBotIds(String username) {
    // 统一的机器人查询逻辑
}

// 两个方法都调用
List<Long> myBotIds = getUserAccessibleBotIds(username);
```

#### 5. 终极优化：提取机器人查询到调用层

**问题发现**：
- 在 `myBot` 方法中，`getBotOverview` 和 `getBotPushDetail` 都会调用 `getUserAccessibleBotIds`
- 虽然提取了公共方法，但在同一个请求中仍然会执行 **2 次相同的机器人查询**

**优化策略**：
将机器人查询提取到 `myBot` 方法中，作为参数传递给子方法

```java
// 优化前：在同一个请求中重复查询
@Override
public BaseResult<MyBotDTO> myBot(String username) {
    BotOverviewDTO botOverviewDTO = this.getBotOverview(username);        // 内部查询机器人
    List<BotPushDetailDTO> botPushDetailDTO = this.getBotPushDetail(username); // 内部再次查询机器人
}

// 优化后：提取到上层，避免重复查询
@Override
public BaseResult<MyBotDTO> myBot(String username) {
    List<Long> myBotIds = getUserAccessibleBotIds(username);  // 只查询一次
    BotOverviewDTO botOverviewDTO = this.getBotOverview(username, myBotIds);
    List<BotPushDetailDTO> botPushDetailDTO = this.getBotPushDetail(username, myBotIds);
}
```

#### 6. 最终优化：提取子表查询到上下文

**问题发现**：
- `getBotOverview` 和 `getBotPushDetail` 方法中都有相同的子表查询逻辑
- 每个方法都查询 3 个子表（lark_task_info, email_task_info, sms_task_info），总共 **6 次子表查询**

**优化策略**：
将子表查询提取到 `TaskDataContext` 中，一次性查询后在两个方法间共享

```java
// 优化前：两个方法中重复的子表查询
// getBotOverview 方法
List<LarkTaskInfo> allLarkRecords = larkTaskInfoMapper.selectList(...);
List<EmailTaskInfo> allEmailRecords = emailTaskInfoMapper.selectList(...);
List<SmsTaskInfo> allSmsRecords = smsTaskInfoMapper.selectList(...);

// getBotPushDetail 方法
List<LarkTaskInfo> allLarkRecords = larkTaskInfoMapper.selectList(...); // 重复查询
List<EmailTaskInfo> allEmailRecords = emailTaskInfoMapper.selectList(...); // 重复查询
List<SmsTaskInfo> allSmsRecords = smsTaskInfoMapper.selectList(...); // 重复查询

// 优化后：在 TaskDataContext 中统一查询
private TaskDataContext getTaskDataContext(String username) {
    // ... 其他查询逻辑

    // 一次性查询所有子表数据
    List<LarkTaskInfo> allLarkRecords = larkTaskInfoMapper.selectList(...);
    List<EmailTaskInfo> allEmailRecords = emailTaskInfoMapper.selectList(...);
    List<SmsTaskInfo> allSmsRecords = smsTaskInfoMapper.selectList(...);

    return new TaskDataContext(..., allLarkRecords, allEmailRecords, allSmsRecords);
}

// 两个方法都从上下文获取数据
List<LarkTaskInfo> allLarkRecords = taskDataContext.getAllLarkRecords();
```

## 优化效果

### 性能提升

#### myBot 方法（主要调用入口）
1. **机器人查询优化**: 从 4 次机器人查询减少到 **2 次**（只在最上层查询一次）
2. **总查询次数**: 从 **20 次**减少到 **6 次**，优化 **70%**

#### getBotOverview 方法
1. **主表查询优化**: 从 2 次 TaskInfo 查询减少到 1 次
2. **子表查询优化**: 从 6 次子表查询减少到 **3 次**
3. **机器人查询优化**: 从 2 次机器人查询减少到 **0 次**（通过参数传递）
4. **查询次数**: 从 **10 次**减少到 **4 次**

#### getBotPushDetail 方法
1. **主表查询优化**: 从 2 次 TaskInfo 查询减少到 1 次
2. **子表查询优化**: 从 6 次子表查询减少到 **3 次**
3. **机器人查询优化**: 从 2 次机器人查询减少到 **0 次**（通过参数传递）
4. **查询次数**: 从 **10 次**减少到 **4 次**

#### 机器人查询层级优化
1. **提取到调用层**: 在 `myBot` 方法中统一查询机器人，避免子方法重复查询
2. **参数传递**: 通过方法参数传递机器人ID列表，避免重复数据库查询
3. **向后兼容**: 保留原方法签名，确保其他调用方不受影响

#### 总体效果
1. **数据库压力降低**: `myBot` 方法总共减少了 **70%** 的查询次数
2. **响应时间优化**: 特别是在高并发场景下，数据库连接池压力显著降低
3. **网络开销减少**: 减少了数据库连接次数和网络传输
4. **索引利用率高**: 充分利用 `(task_id, bot_id)` 联合索引
5. **内存计算高效**: 使用 Java Stream 和 HashSet 进行高效的内存过滤和分组
6. **代码维护性**: 层次化的查询优化，减少重复代码，提高可维护性
7. **架构清晰**: 数据查询和业务逻辑分离，职责更加明确

### 内存使用
1. **内存开销可控**: 2万条记录的 ID 和 createTime 字段，内存占用约 1-2MB
2. **计算效率高**: 内存中的时间过滤和分组操作比数据库查询快得多

### 代码维护性
1. **逻辑更清晰**: 一次查询 + 内存分组的逻辑更容易理解
2. **扩展性更好**: 如果需要增加其他时间范围的统计，只需要在内存中增加过滤条件

## 其他优化建议

### 1. 索引优化
建议添加联合索引：
```sql
-- 针对 task_status 和 create_time 的联合索引
CREATE INDEX idx_task_status_create_time ON task_info(task_status, create_time);
```

### 2. 缓存策略
如果查询频率非常高，可以考虑：
- Redis 缓存热点数据
- 定时任务预计算统计结果
- 使用本地缓存（如 Caffeine）缓存短期结果

### 3. 分页查询
如果数据量继续增长，可以考虑：
- 分页查询 TaskInfo
- 流式处理大数据集

## 总结

这次优化通过**减少数据库查询次数**和**利用内存计算**的方式，在数据量相对较小的情况下显著提升了性能。优化方案简单有效，代码可读性好，是一个典型的**以空间换时间**的优化策略。

在当前数据规模下（2万+ 主表数据），这种优化方案是最合适的。如果未来数据量增长到百万级别，可以考虑引入更复杂的缓存和分页策略。
