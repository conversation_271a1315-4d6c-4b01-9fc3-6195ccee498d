# TaskInfo 查询优化方案

## 问题分析

### 原始逻辑存在的问题
1. **重复查询**: 在 `getBotOverview` 和 `getBotPushDetail` 方法中，分别查询本月和历史数据，导致多次数据库查询
2. **查询频次高**: 每次都需要执行两次 TaskInfo 查询（本月 + 历史）
3. **性能瓶颈**: 虽然数据量不大（TaskInfo 2万+，子表千级别），但频繁的数据库查询影响性能

### 数据量情况
- TaskInfo 主表：2万+ 数据
- 子表（lark_task_info, email_task_info, sms_task_info）：千级别数据

## 优化方案

### 核心思路
**一次性查询 + 内存计算**：将原来的两次数据库查询优化为一次查询，然后在内存中进行时间范围过滤和分组。

### 具体优化内容

#### 1. getBotOverview 方法优化

**优化前**:
```java
// 查询本月任务
List<TaskInfo> taskInfoListCurMonth = taskInfoMapper.selectList(
    Wrappers.<TaskInfo>lambdaQuery()
            .in(TaskInfo::getTaskStatus, effectiveStatus)
            .ge(TaskInfo::getCreateTime, currentMonthStart)
            .le(TaskInfo::getCreateTime, currentMonthEnd)
            .select(TaskInfo::getId)
);

// 查询所有历史任务
List<TaskInfo> taskInfoListUpToNow = taskInfoMapper.selectList(
    Wrappers.<TaskInfo>lambdaQuery()
            .in(TaskInfo::getTaskStatus, effectiveStatus)
            .select(TaskInfo::getId)
);
```

**优化后**:
```java
// 一次性查询所有有效状态的任务
List<TaskInfo> allEffectiveTaskInfoList = taskInfoMapper.selectList(
    Wrappers.<TaskInfo>lambdaQuery()
            .in(TaskInfo::getTaskStatus, effectiveStatus)
            .select(TaskInfo::getId, TaskInfo::getCreateTime)
);

// 在内存中按时间分组
List<Long> taskIdsCurMonth = allEffectiveTaskInfoList.stream()
        .filter(task -> task.getCreateTime() >= currentMonthStart && task.getCreateTime() <= currentMonthEnd)
        .map(TaskInfo::getId)
        .distinct()
        .collect(Collectors.toList());

List<Long> taskIdsUpToNow = allEffectiveTaskInfoList.stream()
        .map(TaskInfo::getId)
        .distinct()
        .collect(Collectors.toList());
```

#### 2. getBotPushDetail 方法优化

采用相同的优化策略，同时增加了空集合检查，避免不必要的子表查询：

```java
Map<Long, Long> larkCurMonthPushMap = taskInfoIdListCurMonth.isEmpty() ? 
        new HashMap<>() : 
        larkTaskInfoMapper.selectLarkPushCount(taskInfoIdListCurMonth, myBotIds)
                .stream().collect(Collectors.toMap(PushCountBo::getBotId, PushCountBo::getPushCount));
```

## 优化效果

### 性能提升
1. **查询次数减少**: 从每次调用 2 次 TaskInfo 查询减少到 1 次
2. **数据库压力降低**: 减少了 50% 的主表查询次数
3. **响应时间优化**: 特别是在高并发场景下，数据库连接池压力显著降低

### 内存使用
1. **内存开销可控**: 2万条记录的 ID 和 createTime 字段，内存占用约 1-2MB
2. **计算效率高**: 内存中的时间过滤和分组操作比数据库查询快得多

### 代码维护性
1. **逻辑更清晰**: 一次查询 + 内存分组的逻辑更容易理解
2. **扩展性更好**: 如果需要增加其他时间范围的统计，只需要在内存中增加过滤条件

## 其他优化建议

### 1. 索引优化
建议添加联合索引：
```sql
-- 针对 task_status 和 create_time 的联合索引
CREATE INDEX idx_task_status_create_time ON task_info(task_status, create_time);
```

### 2. 缓存策略
如果查询频率非常高，可以考虑：
- Redis 缓存热点数据
- 定时任务预计算统计结果
- 使用本地缓存（如 Caffeine）缓存短期结果

### 3. 分页查询
如果数据量继续增长，可以考虑：
- 分页查询 TaskInfo
- 流式处理大数据集

## 总结

这次优化通过**减少数据库查询次数**和**利用内存计算**的方式，在数据量相对较小的情况下显著提升了性能。优化方案简单有效，代码可读性好，是一个典型的**以空间换时间**的优化策略。

在当前数据规模下（2万+ 主表数据），这种优化方案是最合适的。如果未来数据量增长到百万级别，可以考虑引入更复杂的缓存和分页策略。
